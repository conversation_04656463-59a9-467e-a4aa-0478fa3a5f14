// Fill out your copyright notice in the Description page of Project Settings.

#include "UnitDatabaseSubsystem.h"
#include "RTSEconomySubsystem.h"
#include "Engine/Engine.h"
#include "Engine/World.h"

void UUnitDatabaseSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    LoadUnitDefinitions();
    UE_LOG(LogTemp, Log, TEXT("UnitDatabaseSubsystem initialized"));
}

void UUnitDatabaseSubsystem::Deinitialize()
{
    // Mass Entity template cache removed
    UE_LOG(LogTemp, Log, TEXT("UnitDatabaseSubsystem deinitialized"));

    Super::Deinitialize();
}

FRTSUnitDefinition UUnitDatabaseSubsystem::GetUnitDefinition(FName UnitID) const
{
    if (UnitDefinitionsTable)
    {
        FRTSUnitDefinition* FoundDefinition = UnitDefinitionsTable->FindRow<FRTSUnitDefinition>(UnitID, TEXT(""));
        if (FoundDefinition)
        {
            return *FoundDefinition;
        }
    }

    // Return default/empty definition if not found
    return FRTSUnitDefinition();
}

TArray<FRTSUnitDefinition> UUnitDatabaseSubsystem::GetAllUnitDefinitions() const
{
    TArray<FRTSUnitDefinition> AllDefinitions;

    if (UnitDefinitionsTable)
    {
        TArray<FRTSUnitDefinition*> AllRows;
        UnitDefinitionsTable->GetAllRows<FRTSUnitDefinition>(TEXT(""), AllRows);

        for (FRTSUnitDefinition* Row : AllRows)
        {
            if (Row)
            {
                AllDefinitions.Add(*Row);
            }
        }
    }

    return AllDefinitions;
}

TArray<FRTSUnitDefinition> UUnitDatabaseSubsystem::GetUnitDefinitionsByTag(FName Tag) const
{
    TArray<FRTSUnitDefinition> FilteredDefinitions;
    TArray<FRTSUnitDefinition> AllDefinitions = GetAllUnitDefinitions();

    for (const FRTSUnitDefinition& Definition : AllDefinitions)
    {
        if (Definition.UnitTags.Contains(Tag))
        {
            FilteredDefinitions.Add(Definition);
        }
    }

    return FilteredDefinitions;
}

// Mass Entity template system removed - using direct actor spawning instead
bool UUnitDatabaseSubsystem::GetUnitTemplate(FName UnitID, UWorld* World)
{
    // Simplified approach - just check if unit definition exists
    FRTSUnitDefinition UnitDef = GetUnitDefinition(UnitID);
    return UnitDef.UnitClass != nullptr;
}

bool UUnitDatabaseSubsystem::PrepareUnitTemplate(FName UnitID, UWorld* World)
{
    return GetUnitTemplate(UnitID, World);
}

bool UUnitDatabaseSubsystem::IsUnitTemplateReady(FName UnitID) const
{
    FRTSUnitDefinition UnitDef = GetUnitDefinition(UnitID);
    return UnitDef.UnitClass != nullptr;
}

AActor* UUnitDatabaseSubsystem::SpawnUnit(FName UnitID, FVector Location, FRotator Rotation, UWorld* World)
{
    if (!World)
    {
        return nullptr;
    }

    // Get unit definition
    FRTSUnitDefinition UnitDef = GetUnitDefinition(UnitID);
    if (!UnitDef.UnitClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("No unit class found for UnitID: %s"), *UnitID.ToString());
        return nullptr;
    }

    // Spawn actor directly
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* SpawnedActor = World->SpawnActor<AActor>(UnitDef.UnitClass, Location, Rotation, SpawnParams);
    if (SpawnedActor)
    {
        UE_LOG(LogTemp, Log, TEXT("Successfully spawned unit: %s"), *UnitID.ToString());
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to spawn unit: %s"), *UnitID.ToString());
    }

    return SpawnedActor;
}

bool UUnitDatabaseSubsystem::SpawnUnitSimple(FName UnitID, FVector Location, FRotator Rotation, UWorld* World)
{
    AActor* SpawnedActor = SpawnUnit(UnitID, Location, Rotation, World);
    return SpawnedActor != nullptr;
}

void UUnitDatabaseSubsystem::LoadUnitDefinitions()
{
    // TODO: Load from a data table asset
    // For now, this is a placeholder - you would typically load from a DataTable asset
    // Example:
    // UnitDefinitionsTable = LoadObject<UDataTable>(nullptr, TEXT("/Game/Data/UnitDefinitions"));

    UE_LOG(LogTemp, Log, TEXT("LoadUnitDefinitions called - implement data table loading here"));
}



FRTSResourceCost UUnitDatabaseSubsystem::GetUnitResourceCost(FName UnitID) const
{
    FRTSUnitDefinition UnitDef = GetUnitDefinition(UnitID);
    return UnitDef.ResourceCost;
}

FRTSResourceCost UUnitDatabaseSubsystem::GetUnitMaintenanceCost(FName UnitID) const
{
    FRTSUnitDefinition UnitDef = GetUnitDefinition(UnitID);
    return UnitDef.MaintenanceCost;
}

bool UUnitDatabaseSubsystem::CanAffordUnit(FName UnitID, int32 PlayerID) const
{
    // Get the economy subsystem
    URTSEconomySubsystem* EconomySubsystem = GetGameInstance()->GetSubsystem<URTSEconomySubsystem>();
    if (!EconomySubsystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("Economy subsystem not found"));
        return false;
    }

    FRTSResourceCost UnitCost = GetUnitResourceCost(UnitID);
    return EconomySubsystem->HasSufficientResources(PlayerID, UnitCost);
}

AActor* UUnitDatabaseSubsystem::SpawnUnitWithEconomy(FName UnitID, FVector Location, FRotator Rotation, UWorld* World, int32 PlayerID)
{
    // Get the economy subsystem
    URTSEconomySubsystem* EconomySubsystem = GetGameInstance()->GetSubsystem<URTSEconomySubsystem>();
    if (!EconomySubsystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("Economy subsystem not found"));
        return nullptr;
    }

    // Check if player can afford the unit
    FRTSResourceCost UnitCost = GetUnitResourceCost(UnitID);
    if (!EconomySubsystem->HasSufficientResources(PlayerID, UnitCost))
    {
        UE_LOG(LogTemp, Warning, TEXT("Player %d cannot afford unit %s"), PlayerID, *UnitID.ToString());
        return nullptr;
    }

    // Consume resources
    if (!EconomySubsystem->ConsumeResources(PlayerID, UnitCost))
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to consume resources for unit %s"), *UnitID.ToString());
        return nullptr;
    }

    // Spawn the unit
    AActor* SpawnedUnit = SpawnUnit(UnitID, Location, Rotation, World);
    if (SpawnedUnit)
    {
        // Add maintenance cost as ongoing drain
        FRTSResourceCost MaintenanceCost = GetUnitMaintenanceCost(UnitID);
        if (MaintenanceCost.PowerCost > 0.0f)
        {
            EconomySubsystem->AddResourceDrain(PlayerID, ERTSResourceType::Power, MaintenanceCost.PowerCost);
        }
        if (MaintenanceCost.MatterCost > 0.0f)
        {
            EconomySubsystem->AddResourceDrain(PlayerID, ERTSResourceType::Matter, MaintenanceCost.MatterCost);
        }

        UE_LOG(LogTemp, Log, TEXT("Successfully spawned unit %s for player %d with economy integration"), *UnitID.ToString(), PlayerID);
    }

    return SpawnedUnit;
}

bool UUnitDatabaseSubsystem::StartUnitProduction(FName UnitID, int32 PlayerID, float& OutProductionTime)
{
    // Get the economy subsystem
    URTSEconomySubsystem* EconomySubsystem = GetGameInstance()->GetSubsystem<URTSEconomySubsystem>();
    if (!EconomySubsystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("Economy subsystem not found"));
        return false;
    }

    // Get unit definition
    FRTSUnitDefinition UnitDef = GetUnitDefinition(UnitID);
    if (!UnitDef.UnitClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("Unit definition not found for %s"), *UnitID.ToString());
        return false;
    }

    // Check if player can afford the unit
    if (!EconomySubsystem->HasSufficientResources(PlayerID, UnitDef.ResourceCost))
    {
        UE_LOG(LogTemp, Warning, TEXT("Player %d cannot afford unit %s"), PlayerID, *UnitID.ToString());
        return false;
    }

    // Start production by adding resource drain over time
    float ProductionTime = UnitDef.ProductionTime;

    // Apply build speed multiplier from economy system
    float BuildSpeedMultiplier = EconomySubsystem->GetBuildSpeedMultiplier(PlayerID);
    ProductionTime /= BuildSpeedMultiplier;

    // Add resource drain for the production duration
    if (ProductionTime > 0.0f)
    {
        float PowerDrainPerSecond = UnitDef.ResourceCost.PowerCost / ProductionTime;
        float MatterDrainPerSecond = UnitDef.ResourceCost.MatterCost / ProductionTime;

        if (PowerDrainPerSecond > 0.0f)
        {
            EconomySubsystem->AddResourceDrain(PlayerID, ERTSResourceType::Power, PowerDrainPerSecond);
        }
        if (MatterDrainPerSecond > 0.0f)
        {
            EconomySubsystem->AddResourceDrain(PlayerID, ERTSResourceType::Matter, MatterDrainPerSecond);
        }
    }

    OutProductionTime = ProductionTime;
    UE_LOG(LogTemp, Log, TEXT("Started production of unit %s for player %d (Production time: %.2f seconds)"), *UnitID.ToString(), PlayerID, ProductionTime);
    return true;
}


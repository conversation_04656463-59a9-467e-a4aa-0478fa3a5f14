// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSCombatBehaviorTree.h"

#ifdef ARMORWARS_RTSCombatBehaviorTree_generated_h
#error "RTSCombatBehaviorTree.generated.h already included, missing '#pragma once' in RTSCombatBehaviorTree.h"
#endif
#define ARMORWARS_RTSCombatBehaviorTree_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class URTSBehaviorNode;

// ********** Begin Class URTSCombatBehaviorTreeFactory ********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_21_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCreateDefensiveCombatTree); \
	DECLARE_FUNCTION(execCreateAggressiveCombatTree); \
	DECLARE_FUNCTION(execCreateSupportCombatTree); \
	DECLARE_FUNCTION(execCreateTacticalCombatTree); \
	DECLARE_FUNCTION(execCreateBasicCombatTree);


ARMORWARS_API UClass* Z_Construct_UClass_URTSCombatBehaviorTreeFactory_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_21_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSCombatBehaviorTreeFactory(); \
	friend struct Z_Construct_UClass_URTSCombatBehaviorTreeFactory_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSCombatBehaviorTreeFactory_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSCombatBehaviorTreeFactory, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSCombatBehaviorTreeFactory_NoRegister) \
	DECLARE_SERIALIZER(URTSCombatBehaviorTreeFactory)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_21_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSCombatBehaviorTreeFactory(URTSCombatBehaviorTreeFactory&&) = delete; \
	URTSCombatBehaviorTreeFactory(const URTSCombatBehaviorTreeFactory&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSCombatBehaviorTreeFactory); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSCombatBehaviorTreeFactory); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSCombatBehaviorTreeFactory) \
	NO_API virtual ~URTSCombatBehaviorTreeFactory();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_18_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_21_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_21_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_21_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_21_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSCombatBehaviorTreeFactory;

// ********** End Class URTSCombatBehaviorTreeFactory **********************************************

// ********** Begin Class URTSAdvancedTargetSelectionTask ******************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSAdvancedTargetSelectionTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_58_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSAdvancedTargetSelectionTask(); \
	friend struct Z_Construct_UClass_URTSAdvancedTargetSelectionTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSAdvancedTargetSelectionTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSAdvancedTargetSelectionTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSAdvancedTargetSelectionTask_NoRegister) \
	DECLARE_SERIALIZER(URTSAdvancedTargetSelectionTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_58_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSAdvancedTargetSelectionTask(URTSAdvancedTargetSelectionTask&&) = delete; \
	URTSAdvancedTargetSelectionTask(const URTSAdvancedTargetSelectionTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSAdvancedTargetSelectionTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSAdvancedTargetSelectionTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSAdvancedTargetSelectionTask) \
	NO_API virtual ~URTSAdvancedTargetSelectionTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_55_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_58_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_58_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_58_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSAdvancedTargetSelectionTask;

// ********** End Class URTSAdvancedTargetSelectionTask ********************************************

// ********** Begin Class URTSTacticalPositioningTask **********************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSTacticalPositioningTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_106_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSTacticalPositioningTask(); \
	friend struct Z_Construct_UClass_URTSTacticalPositioningTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSTacticalPositioningTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSTacticalPositioningTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSTacticalPositioningTask_NoRegister) \
	DECLARE_SERIALIZER(URTSTacticalPositioningTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_106_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSTacticalPositioningTask(URTSTacticalPositioningTask&&) = delete; \
	URTSTacticalPositioningTask(const URTSTacticalPositioningTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSTacticalPositioningTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSTacticalPositioningTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSTacticalPositioningTask) \
	NO_API virtual ~URTSTacticalPositioningTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_103_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_106_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_106_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_106_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSTacticalPositioningTask;

// ********** End Class URTSTacticalPositioningTask ************************************************

// ********** Begin Class URTSCoordinatedAttackTask ************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSCoordinatedAttackTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_155_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSCoordinatedAttackTask(); \
	friend struct Z_Construct_UClass_URTSCoordinatedAttackTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSCoordinatedAttackTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSCoordinatedAttackTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSCoordinatedAttackTask_NoRegister) \
	DECLARE_SERIALIZER(URTSCoordinatedAttackTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_155_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSCoordinatedAttackTask(URTSCoordinatedAttackTask&&) = delete; \
	URTSCoordinatedAttackTask(const URTSCoordinatedAttackTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSCoordinatedAttackTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSCoordinatedAttackTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSCoordinatedAttackTask) \
	NO_API virtual ~URTSCoordinatedAttackTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_152_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_155_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_155_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_155_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSCoordinatedAttackTask;

// ********** End Class URTSCoordinatedAttackTask **************************************************

// ********** Begin Class URTSReturnFireTask *******************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSReturnFireTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_198_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSReturnFireTask(); \
	friend struct Z_Construct_UClass_URTSReturnFireTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSReturnFireTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSReturnFireTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSReturnFireTask_NoRegister) \
	DECLARE_SERIALIZER(URTSReturnFireTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_198_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSReturnFireTask(URTSReturnFireTask&&) = delete; \
	URTSReturnFireTask(const URTSReturnFireTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSReturnFireTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSReturnFireTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSReturnFireTask) \
	NO_API virtual ~URTSReturnFireTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_195_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_198_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_198_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_198_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSReturnFireTask;

// ********** End Class URTSReturnFireTask *********************************************************

// ********** Begin Class URTSUnderAttackCondition *************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSUnderAttackCondition_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_244_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSUnderAttackCondition(); \
	friend struct Z_Construct_UClass_URTSUnderAttackCondition_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSUnderAttackCondition_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSUnderAttackCondition, URTSConditionNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSUnderAttackCondition_NoRegister) \
	DECLARE_SERIALIZER(URTSUnderAttackCondition)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_244_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSUnderAttackCondition(URTSUnderAttackCondition&&) = delete; \
	URTSUnderAttackCondition(const URTSUnderAttackCondition&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSUnderAttackCondition); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSUnderAttackCondition); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSUnderAttackCondition) \
	NO_API virtual ~URTSUnderAttackCondition();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_241_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_244_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_244_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_244_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSUnderAttackCondition;

// ********** End Class URTSUnderAttackCondition ***************************************************

// ********** Begin Class URTSTacticalAdvantageCondition *******************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSTacticalAdvantageCondition_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_274_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSTacticalAdvantageCondition(); \
	friend struct Z_Construct_UClass_URTSTacticalAdvantageCondition_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSTacticalAdvantageCondition_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSTacticalAdvantageCondition, URTSConditionNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSTacticalAdvantageCondition_NoRegister) \
	DECLARE_SERIALIZER(URTSTacticalAdvantageCondition)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_274_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSTacticalAdvantageCondition(URTSTacticalAdvantageCondition&&) = delete; \
	URTSTacticalAdvantageCondition(const URTSTacticalAdvantageCondition&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSTacticalAdvantageCondition); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSTacticalAdvantageCondition); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSTacticalAdvantageCondition) \
	NO_API virtual ~URTSTacticalAdvantageCondition();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_271_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_274_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_274_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h_274_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSTacticalAdvantageCondition;

// ********** End Class URTSTacticalAdvantageCondition *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSCombatBehaviorTree_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

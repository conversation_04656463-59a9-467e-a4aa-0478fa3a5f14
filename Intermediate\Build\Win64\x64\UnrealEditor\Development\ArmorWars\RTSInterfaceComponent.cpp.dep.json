{"Version": "1.2", "Data": {"Source": "f:\\armorwars\\source\\armorwars\\private\\components\\rtsinterfacecomponent.cpp", "ProvidedModule": "", "PCH": "f:\\armorwars\\intermediate\\build\\win64\\x64\\armorwarseditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["f:\\armorwars\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\armorwars\\definitions.armorwars.h", "f:\\armorwars\\source\\armorwars\\public\\components\\rtsinterfacecomponent.h", "f:\\armorwars\\source\\armorwars\\public\\interfaces\\rtsinterfaces.h", "f:\\armorwars\\source\\armorwars\\public\\interfaces\\rtsgameinstanceinterface.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsgameinstanceinterface.generated.h", "f:\\armorwars\\source\\armorwars\\public\\interfaces\\rtspawninterface.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtspawninterface.generated.h", "f:\\armorwars\\source\\armorwars\\public\\interfaces\\rtshudinterface.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtshudinterface.generated.h", "f:\\armorwars\\source\\armorwars\\public\\interfaces\\rtsunitselectioninterface.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunitselectioninterface.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsinterfacecomponent.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsselectionsystem.h", "f:\\armorwars\\source\\armorwars\\public\\rtsbaseactor.h", "f:\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsbaseactor.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsselectionsystem.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsplayercontroller.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputactionvalue.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputactionvalue.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsplayercontroller.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsgameinstance.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsgameinstance.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtshud.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\hud.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\hudhitbox.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\debugtextinfo.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\debugtextinfo.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hud.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtshud.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}
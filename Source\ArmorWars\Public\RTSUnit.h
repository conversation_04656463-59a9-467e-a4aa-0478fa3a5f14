#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "RTSBaseActor.h"
#include "RTSUnitAIComponent.h"
#include "RTSCommand.h"
#include "RTSUnit.generated.h"

class UStaticMeshComponent;
class UCapsuleComponent;
class URT<PERSON>rmorComponent;
class URTSTacticalAIComponent;
class URTSWeaponController;
class UPawnMovementComponent;
class URTSCommandComponent;
class URTSBlackboardComponent;
class URTSReturnFireComponent;

// Movement state enum for better movement control
// NOTE: This movement state system is WORKING and ACTIVELY USED - DO NOT REMOVE
// It provides proper RTS-style movement with turn-in-place and gradual turning behavior
// Used by both movement components and fallback movement system
UENUM(BlueprintType)
enum class ERTSMovementState : uint8
{
    Stationary          UMETA(DisplayName = "Stationary"),
    TurningInPlace      UMETA(DisplayName = "Turning In Place"),
    Moving              UMETA(DisplayName = "Moving"),
    TurningWhileMoving  UMETA(DisplayName = "Turning While Moving")
};

// Delegate declarations (from RTSBaseActor)
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHealthChanged, float, CurrentHealth, float, MaxHealth);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDamageReceived, float, DamageAmount, AActor*, DamageSource);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnActorDeath, AActor*, DeadActor);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnUnitSelectionChanged, AActor*, Actor, bool, bSelected);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMovementChanged, ARTSUnit*, Unit, FVector, TargetLocation);

/**
 * Unit class for all movable RTS units
 * Inherits from APawn to support AI controller possession and movement components
 * Includes shared functionality from RTSBaseActor through composition
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API ARTSUnit : public ARTSBaseActor
{
    GENERATED_BODY()

public:
    ARTSUnit();

protected:
    virtual void BeginPlay() override;

public:
    virtual void Tick(float DeltaTime) override;

    // Override TakeDamage from AActor
    virtual float TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser) override;

protected:
    // Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UStaticMeshComponent* MeshComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UCapsuleComponent* CollisionComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    URTSArmorComponent* ArmorComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    URTSUnitAIComponent* AIComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    URTSTacticalAIComponent* TacticalAIComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    URTSWeaponController* WeaponController;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    URTSCommandComponent* CommandComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    URTSBlackboardComponent* BlackboardComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    URTSReturnFireComponent* ReturnFireComponent;

    // Shared properties from RTSBaseActor (composition pattern)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Actor Type")
    ERTSActorType ActorType = ERTSActorType::Unit;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Team")
    int32 TeamID = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Tech")
    ERTSTechLevel TechLevel = ERTSTechLevel::Tech1;

    // Health System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Health", meta = (ClampMin = "1.0"))
    float MaxHealth = 100.0f;

    UPROPERTY(BlueprintReadOnly, Category = "RTS|Health")
    float CurrentHealth = 100.0f;

    // Selection
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Selection")
    bool bIsSelected = false;

    // Gameplay Tags
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Gameplay Tags")
    FGameplayTagContainer GameplayTags;

    // Delegates (from RTSBaseActor)
    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnHealthChanged OnHealthChanged;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnDamageReceived OnDamageReceived;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnActorDeath OnActorDeath;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnUnitSelectionChanged OnSelectionChanged;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnMovementChanged OnMovementChanged;

public:
    // Unit domain (Land, Air, Sea, Subnautical)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Unit")
    ERTSUnitDomain UnitDomain = ERTSUnitDomain::Land;



    // Movement speed
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Movement", meta = (ClampMin = "0.0"))
    float MovementSpeed = 300.0f;

    // Turn rate (degrees per second)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Movement", meta = (ClampMin = "0.0"))
    float TurnRate = 180.0f;

    // Acceleration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Movement", meta = (ClampMin = "0.0"))
    float Acceleration = 600.0f;

    // Deceleration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Movement", meta = (ClampMin = "0.0"))
    float Deceleration = 1200.0f;

    // Angle tolerance for forward movement (degrees) - unit will turn if facing direction differs by more than this
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Movement", meta = (ClampMin = "0.0", ClampMax = "45.0"))
    float TurningAngleTolerance = 5.0f;

    // Distance tolerance for reaching destination (cm)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Movement", meta = (ClampMin = "10.0"))
    float ArrivalDistance = 100.0f;

    // Synchronized movement speed (overrides MovementSpeed when in formation)
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Movement")
    float SynchronizedMovementSpeed = 0.0f;

    // Whether this unit is using synchronized movement
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Movement")
    bool bUseSynchronizedMovement = false;

    // Vision range
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Vision", meta = (ClampMin = "0.0"))
    float VisionRange = 800.0f;

    // Current movement target location
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Movement")
    FVector TargetLocation = FVector::ZeroVector;

    // Whether the unit is currently moving
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Movement")
    bool bIsMoving = false;

    // Current movement state for better movement control
    // NOTE: This movement state tracking is WORKING and ACTIVELY USED - DO NOT REMOVE
    // Provides proper RTS movement behavior with turn-in-place and gradual turning
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Movement")
    ERTSMovementState MovementState = ERTSMovementState::Stationary;

    // Formation-related properties
    // NOTE: This formation system is WORKING and ACTIVELY USED - DO NOT REMOVE
    // Enables proper formation movement where units move to target + formation offset
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Formation")
    bool bIsInFormation = false;

    UPROPERTY(BlueprintReadOnly, Category = "RTS|Formation")
    FVector FormationOffset = FVector::ZeroVector;

    // Collision avoidance properties
    // NOTE: This collision avoidance system is WORKING and ACTIVELY USED - DO NOT REMOVE
    // Prevents units from moving through each other and provides smooth avoidance
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Movement", meta = (ClampMin = "50.0"))
    float CollisionAvoidanceRadius = 150.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Movement", meta = (ClampMin = "0.0"))
    float CollisionAvoidanceStrength = 1.0f;

    // Time tracking for avoiding rapid direction changes
    // NOTE: This avoidance cooldown system is WORKING and ACTIVELY USED - DO NOT REMOVE
    // Prevents oscillation and rapid direction changes that cause movement issues
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Movement")
    float LastAvoidanceTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Movement", meta = (ClampMin = "0.1"))
    float AvoidanceCooldown = 0.5f; // Minimum time between avoidance maneuvers

public:
    // Health Functions (from RTSBaseActor)
    UFUNCTION(BlueprintPure, Category = "RTS|Health")
    float GetHealth() const { return CurrentHealth; }

    UFUNCTION(BlueprintPure, Category = "RTS|Health")
    float GetMaxHealth() const { return MaxHealth; }

    UFUNCTION(BlueprintPure, Category = "RTS|Health")
    float GetHealthPercentage() const { return MaxHealth > 0.0f ? (CurrentHealth / MaxHealth) : 0.0f; }

    UFUNCTION(BlueprintPure, Category = "RTS|Health")
    bool IsAlive() const { return CurrentHealth > 0.0f; }

    UFUNCTION(BlueprintPure, Category = "RTS|Health")
    bool IsDead() const { return CurrentHealth <= 0.0f; }

    UFUNCTION(BlueprintCallable, Category = "RTS|Health")
    void TakeDamageSimple(float DamageAmount, AActor* DamageSource = nullptr);

    UFUNCTION(BlueprintCallable, Category = "RTS|Health")
    void Heal(float HealAmount);

    UFUNCTION(BlueprintCallable, Category = "RTS|Health")
    void SetHealth(float NewHealth);

    // Team Functions (from RTSBaseActor)
    UFUNCTION(BlueprintPure, Category = "RTS|Team")
    int32 GetTeamID() const { return TeamID; }

    UFUNCTION(BlueprintCallable, Category = "RTS|Team")
    void SetTeamID(int32 NewTeamID) { TeamID = NewTeamID; }

    UFUNCTION(BlueprintPure, Category = "RTS|Team")
    bool IsOnSameTeam(const AActor* OtherActor) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Team")
    bool IsEnemy(const AActor* OtherActor) const;

    // Selection Functions (from RTSBaseActor)
    UFUNCTION(BlueprintPure, Category = "RTS|Selection")
    bool IsSelected() const { return bIsSelected; }

    UFUNCTION(BlueprintCallable, Category = "RTS|Selection")
    void SetSelected(bool bSelected);

    // Movement functions
    UFUNCTION(BlueprintCallable, Category = "RTS|Movement")
    void MoveToLocation(const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "RTS|Movement")
    void MoveToLocationSynchronized(const FVector& Location, float SynchronizedSpeed);

    UFUNCTION(BlueprintCallable, Category = "RTS|Movement")
    void StopMovement();

    UFUNCTION(BlueprintCallable, Category = "RTS|Movement")
    void AttackMoveToLocation(const FVector& Location);

    UFUNCTION(BlueprintPure, Category = "RTS|Movement")
    bool IsMoving() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Movement")
    float GetMovementSpeed() const { return MovementSpeed; }

    UFUNCTION(BlueprintPure, Category = "RTS|Movement")
    float GetDistanceToTarget() const;

    // Movement state functions
    // NOTE: These movement state functions are WORKING and ACTIVELY USED - DO NOT REMOVE
    // They provide proper RTS movement behavior and state tracking
    UFUNCTION(BlueprintPure, Category = "RTS|Movement")
    ERTSMovementState GetMovementState() const { return MovementState; }

    UFUNCTION(BlueprintCallable, Category = "RTS|Movement")
    void SetMovementState(ERTSMovementState NewState);

    // Formation movement functions
    // NOTE: These formation functions are WORKING and ACTIVELY USED - DO NOT REMOVE
    // They enable proper formation movement where units move to target + formation offset
    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    void MoveToLocationWithFormation(const FVector& Location, const FVector& FormationOffsetParam);

    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    void SetFormationData(bool bInFormation, const FVector& Offset);

    UFUNCTION(BlueprintPure, Category = "RTS|Formation")
    bool IsInFormation() const { return bIsInFormation; }

    UFUNCTION(BlueprintPure, Category = "RTS|Formation")
    FVector GetFormationOffset() const { return FormationOffset; }

    // Collision avoidance functions
    // NOTE: These collision avoidance functions are WORKING and ACTIVELY USED - DO NOT REMOVE
    // They prevent units from moving through each other and provide smooth unit separation
    UFUNCTION(BlueprintCallable, Category = "RTS|Movement")
    FVector CalculateCollisionAvoidance(const FVector& DesiredDirection) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Movement")
    TArray<ARTSUnit*> GetNearbyUnits(float Radius) const;



    // Domain-specific functions
    UFUNCTION(BlueprintPure, Category = "RTS|Unit")
    bool IsLandUnit() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Unit")
    bool IsAirUnit() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Unit")
    bool IsSeaUnit() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Unit")
    bool IsSubnauticalUnit() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Unit")
    ERTSUnitDomain GetUnitDomain() const { return UnitDomain; }

    // Tech level functions
    UFUNCTION(BlueprintPure, Category = "RTS|Tech")
    ERTSTechLevel GetTechLevel() const { return TechLevel; }

    UFUNCTION(BlueprintCallable, Category = "RTS|Tech")
    void SetTechLevel(ERTSTechLevel NewTechLevel) { TechLevel = NewTechLevel; }

    UFUNCTION(BlueprintPure, Category = "RTS|Tech")
    bool IsAdvancedUnit() const { return static_cast<uint8>(GetTechLevel()) >= static_cast<uint8>(ERTSTechLevel::Tech3); }

    UFUNCTION(BlueprintPure, Category = "RTS|Tech")
    bool CanBeProducedByTechLevel(ERTSTechLevel FactoryTechLevel) const;

    // Controller access (now that RTSUnit inherits from APawn)
    // GetController() is already available from APawn base class

    // Combat functions using weapon controller
    UFUNCTION(BlueprintCallable, Category = "RTS|Combat")
    void AttackTarget(AActor* Target);

    UFUNCTION(BlueprintCallable, Category = "RTS|Combat")
    void StopAttacking();

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    bool CanAttackTarget(const AActor* Target) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    bool IsInAttackRange(const AActor* Target) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    bool IsAttacking() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    bool HasWeapons() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    AActor* GetCurrentTarget() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    float GetMaxAttackRange() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    float GetTotalDamagePerSecond() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    bool CanTargetDomain(ERTSUnitDomain Domain) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    float GetAttackRange() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    float GetAttackDamage() const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Combat")
    TArray<ARTSBaseActor*> FindEnemiesInRange(float Range) const;



    // Command System Functions
    UFUNCTION(BlueprintCallable, Category = "RTS|Commands")
    bool IssueCommand(const FRTSCommand& Command, bool bClearQueue = false);

    UFUNCTION(BlueprintCallable, Category = "RTS|Commands")
    bool IssueMoveCommand(const FVector& MoveTargetLocation, ERTSCommandPriority Priority = ERTSCommandPriority::Normal, bool bClearQueue = true);

    UFUNCTION(BlueprintCallable, Category = "RTS|Commands")
    bool IssueAttackCommand(ARTSBaseActor* Target, ERTSCommandPriority Priority = ERTSCommandPriority::Normal, bool bClearQueue = true);

    UFUNCTION(BlueprintCallable, Category = "RTS|Commands")
    bool IssueStopCommand(ERTSCommandPriority Priority = ERTSCommandPriority::High);

    UFUNCTION(BlueprintCallable, Category = "RTS|Commands")
    void ClearCommandQueue();

    UFUNCTION(BlueprintPure, Category = "RTS|Commands")
    bool HasCommands() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Commands")
    FRTSCommand GetCurrentCommand() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Commands")
    URTSCommandComponent* GetCommandComponent() const { return CommandComponent; }

    UFUNCTION(BlueprintPure, Category = "RTS|AI")
    URTSBlackboardComponent* GetBlackboardComponent() const { return BlackboardComponent; }

    UFUNCTION(BlueprintPure, Category = "RTS|Weapons")
    URTSWeaponController* GetWeaponController() const { return WeaponController; }

    // AI Control Functions
    UFUNCTION(BlueprintCallable, Category = "RTS|AI")
    void SetAIBehavior(ERTSAIBehavior Behavior);

    UFUNCTION(BlueprintCallable, Category = "RTS|AI")
    void SetAIPatrolPoints(const TArray<FVector>& Points);

    UFUNCTION(BlueprintCallable, Category = "RTS|AI")
    void SetAIDefendPosition(const FVector& Position);

    UFUNCTION(BlueprintCallable, Category = "RTS|AI")
    void JoinFormation(ARTSUnit* Leader, ERTSFormationRole FormationRole, const FVector& Offset);

    UFUNCTION(BlueprintCallable, Category = "RTS|AI")
    void LeaveFormation();

protected:
    // Internal movement handling
    virtual void UpdateMovement(float DeltaTime);
    virtual void UpdateMovementStateForComponent(float DeltaTime);
    virtual void UpdateFallbackMovement(float DeltaTime);

    // Internal combat handling
    virtual void UpdateCombat(float DeltaTime);

    // Configure components based on unit type
    void ConfigureComponentsForUnitType();

    // Fallback movement for when no movement component is available
    void FallbackMoveToLocation(const FVector& Location);

    // Protected event functions (from RTSBaseActor)
    virtual void HandleDeath();

    // Blueprint events
    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Events")
    void OnTakeDamage(float DamageAmount, AActor* DamageSource);

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Events")
    void OnHealed(float HealAmount);

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Events")
    void OnDeath();

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Movement")
    void OnMovementStarted(const FVector& Destination);

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Movement")
    void OnMovementStopped();

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Movement")
    void OnReachedDestination();

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Combat")
    void OnAttackStarted(ARTSBaseActor* Target);

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Combat")
    void OnAttackStopped();

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Combat")
    void OnTargetLost(ARTSBaseActor* LostTarget);
};

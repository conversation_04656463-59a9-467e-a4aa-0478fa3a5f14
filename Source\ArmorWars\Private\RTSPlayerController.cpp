// Fill out your copyright notice in the Description page of Project Settings.

#include "RTSPlayerController.h"
#include "RTSSelectionSystem.h"
#include "RTSUnit.h"
#include "RTSBuilding.h"
#include "RTSBaseActor.h"
#include "RTSCommand.h"
#include "RTSCommandComponent.h"
#include "RTSFormationManager.h"
#include "RTSHUD.h"
#include "RTSPawn.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/InputComponent.h"
#include "Kismet/GameplayStatics.h"
// Enhanced Input includes
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputMappingContext.h"
#include "InputAction.h"

ARTSPlayerController::ARTSPlayerController()
{
	// Enable tick
	PrimaryActorTick.bCanEverTick = true;

	// Selection system will be retrieved from world subsystem

	// Set default values
	PlayerTeamID = 0;
	CameraMovementSpeed = 1000.0f;
	CameraZoomSpeed = 100.0f;
	CameraRotationSpeed = 90.0f;
	bEnableDebugLogging = false;

	// Enable mouse cursor
	bShowMouseCursor = true;
	bEnableClickEvents = true;
	bEnableMouseOverEvents = true;
}

void ARTSPlayerController::BeginPlay()
{
	Super::BeginPlay();

	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: BeginPlay for player %s"), *GetName());
	}

	// Set up Enhanced Input Mapping Context
	if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(GetLocalPlayer()))
	{
		if (DefaultMappingContext)
		{
			Subsystem->AddMappingContext(DefaultMappingContext, 0);

			if (bEnableDebugLogging)
			{
				UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Enhanced Input mapping context added"));
			}
		}
		else if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: DefaultMappingContext is null - Enhanced Input not configured"));
		}
	}

	// Initialize selection system
	if (URTSSelectionSystem* SelectionSys = GetSelectionSystem())
	{
		SelectionSys->InitializeSelectionSystem(this);
		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Selection system initialized"));
		}
	}
}

void ARTSPlayerController::SetupInputComponent()
{
	Super::SetupInputComponent();

	// Set up Enhanced Input Component
	if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(InputComponent))
	{
		// Bind Enhanced Input Actions
		if (PrimaryAction)
		{
			EnhancedInputComponent->BindAction(PrimaryAction, ETriggerEvent::Started, this, &ARTSPlayerController::OnPrimaryActionPressed);
			EnhancedInputComponent->BindAction(PrimaryAction, ETriggerEvent::Completed, this, &ARTSPlayerController::OnPrimaryActionReleased);
			UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Primary Action bound successfully"));
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("RTSPlayerController: Primary Action is NULL - Enhanced Input not configured!"));
		}

		if (SecondaryAction)
		{
			EnhancedInputComponent->BindAction(SecondaryAction, ETriggerEvent::Triggered, this, &ARTSPlayerController::OnSecondaryAction);
		}

		if (CameraMoveForwardAction)
		{
			EnhancedInputComponent->BindAction(CameraMoveForwardAction, ETriggerEvent::Triggered, this, &ARTSPlayerController::OnCameraMoveForward);
		}

		if (CameraMoveRightAction)
		{
			EnhancedInputComponent->BindAction(CameraMoveRightAction, ETriggerEvent::Triggered, this, &ARTSPlayerController::OnCameraMoveRight);
		}

		if (CameraZoomAction)
		{
			EnhancedInputComponent->BindAction(CameraZoomAction, ETriggerEvent::Triggered, this, &ARTSPlayerController::OnCameraZoom);
		}

		if (CameraRotateAction)
		{
			EnhancedInputComponent->BindAction(CameraRotateAction, ETriggerEvent::Triggered, this, &ARTSPlayerController::OnCameraRotate);
		}

		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Enhanced Input component setup complete"));
		}
	}
	else if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Enhanced Input Component not found - using legacy input"));
	}
}

void ARTSPlayerController::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	// Update selection box if active
	if (bIsDrawingSelectionBox)
	{
		UpdateSelectionBox();
	}

	// Update selection system
	if (URTSSelectionSystem* SelectionSys = GetSelectionSystem())
	{
		// Selection system tick logic would go here
	}
}

void ARTSPlayerController::OnPrimaryAction(const FInputActionValue& Value)
{
	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Primary action performed - Value: %s"),
			*Value.ToString());
	}

	// Handle selection logic here
	// This would typically involve raycasting from mouse position
	// For selection box: check if this is press/release and handle accordingly

	// Call Blueprint event
	OnPrimaryActionPerformed();
}

void ARTSPlayerController::OnPrimaryActionPressed(const FInputActionValue& Value)
{
	// Always log this to help debug
	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Primary action pressed - Starting selection box"));

	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Primary action pressed - Value: %s"), *Value.ToString());
	}

	// Start selection box (this will handle both single clicks and box selection)
	StartSelectionBox();

	// Call Blueprint event
	OnPrimaryActionPerformed();
}

void ARTSPlayerController::OnPrimaryActionReleased(const FInputActionValue& Value)
{
	// Always log this to help debug
	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Primary action released - Ending selection box"));

	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Primary action released - Value: %s"), *Value.ToString());
	}

	// End selection box
	EndSelectionBox();
}

void ARTSPlayerController::OnSecondaryAction(const FInputActionValue& Value)
{
	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Secondary action performed - Value: %s"),
			*Value.ToString());
	}

	// Handle command issuing logic - right click for move/attack commands
	IssueMoveCommand();

	// Call Blueprint event
	OnSecondaryActionPerformed();
}

void ARTSPlayerController::OnCameraMoveForward(const FInputActionValue& Value)
{
	float AxisValue = Value.Get<float>();

	if (bEnableDebugLogging && FMath::Abs(AxisValue) > 0.1f)
	{
		UE_LOG(LogTemp, VeryVerbose, TEXT("RTSPlayerController: Camera move forward - Value: %.2f"), AxisValue);
	}

	if (FMath::Abs(AxisValue) > 0.1f)
	{
		// Control the RTS pawn directly
		if (ARTSPawn* RTSPawn = Cast<ARTSPawn>(GetPawn()))
		{
			RTSPawn->MoveCameraForward(AxisValue);
		}

		// Call Blueprint event for camera movement
		OnCameraMoveRequested(AxisValue);
	}
}

void ARTSPlayerController::OnCameraMoveRight(const FInputActionValue& Value)
{
	float AxisValue = Value.Get<float>();

	if (bEnableDebugLogging && FMath::Abs(AxisValue) > 0.1f)
	{
		UE_LOG(LogTemp, VeryVerbose, TEXT("RTSPlayerController: Camera move right - Value: %.2f"), AxisValue);
	}

	if (FMath::Abs(AxisValue) > 0.1f)
	{
		// Control the RTS pawn directly
		if (ARTSPawn* RTSPawn = Cast<ARTSPawn>(GetPawn()))
		{
			RTSPawn->MoveCameraRight(AxisValue);
		}

		// Call Blueprint event for camera movement
		OnCameraMoveRequested(AxisValue);
	}
}

void ARTSPlayerController::OnCameraZoom(const FInputActionValue& Value)
{
	float AxisValue = Value.Get<float>();

	if (bEnableDebugLogging && FMath::Abs(AxisValue) > 0.1f)
	{
		UE_LOG(LogTemp, VeryVerbose, TEXT("RTSPlayerController: Camera zoom - Value: %.2f"), AxisValue);
	}

	if (FMath::Abs(AxisValue) > 0.1f)
	{
		// Control the RTS pawn directly
		if (ARTSPawn* RTSPawn = Cast<ARTSPawn>(GetPawn()))
		{
			RTSPawn->ZoomCamera(AxisValue);
		}

		// Call Blueprint event for camera zoom
		OnCameraZoomRequested(AxisValue);
	}
}

void ARTSPlayerController::OnCameraRotate(const FInputActionValue& Value)
{
	float AxisValue = Value.Get<float>();

	if (bEnableDebugLogging && FMath::Abs(AxisValue) > 0.1f)
	{
		UE_LOG(LogTemp, VeryVerbose, TEXT("RTSPlayerController: Camera rotate - Value: %.2f"), AxisValue);
	}

	if (FMath::Abs(AxisValue) > 0.1f)
	{
		// Control the RTS pawn directly
		if (ARTSPawn* RTSPawn = Cast<ARTSPawn>(GetPawn()))
		{
			RTSPawn->RotateCamera(AxisValue);
		}

		// Call Blueprint event for camera rotation
		OnCameraRotateRequested(AxisValue);
	}
}

URTSSelectionSystem* ARTSPlayerController::GetSelectionSystem() const
{
	if (UWorld* World = GetWorld())
	{
		return World->GetSubsystem<URTSSelectionSystem>();
	}
	return nullptr;
}

int32 ARTSPlayerController::GetPlayerTeamID() const
{
	return PlayerTeamID;
}

void ARTSPlayerController::SetPlayerTeamID(int32 NewTeamID)
{
	PlayerTeamID = NewTeamID;
	
	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Player team ID set to %d"), PlayerTeamID);
	}
}



void ARTSPlayerController::IssueAttackCommand(AActor* TargetActor)
{
	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Attack command issued on target: %s"), 
			TargetActor ? *TargetActor->GetName() : TEXT("None"));
	}

	// Implementation would involve getting selected units and issuing attack commands
	// This would integrate with your existing unit command system
}

void ARTSPlayerController::IssueStopCommand()
{
	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Stop command issued to %d selected units"), SelectedUnits.Num());

	// Issue stop commands to all selected units
	for (AActor* Unit : SelectedUnits)
	{
		// Check if unit is alive (different methods for different types)
		bool bIsAlive = false;
		if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
		{
			bIsAlive = RTSUnit->IsAlive();
		}
		else if (ARTSBaseActor* BaseActor = Cast<ARTSBaseActor>(Unit))
		{
			bIsAlive = BaseActor->IsAlive();
		}

		if (Unit && IsValid(Unit) && bIsAlive)
		{
			// Call stop command on unit
			if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
			{
				RTSUnit->StopMovement();

				if (bEnableDebugLogging)
				{
					UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Issued stop command to unit %s"), *Unit->GetName());
				}
			}
		}
	}
}

void ARTSPlayerController::IssueMoveCommand()
{
	FVector TargetLocation;
	if (!GetMouseWorldLocation(TargetLocation))
	{
		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Failed to get mouse world location for move command"));
		}
		return;
	}

	IssueMoveCommandToLocation(TargetLocation);
}

void ARTSPlayerController::IssueMoveCommandToLocation(const FVector& TargetLocation)
{
	if (!IsValidMoveLocation(TargetLocation))
	{
		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Invalid move location: %s"), *TargetLocation.ToString());
		}
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Move command issued to %d units, target: %s"),
		SelectedUnits.Num(), *TargetLocation.ToString());

	// Get formation manager
	URTSFormationManager* FormationManager = GetWorld()->GetSubsystem<URTSFormationManager>();

	if (SelectedUnits.Num() > 1 && FormationManager)
	{
		// Multiple units - create formation and move as group
		TArray<ARTSUnit*> RTSUnits;
		for (AActor* Unit : SelectedUnits)
		{
			if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
			{
				if (RTSUnit->IsAlive())
				{
					RTSUnits.Add(RTSUnit);
				}
			}
		}

		if (RTSUnits.Num() > 1)
		{
			// Create formation
			int32 FormationID = FormationManager->CreateFormation(RTSUnits, ERTSFormationType::Line, 200.0f);

			if (FormationID >= 0)
			{
				// Move formation to target location
				FormationManager->MoveFormation(FormationID, TargetLocation, ERTSCommandPriority::Normal);

				if (bEnableDebugLogging)
				{
					UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Created formation %d with %d units, moving to %s"),
						FormationID, RTSUnits.Num(), *TargetLocation.ToString());
				}
			}
		}
		else if (RTSUnits.Num() == 1)
		{
			// Only one valid unit, issue normal move command
			RTSUnits[0]->IssueMoveCommand(TargetLocation, ERTSCommandPriority::Normal, true);
		}
	}
	else
	{
		// Single unit or no formation manager - issue individual move commands
		for (AActor* Unit : SelectedUnits)
		{
			// Check if unit is alive
			bool bIsAlive = false;
			if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
			{
				bIsAlive = RTSUnit->IsAlive();
			}
			else if (ARTSBaseActor* BaseActor = Cast<ARTSBaseActor>(Unit))
			{
				bIsAlive = BaseActor->IsAlive();
			}

			if (Unit && IsValid(Unit) && bIsAlive)
			{
				if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
				{
					RTSUnit->IssueMoveCommand(TargetLocation, ERTSCommandPriority::Normal, true);

					if (bEnableDebugLogging)
					{
						UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Issued individual move command to unit %s, target: %s"),
							*Unit->GetName(), *TargetLocation.ToString());
					}
				}
			}
		}
	}

	// Call Blueprint event
	OnMoveCommandIssued(TargetLocation, SelectedUnits);
}

void ARTSPlayerController::IssueAttackMoveCommand()
{
	FVector TargetLocation;
	if (!GetMouseWorldLocation(TargetLocation))
	{
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Attack-move command issued to %d units, target: %s"),
		SelectedUnits.Num(), *TargetLocation.ToString());

	// Issue attack-move commands to all selected units
	for (AActor* Unit : SelectedUnits)
	{
		// Check if unit is alive (different methods for different types)
		bool bIsAlive = false;
		if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
		{
			bIsAlive = RTSUnit->IsAlive();
		}
		else if (ARTSBaseActor* BaseActor = Cast<ARTSBaseActor>(Unit))
		{
			bIsAlive = BaseActor->IsAlive();
		}

		if (Unit && IsValid(Unit) && bIsAlive)
		{
			if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
			{
				// Use new command system for attack-move
				FRTSCommand AttackMoveCommand;
				AttackMoveCommand.CommandType = ERTSCommandType::AttackMove;
				AttackMoveCommand.Priority = ERTSCommandPriority::Normal;
				AttackMoveCommand.TargetLocation = TargetLocation;
				AttackMoveCommand.bInterruptible = true;

				RTSUnit->IssueCommand(AttackMoveCommand, true);

				if (bEnableDebugLogging)
				{
					UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Issued attack-move command to unit %s"), *Unit->GetName());
				}
			}
		}
	}

	// Call Blueprint event
	OnAttackMoveCommandIssued(TargetLocation, SelectedUnits);
}

bool ARTSPlayerController::GetMouseWorldLocation(FVector& WorldLocation)
{
	FVector WorldDirection;
	FVector2D MousePosition = GetCurrentMousePosition();

	if (!ScreenToWorldPosition(MousePosition, WorldLocation, WorldDirection))
	{
		return false;
	}

	// Perform line trace to find ground
	FHitResult HitResult;
	FVector TraceStart = WorldLocation;
	FVector TraceEnd = WorldLocation + (WorldDirection * 10000.0f); // 100 meter trace

	FCollisionQueryParams QueryParams;
	QueryParams.bTraceComplex = false;
	QueryParams.AddIgnoredActor(GetPawn()); // Ignore camera pawn

	bool bHit = GetWorld()->LineTraceSingleByChannel(
		HitResult,
		TraceStart,
		TraceEnd,
		ECC_WorldStatic, // Trace for ground/static objects
		QueryParams
	);

	if (bHit)
	{
		WorldLocation = HitResult.Location;
		return true;
	}

	// If no ground hit, use a default Z level
	WorldLocation.Z = 0.0f;
	return true;
}

bool ARTSPlayerController::IsValidMoveLocation(const FVector& Location)
{
	// Basic validation - can be expanded with navigation mesh checks
	// For now, just check if it's not too far from origin
	float MaxDistance = 10000.0f; // 100 meters
	return Location.Size() <= MaxDistance;
}

TArray<FVector> ARTSPlayerController::CalculateFormationPositions(const FVector& CenterLocation, int32 UnitCount)
{
	TArray<FVector> Positions;

	if (UnitCount <= 0)
	{
		return Positions;
	}

	if (UnitCount == 1)
	{
		Positions.Add(CenterLocation);
		return Positions;
	}

	// Formation settings
	float UnitSpacing = 200.0f; // Distance between units
	int32 UnitsPerRow = FMath::CeilToInt(FMath::Sqrt((float)UnitCount)); // Square-ish formation

	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Calculating formation for %d units, %d per row"),
			UnitCount, UnitsPerRow);
	}

	// Calculate positions in a grid formation
	for (int32 i = 0; i < UnitCount; i++)
	{
		int32 Row = i / UnitsPerRow;
		int32 Col = i % UnitsPerRow;

		// Center the formation
		float OffsetX = (Col - (UnitsPerRow - 1) * 0.5f) * UnitSpacing;
		float OffsetY = (Row - (FMath::CeilToInt((float)UnitCount / UnitsPerRow) - 1) * 0.5f) * UnitSpacing;

		FVector Position = CenterLocation + FVector(OffsetX, OffsetY, 0.0f);
		Positions.Add(Position);

		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, VeryVerbose, TEXT("RTSPlayerController: Formation position %d: %s"),
				i, *Position.ToString());
		}
	}

	return Positions;
}

// Selection Box Implementation
void ARTSPlayerController::StartSelectionBox()
{
	SelectionBoxStartPosition = GetCurrentMousePosition();
	CurrentMousePosition = SelectionBoxStartPosition;
	bIsDrawingSelectionBox = true;

	// Always log this to help debug
	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Selection box started at %s"),
		*SelectionBoxStartPosition.ToString());

	// Tell HUD to start drawing selection box
	AHUD* HUD = GetHUD();
	if (HUD)
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: HUD found: %s"), *HUD->GetClass()->GetName());

		if (ARTSHUD* RTSHUD = Cast<ARTSHUD>(HUD))
		{
			UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: RTSHUD cast successful, calling StartSelectionBox"));
			RTSHUD->StartSelectionBox(SelectionBoxStartPosition);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("RTSPlayerController: Failed to cast HUD to ARTSHUD - HUD type: %s"),
				*HUD->GetClass()->GetName());
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("RTSPlayerController: No HUD found!"));
	}
}

void ARTSPlayerController::UpdateSelectionBox()
{
	if (bIsDrawingSelectionBox)
	{
		FVector2D NewMousePosition = GetCurrentMousePosition();

		// Check if mouse moved enough to continue selection box
		float Distance = FVector2D::Distance(SelectionBoxStartPosition, NewMousePosition);
		if (Distance >= SelectionBoxMinDistance)
		{
			CurrentMousePosition = NewMousePosition;

			// Tell HUD to update selection box
			if (AHUD* HUD = GetHUD())
			{
				if (ARTSHUD* RTSHUD = Cast<ARTSHUD>(HUD))
				{
					RTSHUD->UpdateSelectionBox(CurrentMousePosition);
				}
			}

			if (bEnableDebugLogging)
			{
				UE_LOG(LogTemp, VeryVerbose, TEXT("RTSPlayerController: Selection box updated to %s (Distance: %.2f)"),
					*CurrentMousePosition.ToString(), Distance);
			}
		}
	}
}

void ARTSPlayerController::EndSelectionBox()
{
	if (bIsDrawingSelectionBox)
	{
		bIsDrawingSelectionBox = false;

		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Selection box ended - Start: %s, End: %s"),
				*SelectionBoxStartPosition.ToString(), *CurrentMousePosition.ToString());
		}

		// Tell HUD to end selection box
		if (AHUD* HUD = GetHUD())
		{
			if (ARTSHUD* RTSHUD = Cast<ARTSHUD>(HUD))
			{
				RTSHUD->EndSelectionBox();
			}
		}

		// Perform selection logic
		PerformBoxSelection();
	}
}

FVector2D ARTSPlayerController::GetCurrentMousePosition() const
{
	float MouseX, MouseY;
	GetMousePosition(MouseX, MouseY);
	return FVector2D(MouseX, MouseY);
}

ARTSPawn* ARTSPlayerController::GetRTSPawn() const
{
	return Cast<ARTSPawn>(GetPawn());
}

void ARTSPlayerController::SetCameraMovementSpeed(float NewSpeed)
{
	if (ARTSPawn* RTSPawn = GetRTSPawn())
	{
		RTSPawn->SetCameraMovementSpeed(NewSpeed);

		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Camera movement speed set to %.2f"), NewSpeed);
		}
	}
	else if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Cannot set camera movement speed - no RTS pawn possessed"));
	}
}

void ARTSPlayerController::SetCameraZoomSpeed(float NewSpeed)
{
	if (ARTSPawn* RTSPawn = GetRTSPawn())
	{
		RTSPawn->SetCameraZoomSpeed(NewSpeed);

		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Camera zoom speed set to %.2f"), NewSpeed);
		}
	}
	else if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Cannot set camera zoom speed - no RTS pawn possessed"));
	}
}

void ARTSPlayerController::SetCameraRotationSpeed(float NewSpeed)
{
	if (ARTSPawn* RTSPawn = GetRTSPawn())
	{
		RTSPawn->SetCameraRotationSpeed(NewSpeed);

		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Camera rotation speed set to %.2f"), NewSpeed);
		}
	}
	else if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Cannot set camera rotation speed - no RTS pawn possessed"));
	}
}

void ARTSPlayerController::PerformBoxSelection()
{
	// Check if we actually dragged (minimum distance)
	float Distance = FVector2D::Distance(SelectionBoxStartPosition, CurrentMousePosition);

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: PerformBoxSelection - Distance: %.2f, MinDistance: %.2f"),
		Distance, SelectionBoxMinDistance);

	if (Distance < SelectionBoxMinDistance)
	{
		// Small movement, treat as single selection
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Small movement, performing single selection"));
		PerformSingleSelection();
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Large movement, performing box selection"));

	// Find units in selection box (try both methods)
	TArray<AActor*> UnitsInBox = FindUnitsInSelectionBox(SelectionBoxStartPosition, CurrentMousePosition);

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Screen space method found %d units"), UnitsInBox.Num());

	// If screen space method found nothing, try world space method
	if (UnitsInBox.Num() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Trying world space method as fallback"));
		UnitsInBox = FindUnitsInSelectionBoxWorldSpace(SelectionBoxStartPosition, CurrentMousePosition);
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: World space method found %d units"), UnitsInBox.Num());
	}

	// Clear current selection and select found units
	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: About to clear selection and select %d units"), UnitsInBox.Num());
	ClearUnitSelection();

	for (AActor* Unit : UnitsInBox)
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Processing unit %s"), Unit ? *Unit->GetName() : TEXT("NULL"));

		// Check if unit is alive (different methods for different types)
		bool bIsAlive = false;
		if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
		{
			bIsAlive = RTSUnit->IsAlive();
		}
		else if (ARTSBaseActor* BaseActor = Cast<ARTSBaseActor>(Unit))
		{
			bIsAlive = BaseActor->IsAlive();
		}

		if (Unit && bIsAlive)
		{
			UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Unit %s is valid and alive, adding to selection"), *Unit->GetName());
			AddUnitToSelection(Unit);

			UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Successfully selected unit %s"), *Unit->GetName());
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Unit %s is invalid or not alive"),
				Unit ? *Unit->GetName() : TEXT("NULL"));
		}
	}
}

void ARTSPlayerController::PerformSingleSelection()
{
	FVector WorldPosition, WorldDirection;
	if (!ScreenToWorldPosition(GetCurrentMousePosition(), WorldPosition, WorldDirection))
	{
		return;
	}

	// Perform line trace to find unit under mouse
	FHitResult HitResult;
	FVector TraceStart = WorldPosition;
	FVector TraceEnd = WorldPosition + (WorldDirection * 10000.0f); // 100 meter trace

	FCollisionQueryParams QueryParams;
	QueryParams.bTraceComplex = false;
	QueryParams.AddIgnoredActor(GetPawn()); // Ignore camera pawn

	bool bHit = GetWorld()->LineTraceSingleByChannel(
		HitResult,
		TraceStart,
		TraceEnd,
		ECC_Pawn, // Trace for pawns
		QueryParams
	);

	if (bHit && HitResult.GetActor())
	{
		AActor* HitActor = HitResult.GetActor();

		// Check for RTSUnit (Pawn) first, then RTSBaseActor
		if (ARTSUnit* HitUnit = Cast<ARTSUnit>(HitActor))
		{
			if (bEnableDebugLogging)
			{
				UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Single selection hit RTSUnit %s"), *HitUnit->GetName());
			}

			// Select the unit directly as AActor
			ClearUnitSelection();
			AddUnitToSelection(HitUnit);
		}
		else if (ARTSBaseActor* HitBaseActor = Cast<ARTSBaseActor>(HitActor))
		{
			if (bEnableDebugLogging)
			{
				UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Single selection hit RTSBaseActor %s"), *HitBaseActor->GetName());
			}

			// Select the unit
			ClearUnitSelection();
			AddUnitToSelection(HitBaseActor);
		}
		else if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Single selection hit non-unit actor %s"),
				*HitActor->GetName());
		}
	}
	else if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Single selection hit nothing"));

		// Clear selection when clicking empty space
		ClearUnitSelection();
	}
}

TArray<AActor*> ARTSPlayerController::FindUnitsInSelectionBox(const FVector2D& StartPos, const FVector2D& EndPos)
{
	TArray<AActor*> UnitsInBox;

	if (!GetWorld())
	{
		UE_LOG(LogTemp, Error, TEXT("RTSPlayerController: FindUnitsInSelectionBox - No world!"));
		return UnitsInBox;
	}

	// Calculate selection box bounds
	float MinX = FMath::Min(StartPos.X, EndPos.X);
	float MinY = FMath::Min(StartPos.Y, EndPos.Y);
	float MaxX = FMath::Max(StartPos.X, EndPos.X);
	float MaxY = FMath::Max(StartPos.Y, EndPos.Y);

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: FindUnitsInSelectionBox - Bounds: MinX=%.2f, MinY=%.2f, MaxX=%.2f, MaxY=%.2f"),
		MinX, MinY, MaxX, MaxY);

	// Find all RTS actors in the world
	TArray<AActor*> FoundActors;
	UGameplayStatics::GetAllActorsOfClass(GetWorld(), ARTSBaseActor::StaticClass(), FoundActors);

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Found %d ARTSBaseActor instances in world"), FoundActors.Num());

	// Also try finding all actors to see what's in the world
	TArray<AActor*> AllActors;
	UGameplayStatics::GetAllActorsOfClass(GetWorld(), AActor::StaticClass(), AllActors);
	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Total actors in world: %d"), AllActors.Num());

	for (AActor* Actor : FoundActors)
	{
		if (ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
		{
			UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Checking unit %s at world pos %s"),
				*RTSActor->GetName(), *RTSActor->GetActorLocation().ToString());

			if (!RTSActor->IsAlive())
			{
				UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Unit %s is not alive, skipping"), *RTSActor->GetName());
				continue;
			}

			// Convert world position to screen position
			FVector2D ScreenPosition;
			if (ProjectWorldLocationToScreen(RTSActor->GetActorLocation(), ScreenPosition))
			{
				UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Unit %s screen pos: %s"),
					*RTSActor->GetName(), *ScreenPosition.ToString());

				// Check if unit is within selection box
				if (ScreenPosition.X >= MinX && ScreenPosition.X <= MaxX &&
					ScreenPosition.Y >= MinY && ScreenPosition.Y <= MaxY)
				{
					UnitsInBox.Add(RTSActor);
					UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Unit %s is IN selection box!"), *RTSActor->GetName());
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Unit %s is OUTSIDE selection box"), *RTSActor->GetName());
				}
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Failed to project unit %s to screen"), *RTSActor->GetName());
			}
		}
	}

	// Also find RTSUnit pawns
	TArray<AActor*> FoundUnits;
	UGameplayStatics::GetAllActorsOfClass(GetWorld(), ARTSUnit::StaticClass(), FoundUnits);

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Found %d RTSUnit instances in world"), FoundUnits.Num());

	for (AActor* Actor : FoundUnits)
	{
		if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Actor))
		{
			UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Checking RTSUnit %s at world pos %s"),
				*RTSUnit->GetName(), *RTSUnit->GetActorLocation().ToString());

			if (!RTSUnit->IsAlive())
			{
				UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: RTSUnit %s is not alive, skipping"), *RTSUnit->GetName());
				continue;
			}

			// Convert world position to screen position
			FVector2D ScreenPosition;
			if (ProjectWorldLocationToScreen(RTSUnit->GetActorLocation(), ScreenPosition))
			{
				UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: RTSUnit %s screen pos: %s"),
					*RTSUnit->GetName(), *ScreenPosition.ToString());

				// Check if unit is within selection box
				if (ScreenPosition.X >= MinX && ScreenPosition.X <= MaxX &&
					ScreenPosition.Y >= MinY && ScreenPosition.Y <= MaxY)
				{
					UnitsInBox.Add(RTSUnit);
					UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: RTSUnit %s is IN selection box!"), *RTSUnit->GetName());
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: RTSUnit %s is OUTSIDE selection box"), *RTSUnit->GetName());
				}
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Failed to project RTSUnit %s to screen"), *RTSUnit->GetName());
			}
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: FindUnitsInSelectionBox returning %d units"), UnitsInBox.Num());
	return UnitsInBox;
}

TArray<AActor*> ARTSPlayerController::FindUnitsInSelectionBoxWorldSpace(const FVector2D& StartPos, const FVector2D& EndPos)
{
	TArray<AActor*> UnitsInBox;

	if (!GetWorld())
	{
		return UnitsInBox;
	}

	// Get camera location and rotation
	FVector CameraLocation;
	FRotator CameraRotation;
	GetPlayerViewPoint(CameraLocation, CameraRotation);

	// Create frustum from selection box corners
	FVector WorldStart1, WorldDir1, WorldStart2, WorldDir2, WorldStart3, WorldDir3, WorldStart4, WorldDir4;

	// Get world rays for all four corners of selection box
	float MinX = FMath::Min(StartPos.X, EndPos.X);
	float MinY = FMath::Min(StartPos.Y, EndPos.Y);
	float MaxX = FMath::Max(StartPos.X, EndPos.X);
	float MaxY = FMath::Max(StartPos.Y, EndPos.Y);

	DeprojectScreenPositionToWorld(MinX, MinY, WorldStart1, WorldDir1);
	DeprojectScreenPositionToWorld(MaxX, MinY, WorldStart2, WorldDir2);
	DeprojectScreenPositionToWorld(MaxX, MaxY, WorldStart3, WorldDir3);
	DeprojectScreenPositionToWorld(MinX, MaxY, WorldStart4, WorldDir4);

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: WorldSpace selection - Camera at %s"), *CameraLocation.ToString());

	// Find all RTS actors in the world
	TArray<AActor*> FoundActors;
	UGameplayStatics::GetAllActorsOfClass(GetWorld(), ARTSBaseActor::StaticClass(), FoundActors);

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: WorldSpace found %d ARTSBaseActor instances"), FoundActors.Num());

	for (AActor* Actor : FoundActors)
	{
		if (ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
		{
			if (!RTSActor->IsAlive())
			{
				continue;
			}

			// Simple method: check if unit projects to within selection box
			FVector2D ScreenPos;
			if (ProjectWorldLocationToScreen(RTSActor->GetActorLocation(), ScreenPos))
			{
				if (ScreenPos.X >= MinX && ScreenPos.X <= MaxX && ScreenPos.Y >= MinY && ScreenPos.Y <= MaxY)
				{
					UnitsInBox.Add(RTSActor);
					UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: WorldSpace selected unit %s"), *RTSActor->GetName());
				}
			}
		}
	}

	return UnitsInBox;
}

bool ARTSPlayerController::ScreenToWorldPosition(const FVector2D& ScreenPosition, FVector& WorldPosition, FVector& WorldDirection)
{
	return DeprojectScreenPositionToWorld(ScreenPosition.X, ScreenPosition.Y, WorldPosition, WorldDirection);
}

void ARTSPlayerController::ClearUnitSelection()
{
	// Clear selection state on all previously selected units
	for (AActor* Unit : SelectedUnits)
	{
		if (Unit && IsValid(Unit))
		{
			// Set selected state (different methods for different types)
			if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
			{
				RTSUnit->SetSelected(false);
			}
			else if (ARTSBaseActor* BaseActor = Cast<ARTSBaseActor>(Unit))
			{
				BaseActor->SetSelected(false);
			}
		}
	}

	SelectedUnits.Empty();

	if (bEnableDebugLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Selection cleared"));
	}

	// Call Blueprint event
	OnSelectionChanged(SelectedUnits);
}

void ARTSPlayerController::AddUnitToSelection(AActor* Unit)
{
	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: AddUnitToSelection called with unit: %s"),
		Unit ? *Unit->GetName() : TEXT("NULL"));

	if (!Unit || !IsValid(Unit))
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Unit is null or invalid"));
		return;
	}

	// Check if unit is alive (different methods for different types)
	bool bIsAlive = true;
	if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
	{
		bIsAlive = RTSUnit->IsAlive();
	}
	else if (ARTSBaseActor* BaseActor = Cast<ARTSBaseActor>(Unit))
	{
		bIsAlive = BaseActor->IsAlive();
	}

	if (!bIsAlive)
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Unit %s is not alive"), *Unit->GetName());
		return;
	}

	// Don't add if already selected
	if (SelectedUnits.Contains(Unit))
	{
		UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Unit %s is already selected"), *Unit->GetName());
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Adding unit %s to selection"), *Unit->GetName());
	SelectedUnits.Add(Unit);

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Calling SetSelected(true) on unit %s"), *Unit->GetName());

	// Set selected state (different methods for different types)
	if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
	{
		RTSUnit->SetSelected(true);
	}
	else if (ARTSBaseActor* BaseActor = Cast<ARTSBaseActor>(Unit))
	{
		BaseActor->SetSelected(true);
	}

	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Added unit %s to selection (Total: %d)"),
		*Unit->GetName(), SelectedUnits.Num());

	// Call Blueprint event
	UE_LOG(LogTemp, Warning, TEXT("RTSPlayerController: Calling OnSelectionChanged Blueprint event"));
	OnSelectionChanged(SelectedUnits);
}

void ARTSPlayerController::RemoveUnitFromSelection(AActor* Unit)
{
	if (!Unit || !IsValid(Unit))
	{
		return;
	}

	if (SelectedUnits.Remove(Unit) > 0)
	{
		// Set selected state (different methods for different types)
		if (ARTSUnit* RTSUnit = Cast<ARTSUnit>(Unit))
		{
			RTSUnit->SetSelected(false);
		}
		else if (ARTSBaseActor* BaseActor = Cast<ARTSBaseActor>(Unit))
		{
			BaseActor->SetSelected(false);
		}

		if (bEnableDebugLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("RTSPlayerController: Removed unit %s from selection (Total: %d)"),
				*Unit->GetName(), SelectedUnits.Num());
		}

		// Call Blueprint event
		OnSelectionChanged(SelectedUnits);
	}
}

void ARTSPlayerController::SetUnitSelection(const TArray<AActor*>& Units)
{
	ClearUnitSelection();

	for (AActor* Unit : Units)
	{
		AddUnitToSelection(Unit);
	}
}

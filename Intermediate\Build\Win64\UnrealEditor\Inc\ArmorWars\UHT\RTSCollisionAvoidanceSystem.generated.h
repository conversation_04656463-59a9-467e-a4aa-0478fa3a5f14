// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSCollisionAvoidanceSystem.h"

#ifdef ARMORWARS_RTSCollisionAvoidanceSystem_generated_h
#error "RTSCollisionAvoidanceSystem.generated.h already included, missing '#pragma once' in RTSCollisionAvoidanceSystem.h"
#endif
#define ARMORWARS_RTSCollisionAvoidanceSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSUnit;

// ********** Begin ScriptStruct FRTSCollisionAvoidanceParams **************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_14_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSCollisionAvoidanceParams;
// ********** End ScriptStruct FRTSCollisionAvoidanceParams ****************************************

// ********** Begin ScriptStruct FRTSSpatialGridCell ***********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_73_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSSpatialGridCell;
// ********** End ScriptStruct FRTSSpatialGridCell *************************************************

// ********** Begin ScriptStruct FRTSCollisionAvoidanceData ****************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_88_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSCollisionAvoidanceData;
// ********** End ScriptStruct FRTSCollisionAvoidanceData ******************************************

// ********** Begin Class URTSCollisionAvoidanceComponent ******************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_133_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetOwnerUnit); \
	DECLARE_FUNCTION(execGetDistanceToUnit); \
	DECLARE_FUNCTION(execIsUnitInAvoidanceRange); \
	DECLARE_FUNCTION(execIsUnitInPersonalSpace); \
	DECLARE_FUNCTION(execFindNearbyUnits); \
	DECLARE_FUNCTION(execCalculateObstacleAvoidanceForce); \
	DECLARE_FUNCTION(execCalculateCohesionForce); \
	DECLARE_FUNCTION(execCalculateAlignmentForce); \
	DECLARE_FUNCTION(execCalculateSeparationForce); \
	DECLARE_FUNCTION(execUpdateAvoidanceData); \
	DECLARE_FUNCTION(execGetAdjustedMovementDirection); \
	DECLARE_FUNCTION(execCalculateAvoidanceForce);


ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceComponent_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_133_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSCollisionAvoidanceComponent(); \
	friend struct Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSCollisionAvoidanceComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSCollisionAvoidanceComponent_NoRegister) \
	DECLARE_SERIALIZER(URTSCollisionAvoidanceComponent)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_133_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSCollisionAvoidanceComponent(URTSCollisionAvoidanceComponent&&) = delete; \
	URTSCollisionAvoidanceComponent(const URTSCollisionAvoidanceComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSCollisionAvoidanceComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSCollisionAvoidanceComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSCollisionAvoidanceComponent) \
	NO_API virtual ~URTSCollisionAvoidanceComponent();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_130_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_133_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_133_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_133_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_133_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSCollisionAvoidanceComponent;

// ********** End Class URTSCollisionAvoidanceComponent ********************************************

// ********** Begin Class URTSCollisionAvoidanceManager ********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_218_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetRegisteredUnitCount); \
	DECLARE_FUNCTION(execGridCellToWorldLocation); \
	DECLARE_FUNCTION(execWorldLocationToGridCell); \
	DECLARE_FUNCTION(execCleanupInvalidUnits); \
	DECLARE_FUNCTION(execUpdateSpatialGrid); \
	DECLARE_FUNCTION(execGetNearbyUnits); \
	DECLARE_FUNCTION(execGetUnitsInCell); \
	DECLARE_FUNCTION(execGetUnitsInRadius); \
	DECLARE_FUNCTION(execUnregisterUnit); \
	DECLARE_FUNCTION(execRegisterUnit);


ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceManager_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_218_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSCollisionAvoidanceManager(); \
	friend struct Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceManager_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSCollisionAvoidanceManager, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSCollisionAvoidanceManager_NoRegister) \
	DECLARE_SERIALIZER(URTSCollisionAvoidanceManager)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_218_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSCollisionAvoidanceManager(URTSCollisionAvoidanceManager&&) = delete; \
	URTSCollisionAvoidanceManager(const URTSCollisionAvoidanceManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSCollisionAvoidanceManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSCollisionAvoidanceManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSCollisionAvoidanceManager) \
	NO_API virtual ~URTSCollisionAvoidanceManager();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_215_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_218_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_218_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_218_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h_218_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSCollisionAvoidanceManager;

// ********** End Class URTSCollisionAvoidanceManager **********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

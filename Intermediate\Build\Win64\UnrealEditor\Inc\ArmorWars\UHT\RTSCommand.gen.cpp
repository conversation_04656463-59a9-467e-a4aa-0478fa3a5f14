// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSCommand.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSCommand() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandPriority();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandStatus();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandType();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSCommand();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSCommandQueue();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSCommandType ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSCommandType;
static UEnum* ERTSCommandType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSCommandType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSCommandType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSCommandType, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSCommandType"));
	}
	return Z_Registration_Info_UEnum_ERTSCommandType.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSCommandType>()
{
	return ERTSCommandType_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSCommandType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AttackMove.DisplayName", "Attack Move" },
		{ "AttackMove.Name", "ERTSCommandType::AttackMove" },
		{ "AttackTarget.DisplayName", "Attack Target" },
		{ "AttackTarget.Name", "ERTSCommandType::AttackTarget" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command types for RTS units\n" },
#endif
		{ "Follow.DisplayName", "Follow" },
		{ "Follow.Name", "ERTSCommandType::Follow" },
		{ "Formation.DisplayName", "Formation" },
		{ "Formation.Name", "ERTSCommandType::Formation" },
		{ "Guard.DisplayName", "Guard" },
		{ "Guard.Name", "ERTSCommandType::Guard" },
		{ "Hold.DisplayName", "Hold Position" },
		{ "Hold.Name", "ERTSCommandType::Hold" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
		{ "Move.DisplayName", "Move" },
		{ "Move.Name", "ERTSCommandType::Move" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ERTSCommandType::None" },
		{ "Patrol.DisplayName", "Patrol" },
		{ "Patrol.Name", "ERTSCommandType::Patrol" },
		{ "Retreat.DisplayName", "Retreat" },
		{ "Retreat.Name", "ERTSCommandType::Retreat" },
		{ "SpecialAbility.DisplayName", "Special Ability" },
		{ "SpecialAbility.Name", "ERTSCommandType::SpecialAbility" },
		{ "Stop.DisplayName", "Stop" },
		{ "Stop.Name", "ERTSCommandType::Stop" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command types for RTS units" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSCommandType::None", (int64)ERTSCommandType::None },
		{ "ERTSCommandType::Move", (int64)ERTSCommandType::Move },
		{ "ERTSCommandType::AttackMove", (int64)ERTSCommandType::AttackMove },
		{ "ERTSCommandType::AttackTarget", (int64)ERTSCommandType::AttackTarget },
		{ "ERTSCommandType::Stop", (int64)ERTSCommandType::Stop },
		{ "ERTSCommandType::Hold", (int64)ERTSCommandType::Hold },
		{ "ERTSCommandType::Patrol", (int64)ERTSCommandType::Patrol },
		{ "ERTSCommandType::Follow", (int64)ERTSCommandType::Follow },
		{ "ERTSCommandType::Guard", (int64)ERTSCommandType::Guard },
		{ "ERTSCommandType::Formation", (int64)ERTSCommandType::Formation },
		{ "ERTSCommandType::Retreat", (int64)ERTSCommandType::Retreat },
		{ "ERTSCommandType::SpecialAbility", (int64)ERTSCommandType::SpecialAbility },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSCommandType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSCommandType",
	"ERTSCommandType",
	Z_Construct_UEnum_ArmorWars_ERTSCommandType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSCommandType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSCommandType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSCommandType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandType()
{
	if (!Z_Registration_Info_UEnum_ERTSCommandType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSCommandType.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSCommandType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSCommandType.InnerSingleton;
}
// ********** End Enum ERTSCommandType *************************************************************

// ********** Begin Enum ERTSCommandPriority *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSCommandPriority;
static UEnum* ERTSCommandPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSCommandPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSCommandPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSCommandPriority"));
	}
	return Z_Registration_Info_UEnum_ERTSCommandPriority.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSCommandPriority>()
{
	return ERTSCommandPriority_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSCommandPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command priority levels\n" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "ERTSCommandPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "ERTSCommandPriority::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "ERTSCommandPriority::Low" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "ERTSCommandPriority::Normal" },
		{ "Override.DisplayName", "Override" },
		{ "Override.Name", "ERTSCommandPriority::Override" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command priority levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSCommandPriority::Low", (int64)ERTSCommandPriority::Low },
		{ "ERTSCommandPriority::Normal", (int64)ERTSCommandPriority::Normal },
		{ "ERTSCommandPriority::High", (int64)ERTSCommandPriority::High },
		{ "ERTSCommandPriority::Critical", (int64)ERTSCommandPriority::Critical },
		{ "ERTSCommandPriority::Override", (int64)ERTSCommandPriority::Override },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSCommandPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSCommandPriority",
	"ERTSCommandPriority",
	Z_Construct_UEnum_ArmorWars_ERTSCommandPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSCommandPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSCommandPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandPriority()
{
	if (!Z_Registration_Info_UEnum_ERTSCommandPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSCommandPriority.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSCommandPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSCommandPriority.InnerSingleton;
}
// ********** End Enum ERTSCommandPriority *********************************************************

// ********** Begin Enum ERTSCommandStatus *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSCommandStatus;
static UEnum* ERTSCommandStatus_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSCommandStatus.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSCommandStatus.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSCommandStatus, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSCommandStatus"));
	}
	return Z_Registration_Info_UEnum_ERTSCommandStatus.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSCommandStatus>()
{
	return ERTSCommandStatus_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSCommandStatus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cancelled.DisplayName", "Cancelled" },
		{ "Cancelled.Name", "ERTSCommandStatus::Cancelled" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command execution status\n" },
#endif
		{ "Completed.DisplayName", "Completed" },
		{ "Completed.Name", "ERTSCommandStatus::Completed" },
		{ "Executing.DisplayName", "Executing" },
		{ "Executing.Name", "ERTSCommandStatus::Executing" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "ERTSCommandStatus::Failed" },
		{ "Interrupted.DisplayName", "Interrupted" },
		{ "Interrupted.Name", "ERTSCommandStatus::Interrupted" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
		{ "Pending.DisplayName", "Pending" },
		{ "Pending.Name", "ERTSCommandStatus::Pending" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command execution status" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSCommandStatus::Pending", (int64)ERTSCommandStatus::Pending },
		{ "ERTSCommandStatus::Executing", (int64)ERTSCommandStatus::Executing },
		{ "ERTSCommandStatus::Completed", (int64)ERTSCommandStatus::Completed },
		{ "ERTSCommandStatus::Failed", (int64)ERTSCommandStatus::Failed },
		{ "ERTSCommandStatus::Cancelled", (int64)ERTSCommandStatus::Cancelled },
		{ "ERTSCommandStatus::Interrupted", (int64)ERTSCommandStatus::Interrupted },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSCommandStatus_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSCommandStatus",
	"ERTSCommandStatus",
	Z_Construct_UEnum_ArmorWars_ERTSCommandStatus_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSCommandStatus_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSCommandStatus_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSCommandStatus_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandStatus()
{
	if (!Z_Registration_Info_UEnum_ERTSCommandStatus.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSCommandStatus.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSCommandStatus_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSCommandStatus.InnerSingleton;
}
// ********** End Enum ERTSCommandStatus ***********************************************************

// ********** Begin Enum ERTSFormationCommandType **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSFormationCommandType;
static UEnum* ERTSFormationCommandType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSFormationCommandType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSFormationCommandType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSFormationCommandType"));
	}
	return Z_Registration_Info_UEnum_ERTSFormationCommandType.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSFormationCommandType>()
{
	return ERTSFormationCommandType_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Circle.DisplayName", "Circle" },
		{ "Circle.Name", "ERTSFormationCommandType::Circle" },
		{ "Column.DisplayName", "Column" },
		{ "Column.Name", "ERTSFormationCommandType::Column" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation types for group commands\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "ERTSFormationCommandType::Custom" },
		{ "Line.DisplayName", "Line" },
		{ "Line.Name", "ERTSFormationCommandType::Line" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
		{ "Scattered.DisplayName", "Scattered" },
		{ "Scattered.Name", "ERTSFormationCommandType::Scattered" },
		{ "Square.DisplayName", "Square" },
		{ "Square.Name", "ERTSFormationCommandType::Square" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation types for group commands" },
#endif
		{ "Wedge.DisplayName", "Wedge" },
		{ "Wedge.Name", "ERTSFormationCommandType::Wedge" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSFormationCommandType::Line", (int64)ERTSFormationCommandType::Line },
		{ "ERTSFormationCommandType::Wedge", (int64)ERTSFormationCommandType::Wedge },
		{ "ERTSFormationCommandType::Circle", (int64)ERTSFormationCommandType::Circle },
		{ "ERTSFormationCommandType::Column", (int64)ERTSFormationCommandType::Column },
		{ "ERTSFormationCommandType::Square", (int64)ERTSFormationCommandType::Square },
		{ "ERTSFormationCommandType::Scattered", (int64)ERTSFormationCommandType::Scattered },
		{ "ERTSFormationCommandType::Custom", (int64)ERTSFormationCommandType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSFormationCommandType",
	"ERTSFormationCommandType",
	Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType()
{
	if (!Z_Registration_Info_UEnum_ERTSFormationCommandType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSFormationCommandType.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSFormationCommandType.InnerSingleton;
}
// ********** End Enum ERTSFormationCommandType ****************************************************

// ********** Begin ScriptStruct FRTSCommand *******************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSCommand;
class UScriptStruct* FRTSCommand::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSCommand.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSCommand.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSCommand, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSCommand"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSCommand.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSCommand_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Base structure for RTS commands\n * Contains all necessary information for command execution\n */" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base structure for RTS commands\nContains all necessary information for command execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommandType_MetaData[] = {
		{ "Category", "Command" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command identification\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Command" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Status_MetaData[] = {
		{ "Category", "Command" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "Category", "Command" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetActor_MetaData[] = {
		{ "Category", "Command" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WaypointLocations_MetaData[] = {
		{ "Category", "Command" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationType_MetaData[] = {
		{ "Category", "Command" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationSpacing_MetaData[] = {
		{ "Category", "Command" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommandTime_MetaData[] = {
		{ "Category", "Command" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command metadata\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command metadata" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionStartTime_MetaData[] = {
		{ "Category", "Command" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeoutDuration_MetaData[] = {
		{ "Category", "Command" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bQueueCommand_MetaData[] = {
		{ "Category", "Command" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 0 = no timeout\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "0 = no timeout" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInterruptible_MetaData[] = {
		{ "Category", "Command" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommandTags_MetaData[] = {
		{ "Category", "Command" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationOffset_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation-specific data\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation-specific data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationLeader_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationIndex_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityName_MetaData[] = {
		{ "Category", "Special" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Special ability data\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Special ability data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityParameters_MetaData[] = {
		{ "Category", "Special" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CommandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CommandType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Status_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Status;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WaypointLocations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WaypointLocations;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FormationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FormationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FormationSpacing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CommandTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExecutionStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeoutDuration;
	static void NewProp_bQueueCommand_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bQueueCommand;
	static void NewProp_bInterruptible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInterruptible;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CommandTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FormationOffset;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_FormationLeader;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FormationIndex;
	static const UECodeGen_Private::FNamePropertyParams NewProp_AbilityName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilityParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilityParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AbilityParameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSCommand>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_CommandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_CommandType = { "CommandType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, CommandType), Z_Construct_UEnum_ArmorWars_ERTSCommandType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommandType_MetaData), NewProp_CommandType_MetaData) }; // 4246000564
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 136588220
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_Status_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_Status = { "Status", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, Status), Z_Construct_UEnum_ArmorWars_ERTSCommandStatus, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Status_MetaData), NewProp_Status_MetaData) }; // 3736025705
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, TargetActor), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetActor_MetaData), NewProp_TargetActor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_WaypointLocations_Inner = { "WaypointLocations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_WaypointLocations = { "WaypointLocations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, WaypointLocations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WaypointLocations_MetaData), NewProp_WaypointLocations_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationType = { "FormationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, FormationType), Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationType_MetaData), NewProp_FormationType_MetaData) }; // 2474596395
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationSpacing = { "FormationSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, FormationSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationSpacing_MetaData), NewProp_FormationSpacing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_CommandTime = { "CommandTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, CommandTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommandTime_MetaData), NewProp_CommandTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_ExecutionStartTime = { "ExecutionStartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, ExecutionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionStartTime_MetaData), NewProp_ExecutionStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_TimeoutDuration = { "TimeoutDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, TimeoutDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeoutDuration_MetaData), NewProp_TimeoutDuration_MetaData) };
void Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_bQueueCommand_SetBit(void* Obj)
{
	((FRTSCommand*)Obj)->bQueueCommand = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_bQueueCommand = { "bQueueCommand", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSCommand), &Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_bQueueCommand_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bQueueCommand_MetaData), NewProp_bQueueCommand_MetaData) };
void Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_bInterruptible_SetBit(void* Obj)
{
	((FRTSCommand*)Obj)->bInterruptible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_bInterruptible = { "bInterruptible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSCommand), &Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_bInterruptible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInterruptible_MetaData), NewProp_bInterruptible_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_CommandTags = { "CommandTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, CommandTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommandTags_MetaData), NewProp_CommandTags_MetaData) }; // 2104890724
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationOffset = { "FormationOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, FormationOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationOffset_MetaData), NewProp_FormationOffset_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationLeader = { "FormationLeader", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, FormationLeader), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationLeader_MetaData), NewProp_FormationLeader_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationIndex = { "FormationIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, FormationIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationIndex_MetaData), NewProp_FormationIndex_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_AbilityName = { "AbilityName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, AbilityName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityName_MetaData), NewProp_AbilityName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_AbilityParameters_ValueProp = { "AbilityParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_AbilityParameters_Key_KeyProp = { "AbilityParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_AbilityParameters = { "AbilityParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommand, AbilityParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityParameters_MetaData), NewProp_AbilityParameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_CommandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_CommandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_Status_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_Status,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_WaypointLocations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_WaypointLocations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_CommandTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_ExecutionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_TimeoutDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_bQueueCommand,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_bInterruptible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_CommandTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationLeader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_FormationIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_AbilityName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_AbilityParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_AbilityParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommand_Statics::NewProp_AbilityParameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSCommand_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSCommand",
	Z_Construct_UScriptStruct_FRTSCommand_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCommand_Statics::PropPointers),
	sizeof(FRTSCommand),
	alignof(FRTSCommand),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCommand_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSCommand_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSCommand()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSCommand.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSCommand.InnerSingleton, Z_Construct_UScriptStruct_FRTSCommand_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSCommand.InnerSingleton;
}
// ********** End ScriptStruct FRTSCommand *********************************************************

// ********** Begin ScriptStruct FRTSCommandQueue **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSCommandQueue;
class UScriptStruct* FRTSCommandQueue::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSCommandQueue.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSCommandQueue.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSCommandQueue, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSCommandQueue"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSCommandQueue.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSCommandQueue_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Command queue for managing multiple commands per unit\n */" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command queue for managing multiple commands per unit" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Commands_MetaData[] = {
		{ "Category", "Command Queue" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentCommandIndex_MetaData[] = {
		{ "Category", "Command Queue" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxQueueSize_MetaData[] = {
		{ "Category", "Command Queue" },
		{ "ModuleRelativePath", "Public/RTSCommand.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Commands_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Commands;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentCommandIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxQueueSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSCommandQueue>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::NewProp_Commands_Inner = { "Commands", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(0, nullptr) }; // 1544416680
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::NewProp_Commands = { "Commands", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommandQueue, Commands), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Commands_MetaData), NewProp_Commands_MetaData) }; // 1544416680
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::NewProp_CurrentCommandIndex = { "CurrentCommandIndex", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommandQueue, CurrentCommandIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentCommandIndex_MetaData), NewProp_CurrentCommandIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::NewProp_MaxQueueSize = { "MaxQueueSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommandQueue, MaxQueueSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxQueueSize_MetaData), NewProp_MaxQueueSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::NewProp_Commands_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::NewProp_Commands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::NewProp_CurrentCommandIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::NewProp_MaxQueueSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSCommandQueue",
	Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::PropPointers),
	sizeof(FRTSCommandQueue),
	alignof(FRTSCommandQueue),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSCommandQueue()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSCommandQueue.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSCommandQueue.InnerSingleton, Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSCommandQueue.InnerSingleton;
}
// ********** End ScriptStruct FRTSCommandQueue ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommand_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSCommandType_StaticEnum, TEXT("ERTSCommandType"), &Z_Registration_Info_UEnum_ERTSCommandType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4246000564U) },
		{ ERTSCommandPriority_StaticEnum, TEXT("ERTSCommandPriority"), &Z_Registration_Info_UEnum_ERTSCommandPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 136588220U) },
		{ ERTSCommandStatus_StaticEnum, TEXT("ERTSCommandStatus"), &Z_Registration_Info_UEnum_ERTSCommandStatus, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3736025705U) },
		{ ERTSFormationCommandType_StaticEnum, TEXT("ERTSFormationCommandType"), &Z_Registration_Info_UEnum_ERTSFormationCommandType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2474596395U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRTSCommand::StaticStruct, Z_Construct_UScriptStruct_FRTSCommand_Statics::NewStructOps, TEXT("RTSCommand"), &Z_Registration_Info_UScriptStruct_FRTSCommand, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSCommand), 1544416680U) },
		{ FRTSCommandQueue::StaticStruct, Z_Construct_UScriptStruct_FRTSCommandQueue_Statics::NewStructOps, TEXT("RTSCommandQueue"), &Z_Registration_Info_UScriptStruct_FRTSCommandQueue, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSCommandQueue), 3307480973U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommand_h__Script_ArmorWars_133009178(TEXT("/Script/ArmorWars"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommand_h__Script_ArmorWars_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommand_h__Script_ArmorWars_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommand_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommand_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

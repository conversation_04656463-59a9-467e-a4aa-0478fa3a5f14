// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSArmorTypes.h"

#ifdef ARMORWARS_RTSArmorTypes_generated_h
#error "RTSArmorTypes.generated.h already included, missing '#pragma once' in RTSArmorTypes.h"
#endif
#define ARMORWARS_RTSArmorTypes_generated_h

#include "Templates/IsUEnumClass.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ReflectedTypeAccessors.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSArmorTypes_h

// ********** Begin Enum ERTSArmorType *************************************************************
#define FOREACH_ENUM_ERTSARMORTYPE(op) \
	op(ERTSArmorType::Light) \
	op(ERTSArmorType::Medium) \
	op(ERTSArmorType::Heavy) \
	op(ERTSArmorType::Reactive) \
	op(ERTSArmorType::Composite) 

enum class ERTSArmorType : uint8;
template<> struct TIsUEnumClass<ERTSArmorType> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSArmorType>();
// ********** End Enum ERTSArmorType ***************************************************************

// ********** Begin Enum ERTSArmorFacing ***********************************************************
#define FOREACH_ENUM_ERTSARMORFACING(op) \
	op(ERTSArmorFacing::Front) \
	op(ERTSArmorFacing::Side) \
	op(ERTSArmorFacing::Rear) \
	op(ERTSArmorFacing::Top) \
	op(ERTSArmorFacing::Bottom) 

enum class ERTSArmorFacing : uint8;
template<> struct TIsUEnumClass<ERTSArmorFacing> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSArmorFacing>();
// ********** End Enum ERTSArmorFacing *************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

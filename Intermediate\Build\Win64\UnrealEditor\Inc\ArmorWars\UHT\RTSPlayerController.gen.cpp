// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSPlayerController.h"
#include "InputActionValue.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSPlayerController() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSPawn_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSPlayerController();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSPlayerController_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSSelectionSystem_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APlayerController();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputAction_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputMappingContext_NoRegister();
ENHANCEDINPUT_API UScriptStruct* Z_Construct_UScriptStruct_FInputActionValue();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Class ARTSPlayerController Function AddUnitToSelection *************************
struct Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics
{
	struct RTSPlayerController_eventAddUnitToSelection_Parms
	{
		AActor* Unit;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Add unit to selection */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Add unit to selection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventAddUnitToSelection_Parms, Unit), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::NewProp_Unit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "AddUnitToSelection", Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::RTSPlayerController_eventAddUnitToSelection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::RTSPlayerController_eventAddUnitToSelection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execAddUnitToSelection)
{
	P_GET_OBJECT(AActor,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddUnitToSelection(Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function AddUnitToSelection ***************************

// ********** Begin Class ARTSPlayerController Function CalculateFormationPositions ****************
struct Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics
{
	struct RTSPlayerController_eventCalculateFormationPositions_Parms
	{
		FVector CenterLocation;
		int32 UnitCount;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calculate formation positions for multiple units */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calculate formation positions for multiple units" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterLocation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnitCount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::NewProp_CenterLocation = { "CenterLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventCalculateFormationPositions_Parms, CenterLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterLocation_MetaData), NewProp_CenterLocation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::NewProp_UnitCount = { "UnitCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventCalculateFormationPositions_Parms, UnitCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventCalculateFormationPositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::NewProp_CenterLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::NewProp_UnitCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "CalculateFormationPositions", Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::RTSPlayerController_eventCalculateFormationPositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::RTSPlayerController_eventCalculateFormationPositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execCalculateFormationPositions)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CenterLocation);
	P_GET_PROPERTY(FIntProperty,Z_Param_UnitCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->CalculateFormationPositions(Z_Param_Out_CenterLocation,Z_Param_UnitCount);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function CalculateFormationPositions ******************

// ********** Begin Class ARTSPlayerController Function ClearUnitSelection *************************
struct Z_Construct_UFunction_ARTSPlayerController_ClearUnitSelection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Clear current selection */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clear current selection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_ClearUnitSelection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "ClearUnitSelection", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_ClearUnitSelection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_ClearUnitSelection_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_ClearUnitSelection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_ClearUnitSelection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execClearUnitSelection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearUnitSelection();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function ClearUnitSelection ***************************

// ********** Begin Class ARTSPlayerController Function EndSelectionBox ****************************
struct Z_Construct_UFunction_ARTSPlayerController_EndSelectionBox_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** End selection box and perform selection */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "End selection box and perform selection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_EndSelectionBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "EndSelectionBox", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_EndSelectionBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_EndSelectionBox_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_EndSelectionBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_EndSelectionBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execEndSelectionBox)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EndSelectionBox();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function EndSelectionBox ******************************

// ********** Begin Class ARTSPlayerController Function FindUnitsInSelectionBox ********************
struct Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics
{
	struct RTSPlayerController_eventFindUnitsInSelectionBox_Parms
	{
		FVector2D StartPos;
		FVector2D EndPos;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Find units within screen space selection box */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Find units within screen space selection box" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventFindUnitsInSelectionBox_Parms, StartPos), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventFindUnitsInSelectionBox_Parms, EndPos), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventFindUnitsInSelectionBox_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "FindUnitsInSelectionBox", Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::RTSPlayerController_eventFindUnitsInSelectionBox_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::RTSPlayerController_eventFindUnitsInSelectionBox_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execFindUnitsInSelectionBox)
{
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->FindUnitsInSelectionBox(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function FindUnitsInSelectionBox **********************

// ********** Begin Class ARTSPlayerController Function FindUnitsInSelectionBoxWorldSpace **********
struct Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics
{
	struct RTSPlayerController_eventFindUnitsInSelectionBoxWorldSpace_Parms
	{
		FVector2D StartPos;
		FVector2D EndPos;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Alternative method: Find units using world space frustum */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alternative method: Find units using world space frustum" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventFindUnitsInSelectionBoxWorldSpace_Parms, StartPos), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventFindUnitsInSelectionBoxWorldSpace_Parms, EndPos), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventFindUnitsInSelectionBoxWorldSpace_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "FindUnitsInSelectionBoxWorldSpace", Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::RTSPlayerController_eventFindUnitsInSelectionBoxWorldSpace_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::RTSPlayerController_eventFindUnitsInSelectionBoxWorldSpace_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execFindUnitsInSelectionBoxWorldSpace)
{
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_EndPos);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->FindUnitsInSelectionBoxWorldSpace(Z_Param_Out_StartPos,Z_Param_Out_EndPos);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function FindUnitsInSelectionBoxWorldSpace ************

// ********** Begin Class ARTSPlayerController Function GetCurrentMousePosition ********************
struct Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics
{
	struct RTSPlayerController_eventGetCurrentMousePosition_Parms
	{
		FVector2D ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get current mouse position in screen space */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current mouse position in screen space" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventGetCurrentMousePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "GetCurrentMousePosition", Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::RTSPlayerController_eventGetCurrentMousePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::RTSPlayerController_eventGetCurrentMousePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execGetCurrentMousePosition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector2D*)Z_Param__Result=P_THIS->GetCurrentMousePosition();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function GetCurrentMousePosition **********************

// ********** Begin Class ARTSPlayerController Function GetMouseWorldLocation **********************
struct Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics
{
	struct RTSPlayerController_eventGetMouseWorldLocation_Parms
	{
		FVector WorldLocation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get world location from mouse cursor */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get world location from mouse cursor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventGetMouseWorldLocation_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSPlayerController_eventGetMouseWorldLocation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSPlayerController_eventGetMouseWorldLocation_Parms), &Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "GetMouseWorldLocation", Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::RTSPlayerController_eventGetMouseWorldLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::RTSPlayerController_eventGetMouseWorldLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execGetMouseWorldLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GetMouseWorldLocation(Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function GetMouseWorldLocation ************************

// ********** Begin Class ARTSPlayerController Function GetPlayerTeamID ****************************
struct Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics
{
	struct RTSPlayerController_eventGetPlayerTeamID_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Player Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get the player's team ID */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get the player's team ID" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventGetPlayerTeamID_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "GetPlayerTeamID", Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::RTSPlayerController_eventGetPlayerTeamID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::RTSPlayerController_eventGetPlayerTeamID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execGetPlayerTeamID)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPlayerTeamID();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function GetPlayerTeamID ******************************

// ********** Begin Class ARTSPlayerController Function GetRTSPawn *********************************
struct Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics
{
	struct RTSPlayerController_eventGetRTSPawn_Parms
	{
		ARTSPawn* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get the RTS pawn if possessed */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get the RTS pawn if possessed" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventGetRTSPawn_Parms, ReturnValue), Z_Construct_UClass_ARTSPawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "GetRTSPawn", Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::RTSPlayerController_eventGetRTSPawn_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::RTSPlayerController_eventGetRTSPawn_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execGetRTSPawn)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSPawn**)Z_Param__Result=P_THIS->GetRTSPawn();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function GetRTSPawn ***********************************

// ********** Begin Class ARTSPlayerController Function GetSelectedUnits ***************************
struct Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics
{
	struct RTSPlayerController_eventGetSelectedUnits_Parms
	{
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get currently selected units */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get currently selected units" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventGetSelectedUnits_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "GetSelectedUnits", Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::RTSPlayerController_eventGetSelectedUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::RTSPlayerController_eventGetSelectedUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execGetSelectedUnits)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetSelectedUnits();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function GetSelectedUnits *****************************

// ********** Begin Class ARTSPlayerController Function GetSelectionSystem *************************
struct Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics
{
	struct RTSPlayerController_eventGetSelectionSystem_Parms
	{
		URTSSelectionSystem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Player Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get the selection system */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get the selection system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventGetSelectionSystem_Parms, ReturnValue), Z_Construct_UClass_URTSSelectionSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "GetSelectionSystem", Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::RTSPlayerController_eventGetSelectionSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::RTSPlayerController_eventGetSelectionSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execGetSelectionSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSSelectionSystem**)Z_Param__Result=P_THIS->GetSelectionSystem();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function GetSelectionSystem ***************************

// ********** Begin Class ARTSPlayerController Function IssueAttackCommand *************************
struct Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics
{
	struct RTSPlayerController_eventIssueAttackCommand_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Issue an attack command to selected units */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Issue an attack command to selected units" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventIssueAttackCommand_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "IssueAttackCommand", Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::RTSPlayerController_eventIssueAttackCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::RTSPlayerController_eventIssueAttackCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execIssueAttackCommand)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IssueAttackCommand(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function IssueAttackCommand ***************************

// ********** Begin Class ARTSPlayerController Function IssueAttackMoveCommand *********************
struct Z_Construct_UFunction_ARTSPlayerController_IssueAttackMoveCommand_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Issue an attack-move command to selected units */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Issue an attack-move command to selected units" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_IssueAttackMoveCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "IssueAttackMoveCommand", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IssueAttackMoveCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_IssueAttackMoveCommand_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_IssueAttackMoveCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_IssueAttackMoveCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execIssueAttackMoveCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IssueAttackMoveCommand();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function IssueAttackMoveCommand ***********************

// ********** Begin Class ARTSPlayerController Function IssueMoveCommand ***************************
struct Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommand_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Issue a move command to selected units at mouse position */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Issue a move command to selected units at mouse position" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "IssueMoveCommand", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommand_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execIssueMoveCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IssueMoveCommand();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function IssueMoveCommand *****************************

// ********** Begin Class ARTSPlayerController Function IssueMoveCommandToLocation *****************
struct Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics
{
	struct RTSPlayerController_eventIssueMoveCommandToLocation_Parms
	{
		FVector TargetLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Issue a move command to selected units at specific location */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Issue a move command to selected units at specific location" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventIssueMoveCommandToLocation_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::NewProp_TargetLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "IssueMoveCommandToLocation", Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::RTSPlayerController_eventIssueMoveCommandToLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::RTSPlayerController_eventIssueMoveCommandToLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execIssueMoveCommandToLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TargetLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IssueMoveCommandToLocation(Z_Param_Out_TargetLocation);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function IssueMoveCommandToLocation *******************

// ********** Begin Class ARTSPlayerController Function IssueStopCommand ***************************
struct Z_Construct_UFunction_ARTSPlayerController_IssueStopCommand_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Issue a stop command to selected units */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Issue a stop command to selected units" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_IssueStopCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "IssueStopCommand", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IssueStopCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_IssueStopCommand_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_IssueStopCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_IssueStopCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execIssueStopCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IssueStopCommand();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function IssueStopCommand *****************************

// ********** Begin Class ARTSPlayerController Function IsValidMoveLocation ************************
struct Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics
{
	struct RTSPlayerController_eventIsValidMoveLocation_Parms
	{
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Check if location is valid for movement */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if location is valid for movement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventIsValidMoveLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSPlayerController_eventIsValidMoveLocation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSPlayerController_eventIsValidMoveLocation_Parms), &Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "IsValidMoveLocation", Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::RTSPlayerController_eventIsValidMoveLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::RTSPlayerController_eventIsValidMoveLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execIsValidMoveLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidMoveLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function IsValidMoveLocation **************************

// ********** Begin Class ARTSPlayerController Function OnAttackMoveCommandIssued ******************
struct RTSPlayerController_eventOnAttackMoveCommandIssued_Parms
{
	FVector TargetLocation;
	TArray<AActor*> Units;
};
static FName NAME_ARTSPlayerController_OnAttackMoveCommandIssued = FName(TEXT("OnAttackMoveCommandIssued"));
void ARTSPlayerController::OnAttackMoveCommandIssued(FVector const& TargetLocation, TArray<AActor*> const& Units)
{
	RTSPlayerController_eventOnAttackMoveCommandIssued_Parms Parms;
	Parms.TargetLocation=TargetLocation;
	Parms.Units=Units;
	UFunction* Func = FindFunctionChecked(NAME_ARTSPlayerController_OnAttackMoveCommandIssued);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blueprint event called when attack-move command is issued */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint event called when attack-move command is issued" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Units_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Units_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Units;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnAttackMoveCommandIssued_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::NewProp_Units_Inner = { "Units", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::NewProp_Units = { "Units", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnAttackMoveCommandIssued_Parms, Units), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Units_MetaData), NewProp_Units_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::NewProp_Units_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::NewProp_Units,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnAttackMoveCommandIssued", Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::PropPointers), sizeof(RTSPlayerController_eventOnAttackMoveCommandIssued_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C80800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSPlayerController_eventOnAttackMoveCommandIssued_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSPlayerController Function OnAttackMoveCommandIssued ********************

// ********** Begin Class ARTSPlayerController Function OnCameraMoveForward ************************
struct Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics
{
	struct RTSPlayerController_eventOnCameraMoveForward_Parms
	{
		FInputActionValue Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input action for camera movement forward/backward */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input action for camera movement forward/backward" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnCameraMoveForward_Parms, Value), Z_Construct_UScriptStruct_FInputActionValue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) }; // 203218767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnCameraMoveForward", Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::RTSPlayerController_eventOnCameraMoveForward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::RTSPlayerController_eventOnCameraMoveForward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execOnCameraMoveForward)
{
	P_GET_STRUCT_REF(FInputActionValue,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnCameraMoveForward(Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function OnCameraMoveForward **************************

// ********** Begin Class ARTSPlayerController Function OnCameraMoveRequested **********************
struct RTSPlayerController_eventOnCameraMoveRequested_Parms
{
	float AxisValue;
};
static FName NAME_ARTSPlayerController_OnCameraMoveRequested = FName(TEXT("OnCameraMoveRequested"));
void ARTSPlayerController::OnCameraMoveRequested(float AxisValue)
{
	RTSPlayerController_eventOnCameraMoveRequested_Parms Parms;
	Parms.AxisValue=AxisValue;
	UFunction* Func = FindFunctionChecked(NAME_ARTSPlayerController_OnCameraMoveRequested);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blueprint event called when camera should move */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint event called when camera should move" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AxisValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics::NewProp_AxisValue = { "AxisValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnCameraMoveRequested_Parms, AxisValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics::NewProp_AxisValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnCameraMoveRequested", Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics::PropPointers), sizeof(RTSPlayerController_eventOnCameraMoveRequested_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSPlayerController_eventOnCameraMoveRequested_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSPlayerController Function OnCameraMoveRequested ************************

// ********** Begin Class ARTSPlayerController Function OnCameraMoveRight **************************
struct Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics
{
	struct RTSPlayerController_eventOnCameraMoveRight_Parms
	{
		FInputActionValue Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input action for camera movement left/right */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input action for camera movement left/right" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnCameraMoveRight_Parms, Value), Z_Construct_UScriptStruct_FInputActionValue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) }; // 203218767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnCameraMoveRight", Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::RTSPlayerController_eventOnCameraMoveRight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::RTSPlayerController_eventOnCameraMoveRight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execOnCameraMoveRight)
{
	P_GET_STRUCT_REF(FInputActionValue,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnCameraMoveRight(Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function OnCameraMoveRight ****************************

// ********** Begin Class ARTSPlayerController Function OnCameraRotate *****************************
struct Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics
{
	struct RTSPlayerController_eventOnCameraRotate_Parms
	{
		FInputActionValue Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input action for camera rotation */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input action for camera rotation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnCameraRotate_Parms, Value), Z_Construct_UScriptStruct_FInputActionValue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) }; // 203218767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnCameraRotate", Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::RTSPlayerController_eventOnCameraRotate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::RTSPlayerController_eventOnCameraRotate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execOnCameraRotate)
{
	P_GET_STRUCT_REF(FInputActionValue,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnCameraRotate(Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function OnCameraRotate *******************************

// ********** Begin Class ARTSPlayerController Function OnCameraRotateRequested ********************
struct RTSPlayerController_eventOnCameraRotateRequested_Parms
{
	float AxisValue;
};
static FName NAME_ARTSPlayerController_OnCameraRotateRequested = FName(TEXT("OnCameraRotateRequested"));
void ARTSPlayerController::OnCameraRotateRequested(float AxisValue)
{
	RTSPlayerController_eventOnCameraRotateRequested_Parms Parms;
	Parms.AxisValue=AxisValue;
	UFunction* Func = FindFunctionChecked(NAME_ARTSPlayerController_OnCameraRotateRequested);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blueprint event called when camera should rotate */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint event called when camera should rotate" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AxisValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics::NewProp_AxisValue = { "AxisValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnCameraRotateRequested_Parms, AxisValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics::NewProp_AxisValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnCameraRotateRequested", Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics::PropPointers), sizeof(RTSPlayerController_eventOnCameraRotateRequested_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSPlayerController_eventOnCameraRotateRequested_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSPlayerController Function OnCameraRotateRequested **********************

// ********** Begin Class ARTSPlayerController Function OnCameraZoom *******************************
struct Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics
{
	struct RTSPlayerController_eventOnCameraZoom_Parms
	{
		FInputActionValue Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input action for camera zoom */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input action for camera zoom" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnCameraZoom_Parms, Value), Z_Construct_UScriptStruct_FInputActionValue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) }; // 203218767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnCameraZoom", Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::RTSPlayerController_eventOnCameraZoom_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::RTSPlayerController_eventOnCameraZoom_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execOnCameraZoom)
{
	P_GET_STRUCT_REF(FInputActionValue,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnCameraZoom(Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function OnCameraZoom *********************************

// ********** Begin Class ARTSPlayerController Function OnCameraZoomRequested **********************
struct RTSPlayerController_eventOnCameraZoomRequested_Parms
{
	float AxisValue;
};
static FName NAME_ARTSPlayerController_OnCameraZoomRequested = FName(TEXT("OnCameraZoomRequested"));
void ARTSPlayerController::OnCameraZoomRequested(float AxisValue)
{
	RTSPlayerController_eventOnCameraZoomRequested_Parms Parms;
	Parms.AxisValue=AxisValue;
	UFunction* Func = FindFunctionChecked(NAME_ARTSPlayerController_OnCameraZoomRequested);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blueprint event called when camera should zoom */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint event called when camera should zoom" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AxisValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics::NewProp_AxisValue = { "AxisValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnCameraZoomRequested_Parms, AxisValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics::NewProp_AxisValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnCameraZoomRequested", Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics::PropPointers), sizeof(RTSPlayerController_eventOnCameraZoomRequested_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSPlayerController_eventOnCameraZoomRequested_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSPlayerController Function OnCameraZoomRequested ************************

// ********** Begin Class ARTSPlayerController Function OnMoveCommandIssued ************************
struct RTSPlayerController_eventOnMoveCommandIssued_Parms
{
	FVector TargetLocation;
	TArray<AActor*> Units;
};
static FName NAME_ARTSPlayerController_OnMoveCommandIssued = FName(TEXT("OnMoveCommandIssued"));
void ARTSPlayerController::OnMoveCommandIssued(FVector const& TargetLocation, TArray<AActor*> const& Units)
{
	RTSPlayerController_eventOnMoveCommandIssued_Parms Parms;
	Parms.TargetLocation=TargetLocation;
	Parms.Units=Units;
	UFunction* Func = FindFunctionChecked(NAME_ARTSPlayerController_OnMoveCommandIssued);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blueprint event called when move command is issued */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint event called when move command is issued" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Units_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Units_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Units;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnMoveCommandIssued_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::NewProp_Units_Inner = { "Units", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::NewProp_Units = { "Units", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnMoveCommandIssued_Parms, Units), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Units_MetaData), NewProp_Units_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::NewProp_Units_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::NewProp_Units,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnMoveCommandIssued", Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::PropPointers), sizeof(RTSPlayerController_eventOnMoveCommandIssued_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C80800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSPlayerController_eventOnMoveCommandIssued_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSPlayerController Function OnMoveCommandIssued **************************

// ********** Begin Class ARTSPlayerController Function OnPrimaryAction ****************************
struct Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics
{
	struct RTSPlayerController_eventOnPrimaryAction_Parms
	{
		FInputActionValue Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input action for primary selection (left mouse button) */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input action for primary selection (left mouse button)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnPrimaryAction_Parms, Value), Z_Construct_UScriptStruct_FInputActionValue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) }; // 203218767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnPrimaryAction", Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::RTSPlayerController_eventOnPrimaryAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::RTSPlayerController_eventOnPrimaryAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execOnPrimaryAction)
{
	P_GET_STRUCT_REF(FInputActionValue,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPrimaryAction(Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function OnPrimaryAction ******************************

// ********** Begin Class ARTSPlayerController Function OnPrimaryActionPerformed *******************
static FName NAME_ARTSPlayerController_OnPrimaryActionPerformed = FName(TEXT("OnPrimaryActionPerformed"));
void ARTSPlayerController::OnPrimaryActionPerformed()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSPlayerController_OnPrimaryActionPerformed);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPerformed_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blueprint event called when primary action is performed */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint event called when primary action is performed" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPerformed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnPrimaryActionPerformed", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPerformed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPerformed_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPerformed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPerformed_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSPlayerController Function OnPrimaryActionPerformed *********************

// ********** Begin Class ARTSPlayerController Function OnPrimaryActionPressed *********************
struct Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics
{
	struct RTSPlayerController_eventOnPrimaryActionPressed_Parms
	{
		FInputActionValue Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input action for primary action pressed (start selection box) */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input action for primary action pressed (start selection box)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnPrimaryActionPressed_Parms, Value), Z_Construct_UScriptStruct_FInputActionValue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) }; // 203218767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnPrimaryActionPressed", Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::RTSPlayerController_eventOnPrimaryActionPressed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::RTSPlayerController_eventOnPrimaryActionPressed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execOnPrimaryActionPressed)
{
	P_GET_STRUCT_REF(FInputActionValue,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPrimaryActionPressed(Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function OnPrimaryActionPressed ***********************

// ********** Begin Class ARTSPlayerController Function OnPrimaryActionReleased ********************
struct Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics
{
	struct RTSPlayerController_eventOnPrimaryActionReleased_Parms
	{
		FInputActionValue Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input action for primary action released (end selection box) */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input action for primary action released (end selection box)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnPrimaryActionReleased_Parms, Value), Z_Construct_UScriptStruct_FInputActionValue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) }; // 203218767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnPrimaryActionReleased", Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::RTSPlayerController_eventOnPrimaryActionReleased_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::RTSPlayerController_eventOnPrimaryActionReleased_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execOnPrimaryActionReleased)
{
	P_GET_STRUCT_REF(FInputActionValue,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPrimaryActionReleased(Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function OnPrimaryActionReleased **********************

// ********** Begin Class ARTSPlayerController Function OnSecondaryAction **************************
struct Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics
{
	struct RTSPlayerController_eventOnSecondaryAction_Parms
	{
		FInputActionValue Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input action for secondary action (right mouse button) */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input action for secondary action (right mouse button)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnSecondaryAction_Parms, Value), Z_Construct_UScriptStruct_FInputActionValue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) }; // 203218767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnSecondaryAction", Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::RTSPlayerController_eventOnSecondaryAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::RTSPlayerController_eventOnSecondaryAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execOnSecondaryAction)
{
	P_GET_STRUCT_REF(FInputActionValue,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSecondaryAction(Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function OnSecondaryAction ****************************

// ********** Begin Class ARTSPlayerController Function OnSecondaryActionPerformed *****************
static FName NAME_ARTSPlayerController_OnSecondaryActionPerformed = FName(TEXT("OnSecondaryActionPerformed"));
void ARTSPlayerController::OnSecondaryActionPerformed()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSPlayerController_OnSecondaryActionPerformed);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSPlayerController_OnSecondaryActionPerformed_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blueprint event called when secondary action is performed */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint event called when secondary action is performed" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnSecondaryActionPerformed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnSecondaryActionPerformed", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnSecondaryActionPerformed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnSecondaryActionPerformed_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnSecondaryActionPerformed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnSecondaryActionPerformed_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSPlayerController Function OnSecondaryActionPerformed *******************

// ********** Begin Class ARTSPlayerController Function OnSelectionChanged *************************
struct RTSPlayerController_eventOnSelectionChanged_Parms
{
	TArray<AActor*> NewSelection;
};
static FName NAME_ARTSPlayerController_OnSelectionChanged = FName(TEXT("OnSelectionChanged"));
void ARTSPlayerController::OnSelectionChanged(TArray<AActor*> const& NewSelection)
{
	RTSPlayerController_eventOnSelectionChanged_Parms Parms;
	Parms.NewSelection=NewSelection;
	UFunction* Func = FindFunctionChecked(NAME_ARTSPlayerController_OnSelectionChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blueprint event called when selection changes */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint event called when selection changes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSelection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewSelection_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NewSelection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::NewProp_NewSelection_Inner = { "NewSelection", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::NewProp_NewSelection = { "NewSelection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventOnSelectionChanged_Parms, NewSelection), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSelection_MetaData), NewProp_NewSelection_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::NewProp_NewSelection_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::NewProp_NewSelection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "OnSelectionChanged", Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::PropPointers), sizeof(RTSPlayerController_eventOnSelectionChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08480800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSPlayerController_eventOnSelectionChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSPlayerController Function OnSelectionChanged ***************************

// ********** Begin Class ARTSPlayerController Function PerformBoxSelection ************************
struct Z_Construct_UFunction_ARTSPlayerController_PerformBoxSelection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Perform box selection with current selection box bounds */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perform box selection with current selection box bounds" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_PerformBoxSelection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "PerformBoxSelection", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_PerformBoxSelection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_PerformBoxSelection_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_PerformBoxSelection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_PerformBoxSelection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execPerformBoxSelection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PerformBoxSelection();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function PerformBoxSelection **************************

// ********** Begin Class ARTSPlayerController Function PerformSingleSelection *********************
struct Z_Construct_UFunction_ARTSPlayerController_PerformSingleSelection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Perform single unit selection at mouse position */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perform single unit selection at mouse position" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_PerformSingleSelection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "PerformSingleSelection", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_PerformSingleSelection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_PerformSingleSelection_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_PerformSingleSelection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_PerformSingleSelection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execPerformSingleSelection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PerformSingleSelection();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function PerformSingleSelection ***********************

// ********** Begin Class ARTSPlayerController Function RemoveUnitFromSelection ********************
struct Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics
{
	struct RTSPlayerController_eventRemoveUnitFromSelection_Parms
	{
		AActor* Unit;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remove unit from selection */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove unit from selection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventRemoveUnitFromSelection_Parms, Unit), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::NewProp_Unit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "RemoveUnitFromSelection", Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::RTSPlayerController_eventRemoveUnitFromSelection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::RTSPlayerController_eventRemoveUnitFromSelection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execRemoveUnitFromSelection)
{
	P_GET_OBJECT(AActor,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveUnitFromSelection(Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function RemoveUnitFromSelection **********************

// ********** Begin Class ARTSPlayerController Function ScreenToWorldPosition **********************
struct Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics
{
	struct RTSPlayerController_eventScreenToWorldPosition_Parms
	{
		FVector2D ScreenPosition;
		FVector WorldPosition;
		FVector WorldDirection;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Convert screen position to world position */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Convert screen position to world position" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScreenPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScreenPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldDirection;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::NewProp_ScreenPosition = { "ScreenPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventScreenToWorldPosition_Parms, ScreenPosition), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScreenPosition_MetaData), NewProp_ScreenPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::NewProp_WorldPosition = { "WorldPosition", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventScreenToWorldPosition_Parms, WorldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::NewProp_WorldDirection = { "WorldDirection", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventScreenToWorldPosition_Parms, WorldDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSPlayerController_eventScreenToWorldPosition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSPlayerController_eventScreenToWorldPosition_Parms), &Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::NewProp_ScreenPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::NewProp_WorldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::NewProp_WorldDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "ScreenToWorldPosition", Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::RTSPlayerController_eventScreenToWorldPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::RTSPlayerController_eventScreenToWorldPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execScreenToWorldPosition)
{
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_ScreenPosition);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldPosition);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ScreenToWorldPosition(Z_Param_Out_ScreenPosition,Z_Param_Out_WorldPosition,Z_Param_Out_WorldDirection);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function ScreenToWorldPosition ************************

// ********** Begin Class ARTSPlayerController Function SetCameraMovementSpeed *********************
struct Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics
{
	struct RTSPlayerController_eventSetCameraMovementSpeed_Parms
	{
		float NewSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set camera movement speed on the RTS pawn */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set camera movement speed on the RTS pawn" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::NewProp_NewSpeed = { "NewSpeed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventSetCameraMovementSpeed_Parms, NewSpeed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::NewProp_NewSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "SetCameraMovementSpeed", Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::RTSPlayerController_eventSetCameraMovementSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::RTSPlayerController_eventSetCameraMovementSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execSetCameraMovementSpeed)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCameraMovementSpeed(Z_Param_NewSpeed);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function SetCameraMovementSpeed ***********************

// ********** Begin Class ARTSPlayerController Function SetCameraRotationSpeed *********************
struct Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics
{
	struct RTSPlayerController_eventSetCameraRotationSpeed_Parms
	{
		float NewSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set camera rotation speed on the RTS pawn */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set camera rotation speed on the RTS pawn" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::NewProp_NewSpeed = { "NewSpeed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventSetCameraRotationSpeed_Parms, NewSpeed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::NewProp_NewSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "SetCameraRotationSpeed", Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::RTSPlayerController_eventSetCameraRotationSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::RTSPlayerController_eventSetCameraRotationSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execSetCameraRotationSpeed)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCameraRotationSpeed(Z_Param_NewSpeed);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function SetCameraRotationSpeed ***********************

// ********** Begin Class ARTSPlayerController Function SetCameraZoomSpeed *************************
struct Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics
{
	struct RTSPlayerController_eventSetCameraZoomSpeed_Parms
	{
		float NewSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set camera zoom speed on the RTS pawn */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set camera zoom speed on the RTS pawn" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::NewProp_NewSpeed = { "NewSpeed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventSetCameraZoomSpeed_Parms, NewSpeed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::NewProp_NewSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "SetCameraZoomSpeed", Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::RTSPlayerController_eventSetCameraZoomSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::RTSPlayerController_eventSetCameraZoomSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execSetCameraZoomSpeed)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCameraZoomSpeed(Z_Param_NewSpeed);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function SetCameraZoomSpeed ***************************

// ********** Begin Class ARTSPlayerController Function SetPlayerTeamID ****************************
struct Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics
{
	struct RTSPlayerController_eventSetPlayerTeamID_Parms
	{
		int32 NewTeamID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Player Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set the player's team ID */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set the player's team ID" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewTeamID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::NewProp_NewTeamID = { "NewTeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventSetPlayerTeamID_Parms, NewTeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::NewProp_NewTeamID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "SetPlayerTeamID", Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::RTSPlayerController_eventSetPlayerTeamID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::RTSPlayerController_eventSetPlayerTeamID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execSetPlayerTeamID)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewTeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPlayerTeamID(Z_Param_NewTeamID);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function SetPlayerTeamID ******************************

// ********** Begin Class ARTSPlayerController Function SetUnitSelection ***************************
struct Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics
{
	struct RTSPlayerController_eventSetUnitSelection_Parms
	{
		TArray<AActor*> Units;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set selection (replaces current selection) */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set selection (replaces current selection)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Units_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Units_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Units;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::NewProp_Units_Inner = { "Units", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::NewProp_Units = { "Units", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSPlayerController_eventSetUnitSelection_Parms, Units), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Units_MetaData), NewProp_Units_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::NewProp_Units_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::NewProp_Units,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "SetUnitSelection", Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::RTSPlayerController_eventSetUnitSelection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::RTSPlayerController_eventSetUnitSelection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execSetUnitSelection)
{
	P_GET_TARRAY_REF(AActor*,Z_Param_Out_Units);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetUnitSelection(Z_Param_Out_Units);
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function SetUnitSelection *****************************

// ********** Begin Class ARTSPlayerController Function StartSelectionBox **************************
struct Z_Construct_UFunction_ARTSPlayerController_StartSelectionBox_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Start selection box at current mouse position */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start selection box at current mouse position" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_StartSelectionBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "StartSelectionBox", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_StartSelectionBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_StartSelectionBox_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_StartSelectionBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_StartSelectionBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execStartSelectionBox)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartSelectionBox();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function StartSelectionBox ****************************

// ********** Begin Class ARTSPlayerController Function UpdateSelectionBox *************************
struct Z_Construct_UFunction_ARTSPlayerController_UpdateSelectionBox_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update selection box to current mouse position */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update selection box to current mouse position" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSPlayerController_UpdateSelectionBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSPlayerController, nullptr, "UpdateSelectionBox", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSPlayerController_UpdateSelectionBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSPlayerController_UpdateSelectionBox_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSPlayerController_UpdateSelectionBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSPlayerController_UpdateSelectionBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSPlayerController::execUpdateSelectionBox)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSelectionBox();
	P_NATIVE_END;
}
// ********** End Class ARTSPlayerController Function UpdateSelectionBox ***************************

// ********** Begin Class ARTSPlayerController *****************************************************
void ARTSPlayerController::StaticRegisterNativesARTSPlayerController()
{
	UClass* Class = ARTSPlayerController::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddUnitToSelection", &ARTSPlayerController::execAddUnitToSelection },
		{ "CalculateFormationPositions", &ARTSPlayerController::execCalculateFormationPositions },
		{ "ClearUnitSelection", &ARTSPlayerController::execClearUnitSelection },
		{ "EndSelectionBox", &ARTSPlayerController::execEndSelectionBox },
		{ "FindUnitsInSelectionBox", &ARTSPlayerController::execFindUnitsInSelectionBox },
		{ "FindUnitsInSelectionBoxWorldSpace", &ARTSPlayerController::execFindUnitsInSelectionBoxWorldSpace },
		{ "GetCurrentMousePosition", &ARTSPlayerController::execGetCurrentMousePosition },
		{ "GetMouseWorldLocation", &ARTSPlayerController::execGetMouseWorldLocation },
		{ "GetPlayerTeamID", &ARTSPlayerController::execGetPlayerTeamID },
		{ "GetRTSPawn", &ARTSPlayerController::execGetRTSPawn },
		{ "GetSelectedUnits", &ARTSPlayerController::execGetSelectedUnits },
		{ "GetSelectionSystem", &ARTSPlayerController::execGetSelectionSystem },
		{ "IssueAttackCommand", &ARTSPlayerController::execIssueAttackCommand },
		{ "IssueAttackMoveCommand", &ARTSPlayerController::execIssueAttackMoveCommand },
		{ "IssueMoveCommand", &ARTSPlayerController::execIssueMoveCommand },
		{ "IssueMoveCommandToLocation", &ARTSPlayerController::execIssueMoveCommandToLocation },
		{ "IssueStopCommand", &ARTSPlayerController::execIssueStopCommand },
		{ "IsValidMoveLocation", &ARTSPlayerController::execIsValidMoveLocation },
		{ "OnCameraMoveForward", &ARTSPlayerController::execOnCameraMoveForward },
		{ "OnCameraMoveRight", &ARTSPlayerController::execOnCameraMoveRight },
		{ "OnCameraRotate", &ARTSPlayerController::execOnCameraRotate },
		{ "OnCameraZoom", &ARTSPlayerController::execOnCameraZoom },
		{ "OnPrimaryAction", &ARTSPlayerController::execOnPrimaryAction },
		{ "OnPrimaryActionPressed", &ARTSPlayerController::execOnPrimaryActionPressed },
		{ "OnPrimaryActionReleased", &ARTSPlayerController::execOnPrimaryActionReleased },
		{ "OnSecondaryAction", &ARTSPlayerController::execOnSecondaryAction },
		{ "PerformBoxSelection", &ARTSPlayerController::execPerformBoxSelection },
		{ "PerformSingleSelection", &ARTSPlayerController::execPerformSingleSelection },
		{ "RemoveUnitFromSelection", &ARTSPlayerController::execRemoveUnitFromSelection },
		{ "ScreenToWorldPosition", &ARTSPlayerController::execScreenToWorldPosition },
		{ "SetCameraMovementSpeed", &ARTSPlayerController::execSetCameraMovementSpeed },
		{ "SetCameraRotationSpeed", &ARTSPlayerController::execSetCameraRotationSpeed },
		{ "SetCameraZoomSpeed", &ARTSPlayerController::execSetCameraZoomSpeed },
		{ "SetPlayerTeamID", &ARTSPlayerController::execSetPlayerTeamID },
		{ "SetUnitSelection", &ARTSPlayerController::execSetUnitSelection },
		{ "StartSelectionBox", &ARTSPlayerController::execStartSelectionBox },
		{ "UpdateSelectionBox", &ARTSPlayerController::execUpdateSelectionBox },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ARTSPlayerController;
UClass* ARTSPlayerController::GetPrivateStaticClass()
{
	using TClass = ARTSPlayerController;
	if (!Z_Registration_Info_UClass_ARTSPlayerController.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSPlayerController"),
			Z_Registration_Info_UClass_ARTSPlayerController.InnerSingleton,
			StaticRegisterNativesARTSPlayerController,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ARTSPlayerController.InnerSingleton;
}
UClass* Z_Construct_UClass_ARTSPlayerController_NoRegister()
{
	return ARTSPlayerController::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ARTSPlayerController_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Player Controller for ArmorWars RTS game\n * Handles RTS-specific input and camera controls\n */" },
#endif
		{ "HideCategories", "Collision Rendering Transformation" },
		{ "IncludePath", "RTSPlayerController.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player Controller for ArmorWars RTS game\nHandles RTS-specific input and camera controls" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerTeamID_MetaData[] = {
		{ "Category", "Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** The player's team ID */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "The player's team ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraMovementSpeed_MetaData[] = {
		{ "Category", "Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camera movement speed */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camera movement speed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraZoomSpeed_MetaData[] = {
		{ "Category", "Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camera zoom speed */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camera zoom speed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraRotationSpeed_MetaData[] = {
		{ "Category", "Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camera rotation speed */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camera rotation speed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether debug logging is enabled */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether debug logging is enabled" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDrawingSelectionBox_MetaData[] = {
		{ "Category", "Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether selection box is currently being drawn */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether selection box is currently being drawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionBoxStartPosition_MetaData[] = {
		{ "Category", "Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Start position of selection box in screen space */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start position of selection box in screen space" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMousePosition_MetaData[] = {
		{ "Category", "Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current mouse position for selection box */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current mouse position for selection box" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionBoxMinDistance_MetaData[] = {
		{ "Category", "Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Minimum distance mouse must move to start selection box */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum distance mouse must move to start selection box" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultMappingContext_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input Mapping Context */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input Mapping Context" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimaryAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input Actions */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input Actions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecondaryAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraMoveForwardAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraMoveRightAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraZoomAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraRotateAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedUnits_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Currently selected units */" },
#endif
		{ "ModuleRelativePath", "Public/RTSPlayerController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Currently selected units" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerTeamID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CameraMovementSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CameraZoomSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CameraRotationSpeed;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static void NewProp_bIsDrawingSelectionBox_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDrawingSelectionBox;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SelectionBoxStartPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentMousePosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SelectionBoxMinDistance;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DefaultMappingContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PrimaryAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SecondaryAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CameraMoveForwardAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CameraMoveRightAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CameraZoomAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CameraRotateAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SelectedUnits_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SelectedUnits;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARTSPlayerController_AddUnitToSelection, "AddUnitToSelection" }, // 661440552
		{ &Z_Construct_UFunction_ARTSPlayerController_CalculateFormationPositions, "CalculateFormationPositions" }, // 1242820271
		{ &Z_Construct_UFunction_ARTSPlayerController_ClearUnitSelection, "ClearUnitSelection" }, // 2828964017
		{ &Z_Construct_UFunction_ARTSPlayerController_EndSelectionBox, "EndSelectionBox" }, // 4053180496
		{ &Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBox, "FindUnitsInSelectionBox" }, // 1425662097
		{ &Z_Construct_UFunction_ARTSPlayerController_FindUnitsInSelectionBoxWorldSpace, "FindUnitsInSelectionBoxWorldSpace" }, // 413924126
		{ &Z_Construct_UFunction_ARTSPlayerController_GetCurrentMousePosition, "GetCurrentMousePosition" }, // 665872267
		{ &Z_Construct_UFunction_ARTSPlayerController_GetMouseWorldLocation, "GetMouseWorldLocation" }, // 385069438
		{ &Z_Construct_UFunction_ARTSPlayerController_GetPlayerTeamID, "GetPlayerTeamID" }, // 158669378
		{ &Z_Construct_UFunction_ARTSPlayerController_GetRTSPawn, "GetRTSPawn" }, // 1053969742
		{ &Z_Construct_UFunction_ARTSPlayerController_GetSelectedUnits, "GetSelectedUnits" }, // 797780282
		{ &Z_Construct_UFunction_ARTSPlayerController_GetSelectionSystem, "GetSelectionSystem" }, // 2610299235
		{ &Z_Construct_UFunction_ARTSPlayerController_IssueAttackCommand, "IssueAttackCommand" }, // 2745425892
		{ &Z_Construct_UFunction_ARTSPlayerController_IssueAttackMoveCommand, "IssueAttackMoveCommand" }, // 1828426186
		{ &Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommand, "IssueMoveCommand" }, // 834127499
		{ &Z_Construct_UFunction_ARTSPlayerController_IssueMoveCommandToLocation, "IssueMoveCommandToLocation" }, // 2777600934
		{ &Z_Construct_UFunction_ARTSPlayerController_IssueStopCommand, "IssueStopCommand" }, // 2699932203
		{ &Z_Construct_UFunction_ARTSPlayerController_IsValidMoveLocation, "IsValidMoveLocation" }, // 2790119089
		{ &Z_Construct_UFunction_ARTSPlayerController_OnAttackMoveCommandIssued, "OnAttackMoveCommandIssued" }, // 2855751697
		{ &Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveForward, "OnCameraMoveForward" }, // 1852597618
		{ &Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRequested, "OnCameraMoveRequested" }, // 3731079376
		{ &Z_Construct_UFunction_ARTSPlayerController_OnCameraMoveRight, "OnCameraMoveRight" }, // 1830194812
		{ &Z_Construct_UFunction_ARTSPlayerController_OnCameraRotate, "OnCameraRotate" }, // 1630833987
		{ &Z_Construct_UFunction_ARTSPlayerController_OnCameraRotateRequested, "OnCameraRotateRequested" }, // 2378123709
		{ &Z_Construct_UFunction_ARTSPlayerController_OnCameraZoom, "OnCameraZoom" }, // 2350072797
		{ &Z_Construct_UFunction_ARTSPlayerController_OnCameraZoomRequested, "OnCameraZoomRequested" }, // 2428358522
		{ &Z_Construct_UFunction_ARTSPlayerController_OnMoveCommandIssued, "OnMoveCommandIssued" }, // 1875580956
		{ &Z_Construct_UFunction_ARTSPlayerController_OnPrimaryAction, "OnPrimaryAction" }, // 1919736008
		{ &Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPerformed, "OnPrimaryActionPerformed" }, // 1203148829
		{ &Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionPressed, "OnPrimaryActionPressed" }, // 363546909
		{ &Z_Construct_UFunction_ARTSPlayerController_OnPrimaryActionReleased, "OnPrimaryActionReleased" }, // 1782391669
		{ &Z_Construct_UFunction_ARTSPlayerController_OnSecondaryAction, "OnSecondaryAction" }, // 1331227062
		{ &Z_Construct_UFunction_ARTSPlayerController_OnSecondaryActionPerformed, "OnSecondaryActionPerformed" }, // 3222858835
		{ &Z_Construct_UFunction_ARTSPlayerController_OnSelectionChanged, "OnSelectionChanged" }, // 2502131269
		{ &Z_Construct_UFunction_ARTSPlayerController_PerformBoxSelection, "PerformBoxSelection" }, // 2807383948
		{ &Z_Construct_UFunction_ARTSPlayerController_PerformSingleSelection, "PerformSingleSelection" }, // 2726199924
		{ &Z_Construct_UFunction_ARTSPlayerController_RemoveUnitFromSelection, "RemoveUnitFromSelection" }, // 453684928
		{ &Z_Construct_UFunction_ARTSPlayerController_ScreenToWorldPosition, "ScreenToWorldPosition" }, // 1865138372
		{ &Z_Construct_UFunction_ARTSPlayerController_SetCameraMovementSpeed, "SetCameraMovementSpeed" }, // 3788750249
		{ &Z_Construct_UFunction_ARTSPlayerController_SetCameraRotationSpeed, "SetCameraRotationSpeed" }, // 2281689548
		{ &Z_Construct_UFunction_ARTSPlayerController_SetCameraZoomSpeed, "SetCameraZoomSpeed" }, // 2611352450
		{ &Z_Construct_UFunction_ARTSPlayerController_SetPlayerTeamID, "SetPlayerTeamID" }, // 2848804867
		{ &Z_Construct_UFunction_ARTSPlayerController_SetUnitSelection, "SetUnitSelection" }, // 1798477775
		{ &Z_Construct_UFunction_ARTSPlayerController_StartSelectionBox, "StartSelectionBox" }, // 163217224
		{ &Z_Construct_UFunction_ARTSPlayerController_UpdateSelectionBox, "UpdateSelectionBox" }, // 1970829479
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARTSPlayerController>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_PlayerTeamID = { "PlayerTeamID", nullptr, (EPropertyFlags)0x0020080000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, PlayerTeamID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerTeamID_MetaData), NewProp_PlayerTeamID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraMovementSpeed = { "CameraMovementSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, CameraMovementSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraMovementSpeed_MetaData), NewProp_CameraMovementSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraZoomSpeed = { "CameraZoomSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, CameraZoomSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraZoomSpeed_MetaData), NewProp_CameraZoomSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraRotationSpeed = { "CameraRotationSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, CameraRotationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraRotationSpeed_MetaData), NewProp_CameraRotationSpeed_MetaData) };
void Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((ARTSPlayerController*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSPlayerController), &Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
void Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_bIsDrawingSelectionBox_SetBit(void* Obj)
{
	((ARTSPlayerController*)Obj)->bIsDrawingSelectionBox = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_bIsDrawingSelectionBox = { "bIsDrawingSelectionBox", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSPlayerController), &Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_bIsDrawingSelectionBox_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDrawingSelectionBox_MetaData), NewProp_bIsDrawingSelectionBox_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_SelectionBoxStartPosition = { "SelectionBoxStartPosition", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, SelectionBoxStartPosition), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionBoxStartPosition_MetaData), NewProp_SelectionBoxStartPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CurrentMousePosition = { "CurrentMousePosition", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, CurrentMousePosition), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMousePosition_MetaData), NewProp_CurrentMousePosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_SelectionBoxMinDistance = { "SelectionBoxMinDistance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, SelectionBoxMinDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionBoxMinDistance_MetaData), NewProp_SelectionBoxMinDistance_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_DefaultMappingContext = { "DefaultMappingContext", nullptr, (EPropertyFlags)0x0124080000000015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, DefaultMappingContext), Z_Construct_UClass_UInputMappingContext_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultMappingContext_MetaData), NewProp_DefaultMappingContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_PrimaryAction = { "PrimaryAction", nullptr, (EPropertyFlags)0x0124080000000015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, PrimaryAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimaryAction_MetaData), NewProp_PrimaryAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_SecondaryAction = { "SecondaryAction", nullptr, (EPropertyFlags)0x0124080000000015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, SecondaryAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecondaryAction_MetaData), NewProp_SecondaryAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraMoveForwardAction = { "CameraMoveForwardAction", nullptr, (EPropertyFlags)0x0124080000000015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, CameraMoveForwardAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraMoveForwardAction_MetaData), NewProp_CameraMoveForwardAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraMoveRightAction = { "CameraMoveRightAction", nullptr, (EPropertyFlags)0x0124080000000015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, CameraMoveRightAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraMoveRightAction_MetaData), NewProp_CameraMoveRightAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraZoomAction = { "CameraZoomAction", nullptr, (EPropertyFlags)0x0124080000000015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, CameraZoomAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraZoomAction_MetaData), NewProp_CameraZoomAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraRotateAction = { "CameraRotateAction", nullptr, (EPropertyFlags)0x0124080000000015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, CameraRotateAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraRotateAction_MetaData), NewProp_CameraRotateAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_SelectedUnits_Inner = { "SelectedUnits", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_SelectedUnits = { "SelectedUnits", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSPlayerController, SelectedUnits), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedUnits_MetaData), NewProp_SelectedUnits_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARTSPlayerController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_PlayerTeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraMovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraZoomSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraRotationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_bIsDrawingSelectionBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_SelectionBoxStartPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CurrentMousePosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_SelectionBoxMinDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_DefaultMappingContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_PrimaryAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_SecondaryAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraMoveForwardAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraMoveRightAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraZoomAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_CameraRotateAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_SelectedUnits_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSPlayerController_Statics::NewProp_SelectedUnits,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSPlayerController_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARTSPlayerController_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_APlayerController,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSPlayerController_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARTSPlayerController_Statics::ClassParams = {
	&ARTSPlayerController::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARTSPlayerController_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARTSPlayerController_Statics::PropPointers),
	0,
	0x009003A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSPlayerController_Statics::Class_MetaDataParams), Z_Construct_UClass_ARTSPlayerController_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARTSPlayerController()
{
	if (!Z_Registration_Info_UClass_ARTSPlayerController.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARTSPlayerController.OuterSingleton, Z_Construct_UClass_ARTSPlayerController_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARTSPlayerController.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARTSPlayerController);
ARTSPlayerController::~ARTSPlayerController() {}
// ********** End Class ARTSPlayerController *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARTSPlayerController, ARTSPlayerController::StaticClass, TEXT("ARTSPlayerController"), &Z_Registration_Info_UClass_ARTSPlayerController, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARTSPlayerController), 793618859U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h__Script_ArmorWars_2366204199(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "Components/ActorComponent.h"
#include "RTSCommand.h"
#include "RTSAITestingSystem.generated.h"

class ARTSUnit;
class ARTSBaseActor;
class URTSCommandComponent;
class URTSBehaviorTreeComponent;

// Test scenario types
UENUM(BlueprintType)
enum class ERTSTestScenario : uint8
{
    BasicMovement       UMETA(DisplayName = "Basic Movement"),
    FormationMovement   UMETA(DisplayName = "Formation Movement"),
    CombatEngagement    UMETA(DisplayName = "Combat Engagement"),
    ReturnFire          UMETA(DisplayName = "Return Fire"),
    CollisionAvoidance  UMETA(DisplayName = "Collision Avoidance"),
    CommandPriority     UMETA(DisplayName = "Command Priority"),
    MixedScenario       UMETA(DisplayName = "Mixed Scenario"),
    StressTest          UMETA(DisplayName = "Stress Test")
};

// Test result data
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSTestResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Test Result")
    ERTSTestScenario TestScenario = ERTSTestScenario::BasicMovement;

    UPROPERTY(BlueprintReadOnly, Category = "Test Result")
    bool bTestPassed = false;

    UPROPERTY(BlueprintReadOnly, Category = "Test Result")
    float TestDuration = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Test Result")
    FString TestDescription;

    UPROPERTY(BlueprintReadOnly, Category = "Test Result")
    FString FailureReason;

    UPROPERTY(BlueprintReadOnly, Category = "Test Result")
    TArray<FString> PerformanceMetrics;

    UPROPERTY(BlueprintReadOnly, Category = "Test Result")
    float StartTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Test Result")
    float EndTime = 0.0f;

    // Constructor
    FRTSTestResult()
    {
        TestScenario = ERTSTestScenario::BasicMovement;
        bTestPassed = false;
        TestDuration = 0.0f;
        TestDescription = TEXT("");
        FailureReason = TEXT("");
        StartTime = 0.0f;
        EndTime = 0.0f;
    }
};

// Performance metrics tracking
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSPerformanceMetrics
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float AverageFrameTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float MaxFrameTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 TotalUnitsTracked = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 ActiveCommandsCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 BehaviorTreeUpdatesPerSecond = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float MemoryUsageMB = 0.0f;

    // Constructor
    FRTSPerformanceMetrics()
    {
        AverageFrameTime = 0.0f;
        MaxFrameTime = 0.0f;
        TotalUnitsTracked = 0;
        ActiveCommandsCount = 0;
        BehaviorTreeUpdatesPerSecond = 0;
        MemoryUsageMB = 0.0f;
    }
};

/**
 * Component for debugging individual unit AI behavior
 * Provides detailed logging and visualization for AI debugging
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class ARMORWARS_API URTSAIDebugComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    URTSAIDebugComponent();

protected:
    virtual void BeginPlay() override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Debug settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bEnableDebugLogging = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bEnableVisualDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bLogCommandExecution = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bLogBehaviorTreeStates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bLogMovementData = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    bool bLogCombatData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug Settings")
    float DebugUpdateInterval = 0.5f;

public:
    // Debug functions
    UFUNCTION(BlueprintCallable, Category = "RTS AI Debug")
    void LogCurrentState();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Debug")
    void LogCommandQueue();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Debug")
    void LogBehaviorTreeState();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Debug")
    void LogMovementState();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Debug")
    void LogCombatState();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Debug")
    void DrawDebugInfo();

    // Validation functions
    UFUNCTION(BlueprintCallable, Category = "RTS AI Debug")
    bool ValidateAIComponents();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Debug")
    TArray<FString> GetAIComponentStatus();

    UFUNCTION(BlueprintPure, Category = "RTS AI Debug")
    ARTSUnit* GetOwnerUnit() const;

protected:
    // Internal state
    float LastDebugUpdate = 0.0f;
    TArray<FString> RecentLogMessages;
    int32 MaxLogMessages = 50;

    // Helper functions
    virtual void UpdateDebugInfo(float DeltaTime);
    virtual void AddLogMessage(const FString& Message);
    virtual FString GetFormattedTimestamp() const;
};

/**
 * World subsystem for comprehensive AI testing and debugging
 * Manages test scenarios, performance monitoring, and system validation
 */
UCLASS(BlueprintType)
class ARMORWARS_API URTSAITestingManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    URTSAITestingManager();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    void Tick(float DeltaTime);
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override { return true; }

protected:
    // Test management
    UPROPERTY(BlueprintReadOnly, Category = "Testing")
    TArray<FRTSTestResult> TestResults;

    UPROPERTY(BlueprintReadOnly, Category = "Testing")
    bool bIsRunningTest = false;

    UPROPERTY(BlueprintReadOnly, Category = "Testing")
    ERTSTestScenario CurrentTestScenario = ERTSTestScenario::BasicMovement;

    UPROPERTY(BlueprintReadOnly, Category = "Testing")
    float CurrentTestStartTime = 0.0f;

    // Performance monitoring
    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    FRTSPerformanceMetrics CurrentMetrics;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    TArray<float> FrameTimeHistory;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 MaxFrameHistorySize = 300; // 5 seconds at 60 FPS

    // Test units
    UPROPERTY(BlueprintReadOnly, Category = "Testing")
    TArray<TSoftObjectPtr<ARTSUnit>> TestUnits;

public:
    // Test execution
    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    bool StartTest(ERTSTestScenario TestScenario);

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    void StopCurrentTest(bool bTestPassed = false, const FString& FailureReason = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    void RunAllTests();

    // Test scenarios
    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    bool RunBasicMovementTest();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    bool RunFormationMovementTest();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    bool RunCombatEngagementTest();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    bool RunReturnFireTest();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    bool RunCollisionAvoidanceTest();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    bool RunCommandPriorityTest();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    bool RunStressTest();

    // Test utilities
    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    TArray<ARTSUnit*> SpawnTestUnits(int32 Count, const FVector& SpawnLocation, float SpawnRadius = 500.0f);

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    void CleanupTestUnits();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    bool ValidateTestEnvironment();

    // Performance monitoring
    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    void StartPerformanceMonitoring();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    void StopPerformanceMonitoring();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    void UpdatePerformanceMetrics(float DeltaTime);

    // Results and reporting
    UFUNCTION(BlueprintPure, Category = "RTS AI Testing")
    TArray<FRTSTestResult> GetTestResults() const { return TestResults; }

    UFUNCTION(BlueprintPure, Category = "RTS AI Testing")
    FRTSPerformanceMetrics GetCurrentPerformanceMetrics() const { return CurrentMetrics; }

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    void GenerateTestReport();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    void ExportTestResults(const FString& FilePath);

    // System validation
    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    bool ValidateAISystem();

    UFUNCTION(BlueprintCallable, Category = "RTS AI Testing")
    TArray<FString> GetSystemValidationErrors();

protected:
    // Internal test management
    virtual void ProcessCurrentTest(float DeltaTime);
    virtual bool CheckTestCompletion();
    virtual void RecordTestResult(bool bPassed, const FString& FailureReason = TEXT(""));

    // Test scenario implementations
    virtual bool SetupBasicMovementTest();
    virtual bool SetupFormationMovementTest();
    virtual bool SetupCombatEngagementTest();
    virtual bool SetupReturnFireTest();
    virtual bool SetupCollisionAvoidanceTest();
    virtual bool SetupCommandPriorityTest();
    virtual bool SetupStressTest();

    // Validation helpers
    virtual bool ValidateUnitsReachedDestination(const TArray<ARTSUnit*>& Units, const FVector& Destination, float Tolerance = 100.0f);
    virtual bool ValidateFormationIntegrity(const TArray<ARTSUnit*>& Units);
    virtual bool ValidateCombatBehavior(const TArray<ARTSUnit*>& Units);
    virtual bool ValidatePerformanceThresholds();

public:
    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTestStarted, ERTSTestScenario, TestScenario);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTestCompleted, ERTSTestScenario, TestScenario, bool, bPassed);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPerformanceAlert, const FString&, AlertMessage);

    UPROPERTY(BlueprintAssignable, Category = "RTS AI Testing")
    FOnTestStarted OnTestStarted;

    UPROPERTY(BlueprintAssignable, Category = "RTS AI Testing")
    FOnTestCompleted OnTestCompleted;

    UPROPERTY(BlueprintAssignable, Category = "RTS AI Testing")
    FOnPerformanceAlert OnPerformanceAlert;
};

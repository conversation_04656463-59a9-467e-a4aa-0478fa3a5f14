#include "RTSBehaviorNodes.h"
#include "RTSBehaviorTreeComponent.h"
#include "RTSUnit.h"
#include "RTSCommandComponent.h"
#include "RTSTeamManager.h"
#include "RTSAIController.h"
#include "Engine/World.h"
#include "Engine/Engine.h"

// URTSExecuteCommandTaskNode Implementation

URTSExecuteCommandTaskNode::URTSExecuteCommandTaskNode()
{
    NodeType = ERTSBehaviorNodeType::Task;
    NodeName = TEXT("Execute Command");
    NodeDescription = TEXT("Executes player/AI commands with highest priority");
    MinimumPriority = ERTSCommandPriority::Normal;
    bAllowCommandInterruption = true;
    bExecutingCommand = false;
}

void URTSExecuteCommandTaskNode::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    bExecutingCommand = false;
    CurrentCommand = FRTSCommand();
}

ERTSBehaviorNodeStatus URTSExecuteCommandTaskNode::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    URTSCommandComponent* CommandComp = Unit->GetCommandComponent();
    if (!CommandComp)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    // Check if we have a command to execute
    if (!bExecutingCommand)
    {
        if (!CommandComp->HasCommands())
        {
            return ERTSBehaviorNodeStatus::Failure; // No commands to execute
        }
        
        FRTSCommand NewCommand = CommandComp->GetCurrentCommand();
        if (static_cast<uint8>(NewCommand.Priority) < static_cast<uint8>(MinimumPriority))
        {
            return ERTSBehaviorNodeStatus::Failure; // Command priority too low
        }
        
        CurrentCommand = NewCommand;
        bExecutingCommand = true;
        
        // Store command data in blackboard
        BehaviorTreeComponent->SetBlackboardValue(TEXT("CurrentCommandType"), UEnum::GetValueAsString(CurrentCommand.CommandType));
        BehaviorTreeComponent->SetBlackboardVector(TEXT("CommandTargetLocation"), CurrentCommand.TargetLocation);
        if (CurrentCommand.TargetActor.IsValid())
        {
            BehaviorTreeComponent->SetBlackboardObject(TEXT("CommandTargetActor"), CurrentCommand.TargetActor.Get());
        }
    }
    
    // Execute the current command
    if (bExecutingCommand)
    {
        // Let the command component handle execution
        CommandComp->ExecuteCurrentCommand();
        
        // Check if command is still valid and executing
        if (!CommandComp->HasCommands() || CommandComp->GetCurrentCommand().CommandType != CurrentCommand.CommandType)
        {
            // Command completed or changed
            bExecutingCommand = false;
            return ERTSBehaviorNodeStatus::Success;
        }
        
        return ERTSBehaviorNodeStatus::Running;
    }
    
    return ERTSBehaviorNodeStatus::Failure;
}

// URTSEnhancedMoveTask Implementation

URTSEnhancedMoveTask::URTSEnhancedMoveTask()
{
    NodeType = ERTSBehaviorNodeType::Task;
    NodeName = TEXT("Enhanced Move");
    NodeDescription = TEXT("Enhanced movement with formation support and return fire");
    TargetLocationKey = TEXT("TargetLocation");
    AcceptanceRadius = 100.0f;
    bUseFormationMovement = true;
    bReturnFireWhileMoving = true;
    bStopOnEnemyContact = false;
    bMovementStarted = false;
    LastReturnFireTime = 0.0f;
}

void URTSEnhancedMoveTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    bMovementStarted = false;
    LastTargetLocation = FVector::ZeroVector;
    LastReturnFireTime = 0.0f;
}

ERTSBehaviorNodeStatus URTSEnhancedMoveTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    // Get target location from blackboard
    FVector TargetLocation = BehaviorTreeComponent->GetBlackboardVector(TargetLocationKey);
    if (TargetLocation.IsZero())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    // Check if we need to start movement
    if (!bMovementStarted || !LastTargetLocation.Equals(TargetLocation, 10.0f))
    {
        // Handle formation movement
        if (bUseFormationMovement)
        {
            URTSCommandComponent* CommandComp = Unit->GetCommandComponent();
            if (CommandComp && CommandComp->IsInFormation())
            {
                FVector FormationOffset = CommandComp->GetFormationOffset();
                TargetLocation += FormationOffset;
            }
        }
        
        Unit->MoveToLocation(TargetLocation);
        bMovementStarted = true;
        LastTargetLocation = TargetLocation;
    }
    
    // Handle return fire while moving
    if (bReturnFireWhileMoving)
    {
        float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
        if (CurrentTime - LastReturnFireTime > 1.0f) // Check every second
        {
            // Look for nearby enemies to return fire
            if (UWorld* World = Unit->GetWorld())
            {
                if (URTSTeamManager* TeamManager = World->GetSubsystem<URTSTeamManager>())
                {
                    ARTSBaseActor* NearestEnemy = TeamManager->FindNearestEnemy(Unit->TeamID, Unit->GetActorLocation(), Unit->GetMaxAttackRange());
                    if (NearestEnemy)
                    {
                        Unit->AttackTarget(NearestEnemy);
                        BehaviorTreeComponent->SetBlackboardObject(TEXT("ReturnFireTarget"), NearestEnemy);
                    }
                }
            }
            LastReturnFireTime = CurrentTime;
        }
    }
    
    // Check if we've reached the destination
    float DistanceToTarget = FVector::Dist(Unit->GetActorLocation(), TargetLocation);
    if (DistanceToTarget <= AcceptanceRadius)
    {
        return ERTSBehaviorNodeStatus::Success;
    }
    
    // Check if we should stop on enemy contact
    if (bStopOnEnemyContact)
    {
        UObject* ReturnFireTarget = BehaviorTreeComponent->GetBlackboardObject(TEXT("ReturnFireTarget"));
        if (ReturnFireTarget)
        {
            return ERTSBehaviorNodeStatus::Success; // Stop moving, engage enemy
        }
    }
    
    return ERTSBehaviorNodeStatus::Running;
}

// URTSEnhancedCombatTask Implementation

URTSEnhancedCombatTask::URTSEnhancedCombatTask()
{
    NodeType = ERTSBehaviorNodeType::Task;
    NodeName = TEXT("Enhanced Combat");
    NodeDescription = TEXT("Enhanced combat with stat integration and target selection");
    TargetActorKey = TEXT("TargetActor");
    bAutoSelectTargets = true;
    bUseUnitWeaponRange = true;
    MaxEngagementRange = 1000.0f;
    bPursueTargets = false;
    TargetSwitchCooldown = 2.0f;
    LastTargetSwitchTime = 0.0f;
}

void URTSEnhancedCombatTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    LastTargetSwitchTime = 0.0f;
    CurrentTarget = nullptr;
}

ERTSBehaviorNodeStatus URTSEnhancedCombatTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Get target from blackboard or auto-select
    ARTSBaseActor* Target = Cast<ARTSBaseActor>(BehaviorTreeComponent->GetBlackboardObject(TargetActorKey));
    
    // Auto-select target if needed
    if (bAutoSelectTargets && (!Target || !Target->IsAlive() || (CurrentTime - LastTargetSwitchTime > TargetSwitchCooldown)))
    {
        if (UWorld* World = Unit->GetWorld())
        {
            if (URTSTeamManager* TeamManager = World->GetSubsystem<URTSTeamManager>())
            {
                float EngagementRange = bUseUnitWeaponRange ? Unit->GetMaxAttackRange() : MaxEngagementRange;
                ARTSBaseActor* NewTarget = TeamManager->FindHighestPriorityTarget(Unit->TeamID, Unit->GetActorLocation(), EngagementRange);
                
                if (NewTarget && NewTarget != Target)
                {
                    Target = NewTarget;
                    CurrentTarget = Target;
                    BehaviorTreeComponent->SetBlackboardObject(TargetActorKey, Target);
                    LastTargetSwitchTime = CurrentTime;
                }
            }
        }
    }
    
    if (!Target || !Target->IsAlive())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    // Check if target is in range
    float DistanceToTarget = FVector::Dist(Unit->GetActorLocation(), Target->GetActorLocation());
    float EngagementRange = bUseUnitWeaponRange ? Unit->GetMaxAttackRange() : MaxEngagementRange;
    
    if (DistanceToTarget > EngagementRange)
    {
        if (bPursueTargets)
        {
            // Move closer to target
            Unit->MoveToLocation(Target->GetActorLocation());
            return ERTSBehaviorNodeStatus::Running;
        }
        else
        {
            return ERTSBehaviorNodeStatus::Failure; // Target out of range
        }
    }
    
    // Attack the target
    Unit->AttackTarget(Target);
    
    // Check if target is dead
    if (!Target->IsAlive())
    {
        BehaviorTreeComponent->SetBlackboardObject(TargetActorKey, nullptr);
        return ERTSBehaviorNodeStatus::Success;
    }
    
    return ERTSBehaviorNodeStatus::Running;
}

// URTSHasCommandsCondition Implementation

URTSHasCommandsCondition::URTSHasCommandsCondition()
{
    NodeType = ERTSBehaviorNodeType::Condition;
    NodeName = TEXT("Has Commands");
    NodeDescription = TEXT("Checks if unit has commands to execute");
    MinimumPriority = ERTSCommandPriority::Normal;
    bCheckQueuedCommands = true;
}

ERTSBehaviorNodeStatus URTSHasCommandsCondition::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    URTSCommandComponent* CommandComp = Unit->GetCommandComponent();
    if (!CommandComp)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    if (!CommandComp->HasCommands())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Check command priority
    FRTSCommand CurrentCommand = CommandComp->GetCurrentCommand();
    if (static_cast<uint8>(CurrentCommand.Priority) < static_cast<uint8>(MinimumPriority))
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    return ERTSBehaviorNodeStatus::Success;
}

// URTSEnemiesInRangeCondition Implementation

URTSEnemiesInRangeCondition::URTSEnemiesInRangeCondition()
{
    NodeType = ERTSBehaviorNodeType::Condition;
    NodeName = TEXT("Enemies In Range");
    NodeDescription = TEXT("Checks if enemies are within detection/weapon range");
    DetectionRange = 800.0f;
    bUseWeaponRange = true;
    bStoreNearestEnemy = true;
    NearestEnemyKey = TEXT("NearestEnemy");
    EnemyCountKey = TEXT("EnemyCount");
}

ERTSBehaviorNodeStatus URTSEnemiesInRangeCondition::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    UWorld* World = Unit->GetWorld();
    if (!World)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    URTSTeamManager* TeamManager = World->GetSubsystem<URTSTeamManager>();
    if (!TeamManager)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Determine range to use
    float RangeToUse = bUseWeaponRange ? Unit->GetMaxAttackRange() : DetectionRange;
    if (RangeToUse <= 0.0f)
    {
        RangeToUse = DetectionRange;
    }

    // Find enemies in range
    TArray<ARTSBaseActor*> EnemiesInRange = TeamManager->FindEnemiesInRange(Unit->TeamID, Unit->GetActorLocation(), RangeToUse);

    // Store enemy count in blackboard
    BehaviorTreeComponent->SetBlackboardFloat(EnemyCountKey, static_cast<float>(EnemiesInRange.Num()));

    if (EnemiesInRange.Num() == 0)
    {
        // Clear nearest enemy
        if (bStoreNearestEnemy)
        {
            BehaviorTreeComponent->SetBlackboardObject(NearestEnemyKey, nullptr);
        }
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Store nearest enemy if requested
    if (bStoreNearestEnemy)
    {
        ARTSBaseActor* NearestEnemy = TeamManager->FindNearestEnemy(Unit->TeamID, Unit->GetActorLocation(), RangeToUse);
        BehaviorTreeComponent->SetBlackboardObject(NearestEnemyKey, NearestEnemy);
    }

    return ERTSBehaviorNodeStatus::Success;
}

// URTSInFormationCondition Implementation

URTSInFormationCondition::URTSInFormationCondition()
{
    NodeType = ERTSBehaviorNodeType::Condition;
    NodeName = TEXT("In Formation");
    NodeDescription = TEXT("Checks if unit is currently in a formation");
    bCheckFormationLeaderValid = true;
    bCheckFormationDistance = true;
    MaxFormationDistance = 1000.0f;
}

ERTSBehaviorNodeStatus URTSInFormationCondition::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    URTSCommandComponent* CommandComp = Unit->GetCommandComponent();
    if (!CommandComp)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    if (!CommandComp->IsInFormation())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Check if formation leader is valid
    if (bCheckFormationLeaderValid)
    {
        ARTSUnit* FormationLeader = CommandComp->GetFormationLeader();
        if (!FormationLeader || !FormationLeader->IsAlive())
        {
            return ERTSBehaviorNodeStatus::Failure;
        }

        // Check distance to formation leader
        if (bCheckFormationDistance)
        {
            float DistanceToLeader = FVector::Dist(Unit->GetActorLocation(), FormationLeader->GetActorLocation());
            if (DistanceToLeader > MaxFormationDistance)
            {
                return ERTSBehaviorNodeStatus::Failure;
            }
        }
    }

    return ERTSBehaviorNodeStatus::Success;
}

// URTSCommandInterruptDecorator Implementation

URTSCommandInterruptDecorator::URTSCommandInterruptDecorator()
{
    NodeType = ERTSBehaviorNodeType::Decorator;
    NodeName = TEXT("Command Interrupt");
    NodeDescription = TEXT("Interrupts child when high-priority commands are received");
    MinimumInterruptPriority = ERTSCommandPriority::High;
    CheckInterval = 0.1f;
    LastCheckTime = 0.0f;
}

ERTSBehaviorNodeStatus URTSCommandInterruptDecorator::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent || ChildNodes.Num() == 0)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Check for commands at regular intervals
    if (CurrentTime - LastCheckTime >= CheckInterval)
    {
        ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
        if (Unit)
        {
            URTSCommandComponent* CommandComp = Unit->GetCommandComponent();
            if (CommandComp && CommandComp->HasCommands())
            {
                FRTSCommand CurrentCommand = CommandComp->GetCurrentCommand();
                if (static_cast<uint8>(CurrentCommand.Priority) >= static_cast<uint8>(MinimumInterruptPriority))
                {
                    // High priority command received, abort child
                    if (ChildNodes[0])
                    {
                        ChildNodes[0]->AbortNode(BehaviorTreeComponent);
                    }
                    return ERTSBehaviorNodeStatus::Aborted;
                }
            }
        }
        LastCheckTime = CurrentTime;
    }

    // Execute child node
    if (ChildNodes[0])
    {
        return ChildNodes[0]->ExecuteNode(BehaviorTreeComponent, DeltaTime);
    }

    return ERTSBehaviorNodeStatus::Failure;
}

// URTSStatBasedDecorator Implementation

URTSStatBasedDecorator::URTSStatBasedDecorator()
{
    NodeType = ERTSBehaviorNodeType::Decorator;
    NodeName = TEXT("Stat Based");
    NodeDescription = TEXT("Modifies behavior based on unit stats");
    bCheckMovementSpeed = false;
    MinMovementSpeed = 100.0f;
    bCheckAttackRange = false;
    MinAttackRange = 200.0f;
    bCheckHealth = false;
    MinHealthPercentage = 0.5f;
}

ERTSBehaviorNodeStatus URTSStatBasedDecorator::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent || ChildNodes.Num() == 0)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // Check stat requirements
    if (bCheckMovementSpeed && Unit->GetMovementSpeed() < MinMovementSpeed)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    if (bCheckAttackRange && Unit->GetMaxAttackRange() < MinAttackRange)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    if (bCheckHealth && Unit->GetHealthPercentage() < MinHealthPercentage)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    // All stat requirements met, execute child
    if (ChildNodes[0])
    {
        return ChildNodes[0]->ExecuteNode(BehaviorTreeComponent, DeltaTime);
    }

    return ERTSBehaviorNodeStatus::Failure;
}

// URTSFollowFormationTask Implementation

URTSFollowFormationTask::URTSFollowFormationTask()
{
    NodeType = ERTSBehaviorNodeType::Task;
    NodeName = TEXT("Follow Formation");
    NodeDescription = TEXT("Maintains formation position relative to leader");
    FormationLeaderKey = TEXT("FormationLeader");
    FormationOffsetKey = TEXT("FormationOffset");
    FormationTolerance = 50.0f;
    MaxFormationDistance = 500.0f;
}

void URTSFollowFormationTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    LastFormationPosition = FVector::ZeroVector;
}

ERTSBehaviorNodeStatus URTSFollowFormationTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    // Check if unit is in formation
    URTSCommandComponent* CommandComp = Unit->GetCommandComponent();
    if (!CommandComp || !CommandComp->IsInFormation())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    ARTSUnit* FormationLeader = CommandComp->GetFormationLeader();
    if (!FormationLeader || !FormationLeader->IsAlive())
    {
        return ERTSBehaviorNodeStatus::Failure;
    }
    
    // Calculate formation position
    FVector FormationOffset = CommandComp->GetFormationOffset();
    FVector DesiredPosition = FormationLeader->GetActorLocation() + FormationOffset;
    
    // Check if we're close enough to formation position
    float DistanceToFormationPosition = FVector::Dist(Unit->GetActorLocation(), DesiredPosition);
    if (DistanceToFormationPosition <= FormationTolerance)
    {
        return ERTSBehaviorNodeStatus::Success;
    }
    
    // Check if formation leader is too far away
    float DistanceToLeader = FVector::Dist(Unit->GetActorLocation(), FormationLeader->GetActorLocation());
    if (DistanceToLeader > MaxFormationDistance)
    {
        return ERTSBehaviorNodeStatus::Failure; // Formation broken
    }
    
    // Move to formation position
    Unit->MoveToLocation(DesiredPosition);
    LastFormationPosition = DesiredPosition;
    
    return ERTSBehaviorNodeStatus::Running;
}

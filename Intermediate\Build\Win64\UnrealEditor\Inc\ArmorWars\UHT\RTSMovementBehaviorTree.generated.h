// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSMovementBehaviorTree.h"

#ifdef ARMORWARS_RTSMovementBehaviorTree_generated_h
#error "RTSMovementBehaviorTree.generated.h already included, missing '#pragma once' in RTSMovementBehaviorTree.h"
#endif
#define ARMORWARS_RTSMovementBehaviorTree_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class URTSBehaviorNode;

// ********** Begin Class URTSMovementBehaviorTreeFactory ******************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_20_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCreateCommandResponsiveBehaviorTree); \
	DECLARE_FUNCTION(execCreateAdvancedUnitBehaviorTree); \
	DECLARE_FUNCTION(execCreateCombatMovementTree); \
	DECLARE_FUNCTION(execCreateFormationMovementTree); \
	DECLARE_FUNCTION(execCreateBasicMovementTree);


ARMORWARS_API UClass* Z_Construct_UClass_URTSMovementBehaviorTreeFactory_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_20_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSMovementBehaviorTreeFactory(); \
	friend struct Z_Construct_UClass_URTSMovementBehaviorTreeFactory_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSMovementBehaviorTreeFactory_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSMovementBehaviorTreeFactory, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSMovementBehaviorTreeFactory_NoRegister) \
	DECLARE_SERIALIZER(URTSMovementBehaviorTreeFactory)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_20_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSMovementBehaviorTreeFactory(URTSMovementBehaviorTreeFactory&&) = delete; \
	URTSMovementBehaviorTreeFactory(const URTSMovementBehaviorTreeFactory&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSMovementBehaviorTreeFactory); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSMovementBehaviorTreeFactory); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSMovementBehaviorTreeFactory) \
	NO_API virtual ~URTSMovementBehaviorTreeFactory();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_17_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_20_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_20_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_20_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_20_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSMovementBehaviorTreeFactory;

// ********** End Class URTSMovementBehaviorTreeFactory ********************************************

// ********** Begin Class URTSCollisionAvoidanceMoveTask *******************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_57_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSCollisionAvoidanceMoveTask(); \
	friend struct Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSCollisionAvoidanceMoveTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_NoRegister) \
	DECLARE_SERIALIZER(URTSCollisionAvoidanceMoveTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_57_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSCollisionAvoidanceMoveTask(URTSCollisionAvoidanceMoveTask&&) = delete; \
	URTSCollisionAvoidanceMoveTask(const URTSCollisionAvoidanceMoveTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSCollisionAvoidanceMoveTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSCollisionAvoidanceMoveTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSCollisionAvoidanceMoveTask) \
	NO_API virtual ~URTSCollisionAvoidanceMoveTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_54_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_57_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_57_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_57_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSCollisionAvoidanceMoveTask;

// ********** End Class URTSCollisionAvoidanceMoveTask *********************************************

// ********** Begin Class URTSPathBlockedCondition *************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSPathBlockedCondition_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_106_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSPathBlockedCondition(); \
	friend struct Z_Construct_UClass_URTSPathBlockedCondition_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSPathBlockedCondition_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSPathBlockedCondition, URTSConditionNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSPathBlockedCondition_NoRegister) \
	DECLARE_SERIALIZER(URTSPathBlockedCondition)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_106_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSPathBlockedCondition(URTSPathBlockedCondition&&) = delete; \
	URTSPathBlockedCondition(const URTSPathBlockedCondition&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSPathBlockedCondition); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSPathBlockedCondition); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSPathBlockedCondition) \
	NO_API virtual ~URTSPathBlockedCondition();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_103_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_106_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_106_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_106_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSPathBlockedCondition;

// ********** End Class URTSPathBlockedCondition ***************************************************

// ********** Begin Class URTSFindAlternativePathTask **********************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSFindAlternativePathTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_135_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSFindAlternativePathTask(); \
	friend struct Z_Construct_UClass_URTSFindAlternativePathTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSFindAlternativePathTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSFindAlternativePathTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSFindAlternativePathTask_NoRegister) \
	DECLARE_SERIALIZER(URTSFindAlternativePathTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_135_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSFindAlternativePathTask(URTSFindAlternativePathTask&&) = delete; \
	URTSFindAlternativePathTask(const URTSFindAlternativePathTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSFindAlternativePathTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSFindAlternativePathTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSFindAlternativePathTask) \
	NO_API virtual ~URTSFindAlternativePathTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_132_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_135_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_135_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_135_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSFindAlternativePathTask;

// ********** End Class URTSFindAlternativePathTask ************************************************

// ********** Begin Class URTSFormationSpeedDecorator **********************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSFormationSpeedDecorator_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_177_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSFormationSpeedDecorator(); \
	friend struct Z_Construct_UClass_URTSFormationSpeedDecorator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSFormationSpeedDecorator_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSFormationSpeedDecorator, URTSDecoratorNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSFormationSpeedDecorator_NoRegister) \
	DECLARE_SERIALIZER(URTSFormationSpeedDecorator)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_177_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSFormationSpeedDecorator(URTSFormationSpeedDecorator&&) = delete; \
	URTSFormationSpeedDecorator(const URTSFormationSpeedDecorator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSFormationSpeedDecorator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSFormationSpeedDecorator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSFormationSpeedDecorator) \
	NO_API virtual ~URTSFormationSpeedDecorator();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_174_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_177_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_177_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_177_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSFormationSpeedDecorator;

// ********** End Class URTSFormationSpeedDecorator ************************************************

// ********** Begin Class URTSMaintainFormationSpacingTask *****************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSMaintainFormationSpacingTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_215_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSMaintainFormationSpacingTask(); \
	friend struct Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSMaintainFormationSpacingTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSMaintainFormationSpacingTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSMaintainFormationSpacingTask_NoRegister) \
	DECLARE_SERIALIZER(URTSMaintainFormationSpacingTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_215_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSMaintainFormationSpacingTask(URTSMaintainFormationSpacingTask&&) = delete; \
	URTSMaintainFormationSpacingTask(const URTSMaintainFormationSpacingTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSMaintainFormationSpacingTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSMaintainFormationSpacingTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSMaintainFormationSpacingTask) \
	NO_API virtual ~URTSMaintainFormationSpacingTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_212_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_215_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_215_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h_215_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSMaintainFormationSpacingTask;

// ********** End Class URTSMaintainFormationSpacingTask *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

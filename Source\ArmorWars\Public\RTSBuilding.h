#pragma once

#include "CoreMinimal.h"
#include "RTSBaseActor.h"
#include "Components/StaticMeshComponent.h"
#include "RTSBuilding.generated.h"

class URTSWeaponController;
class URTSFactoryComponent;

// Enum for building states
UENUM(BlueprintType)
enum class ERTSBuildingState : uint8
{
    UnderConstruction   UMETA(DisplayName = "Under Construction"),
    Operational         UMETA(DisplayName = "Operational"),
    Damaged             UMETA(DisplayName = "Damaged"),
    Destroyed           UMETA(DisplayName = "Destroyed"),
    Disabled            UMETA(DisplayName = "Disabled")
};

// Enum for building types
UENUM(BlueprintType)
enum class ERTSBuildingType : uint8
{
    Base                UMETA(DisplayName = "Base"),
    Production          UMETA(DisplayName = "Production"),
    Defense             UMETA(DisplayName = "Defense"),
    Resource            UMETA(DisplayName = "Resource"),
    Research            UMETA(DisplayName = "Research"),
    Support             UMETA(DisplayName = "Support"),
    Special             UMETA(DisplayName = "Special")
};

/**
 * Building class for all static RTS structures
 * Inherits from ARTSBaseActor and adds building-specific functionality
 * such as construction, production, and defensive capabilities
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API ARTSBuilding : public ARTSBaseActor
{
    GENERATED_BODY()

public:
    ARTSBuilding();

protected:
    virtual void BeginPlay() override;

public:
    virtual void Tick(float DeltaTime) override;

protected:
    // Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UStaticMeshComponent* MeshComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    URTSWeaponController* WeaponController;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    URTSFactoryComponent* FactoryComponent;

public:
    // Building type
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Building")
    ERTSBuildingType BuildingType = ERTSBuildingType::Base;

    // Current building state
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Building")
    ERTSBuildingState BuildingState = ERTSBuildingState::UnderConstruction;

    // Construction progress (0.0 to 1.0)
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Construction")
    float ConstructionProgress = 0.0f;

    // Time required to complete construction (in seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Construction", meta = (ClampMin = "0.1"))
    float ConstructionTime = 30.0f;

    // Whether construction is paused
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Construction")
    bool bConstructionPaused = false;

    // Power consumption (if applicable)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Power")
    int32 PowerConsumption = 0;

    // Power generation (if applicable)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Power")
    int32 PowerGeneration = 0;

    // Vision range
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Vision", meta = (ClampMin = "0.0"))
    float VisionRange = 600.0f;



    // Resource cost to build this building
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Economy")
    int32 ResourceCost = 100;

    // Resource generation rate (resources per second, if applicable)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Economy")
    float ResourceGenerationRate = 0.0f;

public:
    // Construction functions
    UFUNCTION(BlueprintCallable, Category = "RTS|Construction")
    void StartConstruction();

    UFUNCTION(BlueprintCallable, Category = "RTS|Construction")
    void PauseConstruction();

    UFUNCTION(BlueprintCallable, Category = "RTS|Construction")
    void ResumeConstruction();

    UFUNCTION(BlueprintCallable, Category = "RTS|Construction")
    void CompleteConstruction();

    UFUNCTION(BlueprintPure, Category = "RTS|Construction")
    bool IsUnderConstruction() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Construction")
    bool IsOperational() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Construction")
    float GetConstructionTimeRemaining() const;

    // State functions
    UFUNCTION(BlueprintCallable, Category = "RTS|Building")
    void SetBuildingState(ERTSBuildingState NewState);

    UFUNCTION(BlueprintPure, Category = "RTS|Building")
    ERTSBuildingState GetBuildingState() const;

    // Combat functions (for defensive buildings)
    UFUNCTION(BlueprintCallable, Category = "RTS|Combat")
    void AttackTarget(ARTSBaseActor* Target);

    UFUNCTION(BlueprintCallable, Category = "RTS|Combat")
    void StopAttacking();

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    bool CanAttackTarget(const ARTSBaseActor* Target) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    bool IsInAttackRange(const ARTSBaseActor* Target) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    bool IsAttacking() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    ARTSBaseActor* GetCurrentTarget() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    float GetMaxAttackRange() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    float GetTotalDamagePerSecond() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    bool CanTargetDomain(ERTSUnitDomain Domain) const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Combat")
    ARTSBaseActor* FindNearestEnemy(float SearchRange = 0.0f) const;

    // Building type functions
    UFUNCTION(BlueprintPure, Category = "RTS|Building")
    bool IsDefensiveBuilding() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Building")
    bool IsProductionBuilding() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Building")
    bool IsResourceBuilding() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Building")
    ERTSBuildingType GetBuildingType() const { return BuildingType; }

    UFUNCTION(BlueprintPure, Category = "RTS|Combat")
    bool HasWeapons() const;

    // Power functions
    UFUNCTION(BlueprintPure, Category = "RTS|Power")
    int32 GetNetPowerGeneration() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Power")
    bool IsGeneratingPower() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Power")
    bool IsConsumingPower() const;

    // Factory functions
    UFUNCTION(BlueprintPure, Category = "RTS|Factory")
    URTSFactoryComponent* GetFactoryComponent() const { return FactoryComponent; }

    UFUNCTION(BlueprintPure, Category = "RTS|Factory")
    bool HasFactory() const { return FactoryComponent != nullptr; }

    UFUNCTION(BlueprintPure, Category = "RTS|Factory")
    bool CanProduceUnits() const;

protected:
    // Internal construction handling
    virtual void UpdateConstruction(float DeltaTime);

    // Internal combat handling (for defensive buildings)
    virtual void UpdateCombat(float DeltaTime);

    // Override death handling to change state
    virtual void HandleDeath() override;

    // Construction start time
    float ConstructionStartTime = 0.0f;

protected:
    // Blueprint events
    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Construction")
    void OnConstructionStarted();

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Construction")
    void OnConstructionCompleted();

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Construction")
    void OnConstructionPaused();

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Construction")
    void OnConstructionResumed();

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Building")
    void OnBuildingStateChangedEvent(ERTSBuildingState OldState, ERTSBuildingState NewState);

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Combat")
    void OnAttackStarted(ARTSBaseActor* Target);

    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Combat")
    void OnAttackStopped();

public:
    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBuildingStateChanged, ARTSBuilding*, Building, ERTSBuildingState, NewState);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnConstructionProgress, ARTSBuilding*, Building, float, Progress);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBuildingCombat, ARTSBuilding*, Building, ARTSBaseActor*, Target);

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnBuildingStateChanged OnBuildingStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnConstructionProgress OnConstructionProgress;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnBuildingCombat OnCombatChanged;
};

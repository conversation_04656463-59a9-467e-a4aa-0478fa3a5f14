{"Version": "1.2", "Data": {"Source": "f:\\armorwars\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\armorwars\\module.armorwars.5.cpp", "ProvidedModule": "", "PCH": "f:\\armorwars\\intermediate\\build\\win64\\x64\\armorwarseditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["f:\\armorwars\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\armorwars\\definitions.armorwars.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscollisionavoidancesystem.gen.cpp", "f:\\armorwars\\source\\armorwars\\public\\rtscollisionavoidancesystem.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscollisionavoidancesystem.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscombatbehaviortree.gen.cpp", "f:\\armorwars\\source\\armorwars\\public\\rtscombatbehaviortree.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "f:\\armorwars\\source\\armorwars\\public\\rtsbehaviornode.h", "f:\\armorwars\\source\\armorwars\\public\\rtsbehaviortreecomponent.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsbehaviortreecomponent.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsbehaviornode.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsbehaviornodes.h", "f:\\armorwars\\source\\armorwars\\public\\rtscommand.h", "f:\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscommand.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsbehaviornodes.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscombatbehaviortree.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscommand.gen.cpp", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscommandcomponent.gen.cpp", "f:\\armorwars\\source\\armorwars\\public\\rtscommandcomponent.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscommandcomponent.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}
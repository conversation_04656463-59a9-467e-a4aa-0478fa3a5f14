// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSWeaponController.h"

#ifdef ARMORWARS_RTSWeaponController_generated_h
#error "RTSWeaponController.generated.h already included, missing '#pragma once' in RTSWeaponController.h"
#endif
#define ARMORWARS_RTSWeaponController_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSBaseActor;
class URTSWeaponComponent;
class URTSWeaponController;
enum class ERTSUnitDomain : uint8;

// ********** Begin Delegate FOnTargetChanged ******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_170_DELEGATE \
static void FOnTargetChanged_DelegateWrapper(const FMulticastScriptDelegate& OnTargetChanged, URTSWeaponController* Controller, ARTSBaseActor* NewTarget);


// ********** End Delegate FOnTargetChanged ********************************************************

// ********** Begin Delegate FOnFiringStateChanged *************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_171_DELEGATE \
static void FOnFiringStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnFiringStateChanged, URTSWeaponController* Controller, bool bFiring);


// ********** End Delegate FOnFiringStateChanged ***************************************************

// ********** Begin Delegate FOnWeaponFired ********************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_172_DELEGATE \
static void FOnWeaponFired_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponFired, URTSWeaponController* Controller, URTSWeaponComponent* Weapon, ARTSBaseActor* Target);


// ********** End Delegate FOnWeaponFired **********************************************************

// ********** Begin Class URTSWeaponController *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnWeaponFiredInternal); \
	DECLARE_FUNCTION(execUpdateTargeting); \
	DECLARE_FUNCTION(execCanTargetDomain); \
	DECLARE_FUNCTION(execGetTotalDamagePerSecond); \
	DECLARE_FUNCTION(execGetMaxRange); \
	DECLARE_FUNCTION(execHasWeapons); \
	DECLARE_FUNCTION(execGetActiveWeapons); \
	DECLARE_FUNCTION(execSetAllWeaponsActive); \
	DECLARE_FUNCTION(execSetActiveWeaponGroup); \
	DECLARE_FUNCTION(execIsFiring); \
	DECLARE_FUNCTION(execCanFire); \
	DECLARE_FUNCTION(execFireAtLocation); \
	DECLARE_FUNCTION(execFireAtTarget); \
	DECLARE_FUNCTION(execStopFiring); \
	DECLARE_FUNCTION(execStartFiring); \
	DECLARE_FUNCTION(execFindBestTarget); \
	DECLARE_FUNCTION(execHasValidTarget); \
	DECLARE_FUNCTION(execGetCurrentTarget); \
	DECLARE_FUNCTION(execClearTarget); \
	DECLARE_FUNCTION(execSetTarget); \
	DECLARE_FUNCTION(execGetWeaponCount); \
	DECLARE_FUNCTION(execGetAllWeapons); \
	DECLARE_FUNCTION(execClearAllWeapons); \
	DECLARE_FUNCTION(execRemoveWeapon); \
	DECLARE_FUNCTION(execAddWeapon);


#define FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_16_CALLBACK_WRAPPERS
ARMORWARS_API UClass* Z_Construct_UClass_URTSWeaponController_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSWeaponController(); \
	friend struct Z_Construct_UClass_URTSWeaponController_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSWeaponController_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSWeaponController, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSWeaponController_NoRegister) \
	DECLARE_SERIALIZER(URTSWeaponController)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_16_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSWeaponController(URTSWeaponController&&) = delete; \
	URTSWeaponController(const URTSWeaponController&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSWeaponController); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSWeaponController); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSWeaponController) \
	NO_API virtual ~URTSWeaponController();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_13_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_16_CALLBACK_WRAPPERS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_16_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSWeaponController;

// ********** End Class URTSWeaponController *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

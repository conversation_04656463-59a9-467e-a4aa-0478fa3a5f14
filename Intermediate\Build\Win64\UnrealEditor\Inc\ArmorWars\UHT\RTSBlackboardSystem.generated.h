// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSBlackboardSystem.h"

#ifdef ARMORWARS_RTSBlackboardSystem_generated_h
#error "RTSBlackboardSystem.generated.h already included, missing '#pragma once' in RTSBlackboardSystem.h"
#endif
#define ARMORWARS_RTSBlackboardSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSBaseActor;
class ARTSUnit;
class UObject;
class URTSCommandComponent;
enum class ERTSBlackboardKeyType : uint8;

// ********** Begin ScriptStruct FRTSBlackboardEntry ***********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h_32_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSBlackboardEntry;
// ********** End ScriptStruct FRTSBlackboardEntry *************************************************

// ********** Begin Class URTSBlackboardComponent **************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h_96_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetOwnerCommandComponent); \
	DECLARE_FUNCTION(execGetOwnerUnit); \
	DECLARE_FUNCTION(execUpdateAllData); \
	DECLARE_FUNCTION(execUpdateCombatData); \
	DECLARE_FUNCTION(execUpdateFormationData); \
	DECLARE_FUNCTION(execUpdateCommandData); \
	DECLARE_FUNCTION(execUpdateUnitData); \
	DECLARE_FUNCTION(execAddPredefinedKey); \
	DECLARE_FUNCTION(execInitializePredefinedKeys); \
	DECLARE_FUNCTION(execGetKeyType); \
	DECLARE_FUNCTION(execGetAllKeys); \
	DECLARE_FUNCTION(execClearAllKeys); \
	DECLARE_FUNCTION(execRemoveKey); \
	DECLARE_FUNCTION(execHasKey); \
	DECLARE_FUNCTION(execGetUnit); \
	DECLARE_FUNCTION(execGetActor); \
	DECLARE_FUNCTION(execGetObject); \
	DECLARE_FUNCTION(execGetRotator); \
	DECLARE_FUNCTION(execGetVector); \
	DECLARE_FUNCTION(execGetString); \
	DECLARE_FUNCTION(execGetFloat); \
	DECLARE_FUNCTION(execGetInt); \
	DECLARE_FUNCTION(execGetBool); \
	DECLARE_FUNCTION(execSetObject); \
	DECLARE_FUNCTION(execSetRotator); \
	DECLARE_FUNCTION(execSetVector); \
	DECLARE_FUNCTION(execSetString); \
	DECLARE_FUNCTION(execSetFloat); \
	DECLARE_FUNCTION(execSetInt); \
	DECLARE_FUNCTION(execSetBool);


ARMORWARS_API UClass* Z_Construct_UClass_URTSBlackboardComponent_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h_96_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSBlackboardComponent(); \
	friend struct Z_Construct_UClass_URTSBlackboardComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSBlackboardComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSBlackboardComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSBlackboardComponent_NoRegister) \
	DECLARE_SERIALIZER(URTSBlackboardComponent)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h_96_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSBlackboardComponent(URTSBlackboardComponent&&) = delete; \
	URTSBlackboardComponent(const URTSBlackboardComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSBlackboardComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSBlackboardComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSBlackboardComponent) \
	NO_API virtual ~URTSBlackboardComponent();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h_93_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h_96_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h_96_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h_96_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h_96_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSBlackboardComponent;

// ********** End Class URTSBlackboardComponent ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h

// ********** Begin Enum ERTSBlackboardKeyType *****************************************************
#define FOREACH_ENUM_ERTSBLACKBOARDKEYTYPE(op) \
	op(ERTSBlackboardKeyType::Bool) \
	op(ERTSBlackboardKeyType::Int) \
	op(ERTSBlackboardKeyType::Float) \
	op(ERTSBlackboardKeyType::String) \
	op(ERTSBlackboardKeyType::Vector) \
	op(ERTSBlackboardKeyType::Rotator) \
	op(ERTSBlackboardKeyType::Object) \
	op(ERTSBlackboardKeyType::Class) \
	op(ERTSBlackboardKeyType::Enum) 

enum class ERTSBlackboardKeyType : uint8;
template<> struct TIsUEnumClass<ERTSBlackboardKeyType> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSBlackboardKeyType>();
// ********** End Enum ERTSBlackboardKeyType *******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

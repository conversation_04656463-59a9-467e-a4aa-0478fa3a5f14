// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSAirUnit.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSAirUnit() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSAirUnit();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSAirUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSAircraftFlightComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSAirMovementComponent_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSFormationType();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Delegate FOnFormationChanged ***************************************************
struct Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics
{
	struct RTSAirUnit_eventOnFormationChanged_Parms
	{
		ARTSAirUnit* AirUnit;
		ERTSFormationType Formation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AirUnit;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Formation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Formation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::NewProp_AirUnit = { "AirUnit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventOnFormationChanged_Parms, AirUnit), Z_Construct_UClass_ARTSAirUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::NewProp_Formation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::NewProp_Formation = { "Formation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventOnFormationChanged_Parms, Formation), Z_Construct_UEnum_ArmorWars_ERTSFormationType, METADATA_PARAMS(0, nullptr) }; // 1472012862
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::NewProp_AirUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::NewProp_Formation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::NewProp_Formation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "OnFormationChanged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::RTSAirUnit_eventOnFormationChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::RTSAirUnit_eventOnFormationChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSAirUnit::FOnFormationChanged_DelegateWrapper(const FMulticastScriptDelegate& OnFormationChanged, ARTSAirUnit* AirUnit, ERTSFormationType Formation)
{
	struct RTSAirUnit_eventOnFormationChanged_Parms
	{
		ARTSAirUnit* AirUnit;
		ERTSFormationType Formation;
	};
	RTSAirUnit_eventOnFormationChanged_Parms Parms;
	Parms.AirUnit=AirUnit;
	Parms.Formation=Formation;
	OnFormationChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFormationChanged *****************************************************

// ********** Begin Delegate FOnAirCombatEngaged ***************************************************
struct Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics
{
	struct RTSAirUnit_eventOnAirCombatEngaged_Parms
	{
		ARTSAirUnit* AirUnit;
		ARTSUnit* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AirUnit;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::NewProp_AirUnit = { "AirUnit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventOnAirCombatEngaged_Parms, AirUnit), Z_Construct_UClass_ARTSAirUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventOnAirCombatEngaged_Parms, Target), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::NewProp_AirUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "OnAirCombatEngaged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::RTSAirUnit_eventOnAirCombatEngaged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::RTSAirUnit_eventOnAirCombatEngaged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSAirUnit::FOnAirCombatEngaged_DelegateWrapper(const FMulticastScriptDelegate& OnAirCombatEngaged, ARTSAirUnit* AirUnit, ARTSUnit* Target)
{
	struct RTSAirUnit_eventOnAirCombatEngaged_Parms
	{
		ARTSAirUnit* AirUnit;
		ARTSUnit* Target;
	};
	RTSAirUnit_eventOnAirCombatEngaged_Parms Parms;
	Parms.AirUnit=AirUnit;
	Parms.Target=Target;
	OnAirCombatEngaged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAirCombatEngaged *****************************************************

// ********** Begin Class ARTSAirUnit Function CanDetectTarget *************************************
struct Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics
{
	struct RTSAirUnit_eventCanDetectTarget_Parms
	{
		const ARTSUnit* Target;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Sensors" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Target_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventCanDetectTarget_Parms, Target), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Target_MetaData), NewProp_Target_MetaData) };
void Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAirUnit_eventCanDetectTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAirUnit_eventCanDetectTarget_Parms), &Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "CanDetectTarget", Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::RTSAirUnit_eventCanDetectTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::RTSAirUnit_eventCanDetectTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execCanDetectTarget)
{
	P_GET_OBJECT(ARTSUnit,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanDetectTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function CanDetectTarget ***************************************

// ********** Begin Class ARTSAirUnit Function EngageAirTarget *************************************
struct Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics
{
	struct RTSAirUnit_eventEngageAirTarget_Parms
	{
		ARTSAirUnit* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Air combat functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Air combat functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventEngageAirTarget_Parms, Target), Z_Construct_UClass_ARTSAirUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "EngageAirTarget", Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::RTSAirUnit_eventEngageAirTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::RTSAirUnit_eventEngageAirTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execEngageAirTarget)
{
	P_GET_OBJECT(ARTSAirUnit,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EngageAirTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function EngageAirTarget ***************************************

// ********** Begin Class ARTSAirUnit Function EngageGroundTarget **********************************
struct Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics
{
	struct RTSAirUnit_eventEngageGroundTarget_Parms
	{
		ARTSUnit* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Combat" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventEngageGroundTarget_Parms, Target), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "EngageGroundTarget", Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::RTSAirUnit_eventEngageGroundTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::RTSAirUnit_eventEngageGroundTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execEngageGroundTarget)
{
	P_GET_OBJECT(ARTSUnit,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EngageGroundTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function EngageGroundTarget ************************************

// ********** Begin Class ARTSAirUnit Function FindNearestAirThreat ********************************
struct Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics
{
	struct RTSAirUnit_eventFindNearestAirThreat_Parms
	{
		ARTSAirUnit* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Combat" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventFindNearestAirThreat_Parms, ReturnValue), Z_Construct_UClass_ARTSAirUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "FindNearestAirThreat", Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::RTSAirUnit_eventFindNearestAirThreat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::RTSAirUnit_eventFindNearestAirThreat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execFindNearestAirThreat)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSAirUnit**)Z_Param__Result=P_THIS->FindNearestAirThreat();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function FindNearestAirThreat **********************************

// ********** Begin Class ARTSAirUnit Function GetAirTargetsInRange ********************************
struct Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics
{
	struct RTSAirUnit_eventGetAirTargetsInRange_Parms
	{
		TArray<ARTSAirUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Combat" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSAirUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventGetAirTargetsInRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "GetAirTargetsInRange", Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::RTSAirUnit_eventGetAirTargetsInRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::RTSAirUnit_eventGetAirTargetsInRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execGetAirTargetsInRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSAirUnit*>*)Z_Param__Result=P_THIS->GetAirTargetsInRange();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function GetAirTargetsInRange **********************************

// ********** Begin Class ARTSAirUnit Function GetRadarContacts ************************************
struct Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics
{
	struct RTSAirUnit_eventGetRadarContacts_Parms
	{
		TArray<ARTSUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Sensors" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventGetRadarContacts_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "GetRadarContacts", Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::RTSAirUnit_eventGetRadarContacts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::RTSAirUnit_eventGetRadarContacts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execGetRadarContacts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSUnit*>*)Z_Param__Result=P_THIS->GetRadarContacts();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function GetRadarContacts **************************************

// ********** Begin Class ARTSAirUnit Function GetVerticalSpeed ************************************
struct Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics
{
	struct RTSAirUnit_eventGetVerticalSpeed_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|State" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventGetVerticalSpeed_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "GetVerticalSpeed", Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::RTSAirUnit_eventGetVerticalSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::RTSAirUnit_eventGetVerticalSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execGetVerticalSpeed)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetVerticalSpeed();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function GetVerticalSpeed **************************************

// ********** Begin Class ARTSAirUnit Function HasFuel *********************************************
struct Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics
{
	struct RTSAirUnit_eventHasFuel_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Fuel" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simple fuel check (autonomous units don't need complex fuel management)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simple fuel check (autonomous units don't need complex fuel management)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAirUnit_eventHasFuel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAirUnit_eventHasFuel_Parms), &Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "HasFuel", Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::RTSAirUnit_eventHasFuel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::RTSAirUnit_eventHasFuel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_HasFuel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_HasFuel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execHasFuel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasFuel();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function HasFuel ***********************************************

// ********** Begin Class ARTSAirUnit Function IsAtCruiseAltitude **********************************
struct Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics
{
	struct RTSAirUnit_eventIsAtCruiseAltitude_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// State query functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "State query functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAirUnit_eventIsAtCruiseAltitude_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAirUnit_eventIsAtCruiseAltitude_Parms), &Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "IsAtCruiseAltitude", Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::RTSAirUnit_eventIsAtCruiseAltitude_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::RTSAirUnit_eventIsAtCruiseAltitude_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execIsAtCruiseAltitude)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAtCruiseAltitude();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function IsAtCruiseAltitude ************************************

// ********** Begin Class ARTSAirUnit Function IsClimbing ******************************************
struct Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics
{
	struct RTSAirUnit_eventIsClimbing_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|State" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAirUnit_eventIsClimbing_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAirUnit_eventIsClimbing_Parms), &Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "IsClimbing", Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::RTSAirUnit_eventIsClimbing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::RTSAirUnit_eventIsClimbing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_IsClimbing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_IsClimbing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execIsClimbing)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsClimbing();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function IsClimbing ********************************************

// ********** Begin Class ARTSAirUnit Function IsDescending ****************************************
struct Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics
{
	struct RTSAirUnit_eventIsDescending_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|State" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAirUnit_eventIsDescending_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAirUnit_eventIsDescending_Parms), &Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "IsDescending", Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::RTSAirUnit_eventIsDescending_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::RTSAirUnit_eventIsDescending_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_IsDescending()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_IsDescending_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execIsDescending)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDescending();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function IsDescending ******************************************

// ********** Begin Class ARTSAirUnit Function IsFormationLeader ***********************************
struct Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics
{
	struct RTSAirUnit_eventIsFormationLeader_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// IsInFormation() is now inherited from RTSUnit base class\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "IsInFormation() is now inherited from RTSUnit base class" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAirUnit_eventIsFormationLeader_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAirUnit_eventIsFormationLeader_Parms), &Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "IsFormationLeader", Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::RTSAirUnit_eventIsFormationLeader_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::RTSAirUnit_eventIsFormationLeader_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execIsFormationLeader)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsFormationLeader();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function IsFormationLeader *************************************

// ********** Begin Class ARTSAirUnit Function OnAirTargetEngaged **********************************
struct RTSAirUnit_eventOnAirTargetEngaged_Parms
{
	ARTSAirUnit* Target;
};
static FName NAME_ARTSAirUnit_OnAirTargetEngaged = FName(TEXT("OnAirTargetEngaged"));
void ARTSAirUnit::OnAirTargetEngaged(ARTSAirUnit* Target)
{
	RTSAirUnit_eventOnAirTargetEngaged_Parms Parms;
	Parms.Target=Target;
	UFunction* Func = FindFunctionChecked(NAME_ARTSAirUnit_OnAirTargetEngaged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Events" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventOnAirTargetEngaged_Parms, Target), Z_Construct_UClass_ARTSAirUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "OnAirTargetEngaged", Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics::PropPointers), sizeof(RTSAirUnit_eventOnAirTargetEngaged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSAirUnit_eventOnAirTargetEngaged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSAirUnit Function OnAirTargetEngaged ************************************

// ********** Begin Class ARTSAirUnit Function OnFormationJoined ***********************************
struct RTSAirUnit_eventOnFormationJoined_Parms
{
	ARTSAirUnit* Leader;
};
static FName NAME_ARTSAirUnit_OnFormationJoined = FName(TEXT("OnFormationJoined"));
void ARTSAirUnit::OnFormationJoined(ARTSAirUnit* Leader)
{
	RTSAirUnit_eventOnFormationJoined_Parms Parms;
	Parms.Leader=Leader;
	UFunction* Func = FindFunctionChecked(NAME_ARTSAirUnit_OnFormationJoined);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Leader;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics::NewProp_Leader = { "Leader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventOnFormationJoined_Parms, Leader), Z_Construct_UClass_ARTSAirUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics::NewProp_Leader,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "OnFormationJoined", Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics::PropPointers), sizeof(RTSAirUnit_eventOnFormationJoined_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSAirUnit_eventOnFormationJoined_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSAirUnit Function OnFormationJoined *************************************

// ********** Begin Class ARTSAirUnit Function OnFormationLeft *************************************
static FName NAME_ARTSAirUnit_OnFormationLeft = FName(TEXT("OnFormationLeft"));
void ARTSAirUnit::OnFormationLeft()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSAirUnit_OnFormationLeft);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSAirUnit_OnFormationLeft_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Events" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_OnFormationLeft_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "OnFormationLeft", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_OnFormationLeft_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_OnFormationLeft_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAirUnit_OnFormationLeft()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_OnFormationLeft_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSAirUnit Function OnFormationLeft ***************************************

// ********** Begin Class ARTSAirUnit Function OnRadarContactDetected ******************************
struct RTSAirUnit_eventOnRadarContactDetected_Parms
{
	ARTSUnit* Contact;
};
static FName NAME_ARTSAirUnit_OnRadarContactDetected = FName(TEXT("OnRadarContactDetected"));
void ARTSAirUnit::OnRadarContactDetected(ARTSUnit* Contact)
{
	RTSAirUnit_eventOnRadarContactDetected_Parms Parms;
	Parms.Contact=Contact;
	UFunction* Func = FindFunctionChecked(NAME_ARTSAirUnit_OnRadarContactDetected);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Events" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Contact;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics::NewProp_Contact = { "Contact", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventOnRadarContactDetected_Parms, Contact), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics::NewProp_Contact,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "OnRadarContactDetected", Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics::PropPointers), sizeof(RTSAirUnit_eventOnRadarContactDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSAirUnit_eventOnRadarContactDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSAirUnit Function OnRadarContactDetected ********************************

// ********** Begin Class ARTSAirUnit Function PerformEvasiveManeuvers *****************************
struct Z_Construct_UFunction_ARTSAirUnit_PerformEvasiveManeuvers_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Flight" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_PerformEvasiveManeuvers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "PerformEvasiveManeuvers", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_PerformEvasiveManeuvers_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_PerformEvasiveManeuvers_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAirUnit_PerformEvasiveManeuvers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_PerformEvasiveManeuvers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execPerformEvasiveManeuvers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PerformEvasiveManeuvers();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function PerformEvasiveManeuvers *******************************

// ********** Begin Class ARTSAirUnit Function SetAltitude *****************************************
struct Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics
{
	struct RTSAirUnit_eventSetAltitude_Parms
	{
		float TargetAltitude;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Flight" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simple flight control functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simple flight control functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetAltitude;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::NewProp_TargetAltitude = { "TargetAltitude", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventSetAltitude_Parms, TargetAltitude), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::NewProp_TargetAltitude,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "SetAltitude", Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::RTSAirUnit_eventSetAltitude_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::RTSAirUnit_eventSetAltitude_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_SetAltitude()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_SetAltitude_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execSetAltitude)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_TargetAltitude);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAltitude(Z_Param_TargetAltitude);
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function SetAltitude *******************************************

// ********** Begin Class ARTSAirUnit Function SetCruiseAltitude ***********************************
struct Z_Construct_UFunction_ARTSAirUnit_SetCruiseAltitude_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Flight" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_SetCruiseAltitude_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "SetCruiseAltitude", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_SetCruiseAltitude_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_SetCruiseAltitude_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAirUnit_SetCruiseAltitude()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_SetCruiseAltitude_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execSetCruiseAltitude)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCruiseAltitude();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function SetCruiseAltitude *************************************

// ********** Begin Class ARTSAirUnit Function SetFormationLeader **********************************
struct Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics
{
	struct RTSAirUnit_eventSetFormationLeader_Parms
	{
		TArray<ARTSAirUnit*> Followers;
		ERTSFormationType Formation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Formation" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Followers_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Followers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Followers;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Formation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Formation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::NewProp_Followers_Inner = { "Followers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSAirUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::NewProp_Followers = { "Followers", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventSetFormationLeader_Parms, Followers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Followers_MetaData), NewProp_Followers_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::NewProp_Formation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::NewProp_Formation = { "Formation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAirUnit_eventSetFormationLeader_Parms, Formation), Z_Construct_UEnum_ArmorWars_ERTSFormationType, METADATA_PARAMS(0, nullptr) }; // 1472012862
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::NewProp_Followers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::NewProp_Followers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::NewProp_Formation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::NewProp_Formation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "SetFormationLeader", Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::RTSAirUnit_eventSetFormationLeader_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::RTSAirUnit_eventSetFormationLeader_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execSetFormationLeader)
{
	P_GET_TARRAY_REF(ARTSAirUnit*,Z_Param_Out_Followers);
	P_GET_ENUM(ERTSFormationType,Z_Param_Formation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFormationLeader(Z_Param_Out_Followers,ERTSFormationType(Z_Param_Formation));
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function SetFormationLeader ************************************

// ********** Begin Class ARTSAirUnit Function ToggleRadar *****************************************
struct Z_Construct_UFunction_ARTSAirUnit_ToggleRadar_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Air Unit|Sensors" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sensor functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sensor functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSAirUnit_ToggleRadar_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSAirUnit, nullptr, "ToggleRadar", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSAirUnit_ToggleRadar_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSAirUnit_ToggleRadar_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSAirUnit_ToggleRadar()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSAirUnit_ToggleRadar_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSAirUnit::execToggleRadar)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ToggleRadar();
	P_NATIVE_END;
}
// ********** End Class ARTSAirUnit Function ToggleRadar *******************************************

// ********** Begin Class ARTSAirUnit **************************************************************
void ARTSAirUnit::StaticRegisterNativesARTSAirUnit()
{
	UClass* Class = ARTSAirUnit::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanDetectTarget", &ARTSAirUnit::execCanDetectTarget },
		{ "EngageAirTarget", &ARTSAirUnit::execEngageAirTarget },
		{ "EngageGroundTarget", &ARTSAirUnit::execEngageGroundTarget },
		{ "FindNearestAirThreat", &ARTSAirUnit::execFindNearestAirThreat },
		{ "GetAirTargetsInRange", &ARTSAirUnit::execGetAirTargetsInRange },
		{ "GetRadarContacts", &ARTSAirUnit::execGetRadarContacts },
		{ "GetVerticalSpeed", &ARTSAirUnit::execGetVerticalSpeed },
		{ "HasFuel", &ARTSAirUnit::execHasFuel },
		{ "IsAtCruiseAltitude", &ARTSAirUnit::execIsAtCruiseAltitude },
		{ "IsClimbing", &ARTSAirUnit::execIsClimbing },
		{ "IsDescending", &ARTSAirUnit::execIsDescending },
		{ "IsFormationLeader", &ARTSAirUnit::execIsFormationLeader },
		{ "PerformEvasiveManeuvers", &ARTSAirUnit::execPerformEvasiveManeuvers },
		{ "SetAltitude", &ARTSAirUnit::execSetAltitude },
		{ "SetCruiseAltitude", &ARTSAirUnit::execSetCruiseAltitude },
		{ "SetFormationLeader", &ARTSAirUnit::execSetFormationLeader },
		{ "ToggleRadar", &ARTSAirUnit::execToggleRadar },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ARTSAirUnit;
UClass* ARTSAirUnit::GetPrivateStaticClass()
{
	using TClass = ARTSAirUnit;
	if (!Z_Registration_Info_UClass_ARTSAirUnit.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSAirUnit"),
			Z_Registration_Info_UClass_ARTSAirUnit.InnerSingleton,
			StaticRegisterNativesARTSAirUnit,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ARTSAirUnit.InnerSingleton;
}
UClass* Z_Construct_UClass_ARTSAirUnit_NoRegister()
{
	return ARTSAirUnit::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ARTSAirUnit_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Specialized air unit class with aircraft-specific functionality\n * Inherits from RTSUnit and adds flight controls, air combat, and formation flying\n */" },
#endif
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "RTSAirUnit.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Specialized air unit class with aircraft-specific functionality\nInherits from RTSUnit and adds flight controls, air combat, and formation flying" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ServiceCeiling_MetaData[] = {
		{ "Category", "Air Unit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Service ceiling (maximum operational altitude)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Service ceiling (maximum operational altitude)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CruiseAltitude_MetaData[] = {
		{ "Category", "Air Unit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Optimal cruise altitude\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimal cruise altitude" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationType_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation flying\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation flying" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationLeader_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation leader (if following in formation)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation leader (if following in formation)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AirToAirRange_MetaData[] = {
		{ "Category", "Air Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Air-to-air engagement range\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Air-to-air engagement range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AirToGroundRange_MetaData[] = {
		{ "Category", "Air Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Air-to-ground engagement range\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Air-to-ground engagement range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanEngageAirTargets_MetaData[] = {
		{ "Category", "Air Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this aircraft can engage air targets\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this aircraft can engage air targets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanEngageGroundTargets_MetaData[] = {
		{ "Category", "Air Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this aircraft can engage ground targets\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this aircraft can engage ground targets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RadarRange_MetaData[] = {
		{ "Category", "Sensors" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Radar range for target detection\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Radar range for target detection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRadarActive_MetaData[] = {
		{ "Category", "Sensors" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether radar is active\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether radar is active" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlightComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AirMovementComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFormationChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAirCombatEngaged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSAirUnit.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ServiceCeiling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CruiseAltitude;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FormationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FormationType;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_FormationLeader;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AirToAirRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AirToGroundRange;
	static void NewProp_bCanEngageAirTargets_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanEngageAirTargets;
	static void NewProp_bCanEngageGroundTargets_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanEngageGroundTargets;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RadarRange;
	static void NewProp_bRadarActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRadarActive;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlightComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AirMovementComponent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFormationChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAirCombatEngaged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARTSAirUnit_CanDetectTarget, "CanDetectTarget" }, // 159663609
		{ &Z_Construct_UFunction_ARTSAirUnit_EngageAirTarget, "EngageAirTarget" }, // 3771864889
		{ &Z_Construct_UFunction_ARTSAirUnit_EngageGroundTarget, "EngageGroundTarget" }, // 3767268306
		{ &Z_Construct_UFunction_ARTSAirUnit_FindNearestAirThreat, "FindNearestAirThreat" }, // 1445922479
		{ &Z_Construct_UFunction_ARTSAirUnit_GetAirTargetsInRange, "GetAirTargetsInRange" }, // 498873510
		{ &Z_Construct_UFunction_ARTSAirUnit_GetRadarContacts, "GetRadarContacts" }, // 4081437440
		{ &Z_Construct_UFunction_ARTSAirUnit_GetVerticalSpeed, "GetVerticalSpeed" }, // 1718060395
		{ &Z_Construct_UFunction_ARTSAirUnit_HasFuel, "HasFuel" }, // 2668507681
		{ &Z_Construct_UFunction_ARTSAirUnit_IsAtCruiseAltitude, "IsAtCruiseAltitude" }, // 962780799
		{ &Z_Construct_UFunction_ARTSAirUnit_IsClimbing, "IsClimbing" }, // 3619447502
		{ &Z_Construct_UFunction_ARTSAirUnit_IsDescending, "IsDescending" }, // 2236981347
		{ &Z_Construct_UFunction_ARTSAirUnit_IsFormationLeader, "IsFormationLeader" }, // 4184109470
		{ &Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature, "OnAirCombatEngaged__DelegateSignature" }, // 1170876763
		{ &Z_Construct_UFunction_ARTSAirUnit_OnAirTargetEngaged, "OnAirTargetEngaged" }, // 3359524157
		{ &Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature, "OnFormationChanged__DelegateSignature" }, // 3143153822
		{ &Z_Construct_UFunction_ARTSAirUnit_OnFormationJoined, "OnFormationJoined" }, // 629129966
		{ &Z_Construct_UFunction_ARTSAirUnit_OnFormationLeft, "OnFormationLeft" }, // 1966991286
		{ &Z_Construct_UFunction_ARTSAirUnit_OnRadarContactDetected, "OnRadarContactDetected" }, // 2266761901
		{ &Z_Construct_UFunction_ARTSAirUnit_PerformEvasiveManeuvers, "PerformEvasiveManeuvers" }, // 4042414415
		{ &Z_Construct_UFunction_ARTSAirUnit_SetAltitude, "SetAltitude" }, // 1354857861
		{ &Z_Construct_UFunction_ARTSAirUnit_SetCruiseAltitude, "SetCruiseAltitude" }, // 3204147043
		{ &Z_Construct_UFunction_ARTSAirUnit_SetFormationLeader, "SetFormationLeader" }, // 2525195210
		{ &Z_Construct_UFunction_ARTSAirUnit_ToggleRadar, "ToggleRadar" }, // 2093415142
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARTSAirUnit>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_ServiceCeiling = { "ServiceCeiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, ServiceCeiling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ServiceCeiling_MetaData), NewProp_ServiceCeiling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_CruiseAltitude = { "CruiseAltitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, CruiseAltitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CruiseAltitude_MetaData), NewProp_CruiseAltitude_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_FormationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_FormationType = { "FormationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, FormationType), Z_Construct_UEnum_ArmorWars_ERTSFormationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationType_MetaData), NewProp_FormationType_MetaData) }; // 1472012862
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_FormationLeader = { "FormationLeader", nullptr, (EPropertyFlags)0x0014000000000004, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, FormationLeader), Z_Construct_UClass_ARTSAirUnit_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationLeader_MetaData), NewProp_FormationLeader_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_AirToAirRange = { "AirToAirRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, AirToAirRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AirToAirRange_MetaData), NewProp_AirToAirRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_AirToGroundRange = { "AirToGroundRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, AirToGroundRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AirToGroundRange_MetaData), NewProp_AirToGroundRange_MetaData) };
void Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bCanEngageAirTargets_SetBit(void* Obj)
{
	((ARTSAirUnit*)Obj)->bCanEngageAirTargets = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bCanEngageAirTargets = { "bCanEngageAirTargets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAirUnit), &Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bCanEngageAirTargets_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanEngageAirTargets_MetaData), NewProp_bCanEngageAirTargets_MetaData) };
void Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bCanEngageGroundTargets_SetBit(void* Obj)
{
	((ARTSAirUnit*)Obj)->bCanEngageGroundTargets = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bCanEngageGroundTargets = { "bCanEngageGroundTargets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAirUnit), &Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bCanEngageGroundTargets_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanEngageGroundTargets_MetaData), NewProp_bCanEngageGroundTargets_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_RadarRange = { "RadarRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, RadarRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RadarRange_MetaData), NewProp_RadarRange_MetaData) };
void Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bRadarActive_SetBit(void* Obj)
{
	((ARTSAirUnit*)Obj)->bRadarActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bRadarActive = { "bRadarActive", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSAirUnit), &Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bRadarActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRadarActive_MetaData), NewProp_bRadarActive_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_FlightComponent = { "FlightComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, FlightComponent), Z_Construct_UClass_URTSAircraftFlightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlightComponent_MetaData), NewProp_FlightComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_AirMovementComponent = { "AirMovementComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, AirMovementComponent), Z_Construct_UClass_URTSAirMovementComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AirMovementComponent_MetaData), NewProp_AirMovementComponent_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_OnFormationChanged = { "OnFormationChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, OnFormationChanged), Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFormationChanged_MetaData), NewProp_OnFormationChanged_MetaData) }; // 3143153822
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_OnAirCombatEngaged = { "OnAirCombatEngaged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSAirUnit, OnAirCombatEngaged), Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAirCombatEngaged_MetaData), NewProp_OnAirCombatEngaged_MetaData) }; // 1170876763
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARTSAirUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_ServiceCeiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_CruiseAltitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_FormationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_FormationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_FormationLeader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_AirToAirRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_AirToGroundRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bCanEngageAirTargets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bCanEngageGroundTargets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_RadarRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_bRadarActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_FlightComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_AirMovementComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_OnFormationChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSAirUnit_Statics::NewProp_OnAirCombatEngaged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAirUnit_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARTSAirUnit_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ARTSUnit,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAirUnit_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARTSAirUnit_Statics::ClassParams = {
	&ARTSAirUnit::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARTSAirUnit_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAirUnit_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSAirUnit_Statics::Class_MetaDataParams), Z_Construct_UClass_ARTSAirUnit_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARTSAirUnit()
{
	if (!Z_Registration_Info_UClass_ARTSAirUnit.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARTSAirUnit.OuterSingleton, Z_Construct_UClass_ARTSAirUnit_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARTSAirUnit.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARTSAirUnit);
ARTSAirUnit::~ARTSAirUnit() {}
// ********** End Class ARTSAirUnit ****************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAirUnit_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARTSAirUnit, ARTSAirUnit::StaticClass, TEXT("ARTSAirUnit"), &Z_Registration_Info_UClass_ARTSAirUnit, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARTSAirUnit), 1158798423U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAirUnit_h__Script_ArmorWars_4011250119(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAirUnit_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAirUnit_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

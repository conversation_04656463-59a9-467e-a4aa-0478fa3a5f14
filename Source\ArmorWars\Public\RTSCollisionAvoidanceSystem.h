#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Subsystems/WorldSubsystem.h"
#include "RTSCollisionAvoidanceSystem.generated.h"

class ARTSUnit;

// Collision avoidance parameters
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSCollisionAvoidanceParams
{
    GENERATED_BODY()

    // Avoidance radii
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    float PersonalSpaceRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    float AvoidanceRadius = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    float LookAheadDistance = 300.0f;

    // Force weights
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    float SeparationWeight = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    float AlignmentWeight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    float CohesionWeight = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    float ObstacleAvoidanceWeight = 4.0f;

    // Performance settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxNearbyUnits = 15;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float UpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseFormationAwareness = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseVelocityMatching = true;

    // Constructor
    FRTSCollisionAvoidanceParams()
    {
        PersonalSpaceRadius = 100.0f;
        AvoidanceRadius = 200.0f;
        LookAheadDistance = 300.0f;
        SeparationWeight = 3.0f;
        AlignmentWeight = 1.0f;
        CohesionWeight = 0.5f;
        ObstacleAvoidanceWeight = 4.0f;
        MaxNearbyUnits = 15;
        UpdateInterval = 0.1f;
        bUseFormationAwareness = true;
        bUseVelocityMatching = true;
    }
};

// Spatial grid cell data
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSSpatialGridCell
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Spatial Grid")
    TArray<TSoftObjectPtr<ARTSUnit>> Units;

    // Constructor
    FRTSSpatialGridCell()
    {
    }
};

// Collision avoidance data for a unit
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSCollisionAvoidanceData
{
    GENERATED_BODY()

    // Current avoidance forces
    UPROPERTY(BlueprintReadOnly, Category = "Avoidance Data")
    FVector SeparationForce = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly, Category = "Avoidance Data")
    FVector AlignmentForce = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly, Category = "Avoidance Data")
    FVector CohesionForce = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly, Category = "Avoidance Data")
    FVector ObstacleAvoidanceForce = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly, Category = "Avoidance Data")
    FVector TotalAvoidanceForce = FVector::ZeroVector;

    // Nearby units
    UPROPERTY(BlueprintReadOnly, Category = "Avoidance Data")
    TArray<TSoftObjectPtr<ARTSUnit>> NearbyUnits;

    // Update tracking
    UPROPERTY(BlueprintReadOnly, Category = "Avoidance Data")
    float LastUpdateTime = 0.0f;

    // Constructor
    FRTSCollisionAvoidanceData()
    {
        SeparationForce = FVector::ZeroVector;
        AlignmentForce = FVector::ZeroVector;
        CohesionForce = FVector::ZeroVector;
        ObstacleAvoidanceForce = FVector::ZeroVector;
        TotalAvoidanceForce = FVector::ZeroVector;
        LastUpdateTime = 0.0f;
    }
};

/**
 * Component for individual unit collision avoidance
 * Handles local collision avoidance calculations and force application
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class ARMORWARS_API URTSCollisionAvoidanceComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    URTSCollisionAvoidanceComponent();

protected:
    virtual void BeginPlay() override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Avoidance parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collision Avoidance")
    FRTSCollisionAvoidanceParams AvoidanceParams;

    // Current avoidance data
    UPROPERTY(BlueprintReadOnly, Category = "Collision Avoidance")
    FRTSCollisionAvoidanceData AvoidanceData;

    // Component settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    bool bEnableCollisionAvoidance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    bool bEnableDebugVisualization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    bool bRespectFormationPositions = true;

public:
    // Main avoidance functions
    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance")
    FVector CalculateAvoidanceForce();

    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance")
    FVector GetAdjustedMovementDirection(const FVector& DesiredDirection);

    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance")
    void UpdateAvoidanceData();

    // Force calculation functions
    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance")
    FVector CalculateSeparationForce();

    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance")
    FVector CalculateAlignmentForce();

    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance")
    FVector CalculateCohesionForce();

    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance")
    FVector CalculateObstacleAvoidanceForce();

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance")
    TArray<ARTSUnit*> FindNearbyUnits();

    UFUNCTION(BlueprintPure, Category = "RTS Collision Avoidance")
    bool IsUnitInPersonalSpace(ARTSUnit* OtherUnit) const;

    UFUNCTION(BlueprintPure, Category = "RTS Collision Avoidance")
    bool IsUnitInAvoidanceRange(ARTSUnit* OtherUnit) const;

    UFUNCTION(BlueprintPure, Category = "RTS Collision Avoidance")
    float GetDistanceToUnit(ARTSUnit* OtherUnit) const;

    UFUNCTION(BlueprintPure, Category = "RTS Collision Avoidance")
    ARTSUnit* GetOwnerUnit() const;

protected:
    // Internal helper functions
    virtual void DrawDebugVisualization();
    virtual bool ShouldAvoidUnit(ARTSUnit* OtherUnit) const;
    virtual float GetAvoidanceWeight(ARTSUnit* OtherUnit) const;
    virtual FVector PredictCollision(ARTSUnit* OtherUnit) const;
    virtual bool IsFormationMate(ARTSUnit* OtherUnit) const;
};

/**
 * World subsystem for managing global collision avoidance
 * Optimizes collision avoidance calculations across all units
 */
UCLASS(BlueprintType)
class ARMORWARS_API URTSCollisionAvoidanceManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    URTSCollisionAvoidanceManager();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    void Tick(float DeltaTime);
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override { return true; }

protected:
    // Registered units for collision avoidance
    UPROPERTY(BlueprintReadOnly, Category = "Collision Avoidance")
    TArray<TSoftObjectPtr<ARTSUnit>> RegisteredUnits;

    // Spatial partitioning for optimization
    UPROPERTY(BlueprintReadOnly, Category = "Collision Avoidance")
    TMap<FIntPoint, FRTSSpatialGridCell> SpatialGrid;

    // Manager settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Manager Settings")
    float GridCellSize = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Manager Settings")
    float GlobalUpdateInterval = 0.05f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Manager Settings")
    bool bUseOptimizedCalculations = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Manager Settings")
    bool bEnableDebugLogging = false;

public:
    // Unit registration
    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance Manager")
    void RegisterUnit(ARTSUnit* Unit);

    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance Manager")
    void UnregisterUnit(ARTSUnit* Unit);

    // Spatial queries
    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance Manager")
    TArray<ARTSUnit*> GetUnitsInRadius(const FVector& Location, float Radius);

    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance Manager")
    TArray<ARTSUnit*> GetUnitsInCell(const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance Manager")
    TArray<ARTSUnit*> GetNearbyUnits(ARTSUnit* Unit, float Radius);

    // Optimization functions
    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance Manager")
    void UpdateSpatialGrid();

    UFUNCTION(BlueprintCallable, Category = "RTS Collision Avoidance Manager")
    void CleanupInvalidUnits();

    // Utility functions
    UFUNCTION(BlueprintPure, Category = "RTS Collision Avoidance Manager")
    FIntPoint WorldLocationToGridCell(const FVector& WorldLocation) const;

    UFUNCTION(BlueprintPure, Category = "RTS Collision Avoidance Manager")
    FVector GridCellToWorldLocation(const FIntPoint& GridCell) const;

    UFUNCTION(BlueprintPure, Category = "RTS Collision Avoidance Manager")
    int32 GetRegisteredUnitCount() const;

protected:
    // Internal state
    float LastGlobalUpdate = 0.0f;

    // Helper functions
    virtual void AddUnitToSpatialGrid(ARTSUnit* Unit);
    virtual void RemoveUnitFromSpatialGrid(ARTSUnit* Unit);
    virtual TArray<FIntPoint> GetCellsInRadius(const FVector& Location, float Radius) const;
};

// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSBlackboardSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSBlackboardSystem() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSBlackboardComponent();
ARMORWARS_API UClass* Z_Construct_UClass_URTSBlackboardComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandComponent_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSBlackboardEntry();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSBlackboardKeyType *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSBlackboardKeyType;
static UEnum* ERTSBlackboardKeyType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSBlackboardKeyType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSBlackboardKeyType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSBlackboardKeyType"));
	}
	return Z_Registration_Info_UEnum_ERTSBlackboardKeyType.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSBlackboardKeyType>()
{
	return ERTSBlackboardKeyType_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Bool.DisplayName", "Boolean" },
		{ "Bool.Name", "ERTSBlackboardKeyType::Bool" },
		{ "Class.DisplayName", "Class" },
		{ "Class.Name", "ERTSBlackboardKeyType::Class" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blackboard key types for type safety\n" },
#endif
		{ "Enum.DisplayName", "Enum" },
		{ "Enum.Name", "ERTSBlackboardKeyType::Enum" },
		{ "Float.DisplayName", "Float" },
		{ "Float.Name", "ERTSBlackboardKeyType::Float" },
		{ "Int.DisplayName", "Integer" },
		{ "Int.Name", "ERTSBlackboardKeyType::Int" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
		{ "Object.DisplayName", "Object" },
		{ "Object.Name", "ERTSBlackboardKeyType::Object" },
		{ "Rotator.DisplayName", "Rotator" },
		{ "Rotator.Name", "ERTSBlackboardKeyType::Rotator" },
		{ "String.DisplayName", "String" },
		{ "String.Name", "ERTSBlackboardKeyType::String" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blackboard key types for type safety" },
#endif
		{ "Vector.DisplayName", "Vector" },
		{ "Vector.Name", "ERTSBlackboardKeyType::Vector" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSBlackboardKeyType::Bool", (int64)ERTSBlackboardKeyType::Bool },
		{ "ERTSBlackboardKeyType::Int", (int64)ERTSBlackboardKeyType::Int },
		{ "ERTSBlackboardKeyType::Float", (int64)ERTSBlackboardKeyType::Float },
		{ "ERTSBlackboardKeyType::String", (int64)ERTSBlackboardKeyType::String },
		{ "ERTSBlackboardKeyType::Vector", (int64)ERTSBlackboardKeyType::Vector },
		{ "ERTSBlackboardKeyType::Rotator", (int64)ERTSBlackboardKeyType::Rotator },
		{ "ERTSBlackboardKeyType::Object", (int64)ERTSBlackboardKeyType::Object },
		{ "ERTSBlackboardKeyType::Class", (int64)ERTSBlackboardKeyType::Class },
		{ "ERTSBlackboardKeyType::Enum", (int64)ERTSBlackboardKeyType::Enum },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSBlackboardKeyType",
	"ERTSBlackboardKeyType",
	Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType()
{
	if (!Z_Registration_Info_UEnum_ERTSBlackboardKeyType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSBlackboardKeyType.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSBlackboardKeyType.InnerSingleton;
}
// ********** End Enum ERTSBlackboardKeyType *******************************************************

// ********** Begin ScriptStruct FRTSBlackboardEntry ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSBlackboardEntry;
class UScriptStruct* FRTSBlackboardEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSBlackboardEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSBlackboardEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSBlackboardEntry, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSBlackboardEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSBlackboardEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blackboard entry structure\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blackboard entry structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_KeyType_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StringValue_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BoolValue_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IntValue_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FloatValue_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VectorValue_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotatorValue_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectValue_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FBytePropertyParams NewProp_KeyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_KeyType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StringValue;
	static void NewProp_BoolValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_BoolValue;
	static const UECodeGen_Private::FIntPropertyParams NewProp_IntValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FloatValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VectorValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RotatorValue;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ObjectValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSBlackboardEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSBlackboardEntry, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_KeyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_KeyType = { "KeyType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSBlackboardEntry, KeyType), Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_KeyType_MetaData), NewProp_KeyType_MetaData) }; // 1126800597
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_StringValue = { "StringValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSBlackboardEntry, StringValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StringValue_MetaData), NewProp_StringValue_MetaData) };
void Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_BoolValue_SetBit(void* Obj)
{
	((FRTSBlackboardEntry*)Obj)->BoolValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_BoolValue = { "BoolValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSBlackboardEntry), &Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_BoolValue_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BoolValue_MetaData), NewProp_BoolValue_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_IntValue = { "IntValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSBlackboardEntry, IntValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IntValue_MetaData), NewProp_IntValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_FloatValue = { "FloatValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSBlackboardEntry, FloatValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FloatValue_MetaData), NewProp_FloatValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_VectorValue = { "VectorValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSBlackboardEntry, VectorValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VectorValue_MetaData), NewProp_VectorValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_RotatorValue = { "RotatorValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSBlackboardEntry, RotatorValue), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotatorValue_MetaData), NewProp_RotatorValue_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_ObjectValue = { "ObjectValue", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSBlackboardEntry, ObjectValue), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectValue_MetaData), NewProp_ObjectValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_KeyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_KeyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_StringValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_BoolValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_IntValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_FloatValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_VectorValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_RotatorValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewProp_ObjectValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSBlackboardEntry",
	Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::PropPointers),
	sizeof(FRTSBlackboardEntry),
	alignof(FRTSBlackboardEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSBlackboardEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSBlackboardEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSBlackboardEntry.InnerSingleton, Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSBlackboardEntry.InnerSingleton;
}
// ********** End ScriptStruct FRTSBlackboardEntry *************************************************

// ********** Begin Class URTSBlackboardComponent Function AddPredefinedKey ************************
struct Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics
{
	struct RTSBlackboardComponent_eventAddPredefinedKey_Parms
	{
		FString Key;
		ERTSBlackboardKeyType KeyType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FBytePropertyParams NewProp_KeyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_KeyType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventAddPredefinedKey_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::NewProp_KeyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::NewProp_KeyType = { "KeyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventAddPredefinedKey_Parms, KeyType), Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType, METADATA_PARAMS(0, nullptr) }; // 1126800597
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::NewProp_KeyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::NewProp_KeyType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "AddPredefinedKey", Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::RTSBlackboardComponent_eventAddPredefinedKey_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::RTSBlackboardComponent_eventAddPredefinedKey_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execAddPredefinedKey)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_ENUM(ERTSBlackboardKeyType,Z_Param_KeyType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddPredefinedKey(Z_Param_Key,ERTSBlackboardKeyType(Z_Param_KeyType));
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function AddPredefinedKey **************************

// ********** Begin Class URTSBlackboardComponent Function ClearAllKeys ****************************
struct Z_Construct_UFunction_URTSBlackboardComponent_ClearAllKeys_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_ClearAllKeys_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "ClearAllKeys", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_ClearAllKeys_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_ClearAllKeys_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_ClearAllKeys()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_ClearAllKeys_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execClearAllKeys)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllKeys();
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function ClearAllKeys ******************************

// ********** Begin Class URTSBlackboardComponent Function GetActor ********************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics
{
	struct RTSBlackboardComponent_eventGetActor_Parms
	{
		FString Key;
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Specialized getters for common types\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Specialized getters for common types" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetActor_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetActor_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetActor", Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::RTSBlackboardComponent_eventGetActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::RTSBlackboardComponent_eventGetActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetActor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->GetActor(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetActor **********************************

// ********** Begin Class URTSBlackboardComponent Function GetAllKeys ******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics
{
	struct RTSBlackboardComponent_eventGetAllKeys_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetAllKeys_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetAllKeys", Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::RTSBlackboardComponent_eventGetAllKeys_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::RTSBlackboardComponent_eventGetAllKeys_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetAllKeys)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAllKeys();
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetAllKeys ********************************

// ********** Begin Class URTSBlackboardComponent Function GetBool *********************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics
{
	struct RTSBlackboardComponent_eventGetBool_Parms
	{
		FString Key;
		bool DefaultValue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Generic value getters\n" },
#endif
		{ "CPP_Default_DefaultValue", "false" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generic value getters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static void NewProp_DefaultValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_DefaultValue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetBool_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
void Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::NewProp_DefaultValue_SetBit(void* Obj)
{
	((RTSBlackboardComponent_eventGetBool_Parms*)Obj)->DefaultValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBlackboardComponent_eventGetBool_Parms), &Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::NewProp_DefaultValue_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBlackboardComponent_eventGetBool_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBlackboardComponent_eventGetBool_Parms), &Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetBool", Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::RTSBlackboardComponent_eventGetBool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::RTSBlackboardComponent_eventGetBool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetBool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetBool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetBool)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_UBOOL(Z_Param_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GetBool(Z_Param_Key,Z_Param_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetBool ***********************************

// ********** Begin Class URTSBlackboardComponent Function GetFloat ********************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics
{
	struct RTSBlackboardComponent_eventGetFloat_Parms
	{
		FString Key;
		float DefaultValue;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "CPP_Default_DefaultValue", "0.000000" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetFloat_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetFloat_Parms, DefaultValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetFloat_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetFloat", Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::RTSBlackboardComponent_eventGetFloat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::RTSBlackboardComponent_eventGetFloat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetFloat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetFloat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetFloat)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFloat(Z_Param_Key,Z_Param_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetFloat **********************************

// ********** Begin Class URTSBlackboardComponent Function GetInt **********************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics
{
	struct RTSBlackboardComponent_eventGetInt_Parms
	{
		FString Key;
		int32 DefaultValue;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "CPP_Default_DefaultValue", "0" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetInt_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetInt_Parms, DefaultValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetInt_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetInt", Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::RTSBlackboardComponent_eventGetInt_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::RTSBlackboardComponent_eventGetInt_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetInt()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetInt_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetInt)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FIntProperty,Z_Param_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetInt(Z_Param_Key,Z_Param_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetInt ************************************

// ********** Begin Class URTSBlackboardComponent Function GetKeyType ******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics
{
	struct RTSBlackboardComponent_eventGetKeyType_Parms
	{
		FString Key;
		ERTSBlackboardKeyType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetKeyType_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetKeyType_Parms, ReturnValue), Z_Construct_UEnum_ArmorWars_ERTSBlackboardKeyType, METADATA_PARAMS(0, nullptr) }; // 1126800597
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetKeyType", Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::RTSBlackboardComponent_eventGetKeyType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::RTSBlackboardComponent_eventGetKeyType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetKeyType)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ERTSBlackboardKeyType*)Z_Param__Result=P_THIS->GetKeyType(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetKeyType ********************************

// ********** Begin Class URTSBlackboardComponent Function GetObject *******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics
{
	struct RTSBlackboardComponent_eventGetObject_Parms
	{
		FString Key;
		UObject* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetObject_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetObject_Parms, ReturnValue), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetObject", Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::RTSBlackboardComponent_eventGetObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::RTSBlackboardComponent_eventGetObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetObject)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UObject**)Z_Param__Result=P_THIS->GetObject(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetObject *********************************

// ********** Begin Class URTSBlackboardComponent Function GetOwnerCommandComponent ****************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics
{
	struct RTSBlackboardComponent_eventGetOwnerCommandComponent_Parms
	{
		URTSCommandComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetOwnerCommandComponent_Parms, ReturnValue), Z_Construct_UClass_URTSCommandComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetOwnerCommandComponent", Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::RTSBlackboardComponent_eventGetOwnerCommandComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::RTSBlackboardComponent_eventGetOwnerCommandComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetOwnerCommandComponent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSCommandComponent**)Z_Param__Result=P_THIS->GetOwnerCommandComponent();
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetOwnerCommandComponent ******************

// ********** Begin Class URTSBlackboardComponent Function GetOwnerUnit ****************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics
{
	struct RTSBlackboardComponent_eventGetOwnerUnit_Parms
	{
		ARTSUnit* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetOwnerUnit_Parms, ReturnValue), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetOwnerUnit", Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::RTSBlackboardComponent_eventGetOwnerUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::RTSBlackboardComponent_eventGetOwnerUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetOwnerUnit)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSUnit**)Z_Param__Result=P_THIS->GetOwnerUnit();
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetOwnerUnit ******************************

// ********** Begin Class URTSBlackboardComponent Function GetRotator ******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics
{
	struct RTSBlackboardComponent_eventGetRotator_Parms
	{
		FString Key;
		FRotator DefaultValue;
		FRotator ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "CPP_Default_DefaultValue", "" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetRotator_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetRotator_Parms, DefaultValue), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetRotator_Parms, ReturnValue), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetRotator", Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::RTSBlackboardComponent_eventGetRotator_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::RTSBlackboardComponent_eventGetRotator_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetRotator()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetRotator_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetRotator)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FRotator*)Z_Param__Result=P_THIS->GetRotator(Z_Param_Key,Z_Param_Out_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetRotator ********************************

// ********** Begin Class URTSBlackboardComponent Function GetString *******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics
{
	struct RTSBlackboardComponent_eventGetString_Parms
	{
		FString Key;
		FString DefaultValue;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "CPP_Default_DefaultValue", "" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetString_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetString_Parms, DefaultValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetString", Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::RTSBlackboardComponent_eventGetString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::RTSBlackboardComponent_eventGetString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetString)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FStrProperty,Z_Param_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetString(Z_Param_Key,Z_Param_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetString *********************************

// ********** Begin Class URTSBlackboardComponent Function GetUnit *********************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics
{
	struct RTSBlackboardComponent_eventGetUnit_Parms
	{
		FString Key;
		ARTSUnit* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetUnit_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetUnit_Parms, ReturnValue), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetUnit", Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::RTSBlackboardComponent_eventGetUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::RTSBlackboardComponent_eventGetUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetUnit)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSUnit**)Z_Param__Result=P_THIS->GetUnit(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetUnit ***********************************

// ********** Begin Class URTSBlackboardComponent Function GetVector *******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics
{
	struct RTSBlackboardComponent_eventGetVector_Parms
	{
		FString Key;
		FVector DefaultValue;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "CPP_Default_DefaultValue", "" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetVector_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetVector_Parms, DefaultValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventGetVector_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "GetVector", Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::RTSBlackboardComponent_eventGetVector_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::RTSBlackboardComponent_eventGetVector_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_GetVector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_GetVector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execGetVector)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetVector(Z_Param_Key,Z_Param_Out_DefaultValue);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function GetVector *********************************

// ********** Begin Class URTSBlackboardComponent Function HasKey **********************************
struct Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics
{
	struct RTSBlackboardComponent_eventHasKey_Parms
	{
		FString Key;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Key management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Key management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventHasKey_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
void Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBlackboardComponent_eventHasKey_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBlackboardComponent_eventHasKey_Parms), &Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "HasKey", Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::RTSBlackboardComponent_eventHasKey_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::RTSBlackboardComponent_eventHasKey_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_HasKey()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_HasKey_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execHasKey)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasKey(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function HasKey ************************************

// ********** Begin Class URTSBlackboardComponent Function InitializePredefinedKeys ****************
struct Z_Construct_UFunction_URTSBlackboardComponent_InitializePredefinedKeys_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Predefined key management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Predefined key management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_InitializePredefinedKeys_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "InitializePredefinedKeys", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_InitializePredefinedKeys_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_InitializePredefinedKeys_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_InitializePredefinedKeys()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_InitializePredefinedKeys_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execInitializePredefinedKeys)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePredefinedKeys();
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function InitializePredefinedKeys ******************

// ********** Begin Class URTSBlackboardComponent Function RemoveKey *******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics
{
	struct RTSBlackboardComponent_eventRemoveKey_Parms
	{
		FString Key;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventRemoveKey_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::NewProp_Key,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "RemoveKey", Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::RTSBlackboardComponent_eventRemoveKey_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::RTSBlackboardComponent_eventRemoveKey_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execRemoveKey)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveKey(Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function RemoveKey *********************************

// ********** Begin Class URTSBlackboardComponent Function SetBool *********************************
struct Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics
{
	struct RTSBlackboardComponent_eventSetBool_Parms
	{
		FString Key;
		bool Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Generic value setters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generic value setters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static void NewProp_Value_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetBool_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
void Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::NewProp_Value_SetBit(void* Obj)
{
	((RTSBlackboardComponent_eventSetBool_Parms*)Obj)->Value = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBlackboardComponent_eventSetBool_Parms), &Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::NewProp_Value_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "SetBool", Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::RTSBlackboardComponent_eventSetBool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::RTSBlackboardComponent_eventSetBool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_SetBool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_SetBool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execSetBool)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_UBOOL(Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBool(Z_Param_Key,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function SetBool ***********************************

// ********** Begin Class URTSBlackboardComponent Function SetFloat ********************************
struct Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics
{
	struct RTSBlackboardComponent_eventSetFloat_Parms
	{
		FString Key;
		float Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetFloat_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetFloat_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "SetFloat", Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::RTSBlackboardComponent_eventSetFloat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::RTSBlackboardComponent_eventSetFloat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_SetFloat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_SetFloat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execSetFloat)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFloat(Z_Param_Key,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function SetFloat **********************************

// ********** Begin Class URTSBlackboardComponent Function SetInt **********************************
struct Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics
{
	struct RTSBlackboardComponent_eventSetInt_Parms
	{
		FString Key;
		int32 Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetInt_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetInt_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "SetInt", Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::RTSBlackboardComponent_eventSetInt_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::RTSBlackboardComponent_eventSetInt_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_SetInt()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_SetInt_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execSetInt)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FIntProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetInt(Z_Param_Key,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function SetInt ************************************

// ********** Begin Class URTSBlackboardComponent Function SetObject *******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics
{
	struct RTSBlackboardComponent_eventSetObject_Parms
	{
		FString Key;
		UObject* Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetObject_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetObject_Parms, Value), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "SetObject", Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::RTSBlackboardComponent_eventSetObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::RTSBlackboardComponent_eventSetObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_SetObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_SetObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execSetObject)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_OBJECT(UObject,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetObject(Z_Param_Key,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function SetObject *********************************

// ********** Begin Class URTSBlackboardComponent Function SetRotator ******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics
{
	struct RTSBlackboardComponent_eventSetRotator_Parms
	{
		FString Key;
		FRotator Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetRotator_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetRotator_Parms, Value), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "SetRotator", Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::RTSBlackboardComponent_eventSetRotator_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::RTSBlackboardComponent_eventSetRotator_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_SetRotator()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_SetRotator_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execSetRotator)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetRotator(Z_Param_Key,Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function SetRotator ********************************

// ********** Begin Class URTSBlackboardComponent Function SetString *******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics
{
	struct RTSBlackboardComponent_eventSetString_Parms
	{
		FString Key;
		FString Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetString_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetString_Parms, Value), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "SetString", Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::RTSBlackboardComponent_eventSetString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::RTSBlackboardComponent_eventSetString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_SetString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_SetString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execSetString)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FStrProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetString(Z_Param_Key,Z_Param_Value);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function SetString *********************************

// ********** Begin Class URTSBlackboardComponent Function SetVector *******************************
struct Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics
{
	struct RTSBlackboardComponent_eventSetVector_Parms
	{
		FString Key;
		FVector Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetVector_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBlackboardComponent_eventSetVector_Parms, Value), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "SetVector", Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::RTSBlackboardComponent_eventSetVector_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::RTSBlackboardComponent_eventSetVector_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_SetVector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_SetVector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execSetVector)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetVector(Z_Param_Key,Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function SetVector *********************************

// ********** Begin Class URTSBlackboardComponent Function UpdateAllData ***************************
struct Z_Construct_UFunction_URTSBlackboardComponent_UpdateAllData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_UpdateAllData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "UpdateAllData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_UpdateAllData_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_UpdateAllData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_UpdateAllData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_UpdateAllData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execUpdateAllData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAllData();
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function UpdateAllData *****************************

// ********** Begin Class URTSBlackboardComponent Function UpdateCombatData ************************
struct Z_Construct_UFunction_URTSBlackboardComponent_UpdateCombatData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_UpdateCombatData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "UpdateCombatData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_UpdateCombatData_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_UpdateCombatData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_UpdateCombatData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_UpdateCombatData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execUpdateCombatData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCombatData();
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function UpdateCombatData **************************

// ********** Begin Class URTSBlackboardComponent Function UpdateCommandData ***********************
struct Z_Construct_UFunction_URTSBlackboardComponent_UpdateCommandData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_UpdateCommandData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "UpdateCommandData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_UpdateCommandData_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_UpdateCommandData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_UpdateCommandData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_UpdateCommandData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execUpdateCommandData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCommandData();
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function UpdateCommandData *************************

// ********** Begin Class URTSBlackboardComponent Function UpdateFormationData *********************
struct Z_Construct_UFunction_URTSBlackboardComponent_UpdateFormationData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_UpdateFormationData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "UpdateFormationData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_UpdateFormationData_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_UpdateFormationData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_UpdateFormationData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_UpdateFormationData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execUpdateFormationData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFormationData();
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function UpdateFormationData ***********************

// ********** Begin Class URTSBlackboardComponent Function UpdateUnitData **************************
struct Z_Construct_UFunction_URTSBlackboardComponent_UpdateUnitData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Blackboard" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Auto-update functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auto-update functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSBlackboardComponent_UpdateUnitData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSBlackboardComponent, nullptr, "UpdateUnitData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSBlackboardComponent_UpdateUnitData_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSBlackboardComponent_UpdateUnitData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSBlackboardComponent_UpdateUnitData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSBlackboardComponent_UpdateUnitData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSBlackboardComponent::execUpdateUnitData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateUnitData();
	P_NATIVE_END;
}
// ********** End Class URTSBlackboardComponent Function UpdateUnitData ****************************

// ********** Begin Class URTSBlackboardComponent **************************************************
void URTSBlackboardComponent::StaticRegisterNativesURTSBlackboardComponent()
{
	UClass* Class = URTSBlackboardComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddPredefinedKey", &URTSBlackboardComponent::execAddPredefinedKey },
		{ "ClearAllKeys", &URTSBlackboardComponent::execClearAllKeys },
		{ "GetActor", &URTSBlackboardComponent::execGetActor },
		{ "GetAllKeys", &URTSBlackboardComponent::execGetAllKeys },
		{ "GetBool", &URTSBlackboardComponent::execGetBool },
		{ "GetFloat", &URTSBlackboardComponent::execGetFloat },
		{ "GetInt", &URTSBlackboardComponent::execGetInt },
		{ "GetKeyType", &URTSBlackboardComponent::execGetKeyType },
		{ "GetObject", &URTSBlackboardComponent::execGetObject },
		{ "GetOwnerCommandComponent", &URTSBlackboardComponent::execGetOwnerCommandComponent },
		{ "GetOwnerUnit", &URTSBlackboardComponent::execGetOwnerUnit },
		{ "GetRotator", &URTSBlackboardComponent::execGetRotator },
		{ "GetString", &URTSBlackboardComponent::execGetString },
		{ "GetUnit", &URTSBlackboardComponent::execGetUnit },
		{ "GetVector", &URTSBlackboardComponent::execGetVector },
		{ "HasKey", &URTSBlackboardComponent::execHasKey },
		{ "InitializePredefinedKeys", &URTSBlackboardComponent::execInitializePredefinedKeys },
		{ "RemoveKey", &URTSBlackboardComponent::execRemoveKey },
		{ "SetBool", &URTSBlackboardComponent::execSetBool },
		{ "SetFloat", &URTSBlackboardComponent::execSetFloat },
		{ "SetInt", &URTSBlackboardComponent::execSetInt },
		{ "SetObject", &URTSBlackboardComponent::execSetObject },
		{ "SetRotator", &URTSBlackboardComponent::execSetRotator },
		{ "SetString", &URTSBlackboardComponent::execSetString },
		{ "SetVector", &URTSBlackboardComponent::execSetVector },
		{ "UpdateAllData", &URTSBlackboardComponent::execUpdateAllData },
		{ "UpdateCombatData", &URTSBlackboardComponent::execUpdateCombatData },
		{ "UpdateCommandData", &URTSBlackboardComponent::execUpdateCommandData },
		{ "UpdateFormationData", &URTSBlackboardComponent::execUpdateFormationData },
		{ "UpdateUnitData", &URTSBlackboardComponent::execUpdateUnitData },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSBlackboardComponent;
UClass* URTSBlackboardComponent::GetPrivateStaticClass()
{
	using TClass = URTSBlackboardComponent;
	if (!Z_Registration_Info_UClass_URTSBlackboardComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSBlackboardComponent"),
			Z_Registration_Info_UClass_URTSBlackboardComponent.InnerSingleton,
			StaticRegisterNativesURTSBlackboardComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSBlackboardComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSBlackboardComponent_NoRegister()
{
	return URTSBlackboardComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSBlackboardComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enhanced blackboard system for RTS AI\n * Stores shared AI data and provides type-safe access\n */" },
#endif
		{ "IncludePath", "RTSBlackboardSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced blackboard system for RTS AI\nStores shared AI data and provides type-safe access" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlackboardData_MetaData[] = {
		{ "Category", "Blackboard" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blackboard data storage\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blackboard data storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredefinedKeys_MetaData[] = {
		{ "Category", "Blackboard Keys" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Predefined keys for common AI data\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Predefined keys for common AI data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoUpdateUnitData_MetaData[] = {
		{ "Category", "Blackboard" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Auto-update settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auto-update settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoUpdateCommandData_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoUpdateFormationData_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoUpdateCombatData_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateInterval_MetaData[] = {
		{ "Category", "Blackboard" },
		{ "ModuleRelativePath", "Public/RTSBlackboardSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BlackboardData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlackboardData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BlackboardData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PredefinedKeys_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PredefinedKeys;
	static void NewProp_bAutoUpdateUnitData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoUpdateUnitData;
	static void NewProp_bAutoUpdateCommandData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoUpdateCommandData;
	static void NewProp_bAutoUpdateFormationData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoUpdateFormationData;
	static void NewProp_bAutoUpdateCombatData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoUpdateCombatData;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateInterval;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSBlackboardComponent_AddPredefinedKey, "AddPredefinedKey" }, // 2619406366
		{ &Z_Construct_UFunction_URTSBlackboardComponent_ClearAllKeys, "ClearAllKeys" }, // 760663262
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetActor, "GetActor" }, // 760620401
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetAllKeys, "GetAllKeys" }, // 4235741164
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetBool, "GetBool" }, // 1972862314
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetFloat, "GetFloat" }, // 1802938972
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetInt, "GetInt" }, // 2229866152
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetKeyType, "GetKeyType" }, // 702765691
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetObject, "GetObject" }, // 3576015253
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerCommandComponent, "GetOwnerCommandComponent" }, // 4246191120
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetOwnerUnit, "GetOwnerUnit" }, // 480777360
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetRotator, "GetRotator" }, // 1646970831
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetString, "GetString" }, // 8610582
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetUnit, "GetUnit" }, // 195687097
		{ &Z_Construct_UFunction_URTSBlackboardComponent_GetVector, "GetVector" }, // 1833484945
		{ &Z_Construct_UFunction_URTSBlackboardComponent_HasKey, "HasKey" }, // 2435904669
		{ &Z_Construct_UFunction_URTSBlackboardComponent_InitializePredefinedKeys, "InitializePredefinedKeys" }, // 1301113546
		{ &Z_Construct_UFunction_URTSBlackboardComponent_RemoveKey, "RemoveKey" }, // 1877796289
		{ &Z_Construct_UFunction_URTSBlackboardComponent_SetBool, "SetBool" }, // 583336295
		{ &Z_Construct_UFunction_URTSBlackboardComponent_SetFloat, "SetFloat" }, // 3801042682
		{ &Z_Construct_UFunction_URTSBlackboardComponent_SetInt, "SetInt" }, // 2359618929
		{ &Z_Construct_UFunction_URTSBlackboardComponent_SetObject, "SetObject" }, // 16372741
		{ &Z_Construct_UFunction_URTSBlackboardComponent_SetRotator, "SetRotator" }, // 2932441789
		{ &Z_Construct_UFunction_URTSBlackboardComponent_SetString, "SetString" }, // 3963485509
		{ &Z_Construct_UFunction_URTSBlackboardComponent_SetVector, "SetVector" }, // 3300031561
		{ &Z_Construct_UFunction_URTSBlackboardComponent_UpdateAllData, "UpdateAllData" }, // 1805731834
		{ &Z_Construct_UFunction_URTSBlackboardComponent_UpdateCombatData, "UpdateCombatData" }, // 182154170
		{ &Z_Construct_UFunction_URTSBlackboardComponent_UpdateCommandData, "UpdateCommandData" }, // 2856167734
		{ &Z_Construct_UFunction_URTSBlackboardComponent_UpdateFormationData, "UpdateFormationData" }, // 475287569
		{ &Z_Construct_UFunction_URTSBlackboardComponent_UpdateUnitData, "UpdateUnitData" }, // 429835271
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSBlackboardComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_BlackboardData_ValueProp = { "BlackboardData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FRTSBlackboardEntry, METADATA_PARAMS(0, nullptr) }; // 2919252159
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_BlackboardData_Key_KeyProp = { "BlackboardData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_BlackboardData = { "BlackboardData", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSBlackboardComponent, BlackboardData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlackboardData_MetaData), NewProp_BlackboardData_MetaData) }; // 2919252159
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_PredefinedKeys_Inner = { "PredefinedKeys", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSBlackboardEntry, METADATA_PARAMS(0, nullptr) }; // 2919252159
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_PredefinedKeys = { "PredefinedKeys", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSBlackboardComponent, PredefinedKeys), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredefinedKeys_MetaData), NewProp_PredefinedKeys_MetaData) }; // 2919252159
void Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateUnitData_SetBit(void* Obj)
{
	((URTSBlackboardComponent*)Obj)->bAutoUpdateUnitData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateUnitData = { "bAutoUpdateUnitData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSBlackboardComponent), &Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateUnitData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoUpdateUnitData_MetaData), NewProp_bAutoUpdateUnitData_MetaData) };
void Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateCommandData_SetBit(void* Obj)
{
	((URTSBlackboardComponent*)Obj)->bAutoUpdateCommandData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateCommandData = { "bAutoUpdateCommandData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSBlackboardComponent), &Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateCommandData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoUpdateCommandData_MetaData), NewProp_bAutoUpdateCommandData_MetaData) };
void Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateFormationData_SetBit(void* Obj)
{
	((URTSBlackboardComponent*)Obj)->bAutoUpdateFormationData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateFormationData = { "bAutoUpdateFormationData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSBlackboardComponent), &Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateFormationData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoUpdateFormationData_MetaData), NewProp_bAutoUpdateFormationData_MetaData) };
void Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateCombatData_SetBit(void* Obj)
{
	((URTSBlackboardComponent*)Obj)->bAutoUpdateCombatData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateCombatData = { "bAutoUpdateCombatData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSBlackboardComponent), &Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateCombatData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoUpdateCombatData_MetaData), NewProp_bAutoUpdateCombatData_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_UpdateInterval = { "UpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSBlackboardComponent, UpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateInterval_MetaData), NewProp_UpdateInterval_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSBlackboardComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_BlackboardData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_BlackboardData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_BlackboardData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_PredefinedKeys_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_PredefinedKeys,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateUnitData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateCommandData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateFormationData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_bAutoUpdateCombatData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSBlackboardComponent_Statics::NewProp_UpdateInterval,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSBlackboardComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSBlackboardComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSBlackboardComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSBlackboardComponent_Statics::ClassParams = {
	&URTSBlackboardComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSBlackboardComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSBlackboardComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSBlackboardComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSBlackboardComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSBlackboardComponent()
{
	if (!Z_Registration_Info_UClass_URTSBlackboardComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSBlackboardComponent.OuterSingleton, Z_Construct_UClass_URTSBlackboardComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSBlackboardComponent.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSBlackboardComponent);
URTSBlackboardComponent::~URTSBlackboardComponent() {}
// ********** End Class URTSBlackboardComponent ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSBlackboardKeyType_StaticEnum, TEXT("ERTSBlackboardKeyType"), &Z_Registration_Info_UEnum_ERTSBlackboardKeyType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1126800597U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRTSBlackboardEntry::StaticStruct, Z_Construct_UScriptStruct_FRTSBlackboardEntry_Statics::NewStructOps, TEXT("RTSBlackboardEntry"), &Z_Registration_Info_UScriptStruct_FRTSBlackboardEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSBlackboardEntry), 2919252159U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSBlackboardComponent, URTSBlackboardComponent::StaticClass, TEXT("URTSBlackboardComponent"), &Z_Registration_Info_UClass_URTSBlackboardComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSBlackboardComponent), 351381067U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h__Script_ArmorWars_3031981612(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h__Script_ArmorWars_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h__Script_ArmorWars_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h__Script_ArmorWars_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBlackboardSystem_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

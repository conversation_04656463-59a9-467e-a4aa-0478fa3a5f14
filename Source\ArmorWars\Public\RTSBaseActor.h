#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Pawn.h"
#include "GameplayTagContainer.h"
#include "RTSBaseActor.generated.h"

// Enum to distinguish between units and buildings
UENUM(BlueprintType)
enum class ERTSActorType : uint8
{
    Unit        UMETA(DisplayName = "Unit"),
    Building    UMETA(DisplayName = "Building")
};

// Enum for unit domains (movement types)
UENUM(BlueprintType)
enum class ERTSUnitDomain : uint8
{
    None        UMETA(DisplayName = "None"),
    Land        UMETA(DisplayName = "Land"),
    Air         UMETA(DisplayName = "Air"),
    Sea         UMETA(DisplayName = "Sea"),
    Subnautical UMETA(DisplayName = "Subnautical")
};

// Enum for aircraft types (sub-category of Air domain)
UENUM(BlueprintType)
enum class ERTSAircraftType : uint8
{
    None        UMETA(DisplayName = "None"),
    VTOL        UMETA(DisplayName = "VTOL (Vertical Take-Off and Landing)"),
    FixedWing   UMETA(DisplayName = "Fixed Wing"),
    Hybrid      UMETA(DisplayName = "Hybrid (VTOL + Fixed Wing)")
};

// Enum for technology levels
UENUM(BlueprintType)
enum class ERTSTechLevel : uint8
{
    Tech1       UMETA(DisplayName = "Tech Level 1"),
    Tech2       UMETA(DisplayName = "Tech Level 2"),
    Tech3       UMETA(DisplayName = "Tech Level 3"),
    Tech4       UMETA(DisplayName = "Tech Level 4")
};



/**
 * Base actor class for all RTS units and buildings
 * Provides common functionality including gameplay tags, actor type identification,
 * and basic stats that can be used by both units and buildings
 */
UCLASS(BlueprintType, Blueprintable, Abstract)
class ARMORWARS_API ARTSBaseActor : public APawn
{
    GENERATED_BODY()

public:
    ARTSBaseActor();

protected:
    virtual void BeginPlay() override;

public:
    // Actor type - Unit or Building
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Type")
    ERTSActorType ActorType = ERTSActorType::Unit;

    // Gameplay tags for this actor
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Tags")
    FGameplayTagContainer GameplayTags;

    // Display name for this actor
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Info")
    FText DisplayName;

    // Description of this actor
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Info")
    FText Description;

    // Icon for UI representation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Info")
    TSoftObjectPtr<UTexture2D> Icon;

    // Team/faction this actor belongs to
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Team")
    int32 TeamID = 0;

    // Technology level of this actor
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Tech")
    ERTSTechLevel TechLevel = ERTSTechLevel::Tech1;

    // Maximum health
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Stats", meta = (ClampMin = "1.0"))
    float MaxHealth = 100.0f;

    // Current health
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Stats")
    float CurrentHealth = 100.0f;

    // Whether this actor can be selected
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RTS|Selection")
    bool bCanBeSelected = true;

    // Whether this actor is currently selected
    UPROPERTY(BlueprintReadOnly, Category = "RTS|Selection")
    bool bIsSelected = false;

public:
    // Gameplay tag functions
    UFUNCTION(BlueprintCallable, Category = "RTS|Tags")
    bool HasGameplayTag(const FGameplayTag& Tag) const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Tags")
    bool HasAnyGameplayTags(const FGameplayTagContainer& TagContainer) const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Tags")
    bool HasAllGameplayTags(const FGameplayTagContainer& TagContainer) const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Tags")
    void AddGameplayTag(const FGameplayTag& Tag);

    UFUNCTION(BlueprintCallable, Category = "RTS|Tags")
    void RemoveGameplayTag(const FGameplayTag& Tag);

    UFUNCTION(BlueprintCallable, Category = "RTS|Tags")
    void AddGameplayTags(const FGameplayTagContainer& TagContainer);

    UFUNCTION(BlueprintCallable, Category = "RTS|Tags")
    void RemoveGameplayTags(const FGameplayTagContainer& TagContainer);

    // Health functions - override the base class virtual function
    virtual float TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser) override;

    // Simplified damage function for Blueprint use
    UFUNCTION(BlueprintCallable, Category = "RTS|Health")
    void TakeDamageSimple(float DamageAmount, AActor* DamageSource = nullptr);

    UFUNCTION(BlueprintCallable, Category = "RTS|Health")
    void Heal(float HealAmount);

    UFUNCTION(BlueprintCallable, Category = "RTS|Health")
    void SetHealth(float NewHealth);

    UFUNCTION(BlueprintPure, Category = "RTS|Health")
    float GetHealthPercentage() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Health")
    bool IsAlive() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Health")
    bool IsDead() const;

    // Selection functions
    UFUNCTION(BlueprintCallable, Category = "RTS|Selection")
    void SetSelected(bool bSelected);

    UFUNCTION(BlueprintPure, Category = "RTS|Selection")
    bool CanBeSelected() const;

    // Type checking functions
    UFUNCTION(BlueprintPure, Category = "RTS|Type")
    bool IsUnit() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Type")
    bool IsBuilding() const;

    // Team functions
    UFUNCTION(BlueprintPure, Category = "RTS|Team")
    bool IsOnSameTeam(const ARTSBaseActor* OtherActor) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Team")
    bool IsEnemy(const ARTSBaseActor* OtherActor) const;

    // Team functions that work with any actor (including RTSUnit pawns)
    UFUNCTION(BlueprintPure, Category = "RTS|Team")
    bool IsOnSameTeamAsActor(const AActor* OtherActor) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Team")
    bool IsEnemyActor(const AActor* OtherActor) const;

    // Get team ID from any actor (RTSBaseActor or RTSUnit)
    UFUNCTION(BlueprintPure, Category = "RTS|Team")
    static int32 GetActorTeamID(const AActor* Actor);

    // Tech level functions
    UFUNCTION(BlueprintPure, Category = "RTS|Tech")
    ERTSTechLevel GetTechLevel() const { return TechLevel; }

    UFUNCTION(BlueprintCallable, Category = "RTS|Tech")
    void SetTechLevel(ERTSTechLevel NewTechLevel);

    UFUNCTION(BlueprintPure, Category = "RTS|Tech")
    bool CanProduceTechLevel(ERTSTechLevel RequiredTechLevel) const;

protected:
    // Called when health reaches zero
    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Health")
    void OnDeath();

    // Called when taking damage
    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Health")
    void OnTakeDamage(float DamageAmount, AActor* DamageSource);

    // Called when healed
    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Health")
    void OnHealed(float HealAmount);

    // Called when selected/deselected
    UFUNCTION(BlueprintImplementableEvent, Category = "RTS|Selection")
    void OnSelectionChangedEvent(bool bSelected);

    // Internal death handling
    virtual void HandleDeath();

public:
    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHealthChanged, float, NewHealth, float, MaxHealth);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDamageReceived, float, DamageAmount, AActor*, DamageSource);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnActorDeath, ARTSBaseActor*, DeadActor);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSelectionChanged, ARTSBaseActor*, Actor, bool, bSelected);

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnHealthChanged OnHealthChanged;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnDamageReceived OnDamageReceived;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnActorDeath OnActorDeath;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnSelectionChanged OnSelectionChanged;
};

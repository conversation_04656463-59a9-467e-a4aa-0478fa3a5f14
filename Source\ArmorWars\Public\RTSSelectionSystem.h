#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "Interfaces/RTSUnitSelectionInterface.h"
#include "RTSBaseActor.h"
#include "RTSSelectionSystem.generated.h"

class ARTSPlayerController;

// Delegate for selection changed events
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRTSSelectionChanged, const TArray<ARTSBaseActor*>&, NewSelection, const TArray<ARTSBaseActor*>&, PreviousSelection);

/**
 * RTS Selection System - Manages unit selection for RTS gameplay
 * Implements the IRTSUnitSelectionInterface for Blueprint compatibility
 */
UCLASS(BlueprintType)
class ARMORWARS_API URTSSelectionSystem : public UWorldSubsystem, public IRTSUnitSelectionInterface
{
    GENERATED_BODY()

public:
    URTSSelectionSystem();

    // UWorldSubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // IRTSUnitSelectionInterface implementation - these are BlueprintImplementableEvents
    // We implement them as BlueprintNativeEvents to provide C++ implementation with Blueprint override capability
    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    void InitializeSelectionSystem(ARTSPlayerController* PlayerController);
    virtual void InitializeSelectionSystem_Implementation(ARTSPlayerController* PlayerController);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    bool SelectUnit(ARTSBaseActor* Unit, bool bAddToSelection);
    virtual bool SelectUnit_Implementation(ARTSBaseActor* Unit, bool bAddToSelection);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    bool DeselectUnit(ARTSBaseActor* Unit);
    virtual bool DeselectUnit_Implementation(ARTSBaseActor* Unit);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    int32 SelectUnits(const TArray<ARTSBaseActor*>& Units, bool bAddToSelection);
    virtual int32 SelectUnits_Implementation(const TArray<ARTSBaseActor*>& Units, bool bAddToSelection);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    int32 SelectUnitsInBox(const FVector2D& StartPosition, const FVector2D& EndPosition, ERTSSelectionFilter Filter);
    virtual int32 SelectUnitsInBox_Implementation(const FVector2D& StartPosition, const FVector2D& EndPosition, ERTSSelectionFilter Filter);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    int32 SelectUnitsInRadius(const FVector& CenterLocation, float Radius, ERTSSelectionFilter Filter);
    virtual int32 SelectUnitsInRadius_Implementation(const FVector& CenterLocation, float Radius, ERTSSelectionFilter Filter);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    void ClearSelection();
    virtual void ClearSelection_Implementation();

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    TArray<ARTSBaseActor*> GetSelectedUnits() const;
    virtual TArray<ARTSBaseActor*> GetSelectedUnits_Implementation() const;

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    int32 GetSelectedUnitCount() const;
    virtual int32 GetSelectedUnitCount_Implementation() const;

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    bool IsUnitSelected(ARTSBaseActor* Unit) const;
    virtual bool IsUnitSelected_Implementation(ARTSBaseActor* Unit) const;

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    bool SaveSelectionGroup(int32 GroupIndex);
    virtual bool SaveSelectionGroup_Implementation(int32 GroupIndex);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    bool LoadSelectionGroup(int32 GroupIndex, bool bAddToSelection);
    virtual bool LoadSelectionGroup_Implementation(int32 GroupIndex, bool bAddToSelection);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    FRTSSelectionGroup GetSelectionGroup(int32 GroupIndex) const;
    virtual FRTSSelectionGroup GetSelectionGroup_Implementation(int32 GroupIndex) const;

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    void ClearSelectionGroup(int32 GroupIndex);
    virtual void ClearSelectionGroup_Implementation(int32 GroupIndex);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    TArray<FRTSSelectionGroup> GetAllSelectionGroups() const;
    virtual TArray<FRTSSelectionGroup> GetAllSelectionGroups_Implementation() const;

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    int32 SelectAllUnitsOfType(ARTSBaseActor* ReferenceUnit, bool bOnScreen);
    virtual int32 SelectAllUnitsOfType_Implementation(ARTSBaseActor* ReferenceUnit, bool bOnScreen);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    int32 SelectAllIdleUnits();
    virtual int32 SelectAllIdleUnits_Implementation();

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    int32 SelectAllMilitaryUnits();
    virtual int32 SelectAllMilitaryUnits_Implementation();

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    TArray<ARTSBaseActor*> FilterSelection(ERTSSelectionFilter Filter) const;
    virtual TArray<ARTSBaseActor*> FilterSelection_Implementation(ERTSSelectionFilter Filter) const;

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    void SetSelectionHighlightVisibility(bool bVisible);
    virtual void SetSelectionHighlightVisibility_Implementation(bool bVisible);

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    void UpdateSelectionHighlights();
    virtual void UpdateSelectionHighlights_Implementation();

    UFUNCTION(BlueprintNativeEvent, Category = "RTS Selection")
    void OnSelectionChanged(const TArray<ARTSBaseActor*>& NewSelection, const TArray<ARTSBaseActor*>& PreviousSelection);
    virtual void OnSelectionChanged_Implementation(const TArray<ARTSBaseActor*>& NewSelection, const TArray<ARTSBaseActor*>& PreviousSelection);

    // Additional C++ methods for direct access
    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    void SetPlayerController(ARTSPlayerController* PlayerController);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    ARTSPlayerController* GetPlayerController() const { return OwningPlayerController; }

    // Selection box functionality
    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    TArray<ARTSBaseActor*> FindUnitsInSelectionBox(const FVector2D& StartPos, const FVector2D& EndPos, ERTSSelectionFilter Filter = ERTSSelectionFilter::None);

    UFUNCTION(BlueprintCallable, Category = "RTS Selection")
    TArray<ARTSBaseActor*> FindUnitsInRadius(const FVector& CenterLocation, float Radius, ERTSSelectionFilter Filter = ERTSSelectionFilter::None);

    // Selection events
    UPROPERTY(BlueprintAssignable, Category = "RTS Selection")
    FOnRTSSelectionChanged OnSelectionChangedEvent;

protected:
    // Currently selected units
    UPROPERTY(BlueprintReadOnly, Category = "Selection")
    TArray<ARTSBaseActor*> SelectedUnits;

    // Selection groups (hotkeys 1-9)
    UPROPERTY(BlueprintReadOnly, Category = "Selection")
    TMap<int32, FRTSSelectionGroup> SelectionGroups;

    // Owning player controller
    UPROPERTY()
    TObjectPtr<ARTSPlayerController> OwningPlayerController;

    // Selection settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection Settings")
    bool bHighlightSelectedUnits = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection Settings")
    bool bEnableDebugLogging = true;

    // Helper methods
    void UpdateUnitSelectionState(ARTSBaseActor* Unit, bool bSelected);
    bool PassesSelectionFilter(ARTSBaseActor* Unit, ERTSSelectionFilter Filter) const;
    TArray<ARTSBaseActor*> GetAllSelectableUnits() const;
    bool IsUnitOnScreen(ARTSBaseActor* Unit) const;
    bool IsUnitIdle(ARTSBaseActor* Unit) const;
    bool IsUnitMilitary(ARTSBaseActor* Unit) const;
};

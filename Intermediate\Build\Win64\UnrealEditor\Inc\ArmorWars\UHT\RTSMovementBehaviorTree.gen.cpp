// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSMovementBehaviorTree.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSMovementBehaviorTree() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSBehaviorNode_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceMoveTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSConditionNode();
ARMORWARS_API UClass* Z_Construct_UClass_URTSDecoratorNode();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFindAlternativePathTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFindAlternativePathTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFormationSpeedDecorator();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFormationSpeedDecorator_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSMaintainFormationSpacingTask();
ARMORWARS_API UClass* Z_Construct_UClass_URTSMaintainFormationSpacingTask_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSMovementBehaviorTreeFactory();
ARMORWARS_API UClass* Z_Construct_UClass_URTSMovementBehaviorTreeFactory_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSPathBlockedCondition();
ARMORWARS_API UClass* Z_Construct_UClass_URTSPathBlockedCondition_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSTaskNode();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Class URTSMovementBehaviorTreeFactory Function CreateAdvancedUnitBehaviorTree **
struct Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics
{
	struct RTSMovementBehaviorTreeFactory_eventCreateAdvancedUnitBehaviorTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Movement Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSMovementBehaviorTreeFactory_eventCreateAdvancedUnitBehaviorTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSMovementBehaviorTreeFactory, nullptr, "CreateAdvancedUnitBehaviorTree", Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::RTSMovementBehaviorTreeFactory_eventCreateAdvancedUnitBehaviorTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::RTSMovementBehaviorTreeFactory_eventCreateAdvancedUnitBehaviorTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSMovementBehaviorTreeFactory::execCreateAdvancedUnitBehaviorTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=URTSMovementBehaviorTreeFactory::CreateAdvancedUnitBehaviorTree();
	P_NATIVE_END;
}
// ********** End Class URTSMovementBehaviorTreeFactory Function CreateAdvancedUnitBehaviorTree ****

// ********** Begin Class URTSMovementBehaviorTreeFactory Function CreateBasicMovementTree *********
struct Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics
{
	struct RTSMovementBehaviorTreeFactory_eventCreateBasicMovementTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Movement Behavior Tree" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Factory methods for creating different movement behavior trees\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Factory methods for creating different movement behavior trees" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSMovementBehaviorTreeFactory_eventCreateBasicMovementTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSMovementBehaviorTreeFactory, nullptr, "CreateBasicMovementTree", Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::RTSMovementBehaviorTreeFactory_eventCreateBasicMovementTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::RTSMovementBehaviorTreeFactory_eventCreateBasicMovementTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSMovementBehaviorTreeFactory::execCreateBasicMovementTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=URTSMovementBehaviorTreeFactory::CreateBasicMovementTree();
	P_NATIVE_END;
}
// ********** End Class URTSMovementBehaviorTreeFactory Function CreateBasicMovementTree ***********

// ********** Begin Class URTSMovementBehaviorTreeFactory Function CreateCombatMovementTree ********
struct Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics
{
	struct RTSMovementBehaviorTreeFactory_eventCreateCombatMovementTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Movement Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSMovementBehaviorTreeFactory_eventCreateCombatMovementTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSMovementBehaviorTreeFactory, nullptr, "CreateCombatMovementTree", Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::RTSMovementBehaviorTreeFactory_eventCreateCombatMovementTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::RTSMovementBehaviorTreeFactory_eventCreateCombatMovementTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSMovementBehaviorTreeFactory::execCreateCombatMovementTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=URTSMovementBehaviorTreeFactory::CreateCombatMovementTree();
	P_NATIVE_END;
}
// ********** End Class URTSMovementBehaviorTreeFactory Function CreateCombatMovementTree **********

// ********** Begin Class URTSMovementBehaviorTreeFactory Function CreateCommandResponsiveBehaviorTree 
struct Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics
{
	struct RTSMovementBehaviorTreeFactory_eventCreateCommandResponsiveBehaviorTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Movement Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSMovementBehaviorTreeFactory_eventCreateCommandResponsiveBehaviorTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSMovementBehaviorTreeFactory, nullptr, "CreateCommandResponsiveBehaviorTree", Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::RTSMovementBehaviorTreeFactory_eventCreateCommandResponsiveBehaviorTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::RTSMovementBehaviorTreeFactory_eventCreateCommandResponsiveBehaviorTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSMovementBehaviorTreeFactory::execCreateCommandResponsiveBehaviorTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=URTSMovementBehaviorTreeFactory::CreateCommandResponsiveBehaviorTree();
	P_NATIVE_END;
}
// ********** End Class URTSMovementBehaviorTreeFactory Function CreateCommandResponsiveBehaviorTree 

// ********** Begin Class URTSMovementBehaviorTreeFactory Function CreateFormationMovementTree *****
struct Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics
{
	struct RTSMovementBehaviorTreeFactory_eventCreateFormationMovementTree_Parms
	{
		URTSBehaviorNode* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Movement Behavior Tree" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSMovementBehaviorTreeFactory_eventCreateFormationMovementTree_Parms, ReturnValue), Z_Construct_UClass_URTSBehaviorNode_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSMovementBehaviorTreeFactory, nullptr, "CreateFormationMovementTree", Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::RTSMovementBehaviorTreeFactory_eventCreateFormationMovementTree_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::RTSMovementBehaviorTreeFactory_eventCreateFormationMovementTree_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSMovementBehaviorTreeFactory::execCreateFormationMovementTree)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSBehaviorNode**)Z_Param__Result=URTSMovementBehaviorTreeFactory::CreateFormationMovementTree();
	P_NATIVE_END;
}
// ********** End Class URTSMovementBehaviorTreeFactory Function CreateFormationMovementTree *******

// ********** Begin Class URTSMovementBehaviorTreeFactory ******************************************
void URTSMovementBehaviorTreeFactory::StaticRegisterNativesURTSMovementBehaviorTreeFactory()
{
	UClass* Class = URTSMovementBehaviorTreeFactory::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CreateAdvancedUnitBehaviorTree", &URTSMovementBehaviorTreeFactory::execCreateAdvancedUnitBehaviorTree },
		{ "CreateBasicMovementTree", &URTSMovementBehaviorTreeFactory::execCreateBasicMovementTree },
		{ "CreateCombatMovementTree", &URTSMovementBehaviorTreeFactory::execCreateCombatMovementTree },
		{ "CreateCommandResponsiveBehaviorTree", &URTSMovementBehaviorTreeFactory::execCreateCommandResponsiveBehaviorTree },
		{ "CreateFormationMovementTree", &URTSMovementBehaviorTreeFactory::execCreateFormationMovementTree },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSMovementBehaviorTreeFactory;
UClass* URTSMovementBehaviorTreeFactory::GetPrivateStaticClass()
{
	using TClass = URTSMovementBehaviorTreeFactory;
	if (!Z_Registration_Info_UClass_URTSMovementBehaviorTreeFactory.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSMovementBehaviorTreeFactory"),
			Z_Registration_Info_UClass_URTSMovementBehaviorTreeFactory.InnerSingleton,
			StaticRegisterNativesURTSMovementBehaviorTreeFactory,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSMovementBehaviorTreeFactory.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSMovementBehaviorTreeFactory_NoRegister()
{
	return URTSMovementBehaviorTreeFactory::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSMovementBehaviorTreeFactory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Factory class for creating movement-focused behavior trees\n * Handles complex movement scenarios including formation movement,\n * return fire while moving, and collision avoidance\n */" },
#endif
		{ "IncludePath", "RTSMovementBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Factory class for creating movement-focused behavior trees\nHandles complex movement scenarios including formation movement,\nreturn fire while moving, and collision avoidance" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateAdvancedUnitBehaviorTree, "CreateAdvancedUnitBehaviorTree" }, // 1736731169
		{ &Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateBasicMovementTree, "CreateBasicMovementTree" }, // 1303125079
		{ &Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCombatMovementTree, "CreateCombatMovementTree" }, // 1659431657
		{ &Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateCommandResponsiveBehaviorTree, "CreateCommandResponsiveBehaviorTree" }, // 1318085671
		{ &Z_Construct_UFunction_URTSMovementBehaviorTreeFactory_CreateFormationMovementTree, "CreateFormationMovementTree" }, // 1317263776
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSMovementBehaviorTreeFactory>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_URTSMovementBehaviorTreeFactory_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSMovementBehaviorTreeFactory_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSMovementBehaviorTreeFactory_Statics::ClassParams = {
	&URTSMovementBehaviorTreeFactory::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSMovementBehaviorTreeFactory_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSMovementBehaviorTreeFactory_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSMovementBehaviorTreeFactory()
{
	if (!Z_Registration_Info_UClass_URTSMovementBehaviorTreeFactory.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSMovementBehaviorTreeFactory.OuterSingleton, Z_Construct_UClass_URTSMovementBehaviorTreeFactory_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSMovementBehaviorTreeFactory.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSMovementBehaviorTreeFactory);
URTSMovementBehaviorTreeFactory::~URTSMovementBehaviorTreeFactory() {}
// ********** End Class URTSMovementBehaviorTreeFactory ********************************************

// ********** Begin Class URTSCollisionAvoidanceMoveTask *******************************************
void URTSCollisionAvoidanceMoveTask::StaticRegisterNativesURTSCollisionAvoidanceMoveTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSCollisionAvoidanceMoveTask;
UClass* URTSCollisionAvoidanceMoveTask::GetPrivateStaticClass()
{
	using TClass = URTSCollisionAvoidanceMoveTask;
	if (!Z_Registration_Info_UClass_URTSCollisionAvoidanceMoveTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSCollisionAvoidanceMoveTask"),
			Z_Registration_Info_UClass_URTSCollisionAvoidanceMoveTask.InnerSingleton,
			StaticRegisterNativesURTSCollisionAvoidanceMoveTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSCollisionAvoidanceMoveTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_NoRegister()
{
	return URTSCollisionAvoidanceMoveTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Specialized movement task that handles collision avoidance\n * Prevents units from bumping into each other during movement\n */" },
#endif
		{ "IncludePath", "RTSMovementBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Specialized movement task that handles collision avoidance\nPrevents units from bumping into each other during movement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvoidanceRadius_MetaData[] = {
		{ "Category", "Collision Avoidance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision avoidance parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision avoidance parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvoidanceStrength_MetaData[] = {
		{ "Category", "Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LookAheadDistance_MetaData[] = {
		{ "Category", "Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAvoidanceUnits_MetaData[] = {
		{ "Category", "Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocationKey_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AcceptanceRadius_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AvoidanceRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AvoidanceStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LookAheadDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxAvoidanceUnits;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetLocationKey;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AcceptanceRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSCollisionAvoidanceMoveTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_AvoidanceRadius = { "AvoidanceRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceMoveTask, AvoidanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvoidanceRadius_MetaData), NewProp_AvoidanceRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_AvoidanceStrength = { "AvoidanceStrength", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceMoveTask, AvoidanceStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvoidanceStrength_MetaData), NewProp_AvoidanceStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_LookAheadDistance = { "LookAheadDistance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceMoveTask, LookAheadDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LookAheadDistance_MetaData), NewProp_LookAheadDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_MaxAvoidanceUnits = { "MaxAvoidanceUnits", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceMoveTask, MaxAvoidanceUnits), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAvoidanceUnits_MetaData), NewProp_MaxAvoidanceUnits_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_TargetLocationKey = { "TargetLocationKey", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceMoveTask, TargetLocationKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocationKey_MetaData), NewProp_TargetLocationKey_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_AcceptanceRadius = { "AcceptanceRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceMoveTask, AcceptanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AcceptanceRadius_MetaData), NewProp_AcceptanceRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_AvoidanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_AvoidanceStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_LookAheadDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_MaxAvoidanceUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_TargetLocationKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::NewProp_AcceptanceRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::ClassParams = {
	&URTSCollisionAvoidanceMoveTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSCollisionAvoidanceMoveTask()
{
	if (!Z_Registration_Info_UClass_URTSCollisionAvoidanceMoveTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSCollisionAvoidanceMoveTask.OuterSingleton, Z_Construct_UClass_URTSCollisionAvoidanceMoveTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSCollisionAvoidanceMoveTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSCollisionAvoidanceMoveTask);
URTSCollisionAvoidanceMoveTask::~URTSCollisionAvoidanceMoveTask() {}
// ********** End Class URTSCollisionAvoidanceMoveTask *********************************************

// ********** Begin Class URTSPathBlockedCondition *************************************************
void URTSPathBlockedCondition::StaticRegisterNativesURTSPathBlockedCondition()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSPathBlockedCondition;
UClass* URTSPathBlockedCondition::GetPrivateStaticClass()
{
	using TClass = URTSPathBlockedCondition;
	if (!Z_Registration_Info_UClass_URTSPathBlockedCondition.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSPathBlockedCondition"),
			Z_Registration_Info_UClass_URTSPathBlockedCondition.InnerSingleton,
			StaticRegisterNativesURTSPathBlockedCondition,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSPathBlockedCondition.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSPathBlockedCondition_NoRegister()
{
	return URTSPathBlockedCondition::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSPathBlockedCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Condition node for checking if path is blocked\n * Used to trigger alternative movement behaviors\n */" },
#endif
		{ "IncludePath", "RTSMovementBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condition node for checking if path is blocked\nUsed to trigger alternative movement behaviors" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckDistance_MetaData[] = {
		{ "Category", "Path Checking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Path checking parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Path checking parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckWidth_MetaData[] = {
		{ "Category", "Path Checking" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckPoints_MetaData[] = {
		{ "Category", "Path Checking" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocationKey_MetaData[] = {
		{ "Category", "Path Checking" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CheckDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CheckWidth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CheckPoints;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetLocationKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSPathBlockedCondition>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSPathBlockedCondition_Statics::NewProp_CheckDistance = { "CheckDistance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSPathBlockedCondition, CheckDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckDistance_MetaData), NewProp_CheckDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSPathBlockedCondition_Statics::NewProp_CheckWidth = { "CheckWidth", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSPathBlockedCondition, CheckWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckWidth_MetaData), NewProp_CheckWidth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSPathBlockedCondition_Statics::NewProp_CheckPoints = { "CheckPoints", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSPathBlockedCondition, CheckPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckPoints_MetaData), NewProp_CheckPoints_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSPathBlockedCondition_Statics::NewProp_TargetLocationKey = { "TargetLocationKey", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSPathBlockedCondition, TargetLocationKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocationKey_MetaData), NewProp_TargetLocationKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSPathBlockedCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSPathBlockedCondition_Statics::NewProp_CheckDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSPathBlockedCondition_Statics::NewProp_CheckWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSPathBlockedCondition_Statics::NewProp_CheckPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSPathBlockedCondition_Statics::NewProp_TargetLocationKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSPathBlockedCondition_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSPathBlockedCondition_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSConditionNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSPathBlockedCondition_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSPathBlockedCondition_Statics::ClassParams = {
	&URTSPathBlockedCondition::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSPathBlockedCondition_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSPathBlockedCondition_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSPathBlockedCondition_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSPathBlockedCondition_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSPathBlockedCondition()
{
	if (!Z_Registration_Info_UClass_URTSPathBlockedCondition.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSPathBlockedCondition.OuterSingleton, Z_Construct_UClass_URTSPathBlockedCondition_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSPathBlockedCondition.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSPathBlockedCondition);
URTSPathBlockedCondition::~URTSPathBlockedCondition() {}
// ********** End Class URTSPathBlockedCondition ***************************************************

// ********** Begin Class URTSFindAlternativePathTask **********************************************
void URTSFindAlternativePathTask::StaticRegisterNativesURTSFindAlternativePathTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSFindAlternativePathTask;
UClass* URTSFindAlternativePathTask::GetPrivateStaticClass()
{
	using TClass = URTSFindAlternativePathTask;
	if (!Z_Registration_Info_UClass_URTSFindAlternativePathTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSFindAlternativePathTask"),
			Z_Registration_Info_UClass_URTSFindAlternativePathTask.InnerSingleton,
			StaticRegisterNativesURTSFindAlternativePathTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSFindAlternativePathTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSFindAlternativePathTask_NoRegister()
{
	return URTSFindAlternativePathTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSFindAlternativePathTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Task node for finding alternative paths when blocked\n * Implements simple pathfinding around obstacles\n */" },
#endif
		{ "IncludePath", "RTSMovementBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task node for finding alternative paths when blocked\nImplements simple pathfinding around obstacles" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchRadius_MetaData[] = {
		{ "Category", "Pathfinding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Pathfinding parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pathfinding parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchAngles_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinClearDistance_MetaData[] = {
		{ "Category", "Pathfinding" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Number of directions to try\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of directions to try" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocationKey_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlternativePathKey_MetaData[] = {
		{ "Category", "Pathfinding" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SearchRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SearchAngles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinClearDistance;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetLocationKey;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AlternativePathKey;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSFindAlternativePathTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSFindAlternativePathTask_Statics::NewProp_SearchRadius = { "SearchRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFindAlternativePathTask, SearchRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchRadius_MetaData), NewProp_SearchRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSFindAlternativePathTask_Statics::NewProp_SearchAngles = { "SearchAngles", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFindAlternativePathTask, SearchAngles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchAngles_MetaData), NewProp_SearchAngles_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSFindAlternativePathTask_Statics::NewProp_MinClearDistance = { "MinClearDistance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFindAlternativePathTask, MinClearDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinClearDistance_MetaData), NewProp_MinClearDistance_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSFindAlternativePathTask_Statics::NewProp_TargetLocationKey = { "TargetLocationKey", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFindAlternativePathTask, TargetLocationKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocationKey_MetaData), NewProp_TargetLocationKey_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URTSFindAlternativePathTask_Statics::NewProp_AlternativePathKey = { "AlternativePathKey", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFindAlternativePathTask, AlternativePathKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlternativePathKey_MetaData), NewProp_AlternativePathKey_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSFindAlternativePathTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFindAlternativePathTask_Statics::NewProp_SearchRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFindAlternativePathTask_Statics::NewProp_SearchAngles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFindAlternativePathTask_Statics::NewProp_MinClearDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFindAlternativePathTask_Statics::NewProp_TargetLocationKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFindAlternativePathTask_Statics::NewProp_AlternativePathKey,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFindAlternativePathTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSFindAlternativePathTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFindAlternativePathTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSFindAlternativePathTask_Statics::ClassParams = {
	&URTSFindAlternativePathTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSFindAlternativePathTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSFindAlternativePathTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFindAlternativePathTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSFindAlternativePathTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSFindAlternativePathTask()
{
	if (!Z_Registration_Info_UClass_URTSFindAlternativePathTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSFindAlternativePathTask.OuterSingleton, Z_Construct_UClass_URTSFindAlternativePathTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSFindAlternativePathTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSFindAlternativePathTask);
URTSFindAlternativePathTask::~URTSFindAlternativePathTask() {}
// ********** End Class URTSFindAlternativePathTask ************************************************

// ********** Begin Class URTSFormationSpeedDecorator **********************************************
void URTSFormationSpeedDecorator::StaticRegisterNativesURTSFormationSpeedDecorator()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSFormationSpeedDecorator;
UClass* URTSFormationSpeedDecorator::GetPrivateStaticClass()
{
	using TClass = URTSFormationSpeedDecorator;
	if (!Z_Registration_Info_UClass_URTSFormationSpeedDecorator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSFormationSpeedDecorator"),
			Z_Registration_Info_UClass_URTSFormationSpeedDecorator.InnerSingleton,
			StaticRegisterNativesURTSFormationSpeedDecorator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSFormationSpeedDecorator.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSFormationSpeedDecorator_NoRegister()
{
	return URTSFormationSpeedDecorator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSFormationSpeedDecorator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Decorator that modifies movement speed based on formation status\n * Ensures formation units move at synchronized speeds\n */" },
#endif
		{ "IncludePath", "RTSMovementBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Decorator that modifies movement speed based on formation status\nEnsures formation units move at synchronized speeds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationSpeedMultiplier_MetaData[] = {
		{ "Category", "Formation Speed" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Speed modification parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Speed modification parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSynchronizeWithSlowestUnit_MetaData[] = {
		{ "Category", "Formation Speed" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Slightly slower in formation\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Slightly slower in formation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinSpeedMultiplier_MetaData[] = {
		{ "Category", "Formation Speed" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSpeedMultiplier_MetaData[] = {
		{ "Category", "Formation Speed" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FormationSpeedMultiplier;
	static void NewProp_bSynchronizeWithSlowestUnit_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSynchronizeWithSlowestUnit;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinSpeedMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSpeedMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSFormationSpeedDecorator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::NewProp_FormationSpeedMultiplier = { "FormationSpeedMultiplier", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFormationSpeedDecorator, FormationSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationSpeedMultiplier_MetaData), NewProp_FormationSpeedMultiplier_MetaData) };
void Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::NewProp_bSynchronizeWithSlowestUnit_SetBit(void* Obj)
{
	((URTSFormationSpeedDecorator*)Obj)->bSynchronizeWithSlowestUnit = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::NewProp_bSynchronizeWithSlowestUnit = { "bSynchronizeWithSlowestUnit", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSFormationSpeedDecorator), &Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::NewProp_bSynchronizeWithSlowestUnit_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSynchronizeWithSlowestUnit_MetaData), NewProp_bSynchronizeWithSlowestUnit_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::NewProp_MinSpeedMultiplier = { "MinSpeedMultiplier", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFormationSpeedDecorator, MinSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinSpeedMultiplier_MetaData), NewProp_MinSpeedMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::NewProp_MaxSpeedMultiplier = { "MaxSpeedMultiplier", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFormationSpeedDecorator, MaxSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSpeedMultiplier_MetaData), NewProp_MaxSpeedMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::NewProp_FormationSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::NewProp_bSynchronizeWithSlowestUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::NewProp_MinSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::NewProp_MaxSpeedMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSDecoratorNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::ClassParams = {
	&URTSFormationSpeedDecorator::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSFormationSpeedDecorator()
{
	if (!Z_Registration_Info_UClass_URTSFormationSpeedDecorator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSFormationSpeedDecorator.OuterSingleton, Z_Construct_UClass_URTSFormationSpeedDecorator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSFormationSpeedDecorator.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSFormationSpeedDecorator);
URTSFormationSpeedDecorator::~URTSFormationSpeedDecorator() {}
// ********** End Class URTSFormationSpeedDecorator ************************************************

// ********** Begin Class URTSMaintainFormationSpacingTask *****************************************
void URTSMaintainFormationSpacingTask::StaticRegisterNativesURTSMaintainFormationSpacingTask()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSMaintainFormationSpacingTask;
UClass* URTSMaintainFormationSpacingTask::GetPrivateStaticClass()
{
	using TClass = URTSMaintainFormationSpacingTask;
	if (!Z_Registration_Info_UClass_URTSMaintainFormationSpacingTask.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSMaintainFormationSpacingTask"),
			Z_Registration_Info_UClass_URTSMaintainFormationSpacingTask.InnerSingleton,
			StaticRegisterNativesURTSMaintainFormationSpacingTask,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSMaintainFormationSpacingTask.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSMaintainFormationSpacingTask_NoRegister()
{
	return URTSMaintainFormationSpacingTask::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Task node for maintaining formation spacing\n * Adjusts unit position to maintain proper formation spacing\n */" },
#endif
		{ "IncludePath", "RTSMovementBehaviorTree.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Task node for maintaining formation spacing\nAdjusts unit position to maintain proper formation spacing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesiredSpacing_MetaData[] = {
		{ "Category", "Formation Spacing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spacing parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spacing parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpacingTolerance_MetaData[] = {
		{ "Category", "Formation Spacing" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSpacingAdjustment_MetaData[] = {
		{ "Category", "Formation Spacing" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpacingCheckInterval_MetaData[] = {
		{ "Category", "Formation Spacing" },
		{ "ModuleRelativePath", "Public/RTSMovementBehaviorTree.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DesiredSpacing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpacingTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSpacingAdjustment;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpacingCheckInterval;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSMaintainFormationSpacingTask>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::NewProp_DesiredSpacing = { "DesiredSpacing", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSMaintainFormationSpacingTask, DesiredSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesiredSpacing_MetaData), NewProp_DesiredSpacing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::NewProp_SpacingTolerance = { "SpacingTolerance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSMaintainFormationSpacingTask, SpacingTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpacingTolerance_MetaData), NewProp_SpacingTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::NewProp_MaxSpacingAdjustment = { "MaxSpacingAdjustment", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSMaintainFormationSpacingTask, MaxSpacingAdjustment), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSpacingAdjustment_MetaData), NewProp_MaxSpacingAdjustment_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::NewProp_SpacingCheckInterval = { "SpacingCheckInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSMaintainFormationSpacingTask, SpacingCheckInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpacingCheckInterval_MetaData), NewProp_SpacingCheckInterval_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::NewProp_DesiredSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::NewProp_SpacingTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::NewProp_MaxSpacingAdjustment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::NewProp_SpacingCheckInterval,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URTSTaskNode,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::ClassParams = {
	&URTSMaintainFormationSpacingTask::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSMaintainFormationSpacingTask()
{
	if (!Z_Registration_Info_UClass_URTSMaintainFormationSpacingTask.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSMaintainFormationSpacingTask.OuterSingleton, Z_Construct_UClass_URTSMaintainFormationSpacingTask_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSMaintainFormationSpacingTask.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSMaintainFormationSpacingTask);
URTSMaintainFormationSpacingTask::~URTSMaintainFormationSpacingTask() {}
// ********** End Class URTSMaintainFormationSpacingTask *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSMovementBehaviorTreeFactory, URTSMovementBehaviorTreeFactory::StaticClass, TEXT("URTSMovementBehaviorTreeFactory"), &Z_Registration_Info_UClass_URTSMovementBehaviorTreeFactory, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSMovementBehaviorTreeFactory), 2906627159U) },
		{ Z_Construct_UClass_URTSCollisionAvoidanceMoveTask, URTSCollisionAvoidanceMoveTask::StaticClass, TEXT("URTSCollisionAvoidanceMoveTask"), &Z_Registration_Info_UClass_URTSCollisionAvoidanceMoveTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSCollisionAvoidanceMoveTask), 1040638103U) },
		{ Z_Construct_UClass_URTSPathBlockedCondition, URTSPathBlockedCondition::StaticClass, TEXT("URTSPathBlockedCondition"), &Z_Registration_Info_UClass_URTSPathBlockedCondition, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSPathBlockedCondition), 3364695380U) },
		{ Z_Construct_UClass_URTSFindAlternativePathTask, URTSFindAlternativePathTask::StaticClass, TEXT("URTSFindAlternativePathTask"), &Z_Registration_Info_UClass_URTSFindAlternativePathTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSFindAlternativePathTask), 1853480050U) },
		{ Z_Construct_UClass_URTSFormationSpeedDecorator, URTSFormationSpeedDecorator::StaticClass, TEXT("URTSFormationSpeedDecorator"), &Z_Registration_Info_UClass_URTSFormationSpeedDecorator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSFormationSpeedDecorator), 1017605751U) },
		{ Z_Construct_UClass_URTSMaintainFormationSpacingTask, URTSMaintainFormationSpacingTask::StaticClass, TEXT("URTSMaintainFormationSpacingTask"), &Z_Registration_Info_UClass_URTSMaintainFormationSpacingTask, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSMaintainFormationSpacingTask), 3778276221U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h__Script_ArmorWars_1953139306(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSMovementBehaviorTree_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSLandMovementComponent.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSLandMovementComponent() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSLandMovementComponent();
ARMORWARS_API UClass* Z_Construct_UClass_URTSLandMovementComponent_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UFloatingPawnMovement();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Class URTSLandMovementComponent Function GetAITargetLocation *******************
struct Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics
{
	struct RTSLandMovementComponent_eventGetAITargetLocation_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandMovementComponent_eventGetAITargetLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSLandMovementComponent, nullptr, "GetAITargetLocation", Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::RTSLandMovementComponent_eventGetAITargetLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::RTSLandMovementComponent_eventGetAITargetLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSLandMovementComponent::execGetAITargetLocation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetAITargetLocation();
	P_NATIVE_END;
}
// ********** End Class URTSLandMovementComponent Function GetAITargetLocation *********************

// ********** Begin Class URTSLandMovementComponent Function GetCurrentSpeedMultiplier *************
struct Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics
{
	struct RTSLandMovementComponent_eventGetCurrentSpeedMultiplier_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Land Movement" },
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandMovementComponent_eventGetCurrentSpeedMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSLandMovementComponent, nullptr, "GetCurrentSpeedMultiplier", Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::RTSLandMovementComponent_eventGetCurrentSpeedMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::RTSLandMovementComponent_eventGetCurrentSpeedMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSLandMovementComponent::execGetCurrentSpeedMultiplier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentSpeedMultiplier();
	P_NATIVE_END;
}
// ********** End Class URTSLandMovementComponent Function GetCurrentSpeedMultiplier ***************

// ********** Begin Class URTSLandMovementComponent Function IsMoving ******************************
struct Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics
{
	struct RTSLandMovementComponent_eventIsMoving_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSLandMovementComponent_eventIsMoving_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSLandMovementComponent_eventIsMoving_Parms), &Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSLandMovementComponent, nullptr, "IsMoving", Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::RTSLandMovementComponent_eventIsMoving_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::RTSLandMovementComponent_eventIsMoving_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSLandMovementComponent_IsMoving()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSLandMovementComponent_IsMoving_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSLandMovementComponent::execIsMoving)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsMoving();
	P_NATIVE_END;
}
// ********** End Class URTSLandMovementComponent Function IsMoving ********************************

// ********** Begin Class URTSLandMovementComponent Function MoveTowardsLocation *******************
struct Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics
{
	struct RTSLandMovementComponent_eventMoveTowardsLocation_Parms
	{
		FVector TargetLocation;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AI Movement Interface\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Movement Interface" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandMovementComponent_eventMoveTowardsLocation_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandMovementComponent_eventMoveTowardsLocation_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSLandMovementComponent, nullptr, "MoveTowardsLocation", Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::RTSLandMovementComponent_eventMoveTowardsLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::RTSLandMovementComponent_eventMoveTowardsLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSLandMovementComponent::execMoveTowardsLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TargetLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MoveTowardsLocation(Z_Param_Out_TargetLocation,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class URTSLandMovementComponent Function MoveTowardsLocation *********************

// ********** Begin Class URTSLandMovementComponent Function SetAITargetLocation *******************
struct Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics
{
	struct RTSLandMovementComponent_eventSetAITargetLocation_Parms
	{
		FVector NewTarget;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewTarget_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewTarget;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::NewProp_NewTarget = { "NewTarget", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandMovementComponent_eventSetAITargetLocation_Parms, NewTarget), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewTarget_MetaData), NewProp_NewTarget_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::NewProp_NewTarget,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSLandMovementComponent, nullptr, "SetAITargetLocation", Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::RTSLandMovementComponent_eventSetAITargetLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::RTSLandMovementComponent_eventSetAITargetLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSLandMovementComponent::execSetAITargetLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_NewTarget);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAITargetLocation(Z_Param_Out_NewTarget);
	P_NATIVE_END;
}
// ********** End Class URTSLandMovementComponent Function SetAITargetLocation *********************

// ********** Begin Class URTSLandMovementComponent Function SetTerrainSpeedMultiplier *************
struct Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics
{
	struct RTSLandMovementComponent_eventSetTerrainSpeedMultiplier_Parms
	{
		float Multiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Land Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Multiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::NewProp_Multiplier = { "Multiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSLandMovementComponent_eventSetTerrainSpeedMultiplier_Parms, Multiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::NewProp_Multiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSLandMovementComponent, nullptr, "SetTerrainSpeedMultiplier", Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::RTSLandMovementComponent_eventSetTerrainSpeedMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::RTSLandMovementComponent_eventSetTerrainSpeedMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSLandMovementComponent::execSetTerrainSpeedMultiplier)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Multiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTerrainSpeedMultiplier(Z_Param_Multiplier);
	P_NATIVE_END;
}
// ********** End Class URTSLandMovementComponent Function SetTerrainSpeedMultiplier ***************

// ********** Begin Class URTSLandMovementComponent Function StopMovement **************************
struct Z_Construct_UFunction_URTSLandMovementComponent_StopMovement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSLandMovementComponent_StopMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSLandMovementComponent, nullptr, "StopMovement", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSLandMovementComponent_StopMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSLandMovementComponent_StopMovement_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSLandMovementComponent_StopMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSLandMovementComponent_StopMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSLandMovementComponent::execStopMovement)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopMovement();
	P_NATIVE_END;
}
// ********** End Class URTSLandMovementComponent Function StopMovement ****************************

// ********** Begin Class URTSLandMovementComponent ************************************************
void URTSLandMovementComponent::StaticRegisterNativesURTSLandMovementComponent()
{
	UClass* Class = URTSLandMovementComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetAITargetLocation", &URTSLandMovementComponent::execGetAITargetLocation },
		{ "GetCurrentSpeedMultiplier", &URTSLandMovementComponent::execGetCurrentSpeedMultiplier },
		{ "IsMoving", &URTSLandMovementComponent::execIsMoving },
		{ "MoveTowardsLocation", &URTSLandMovementComponent::execMoveTowardsLocation },
		{ "SetAITargetLocation", &URTSLandMovementComponent::execSetAITargetLocation },
		{ "SetTerrainSpeedMultiplier", &URTSLandMovementComponent::execSetTerrainSpeedMultiplier },
		{ "StopMovement", &URTSLandMovementComponent::execStopMovement },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSLandMovementComponent;
UClass* URTSLandMovementComponent::GetPrivateStaticClass()
{
	using TClass = URTSLandMovementComponent;
	if (!Z_Registration_Info_UClass_URTSLandMovementComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSLandMovementComponent"),
			Z_Registration_Info_UClass_URTSLandMovementComponent.InnerSingleton,
			StaticRegisterNativesURTSLandMovementComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSLandMovementComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSLandMovementComponent_NoRegister()
{
	return URTSLandMovementComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSLandMovementComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Custom movement component for land-based RTS units\n * Provides vehicle-like movement with forward motion and turning\n * Supports terrain-based speed modifiers and realistic ground movement\n */" },
#endif
		{ "IncludePath", "RTSLandMovementComponent.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom movement component for land-based RTS units\nProvides vehicle-like movement with forward motion and turning\nSupports terrain-based speed modifiers and realistic ground movement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TurnRate_MetaData[] = {
		{ "Category", "Land Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement Properties\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement Properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinTurnRadius_MetaData[] = {
		{ "Category", "Land Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Degrees per second\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Degrees per second" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseRealisticTurning_MetaData[] = {
		{ "Category", "Land Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Minimum turning radius in cm\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum turning radius in cm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainSpeedMultiplier_MetaData[] = {
		{ "Category", "Land Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Use vehicle-like turning vs instant rotation\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Use vehicle-like turning vs instant rotation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArrivalTolerance_MetaData[] = {
		{ "Category", "AI Movement" },
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DecelerationDistanceMultiplier_MetaData[] = {
		{ "Category", "AI Movement" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Deceleration settings\n// NOTE: This deceleration system is WORKING and ACTIVELY USED - DO NOT REMOVE\n// Provides smooth deceleration as units approach their targets for realistic movement\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Deceleration settings\nNOTE: This deceleration system is WORKING and ACTIVELY USED - DO NOT REMOVE\nProvides smooth deceleration as units approach their targets for realistic movement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumSpeedFactor_MetaData[] = {
		{ "Category", "AI Movement" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Start decelerating at this multiple of arrival tolerance\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start decelerating at this multiple of arrival tolerance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug logging\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSLandMovementComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug logging" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TurnRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinTurnRadius;
	static void NewProp_bUseRealisticTurning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseRealisticTurning;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerrainSpeedMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ArrivalTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DecelerationDistanceMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinimumSpeedFactor;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSLandMovementComponent_GetAITargetLocation, "GetAITargetLocation" }, // 14880388
		{ &Z_Construct_UFunction_URTSLandMovementComponent_GetCurrentSpeedMultiplier, "GetCurrentSpeedMultiplier" }, // 1761293473
		{ &Z_Construct_UFunction_URTSLandMovementComponent_IsMoving, "IsMoving" }, // 1240672318
		{ &Z_Construct_UFunction_URTSLandMovementComponent_MoveTowardsLocation, "MoveTowardsLocation" }, // 2270080441
		{ &Z_Construct_UFunction_URTSLandMovementComponent_SetAITargetLocation, "SetAITargetLocation" }, // 1788588461
		{ &Z_Construct_UFunction_URTSLandMovementComponent_SetTerrainSpeedMultiplier, "SetTerrainSpeedMultiplier" }, // 1594997760
		{ &Z_Construct_UFunction_URTSLandMovementComponent_StopMovement, "StopMovement" }, // 2545022381
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSLandMovementComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_TurnRate = { "TurnRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSLandMovementComponent, TurnRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TurnRate_MetaData), NewProp_TurnRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_MinTurnRadius = { "MinTurnRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSLandMovementComponent, MinTurnRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinTurnRadius_MetaData), NewProp_MinTurnRadius_MetaData) };
void Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_bUseRealisticTurning_SetBit(void* Obj)
{
	((URTSLandMovementComponent*)Obj)->bUseRealisticTurning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_bUseRealisticTurning = { "bUseRealisticTurning", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSLandMovementComponent), &Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_bUseRealisticTurning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseRealisticTurning_MetaData), NewProp_bUseRealisticTurning_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_TerrainSpeedMultiplier = { "TerrainSpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSLandMovementComponent, TerrainSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainSpeedMultiplier_MetaData), NewProp_TerrainSpeedMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_ArrivalTolerance = { "ArrivalTolerance", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSLandMovementComponent, ArrivalTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArrivalTolerance_MetaData), NewProp_ArrivalTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_DecelerationDistanceMultiplier = { "DecelerationDistanceMultiplier", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSLandMovementComponent, DecelerationDistanceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DecelerationDistanceMultiplier_MetaData), NewProp_DecelerationDistanceMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_MinimumSpeedFactor = { "MinimumSpeedFactor", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSLandMovementComponent, MinimumSpeedFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumSpeedFactor_MetaData), NewProp_MinimumSpeedFactor_MetaData) };
void Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((URTSLandMovementComponent*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSLandMovementComponent), &Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSLandMovementComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_TurnRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_MinTurnRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_bUseRealisticTurning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_TerrainSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_ArrivalTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_DecelerationDistanceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_MinimumSpeedFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSLandMovementComponent_Statics::NewProp_bEnableDebugLogging,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSLandMovementComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSLandMovementComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UFloatingPawnMovement,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSLandMovementComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSLandMovementComponent_Statics::ClassParams = {
	&URTSLandMovementComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSLandMovementComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSLandMovementComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSLandMovementComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSLandMovementComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSLandMovementComponent()
{
	if (!Z_Registration_Info_UClass_URTSLandMovementComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSLandMovementComponent.OuterSingleton, Z_Construct_UClass_URTSLandMovementComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSLandMovementComponent.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSLandMovementComponent);
URTSLandMovementComponent::~URTSLandMovementComponent() {}
// ********** End Class URTSLandMovementComponent **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandMovementComponent_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSLandMovementComponent, URTSLandMovementComponent::StaticClass, TEXT("URTSLandMovementComponent"), &Z_Registration_Info_UClass_URTSLandMovementComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSLandMovementComponent), 2179853907U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandMovementComponent_h__Script_ArmorWars_4233181817(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandMovementComponent_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSLandMovementComponent_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "RTSBehaviorNode.h"
#include "RTSBehaviorNodes.h"
#include "RTSCombatBehaviorTree.generated.h"

class ARTSUnit;
class ARTSBaseActor;
class URTSBehaviorTreeComponent;

/**
 * Factory class for creating combat-focused behavior trees
 * Handles complex combat scenarios including target prioritization,
 * tactical positioning, and coordinated attacks
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSCombatBehaviorTreeFactory : public UObject
{
    GENERATED_BODY()

public:
    URTSCombatBehaviorTreeFactory();

    // Factory methods for creating different combat behavior trees
    UFUNCTION(BlueprintCallable, Category = "Combat Behavior Tree")
    static URTSBehaviorNode* CreateBasicCombatTree();

    UFUNCTION(BlueprintCallable, Category = "Combat Behavior Tree")
    static URTSBehaviorNode* CreateTacticalCombatTree();

    UFUNCTION(BlueprintCallable, Category = "Combat Behavior Tree")
    static URTSBehaviorNode* CreateSupportCombatTree();

    UFUNCTION(BlueprintCallable, Category = "Combat Behavior Tree")
    static URTSBehaviorNode* CreateAggressiveCombatTree();

    UFUNCTION(BlueprintCallable, Category = "Combat Behavior Tree")
    static URTSBehaviorNode* CreateDefensiveCombatTree();

protected:
    // Helper methods for creating common combat node combinations
    static URTSCompositeNode* CreateTargetAcquisitionSequence();
    static URTSCompositeNode* CreateEngagementSequence();
    static URTSCompositeNode* CreateTacticalPositioningSequence();
    static URTSCompositeNode* CreateReturnFireSequence();
    static URTSCompositeNode* CreateRetreatSequence();
};

/**
 * Advanced target selection task with priority system
 * Selects targets based on threat level, distance, and tactical value
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSAdvancedTargetSelectionTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSAdvancedTargetSelectionTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

protected:
    // Target selection parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    float MaxTargetRange = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    bool bUseUnitWeaponRange = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    float HealthPriorityWeight = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    float DistancePriorityWeight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    float ThreatPriorityWeight = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    FString SelectedTargetKey = TEXT("SelectedTarget");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    float TargetSwitchCooldown = 3.0f;

    // Internal state
    float LastTargetSwitchTime = 0.0f;
    TWeakObjectPtr<ARTSBaseActor> CurrentTarget;

    // Helper functions
    virtual float CalculateTargetPriority(ARTSUnit* Unit, ARTSBaseActor* Target);
    virtual float CalculateThreatLevel(ARTSUnit* Unit, ARTSBaseActor* Target);
    virtual bool IsValidTarget(ARTSUnit* Unit, ARTSBaseActor* Target);
};

/**
 * Tactical positioning task for optimal combat positioning
 * Considers cover, range, and formation positioning
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSTacticalPositioningTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSTacticalPositioningTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

protected:
    // Positioning parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Positioning")
    float OptimalRange = 600.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Positioning")
    float MinRange = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Positioning")
    float MaxRange = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Positioning")
    bool bMaintainFormationPosition = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Positioning")
    bool bSeekCover = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Positioning")
    FString TargetActorKey = TEXT("SelectedTarget");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Positioning")
    FString TacticalPositionKey = TEXT("TacticalPosition");

    // Internal state
    FVector CurrentTacticalPosition = FVector::ZeroVector;
    float LastPositionUpdate = 0.0f;
    float PositionUpdateInterval = 1.0f;

    // Helper functions
    virtual FVector CalculateOptimalPosition(ARTSUnit* Unit, ARTSBaseActor* Target);
    virtual FVector FindCoverPosition(ARTSUnit* Unit, ARTSBaseActor* Target);
    virtual bool IsPositionSafe(ARTSUnit* Unit, const FVector& Position);
};

/**
 * Coordinated attack task for group combat
 * Synchronizes attacks with nearby friendly units
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSCoordinatedAttackTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSCoordinatedAttackTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

protected:
    // Coordination parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Coordinated Attack")
    float CoordinationRadius = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Coordinated Attack")
    int32 MinCoordinatedUnits = 2;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Coordinated Attack")
    float AttackSynchronizationDelay = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Coordinated Attack")
    bool bWaitForAllies = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Coordinated Attack")
    FString TargetActorKey = TEXT("SelectedTarget");

    // Internal state
    TArray<TWeakObjectPtr<ARTSUnit>> CoordinatedUnits;
    float AttackStartTime = 0.0f;
    bool bAttackInitiated = false;

    // Helper functions
    virtual TArray<ARTSUnit*> FindCoordinatedUnits(ARTSUnit* Unit);
    virtual bool AreAlliesReady(ARTSUnit* Unit, const TArray<ARTSUnit*>& Allies);
    virtual void InitiateCoordinatedAttack(ARTSUnit* Unit, ARTSBaseActor* Target);
};

/**
 * Return fire task for defensive combat
 * Automatically returns fire while maintaining primary objectives
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSReturnFireTask : public URTSTaskNode
{
    GENERATED_BODY()

public:
    URTSReturnFireTask();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;
    virtual void InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent) override;

protected:
    // Return fire parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    float ReturnFireRange = 800.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    float ReturnFireDuration = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    bool bPrioritizeAttackers = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    bool bInterruptMovement = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    FString AttackerKey = TEXT("Attacker");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    FString ReturnFireTargetKey = TEXT("ReturnFireTarget");

    // Internal state
    float ReturnFireStartTime = 0.0f;
    TWeakObjectPtr<ARTSBaseActor> CurrentAttacker;
    bool bReturningFire = false;

    // Helper functions
    virtual ARTSBaseActor* FindNearestAttacker(ARTSUnit* Unit);
    virtual bool IsBeingAttacked(ARTSUnit* Unit);
    virtual void StartReturnFire(ARTSUnit* Unit, ARTSBaseActor* Attacker);
};

/**
 * Condition node for checking if unit is under attack
 * Used to trigger defensive behaviors
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSUnderAttackCondition : public URTSConditionNode
{
    GENERATED_BODY()

public:
    URTSUnderAttackCondition();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

protected:
    // Attack detection parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Detection")
    float AttackDetectionTime = 3.0f; // How long to remember being attacked

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Detection")
    bool bStoreAttacker = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Detection")
    FString AttackerKey = TEXT("Attacker");

    // Internal state
    float LastAttackTime = 0.0f;
    TWeakObjectPtr<ARTSBaseActor> LastAttacker;
};

/**
 * Condition node for checking if unit has tactical advantage
 * Considers positioning, numbers, and unit capabilities
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API URTSTacticalAdvantageCondition : public URTSConditionNode
{
    GENERATED_BODY()

public:
    URTSTacticalAdvantageCondition();

    virtual ERTSBehaviorNodeStatus ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime) override;

protected:
    // Advantage calculation parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Advantage")
    float AnalysisRadius = 800.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Advantage")
    float NumbersAdvantageWeight = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Advantage")
    float HealthAdvantageWeight = 1.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Advantage")
    float RangeAdvantageWeight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tactical Advantage")
    float MinAdvantageThreshold = 1.2f; // 20% advantage required

    // Helper functions
    virtual float CalculateTacticalAdvantage(ARTSUnit* Unit);
    virtual int32 CountNearbyAllies(ARTSUnit* Unit);
    virtual int32 CountNearbyEnemies(ARTSUnit* Unit);
    virtual float CalculateAverageAllyHealth(ARTSUnit* Unit);
    virtual float CalculateAverageEnemyHealth(ARTSUnit* Unit);
};

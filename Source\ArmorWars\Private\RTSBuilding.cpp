#include "RTSBuilding.h"
#include "RTSWeaponController.h"
#include "RTSFactoryComponent.h"
#include "RTSUnit.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"

ARTSBuilding::ARTSBuilding()
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Set actor type to Building
    ActorType = ERTSActorType::Building;
    
    // Create components
    MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
    RootComponent = MeshComponent;

    WeaponController = CreateDefaultSubobject<URTSWeaponController>(TEXT("WeaponController"));
    FactoryComponent = CreateDefaultSubobject<URTSFactoryComponent>(TEXT("FactoryComponent"));

    // Buildings can't be selected during construction by default
    bCanBeSelected = false;
}

void ARTSBuilding::BeginPlay()
{
    Super::BeginPlay();
    
    // Start construction automatically
    StartConstruction();
}

void ARTSBuilding::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (IsAlive())
    {
        UpdateConstruction(DeltaTime);
        
        if (IsOperational())
        {
            UpdateCombat(DeltaTime);
        }
    }
}

// Construction Functions
void ARTSBuilding::StartConstruction()
{
    if (BuildingState != ERTSBuildingState::UnderConstruction)
    {
        SetBuildingState(ERTSBuildingState::UnderConstruction);
    }
    
    bConstructionPaused = false;
    ConstructionStartTime = GetWorld()->GetTimeSeconds();
    
    OnConstructionStarted();
}

void ARTSBuilding::PauseConstruction()
{
    if (IsUnderConstruction() && !bConstructionPaused)
    {
        bConstructionPaused = true;
        OnConstructionPaused();
    }
}

void ARTSBuilding::ResumeConstruction()
{
    if (IsUnderConstruction() && bConstructionPaused)
    {
        bConstructionPaused = false;
        // Adjust start time to account for paused time
        float PausedTime = GetWorld()->GetTimeSeconds() - ConstructionStartTime - (ConstructionProgress * ConstructionTime);
        ConstructionStartTime += PausedTime;
        OnConstructionResumed();
    }
}

void ARTSBuilding::CompleteConstruction()
{
    ConstructionProgress = 1.0f;
    SetBuildingState(ERTSBuildingState::Operational);
    bCanBeSelected = true;
    
    OnConstructionCompleted();
}

bool ARTSBuilding::IsUnderConstruction() const
{
    return BuildingState == ERTSBuildingState::UnderConstruction;
}

bool ARTSBuilding::IsOperational() const
{
    return BuildingState == ERTSBuildingState::Operational;
}

float ARTSBuilding::GetConstructionTimeRemaining() const
{
    if (!IsUnderConstruction())
    {
        return 0.0f;
    }
    
    return ConstructionTime * (1.0f - ConstructionProgress);
}

// State Functions
void ARTSBuilding::SetBuildingState(ERTSBuildingState NewState)
{
    if (BuildingState != NewState)
    {
        ERTSBuildingState OldState = BuildingState;
        BuildingState = NewState;
        
        OnBuildingStateChanged.Broadcast(this, NewState);
        OnBuildingStateChangedEvent(OldState, NewState);
    }
}

ERTSBuildingState ARTSBuilding::GetBuildingState() const
{
    return BuildingState;
}

// Combat Functions
void ARTSBuilding::AttackTarget(ARTSBaseActor* Target)
{
    if (!IsOperational() || !Target || !CanAttackTarget(Target) || !WeaponController)
    {
        return;
    }

    if (WeaponController->FireAtTarget(Target))
    {
        OnCombatChanged.Broadcast(this, Target);
        OnAttackStarted(Target);
    }
}

void ARTSBuilding::StopAttacking()
{
    if (WeaponController)
    {
        WeaponController->StopFiring();
        WeaponController->ClearTarget();
        OnAttackStopped();
    }
}

bool ARTSBuilding::CanAttackTarget(const ARTSBaseActor* Target) const
{
    if (!IsOperational() || !Target || !Target->IsAlive() || IsOnSameTeam(Target) || !WeaponController)
    {
        return false;
    }

    // Check if this is a defensive building
    if (!IsDefensiveBuilding())
    {
        return false;
    }

    // Check if we can target this type of unit
    if (const ARTSUnit* TargetUnit = Cast<ARTSUnit>(Target))
    {
        return WeaponController->CanTargetDomain(TargetUnit->UnitDomain);
    }

    // Buildings can always be targeted if we have land targeting capability
    if (Target->IsBuilding())
    {
        return WeaponController->CanTargetDomain(ERTSUnitDomain::Land);
    }

    return true;
}

bool ARTSBuilding::IsInAttackRange(const ARTSBaseActor* Target) const
{
    if (!Target || !WeaponController)
    {
        return false;
    }

    float Distance = FVector::Dist(GetActorLocation(), Target->GetActorLocation());
    return Distance <= WeaponController->GetMaxRange();
}

ARTSBaseActor* ARTSBuilding::FindNearestEnemy(float SearchRange) const
{
    if (SearchRange <= 0.0f)
    {
        SearchRange = GetMaxAttackRange();
    }
    
    ARTSBaseActor* NearestEnemy = nullptr;
    float NearestDistance = FLT_MAX;
    
    if (UWorld* World = GetWorld())
    {
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(World, ARTSBaseActor::StaticClass(), FoundActors);
        
        FVector MyLocation = GetActorLocation();
        
        for (AActor* Actor : FoundActors)
        {
            if (ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
            {
                if (IsEnemy(RTSActor) && RTSActor->IsAlive() && CanAttackTarget(RTSActor))
                {
                    float Distance = FVector::Dist(MyLocation, RTSActor->GetActorLocation());
                    if (Distance <= SearchRange && Distance < NearestDistance)
                    {
                        NearestDistance = Distance;
                        NearestEnemy = RTSActor;
                    }
                }
            }
        }
    }
    
    return NearestEnemy;
}

// Building Type Functions
bool ARTSBuilding::IsDefensiveBuilding() const
{
    return BuildingType == ERTSBuildingType::Defense;
}

bool ARTSBuilding::IsProductionBuilding() const
{
    return BuildingType == ERTSBuildingType::Production;
}

bool ARTSBuilding::IsResourceBuilding() const
{
    return BuildingType == ERTSBuildingType::Resource;
}

bool ARTSBuilding::HasWeapons() const
{
    return WeaponController && WeaponController->GetWeaponCount() > 0;
}

// Power Functions
int32 ARTSBuilding::GetNetPowerGeneration() const
{
    return PowerGeneration - PowerConsumption;
}

bool ARTSBuilding::IsGeneratingPower() const
{
    return PowerGeneration > 0;
}

bool ARTSBuilding::IsConsumingPower() const
{
    return PowerConsumption > 0;
}

// Protected Functions
void ARTSBuilding::UpdateConstruction(float DeltaTime)
{
    if (!IsUnderConstruction() || bConstructionPaused)
    {
        return;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float ElapsedTime = CurrentTime - ConstructionStartTime;
    float NewProgress = FMath::Clamp(ElapsedTime / ConstructionTime, 0.0f, 1.0f);
    
    if (NewProgress != ConstructionProgress)
    {
        ConstructionProgress = NewProgress;
        OnConstructionProgress.Broadcast(this, ConstructionProgress);
        
        if (ConstructionProgress >= 1.0f)
        {
            CompleteConstruction();
        }
    }
}

void ARTSBuilding::UpdateCombat(float DeltaTime)
{
    if (!IsDefensiveBuilding() || !WeaponController)
    {
        return;
    }

    // Auto-target nearest enemy if not currently attacking
    if (!IsAttacking())
    {
        ARTSBaseActor* NearestEnemy = WeaponController->FindBestTarget();
        if (NearestEnemy)
        {
            AttackTarget(NearestEnemy);
        }
    }
}

void ARTSBuilding::HandleDeath()
{
    // Set building state to destroyed
    SetBuildingState(ERTSBuildingState::Destroyed);
    
    // Stop any ongoing attacks
    StopAttacking();
    
    // Call parent implementation
    Super::HandleDeath();
}

// New Combat Functions using Weapon Controller
bool ARTSBuilding::IsAttacking() const
{
    return WeaponController && WeaponController->IsFiring();
}

ARTSBaseActor* ARTSBuilding::GetCurrentTarget() const
{
    return WeaponController ? WeaponController->GetCurrentTarget() : nullptr;
}

float ARTSBuilding::GetMaxAttackRange() const
{
    return WeaponController ? WeaponController->GetMaxRange() : 0.0f;
}

float ARTSBuilding::GetTotalDamagePerSecond() const
{
    return WeaponController ? WeaponController->GetTotalDamagePerSecond() : 0.0f;
}

bool ARTSBuilding::CanTargetDomain(ERTSUnitDomain Domain) const
{
    return WeaponController && WeaponController->CanTargetDomain(Domain);
}

// Factory Functions
bool ARTSBuilding::CanProduceUnits() const
{
    return IsProductionBuilding() && IsOperational() && FactoryComponent && FactoryComponent->IsProducing();
}

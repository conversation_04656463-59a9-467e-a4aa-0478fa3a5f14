// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSPlayerController.h"

#ifdef ARMORWARS_RTSPlayerController_generated_h
#error "RTSPlayerController.generated.h already included, missing '#pragma once' in RTSPlayerController.h"
#endif
#define ARMORWARS_RTSPlayerController_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ARTSPawn;
class URTSSelectionSystem;
struct FInputActionValue;

// ********** Begin Class ARTSPlayerController *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCalculateFormationPositions); \
	DECLARE_FUNCTION(execIsValidMoveLocation); \
	DECLARE_FUNCTION(execGetMouseWorldLocation); \
	DECLARE_FUNCTION(execIssueAttackMoveCommand); \
	DECLARE_FUNCTION(execIssueMoveCommand); \
	DECLARE_FUNCTION(execIssueStopCommand); \
	DECLARE_FUNCTION(execIssueAttackCommand); \
	DECLARE_FUNCTION(execIssueMoveCommandToLocation); \
	DECLARE_FUNCTION(execSetUnitSelection); \
	DECLARE_FUNCTION(execRemoveUnitFromSelection); \
	DECLARE_FUNCTION(execAddUnitToSelection); \
	DECLARE_FUNCTION(execClearUnitSelection); \
	DECLARE_FUNCTION(execGetSelectedUnits); \
	DECLARE_FUNCTION(execScreenToWorldPosition); \
	DECLARE_FUNCTION(execFindUnitsInSelectionBoxWorldSpace); \
	DECLARE_FUNCTION(execFindUnitsInSelectionBox); \
	DECLARE_FUNCTION(execPerformSingleSelection); \
	DECLARE_FUNCTION(execPerformBoxSelection); \
	DECLARE_FUNCTION(execSetCameraRotationSpeed); \
	DECLARE_FUNCTION(execSetCameraZoomSpeed); \
	DECLARE_FUNCTION(execSetCameraMovementSpeed); \
	DECLARE_FUNCTION(execGetRTSPawn); \
	DECLARE_FUNCTION(execGetCurrentMousePosition); \
	DECLARE_FUNCTION(execEndSelectionBox); \
	DECLARE_FUNCTION(execUpdateSelectionBox); \
	DECLARE_FUNCTION(execStartSelectionBox); \
	DECLARE_FUNCTION(execSetPlayerTeamID); \
	DECLARE_FUNCTION(execGetPlayerTeamID); \
	DECLARE_FUNCTION(execGetSelectionSystem); \
	DECLARE_FUNCTION(execOnCameraRotate); \
	DECLARE_FUNCTION(execOnCameraZoom); \
	DECLARE_FUNCTION(execOnCameraMoveRight); \
	DECLARE_FUNCTION(execOnCameraMoveForward); \
	DECLARE_FUNCTION(execOnSecondaryAction); \
	DECLARE_FUNCTION(execOnPrimaryActionReleased); \
	DECLARE_FUNCTION(execOnPrimaryActionPressed); \
	DECLARE_FUNCTION(execOnPrimaryAction);


#define FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h_24_CALLBACK_WRAPPERS
ARMORWARS_API UClass* Z_Construct_UClass_ARTSPlayerController_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h_24_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARTSPlayerController(); \
	friend struct Z_Construct_UClass_ARTSPlayerController_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_ARTSPlayerController_NoRegister(); \
public: \
	DECLARE_CLASS2(ARTSPlayerController, APlayerController, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_ARTSPlayerController_NoRegister) \
	DECLARE_SERIALIZER(ARTSPlayerController)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h_24_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ARTSPlayerController(ARTSPlayerController&&) = delete; \
	ARTSPlayerController(const ARTSPlayerController&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ARTSPlayerController); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARTSPlayerController); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ARTSPlayerController) \
	NO_API virtual ~ARTSPlayerController();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h_21_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h_24_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h_24_CALLBACK_WRAPPERS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h_24_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h_24_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ARTSPlayerController;

// ********** End Class ARTSPlayerController *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSPlayerController_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

{"Version": "1.2", "Data": {"Source": "f:\\armorwars\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\armorwars\\module.armorwars.14.cpp", "ProvidedModule": "", "PCH": "f:\\armorwars\\intermediate\\build\\win64\\x64\\armorwarseditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["f:\\armorwars\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\armorwars\\definitions.armorwars.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsweaponcomponent.gen.cpp", "f:\\armorwars\\source\\armorwars\\public\\rtsweaponcomponent.h", "f:\\armorwars\\source\\armorwars\\public\\rtsprojectile.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spherecomponent.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spherecomponent.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\projectilemovementcomponent.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\movementcomponent.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\movementcomponent.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\projectilemovementcomponent.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsarmorcomponent.h", "f:\\armorwars\\source\\armorwars\\public\\rtsarmortypes.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsarmortypes.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsarmorcomponent.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsprojectile.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsbaseactor.h", "f:\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsbaseactor.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsweaponcomponent.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsweaponcontroller.gen.cpp", "f:\\armorwars\\source\\armorwars\\public\\rtsweaponcontroller.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsweaponcontroller.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\unitdatabasesubsystem.gen.cpp", "f:\\armorwars\\source\\armorwars\\public\\unitdatabasesubsystem.h", "f:\\armorwars\\source\\armorwars\\public\\rtseconomysubsystem.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtseconomysubsystem.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\unitdatabasesubsystem.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}
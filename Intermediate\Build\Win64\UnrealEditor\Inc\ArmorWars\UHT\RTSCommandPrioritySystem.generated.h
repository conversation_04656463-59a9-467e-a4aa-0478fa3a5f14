// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSCommandPrioritySystem.h"

#ifdef ARMORWARS_RTSCommandPrioritySystem_generated_h
#error "RTSCommandPrioritySystem.generated.h already included, missing '#pragma once' in RTSCommandPrioritySystem.h"
#endif
#define ARMORWARS_RTSCommandPrioritySystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class ERTSCommandSource : uint8;
enum class ERTSCommandType : uint8;
struct FRTSCommand;
struct FRTSCommandContext;
struct FRTSPriorityCommand;

// ********** Begin ScriptStruct FRTSCommandContext ************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_29_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSCommandContext_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSCommandContext;
// ********** End ScriptStruct FRTSCommandContext **************************************************

// ********** Begin ScriptStruct FRTSPriorityCommand ***********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_73_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSPriorityCommand;
// ********** End ScriptStruct FRTSPriorityCommand *************************************************

// ********** Begin Delegate FOnCommandAdded *******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_267_DELEGATE \
static void FOnCommandAdded_DelegateWrapper(const FMulticastScriptDelegate& OnCommandAdded, FRTSPriorityCommand const& PriorityCommand);


// ********** End Delegate FOnCommandAdded *********************************************************

// ********** Begin Delegate FOnCommandExecuted ****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_268_DELEGATE \
static void FOnCommandExecuted_DelegateWrapper(const FMulticastScriptDelegate& OnCommandExecuted, FRTSPriorityCommand const& PriorityCommand);


// ********** End Delegate FOnCommandExecuted ******************************************************

// ********** Begin Delegate FOnCommandRemoved *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_269_DELEGATE \
static void FOnCommandRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnCommandRemoved, FRTSPriorityCommand const& PriorityCommand);


// ********** End Delegate FOnCommandRemoved *******************************************************

// ********** Begin Delegate FOnQueueChanged *******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_270_DELEGATE \
static void FOnQueueChanged_DelegateWrapper(const FMulticastScriptDelegate& OnQueueChanged);


// ********** End Delegate FOnQueueChanged *********************************************************

// ********** Begin Class URTSCommandPrioritySystem ************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_125_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCalculateDeadlineFactor); \
	DECLARE_FUNCTION(execCalculateUrgencyFactor); \
	DECLARE_FUNCTION(execGetCommandTypePriorityMultiplier); \
	DECLARE_FUNCTION(execGetSourcePriorityMultiplier); \
	DECLARE_FUNCTION(execCalculateCommandPriority); \
	DECLARE_FUNCTION(execUpdatePriorities); \
	DECLARE_FUNCTION(execExecuteCommand); \
	DECLARE_FUNCTION(execExecuteHighestPriorityCommand); \
	DECLARE_FUNCTION(execCanInterruptCurrentCommand); \
	DECLARE_FUNCTION(execGetQueueSize); \
	DECLARE_FUNCTION(execHasCommands); \
	DECLARE_FUNCTION(execGetCommandsBySource); \
	DECLARE_FUNCTION(execGetCommandsByPriority); \
	DECLARE_FUNCTION(execGetHighestPriorityCommand); \
	DECLARE_FUNCTION(execRemoveCommandsByIssuer); \
	DECLARE_FUNCTION(execRemoveCommandsBySource); \
	DECLARE_FUNCTION(execRemoveCommand); \
	DECLARE_FUNCTION(execClearQueue); \
	DECLARE_FUNCTION(execAddFormationCommand); \
	DECLARE_FUNCTION(execAddEmergencyCommand); \
	DECLARE_FUNCTION(execAddAICommand); \
	DECLARE_FUNCTION(execAddPlayerCommand); \
	DECLARE_FUNCTION(execAddCommand);


ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandPrioritySystem_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_125_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSCommandPrioritySystem(); \
	friend struct Z_Construct_UClass_URTSCommandPrioritySystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandPrioritySystem_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSCommandPrioritySystem, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSCommandPrioritySystem_NoRegister) \
	DECLARE_SERIALIZER(URTSCommandPrioritySystem)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_125_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSCommandPrioritySystem(URTSCommandPrioritySystem&&) = delete; \
	URTSCommandPrioritySystem(const URTSCommandPrioritySystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSCommandPrioritySystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSCommandPrioritySystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSCommandPrioritySystem) \
	NO_API virtual ~URTSCommandPrioritySystem();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_122_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_125_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_125_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_125_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h_125_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSCommandPrioritySystem;

// ********** End Class URTSCommandPrioritySystem **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h

// ********** Begin Enum ERTSCommandSource *********************************************************
#define FOREACH_ENUM_ERTSCOMMANDSOURCE(op) \
	op(ERTSCommandSource::Player) \
	op(ERTSCommandSource::AIController) \
	op(ERTSCommandSource::Formation) \
	op(ERTSCommandSource::Autonomous) \
	op(ERTSCommandSource::Emergency) \
	op(ERTSCommandSource::System) 

enum class ERTSCommandSource : uint8;
template<> struct TIsUEnumClass<ERTSCommandSource> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSCommandSource>();
// ********** End Enum ERTSCommandSource ***********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSLandUnit.h"

#ifdef ARMORWARS_RTSLandUnit_generated_h
#error "RTSLandUnit.generated.h already included, missing '#pragma once' in RTSLandUnit.h"
#endif
#define ARMORWARS_RTSLandUnit_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSLandUnit;
enum class ERTSTerrainType : uint8;
struct FTerrainMovementData;

// ********** Begin ScriptStruct FTerrainMovementData **********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_29_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTerrainMovementData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTerrainMovementData;
// ********** End ScriptStruct FTerrainMovementData ************************************************

// ********** Begin Delegate FOnTerrainChanged *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_162_DELEGATE \
static void FOnTerrainChanged_DelegateWrapper(const FMulticastScriptDelegate& OnTerrainChanged, ARTSLandUnit* LandUnit, ERTSTerrainType NewTerrain);


// ********** End Delegate FOnTerrainChanged *******************************************************

// ********** Begin Class ARTSLandUnit *************************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_65_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetTerrainMovementData); \
	DECLARE_FUNCTION(execSetTerrainMovementData); \
	DECLARE_FUNCTION(execDetectTerrainType); \
	DECLARE_FUNCTION(execUpdateTerrainType); \
	DECLARE_FUNCTION(execCanTraverseTerrain); \
	DECLARE_FUNCTION(execGetCurrentSpeedMultiplier); \
	DECLARE_FUNCTION(execSetDestination);


#define FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_65_CALLBACK_WRAPPERS
ARMORWARS_API UClass* Z_Construct_UClass_ARTSLandUnit_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_65_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARTSLandUnit(); \
	friend struct Z_Construct_UClass_ARTSLandUnit_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_ARTSLandUnit_NoRegister(); \
public: \
	DECLARE_CLASS2(ARTSLandUnit, ARTSUnit, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_ARTSLandUnit_NoRegister) \
	DECLARE_SERIALIZER(ARTSLandUnit)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_65_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ARTSLandUnit(ARTSLandUnit&&) = delete; \
	ARTSLandUnit(const ARTSLandUnit&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ARTSLandUnit); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARTSLandUnit); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ARTSLandUnit) \
	NO_API virtual ~ARTSLandUnit();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_62_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_65_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_65_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_65_CALLBACK_WRAPPERS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_65_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h_65_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ARTSLandUnit;

// ********** End Class ARTSLandUnit ***************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSLandUnit_h

// ********** Begin Enum ERTSTerrainType ***********************************************************
#define FOREACH_ENUM_ERTSTERRAINTYPE(op) \
	op(ERTSTerrainType::Road) \
	op(ERTSTerrainType::Grass) \
	op(ERTSTerrainType::Forest) \
	op(ERTSTerrainType::Desert) \
	op(ERTSTerrainType::Mountain) \
	op(ERTSTerrainType::Swamp) \
	op(ERTSTerrainType::Urban) \
	op(ERTSTerrainType::Water) 

enum class ERTSTerrainType : uint8;
template<> struct TIsUEnumClass<ERTSTerrainType> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSTerrainType>();
// ********** End Enum ERTSTerrainType *************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

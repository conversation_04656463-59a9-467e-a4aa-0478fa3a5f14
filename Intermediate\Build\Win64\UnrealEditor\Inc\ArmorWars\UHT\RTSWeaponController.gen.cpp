// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSWeaponController.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSWeaponController() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSWeaponComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSWeaponController();
ARMORWARS_API UClass* Z_Construct_UClass_URTSWeaponController_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSUnitDomain();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Delegate FOnTargetChanged ******************************************************
struct Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics
{
	struct RTSWeaponController_eventOnTargetChanged_Parms
	{
		URTSWeaponController* Controller;
		ARTSBaseActor* NewTarget;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Controller_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Controller;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewTarget;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::NewProp_Controller = { "Controller", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnTargetChanged_Parms, Controller), Z_Construct_UClass_URTSWeaponController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Controller_MetaData), NewProp_Controller_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::NewProp_NewTarget = { "NewTarget", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnTargetChanged_Parms, NewTarget), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::NewProp_Controller,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::NewProp_NewTarget,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "OnTargetChanged__DelegateSignature", Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::RTSWeaponController_eventOnTargetChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::RTSWeaponController_eventOnTargetChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSWeaponController::FOnTargetChanged_DelegateWrapper(const FMulticastScriptDelegate& OnTargetChanged, URTSWeaponController* Controller, ARTSBaseActor* NewTarget)
{
	struct RTSWeaponController_eventOnTargetChanged_Parms
	{
		URTSWeaponController* Controller;
		ARTSBaseActor* NewTarget;
	};
	RTSWeaponController_eventOnTargetChanged_Parms Parms;
	Parms.Controller=Controller;
	Parms.NewTarget=NewTarget;
	OnTargetChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTargetChanged ********************************************************

// ********** Begin Delegate FOnFiringStateChanged *************************************************
struct Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics
{
	struct RTSWeaponController_eventOnFiringStateChanged_Parms
	{
		URTSWeaponController* Controller;
		bool bFiring;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Controller_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Controller;
	static void NewProp_bFiring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFiring;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::NewProp_Controller = { "Controller", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnFiringStateChanged_Parms, Controller), Z_Construct_UClass_URTSWeaponController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Controller_MetaData), NewProp_Controller_MetaData) };
void Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::NewProp_bFiring_SetBit(void* Obj)
{
	((RTSWeaponController_eventOnFiringStateChanged_Parms*)Obj)->bFiring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::NewProp_bFiring = { "bFiring", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSWeaponController_eventOnFiringStateChanged_Parms), &Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::NewProp_bFiring_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::NewProp_Controller,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::NewProp_bFiring,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "OnFiringStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::RTSWeaponController_eventOnFiringStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::RTSWeaponController_eventOnFiringStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSWeaponController::FOnFiringStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnFiringStateChanged, URTSWeaponController* Controller, bool bFiring)
{
	struct RTSWeaponController_eventOnFiringStateChanged_Parms
	{
		URTSWeaponController* Controller;
		bool bFiring;
	};
	RTSWeaponController_eventOnFiringStateChanged_Parms Parms;
	Parms.Controller=Controller;
	Parms.bFiring=bFiring ? true : false;
	OnFiringStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFiringStateChanged ***************************************************

// ********** Begin Delegate FOnWeaponFired ********************************************************
struct Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics
{
	struct RTSWeaponController_eventOnWeaponFired_Parms
	{
		URTSWeaponController* Controller;
		URTSWeaponComponent* Weapon;
		ARTSBaseActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Controller_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weapon_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Controller;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Weapon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::NewProp_Controller = { "Controller", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnWeaponFired_Parms, Controller), Z_Construct_UClass_URTSWeaponController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Controller_MetaData), NewProp_Controller_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::NewProp_Weapon = { "Weapon", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnWeaponFired_Parms, Weapon), Z_Construct_UClass_URTSWeaponComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weapon_MetaData), NewProp_Weapon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnWeaponFired_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::NewProp_Controller,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::NewProp_Weapon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "OnWeaponFired__DelegateSignature", Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::RTSWeaponController_eventOnWeaponFired_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::RTSWeaponController_eventOnWeaponFired_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSWeaponController::FOnWeaponFired_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponFired, URTSWeaponController* Controller, URTSWeaponComponent* Weapon, ARTSBaseActor* Target)
{
	struct RTSWeaponController_eventOnWeaponFired_Parms
	{
		URTSWeaponController* Controller;
		URTSWeaponComponent* Weapon;
		ARTSBaseActor* Target;
	};
	RTSWeaponController_eventOnWeaponFired_Parms Parms;
	Parms.Controller=Controller;
	Parms.Weapon=Weapon;
	Parms.Target=Target;
	OnWeaponFired.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnWeaponFired **********************************************************

// ********** Begin Class URTSWeaponController Function AddWeapon **********************************
struct Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics
{
	struct RTSWeaponController_eventAddWeapon_Parms
	{
		URTSWeaponComponent* WeaponComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Weapon management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::NewProp_WeaponComponent = { "WeaponComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventAddWeapon_Parms, WeaponComponent), Z_Construct_UClass_URTSWeaponComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponComponent_MetaData), NewProp_WeaponComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::NewProp_WeaponComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "AddWeapon", Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::RTSWeaponController_eventAddWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::RTSWeaponController_eventAddWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_AddWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_AddWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execAddWeapon)
{
	P_GET_OBJECT(URTSWeaponComponent,Z_Param_WeaponComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddWeapon(Z_Param_WeaponComponent);
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function AddWeapon ************************************

// ********** Begin Class URTSWeaponController Function CanFire ************************************
struct Z_Construct_UFunction_URTSWeaponController_CanFire_Statics
{
	struct RTSWeaponController_eventCanFire_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Firing" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSWeaponController_eventCanFire_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSWeaponController_eventCanFire_Parms), &Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "CanFire", Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::RTSWeaponController_eventCanFire_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::RTSWeaponController_eventCanFire_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_CanFire()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_CanFire_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execCanFire)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanFire();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function CanFire **************************************

// ********** Begin Class URTSWeaponController Function CanTargetDomain ****************************
struct Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics
{
	struct RTSWeaponController_eventCanTargetDomain_Parms
	{
		ERTSUnitDomain Domain;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Stats" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Domain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Domain;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::NewProp_Domain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::NewProp_Domain = { "Domain", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventCanTargetDomain_Parms, Domain), Z_Construct_UEnum_ArmorWars_ERTSUnitDomain, METADATA_PARAMS(0, nullptr) }; // 3001890894
void Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSWeaponController_eventCanTargetDomain_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSWeaponController_eventCanTargetDomain_Parms), &Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::NewProp_Domain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::NewProp_Domain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "CanTargetDomain", Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::RTSWeaponController_eventCanTargetDomain_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::RTSWeaponController_eventCanTargetDomain_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_CanTargetDomain()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_CanTargetDomain_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execCanTargetDomain)
{
	P_GET_ENUM(ERTSUnitDomain,Z_Param_Domain);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanTargetDomain(ERTSUnitDomain(Z_Param_Domain));
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function CanTargetDomain ******************************

// ********** Begin Class URTSWeaponController Function ClearAllWeapons ****************************
struct Z_Construct_UFunction_URTSWeaponController_ClearAllWeapons_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_ClearAllWeapons_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "ClearAllWeapons", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_ClearAllWeapons_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_ClearAllWeapons_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSWeaponController_ClearAllWeapons()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_ClearAllWeapons_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execClearAllWeapons)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllWeapons();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function ClearAllWeapons ******************************

// ********** Begin Class URTSWeaponController Function ClearTarget ********************************
struct Z_Construct_UFunction_URTSWeaponController_ClearTarget_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Targeting" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_ClearTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "ClearTarget", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_ClearTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_ClearTarget_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSWeaponController_ClearTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_ClearTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execClearTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearTarget();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function ClearTarget **********************************

// ********** Begin Class URTSWeaponController Function FindBestTarget *****************************
struct Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics
{
	struct RTSWeaponController_eventFindBestTarget_Parms
	{
		float SearchRange;
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Targeting" },
		{ "CPP_Default_SearchRange", "0.000000" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SearchRange;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::NewProp_SearchRange = { "SearchRange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventFindBestTarget_Parms, SearchRange), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventFindBestTarget_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::NewProp_SearchRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "FindBestTarget", Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::RTSWeaponController_eventFindBestTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::RTSWeaponController_eventFindBestTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_FindBestTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_FindBestTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execFindBestTarget)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_SearchRange);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->FindBestTarget(Z_Param_SearchRange);
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function FindBestTarget *******************************

// ********** Begin Class URTSWeaponController Function FireAtLocation *****************************
struct Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics
{
	struct RTSWeaponController_eventFireAtLocation_Parms
	{
		FVector TargetLocation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Firing" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventFireAtLocation_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
void Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSWeaponController_eventFireAtLocation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSWeaponController_eventFireAtLocation_Parms), &Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "FireAtLocation", Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::RTSWeaponController_eventFireAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::RTSWeaponController_eventFireAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_FireAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_FireAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execFireAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TargetLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->FireAtLocation(Z_Param_Out_TargetLocation);
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function FireAtLocation *******************************

// ********** Begin Class URTSWeaponController Function FireAtTarget *******************************
struct Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics
{
	struct RTSWeaponController_eventFireAtTarget_Parms
	{
		ARTSBaseActor* Target;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Firing" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventFireAtTarget_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSWeaponController_eventFireAtTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSWeaponController_eventFireAtTarget_Parms), &Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "FireAtTarget", Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::RTSWeaponController_eventFireAtTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::RTSWeaponController_eventFireAtTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_FireAtTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_FireAtTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execFireAtTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->FireAtTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function FireAtTarget *********************************

// ********** Begin Class URTSWeaponController Function GetActiveWeapons ***************************
struct Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics
{
	struct RTSWeaponController_eventGetActiveWeapons_Parms
	{
		TArray<URTSWeaponComponent*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Selection" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_URTSWeaponComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000588, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventGetActiveWeapons_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "GetActiveWeapons", Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::RTSWeaponController_eventGetActiveWeapons_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::RTSWeaponController_eventGetActiveWeapons_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execGetActiveWeapons)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<URTSWeaponComponent*>*)Z_Param__Result=P_THIS->GetActiveWeapons();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function GetActiveWeapons *****************************

// ********** Begin Class URTSWeaponController Function GetAllWeapons ******************************
struct Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics
{
	struct RTSWeaponController_eventGetAllWeapons_Parms
	{
		TArray<URTSWeaponComponent*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_URTSWeaponComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000588, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventGetAllWeapons_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "GetAllWeapons", Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::RTSWeaponController_eventGetAllWeapons_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::RTSWeaponController_eventGetAllWeapons_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_GetAllWeapons()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_GetAllWeapons_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execGetAllWeapons)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<URTSWeaponComponent*>*)Z_Param__Result=P_THIS->GetAllWeapons();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function GetAllWeapons ********************************

// ********** Begin Class URTSWeaponController Function GetCurrentTarget ***************************
struct Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics
{
	struct RTSWeaponController_eventGetCurrentTarget_Parms
	{
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Targeting" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventGetCurrentTarget_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "GetCurrentTarget", Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::RTSWeaponController_eventGetCurrentTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::RTSWeaponController_eventGetCurrentTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execGetCurrentTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->GetCurrentTarget();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function GetCurrentTarget *****************************

// ********** Begin Class URTSWeaponController Function GetMaxRange ********************************
struct Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics
{
	struct RTSWeaponController_eventGetMaxRange_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Stats" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventGetMaxRange_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "GetMaxRange", Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::RTSWeaponController_eventGetMaxRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::RTSWeaponController_eventGetMaxRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_GetMaxRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_GetMaxRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execGetMaxRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMaxRange();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function GetMaxRange **********************************

// ********** Begin Class URTSWeaponController Function GetTotalDamagePerSecond ********************
struct Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics
{
	struct RTSWeaponController_eventGetTotalDamagePerSecond_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Stats" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventGetTotalDamagePerSecond_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "GetTotalDamagePerSecond", Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::RTSWeaponController_eventGetTotalDamagePerSecond_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::RTSWeaponController_eventGetTotalDamagePerSecond_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execGetTotalDamagePerSecond)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalDamagePerSecond();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function GetTotalDamagePerSecond **********************

// ********** Begin Class URTSWeaponController Function GetWeaponCount *****************************
struct Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics
{
	struct RTSWeaponController_eventGetWeaponCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventGetWeaponCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "GetWeaponCount", Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::RTSWeaponController_eventGetWeaponCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::RTSWeaponController_eventGetWeaponCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_GetWeaponCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_GetWeaponCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execGetWeaponCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetWeaponCount();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function GetWeaponCount *******************************

// ********** Begin Class URTSWeaponController Function HasValidTarget *****************************
struct Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics
{
	struct RTSWeaponController_eventHasValidTarget_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Targeting" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSWeaponController_eventHasValidTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSWeaponController_eventHasValidTarget_Parms), &Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "HasValidTarget", Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::RTSWeaponController_eventHasValidTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::RTSWeaponController_eventHasValidTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_HasValidTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_HasValidTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execHasValidTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasValidTarget();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function HasValidTarget *******************************

// ********** Begin Class URTSWeaponController Function HasWeapons *********************************
struct Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics
{
	struct RTSWeaponController_eventHasWeapons_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Combat statistics\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat statistics" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSWeaponController_eventHasWeapons_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSWeaponController_eventHasWeapons_Parms), &Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "HasWeapons", Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::RTSWeaponController_eventHasWeapons_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::RTSWeaponController_eventHasWeapons_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_HasWeapons()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_HasWeapons_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execHasWeapons)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasWeapons();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function HasWeapons ***********************************

// ********** Begin Class URTSWeaponController Function IsFiring ***********************************
struct Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics
{
	struct RTSWeaponController_eventIsFiring_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Firing" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSWeaponController_eventIsFiring_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSWeaponController_eventIsFiring_Parms), &Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "IsFiring", Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::RTSWeaponController_eventIsFiring_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::RTSWeaponController_eventIsFiring_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_IsFiring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_IsFiring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execIsFiring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsFiring();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function IsFiring *************************************

// ********** Begin Class URTSWeaponController Function OnFiringStarted ****************************
static FName NAME_URTSWeaponController_OnFiringStarted = FName(TEXT("OnFiringStarted"));
void URTSWeaponController::OnFiringStarted()
{
	UFunction* Func = FindFunctionChecked(NAME_URTSWeaponController_OnFiringStarted);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_URTSWeaponController_OnFiringStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_OnFiringStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "OnFiringStarted", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnFiringStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_OnFiringStarted_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSWeaponController_OnFiringStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_OnFiringStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class URTSWeaponController Function OnFiringStarted ******************************

// ********** Begin Class URTSWeaponController Function OnFiringStopped ****************************
static FName NAME_URTSWeaponController_OnFiringStopped = FName(TEXT("OnFiringStopped"));
void URTSWeaponController::OnFiringStopped()
{
	UFunction* Func = FindFunctionChecked(NAME_URTSWeaponController_OnFiringStopped);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_URTSWeaponController_OnFiringStopped_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_OnFiringStopped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "OnFiringStopped", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnFiringStopped_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_OnFiringStopped_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSWeaponController_OnFiringStopped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_OnFiringStopped_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class URTSWeaponController Function OnFiringStopped ******************************

// ********** Begin Class URTSWeaponController Function OnTargetAcquired ***************************
struct RTSWeaponController_eventOnTargetAcquired_Parms
{
	ARTSBaseActor* Target;
};
static FName NAME_URTSWeaponController_OnTargetAcquired = FName(TEXT("OnTargetAcquired"));
void URTSWeaponController::OnTargetAcquired(ARTSBaseActor* Target)
{
	RTSWeaponController_eventOnTargetAcquired_Parms Parms;
	Parms.Target=Target;
	UFunction* Func = FindFunctionChecked(NAME_URTSWeaponController_OnTargetAcquired);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnTargetAcquired_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "OnTargetAcquired", Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics::PropPointers), sizeof(RTSWeaponController_eventOnTargetAcquired_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSWeaponController_eventOnTargetAcquired_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class URTSWeaponController Function OnTargetAcquired *****************************

// ********** Begin Class URTSWeaponController Function OnTargetLost *******************************
struct RTSWeaponController_eventOnTargetLost_Parms
{
	ARTSBaseActor* Target;
};
static FName NAME_URTSWeaponController_OnTargetLost = FName(TEXT("OnTargetLost"));
void URTSWeaponController::OnTargetLost(ARTSBaseActor* Target)
{
	RTSWeaponController_eventOnTargetLost_Parms Parms;
	Parms.Target=Target;
	UFunction* Func = FindFunctionChecked(NAME_URTSWeaponController_OnTargetLost);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnTargetLost_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "OnTargetLost", Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics::PropPointers), sizeof(RTSWeaponController_eventOnTargetLost_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSWeaponController_eventOnTargetLost_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_OnTargetLost()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_OnTargetLost_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class URTSWeaponController Function OnTargetLost *********************************

// ********** Begin Class URTSWeaponController Function OnWeaponFiredInternal **********************
struct Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics
{
	struct RTSWeaponController_eventOnWeaponFiredInternal_Parms
	{
		URTSWeaponComponent* Weapon;
		FVector MuzzleLocation;
		FVector TargetLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal event handler\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal event handler" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weapon_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MuzzleLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Weapon;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MuzzleLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::NewProp_Weapon = { "Weapon", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnWeaponFiredInternal_Parms, Weapon), Z_Construct_UClass_URTSWeaponComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weapon_MetaData), NewProp_Weapon_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::NewProp_MuzzleLocation = { "MuzzleLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnWeaponFiredInternal_Parms, MuzzleLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MuzzleLocation_MetaData), NewProp_MuzzleLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventOnWeaponFiredInternal_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::NewProp_Weapon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::NewProp_MuzzleLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::NewProp_TargetLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "OnWeaponFiredInternal", Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::RTSWeaponController_eventOnWeaponFiredInternal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00C40401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::RTSWeaponController_eventOnWeaponFiredInternal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execOnWeaponFiredInternal)
{
	P_GET_OBJECT(URTSWeaponComponent,Z_Param_Weapon);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_MuzzleLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TargetLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnWeaponFiredInternal(Z_Param_Weapon,Z_Param_Out_MuzzleLocation,Z_Param_Out_TargetLocation);
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function OnWeaponFiredInternal ************************

// ********** Begin Class URTSWeaponController Function RemoveWeapon *******************************
struct Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics
{
	struct RTSWeaponController_eventRemoveWeapon_Parms
	{
		URTSWeaponComponent* WeaponComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::NewProp_WeaponComponent = { "WeaponComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventRemoveWeapon_Parms, WeaponComponent), Z_Construct_UClass_URTSWeaponComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponComponent_MetaData), NewProp_WeaponComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::NewProp_WeaponComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "RemoveWeapon", Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::RTSWeaponController_eventRemoveWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::RTSWeaponController_eventRemoveWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_RemoveWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_RemoveWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execRemoveWeapon)
{
	P_GET_OBJECT(URTSWeaponComponent,Z_Param_WeaponComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveWeapon(Z_Param_WeaponComponent);
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function RemoveWeapon *********************************

// ********** Begin Class URTSWeaponController Function SetActiveWeaponGroup ***********************
struct Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics
{
	struct RTSWeaponController_eventSetActiveWeaponGroup_Parms
	{
		int32 GroupIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Weapon selection and coordination\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon selection and coordination" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::NewProp_GroupIndex = { "GroupIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventSetActiveWeaponGroup_Parms, GroupIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::NewProp_GroupIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "SetActiveWeaponGroup", Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::RTSWeaponController_eventSetActiveWeaponGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::RTSWeaponController_eventSetActiveWeaponGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execSetActiveWeaponGroup)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActiveWeaponGroup(Z_Param_GroupIndex);
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function SetActiveWeaponGroup *************************

// ********** Begin Class URTSWeaponController Function SetAllWeaponsActive ************************
struct Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics
{
	struct RTSWeaponController_eventSetAllWeaponsActive_Parms
	{
		bool bActive;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Selection" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::NewProp_bActive_SetBit(void* Obj)
{
	((RTSWeaponController_eventSetAllWeaponsActive_Parms*)Obj)->bActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::NewProp_bActive = { "bActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSWeaponController_eventSetAllWeaponsActive_Parms), &Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::NewProp_bActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::NewProp_bActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "SetAllWeaponsActive", Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::RTSWeaponController_eventSetAllWeaponsActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::RTSWeaponController_eventSetAllWeaponsActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execSetAllWeaponsActive)
{
	P_GET_UBOOL(Z_Param_bActive);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAllWeaponsActive(Z_Param_bActive);
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function SetAllWeaponsActive **************************

// ********** Begin Class URTSWeaponController Function SetTarget **********************************
struct Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics
{
	struct RTSWeaponController_eventSetTarget_Parms
	{
		ARTSBaseActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Targeting functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Targeting functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventSetTarget_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "SetTarget", Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::RTSWeaponController_eventSetTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::RTSWeaponController_eventSetTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_SetTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_SetTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execSetTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function SetTarget ************************************

// ********** Begin Class URTSWeaponController Function StartFiring ********************************
struct Z_Construct_UFunction_URTSWeaponController_StartFiring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Firing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Firing functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Firing functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_StartFiring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "StartFiring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_StartFiring_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_StartFiring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSWeaponController_StartFiring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_StartFiring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execStartFiring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartFiring();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function StartFiring **********************************

// ********** Begin Class URTSWeaponController Function StopFiring *********************************
struct Z_Construct_UFunction_URTSWeaponController_StopFiring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller|Firing" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_StopFiring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "StopFiring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_StopFiring_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_StopFiring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSWeaponController_StopFiring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_StopFiring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execStopFiring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopFiring();
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function StopFiring ***********************************

// ********** Begin Class URTSWeaponController Function UpdateTargeting ****************************
struct Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics
{
	struct RTSWeaponController_eventUpdateTargeting_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Controller" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSWeaponController_eventUpdateTargeting_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSWeaponController, nullptr, "UpdateTargeting", Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::RTSWeaponController_eventUpdateTargeting_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::RTSWeaponController_eventUpdateTargeting_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSWeaponController_UpdateTargeting()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSWeaponController_UpdateTargeting_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSWeaponController::execUpdateTargeting)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTargeting(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class URTSWeaponController Function UpdateTargeting ******************************

// ********** Begin Class URTSWeaponController *****************************************************
void URTSWeaponController::StaticRegisterNativesURTSWeaponController()
{
	UClass* Class = URTSWeaponController::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddWeapon", &URTSWeaponController::execAddWeapon },
		{ "CanFire", &URTSWeaponController::execCanFire },
		{ "CanTargetDomain", &URTSWeaponController::execCanTargetDomain },
		{ "ClearAllWeapons", &URTSWeaponController::execClearAllWeapons },
		{ "ClearTarget", &URTSWeaponController::execClearTarget },
		{ "FindBestTarget", &URTSWeaponController::execFindBestTarget },
		{ "FireAtLocation", &URTSWeaponController::execFireAtLocation },
		{ "FireAtTarget", &URTSWeaponController::execFireAtTarget },
		{ "GetActiveWeapons", &URTSWeaponController::execGetActiveWeapons },
		{ "GetAllWeapons", &URTSWeaponController::execGetAllWeapons },
		{ "GetCurrentTarget", &URTSWeaponController::execGetCurrentTarget },
		{ "GetMaxRange", &URTSWeaponController::execGetMaxRange },
		{ "GetTotalDamagePerSecond", &URTSWeaponController::execGetTotalDamagePerSecond },
		{ "GetWeaponCount", &URTSWeaponController::execGetWeaponCount },
		{ "HasValidTarget", &URTSWeaponController::execHasValidTarget },
		{ "HasWeapons", &URTSWeaponController::execHasWeapons },
		{ "IsFiring", &URTSWeaponController::execIsFiring },
		{ "OnWeaponFiredInternal", &URTSWeaponController::execOnWeaponFiredInternal },
		{ "RemoveWeapon", &URTSWeaponController::execRemoveWeapon },
		{ "SetActiveWeaponGroup", &URTSWeaponController::execSetActiveWeaponGroup },
		{ "SetAllWeaponsActive", &URTSWeaponController::execSetAllWeaponsActive },
		{ "SetTarget", &URTSWeaponController::execSetTarget },
		{ "StartFiring", &URTSWeaponController::execStartFiring },
		{ "StopFiring", &URTSWeaponController::execStopFiring },
		{ "UpdateTargeting", &URTSWeaponController::execUpdateTargeting },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSWeaponController;
UClass* URTSWeaponController::GetPrivateStaticClass()
{
	using TClass = URTSWeaponController;
	if (!Z_Registration_Info_UClass_URTSWeaponController.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSWeaponController"),
			Z_Registration_Info_UClass_URTSWeaponController.InnerSingleton,
			StaticRegisterNativesURTSWeaponController,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSWeaponController.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSWeaponController_NoRegister()
{
	return URTSWeaponController::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSWeaponController_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Weapon controller component that manages multiple weapon components on an RTS unit\n * Handles weapon coordination, targeting, and firing control\n */" },
#endif
		{ "IncludePath", "RTSWeaponController.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon controller component that manages multiple weapon components on an RTS unit\nHandles weapon coordination, targeting, and firing control" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponComponents_MetaData[] = {
		{ "Category", "Weapon Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Weapon components managed by this controller\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon components managed by this controller" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTarget_MetaData[] = {
		{ "Category", "Weapon Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current target\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current target" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsFiring_MetaData[] = {
		{ "Category", "Weapon Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether the controller is currently firing\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether the controller is currently firing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveWeaponGroup_MetaData[] = {
		{ "Category", "Weapon Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Active weapon group (-1 means all weapons)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active weapon group (-1 means all weapons)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoTarget_MetaData[] = {
		{ "Category", "Weapon Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether to automatically acquire targets\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether to automatically acquire targets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AutoTargetRange_MetaData[] = {
		{ "Category", "Weapon Controller" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Auto-target search range (0 = use max weapon range)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auto-target search range (0 = use max weapon range)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTargetChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFiringStateChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWeaponFired_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/RTSWeaponController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WeaponComponents;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_CurrentTarget;
	static void NewProp_bIsFiring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsFiring;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveWeaponGroup;
	static void NewProp_bAutoTarget_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoTarget;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AutoTargetRange;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTargetChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFiringStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWeaponFired;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSWeaponController_AddWeapon, "AddWeapon" }, // 1406373716
		{ &Z_Construct_UFunction_URTSWeaponController_CanFire, "CanFire" }, // 3068864852
		{ &Z_Construct_UFunction_URTSWeaponController_CanTargetDomain, "CanTargetDomain" }, // 2057292154
		{ &Z_Construct_UFunction_URTSWeaponController_ClearAllWeapons, "ClearAllWeapons" }, // 898100211
		{ &Z_Construct_UFunction_URTSWeaponController_ClearTarget, "ClearTarget" }, // 3441238824
		{ &Z_Construct_UFunction_URTSWeaponController_FindBestTarget, "FindBestTarget" }, // 963054362
		{ &Z_Construct_UFunction_URTSWeaponController_FireAtLocation, "FireAtLocation" }, // 886147953
		{ &Z_Construct_UFunction_URTSWeaponController_FireAtTarget, "FireAtTarget" }, // 1691829751
		{ &Z_Construct_UFunction_URTSWeaponController_GetActiveWeapons, "GetActiveWeapons" }, // 2389121106
		{ &Z_Construct_UFunction_URTSWeaponController_GetAllWeapons, "GetAllWeapons" }, // 1197270536
		{ &Z_Construct_UFunction_URTSWeaponController_GetCurrentTarget, "GetCurrentTarget" }, // 496131636
		{ &Z_Construct_UFunction_URTSWeaponController_GetMaxRange, "GetMaxRange" }, // 3402466614
		{ &Z_Construct_UFunction_URTSWeaponController_GetTotalDamagePerSecond, "GetTotalDamagePerSecond" }, // 2776544061
		{ &Z_Construct_UFunction_URTSWeaponController_GetWeaponCount, "GetWeaponCount" }, // 1577993561
		{ &Z_Construct_UFunction_URTSWeaponController_HasValidTarget, "HasValidTarget" }, // 1716566066
		{ &Z_Construct_UFunction_URTSWeaponController_HasWeapons, "HasWeapons" }, // 2779034726
		{ &Z_Construct_UFunction_URTSWeaponController_IsFiring, "IsFiring" }, // 3817830493
		{ &Z_Construct_UFunction_URTSWeaponController_OnFiringStarted, "OnFiringStarted" }, // 2571498006
		{ &Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature, "OnFiringStateChanged__DelegateSignature" }, // 443916114
		{ &Z_Construct_UFunction_URTSWeaponController_OnFiringStopped, "OnFiringStopped" }, // 2809714589
		{ &Z_Construct_UFunction_URTSWeaponController_OnTargetAcquired, "OnTargetAcquired" }, // 2783779301
		{ &Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature, "OnTargetChanged__DelegateSignature" }, // 1553877399
		{ &Z_Construct_UFunction_URTSWeaponController_OnTargetLost, "OnTargetLost" }, // 2593906932
		{ &Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature, "OnWeaponFired__DelegateSignature" }, // 1234739888
		{ &Z_Construct_UFunction_URTSWeaponController_OnWeaponFiredInternal, "OnWeaponFiredInternal" }, // 3250186417
		{ &Z_Construct_UFunction_URTSWeaponController_RemoveWeapon, "RemoveWeapon" }, // 1238828732
		{ &Z_Construct_UFunction_URTSWeaponController_SetActiveWeaponGroup, "SetActiveWeaponGroup" }, // 2517333831
		{ &Z_Construct_UFunction_URTSWeaponController_SetAllWeaponsActive, "SetAllWeaponsActive" }, // 994883929
		{ &Z_Construct_UFunction_URTSWeaponController_SetTarget, "SetTarget" }, // 2049859909
		{ &Z_Construct_UFunction_URTSWeaponController_StartFiring, "StartFiring" }, // 318721941
		{ &Z_Construct_UFunction_URTSWeaponController_StopFiring, "StopFiring" }, // 304571687
		{ &Z_Construct_UFunction_URTSWeaponController_UpdateTargeting, "UpdateTargeting" }, // 3558018746
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSWeaponController>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_URTSWeaponController_Statics::NewProp_WeaponComponents_Inner = { "WeaponComponents", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_URTSWeaponComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URTSWeaponController_Statics::NewProp_WeaponComponents = { "WeaponComponents", nullptr, (EPropertyFlags)0x002008800000001c, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSWeaponController, WeaponComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponComponents_MetaData), NewProp_WeaponComponents_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_URTSWeaponController_Statics::NewProp_CurrentTarget = { "CurrentTarget", nullptr, (EPropertyFlags)0x0024080000000014, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSWeaponController, CurrentTarget), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTarget_MetaData), NewProp_CurrentTarget_MetaData) };
void Z_Construct_UClass_URTSWeaponController_Statics::NewProp_bIsFiring_SetBit(void* Obj)
{
	((URTSWeaponController*)Obj)->bIsFiring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSWeaponController_Statics::NewProp_bIsFiring = { "bIsFiring", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSWeaponController), &Z_Construct_UClass_URTSWeaponController_Statics::NewProp_bIsFiring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsFiring_MetaData), NewProp_bIsFiring_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSWeaponController_Statics::NewProp_ActiveWeaponGroup = { "ActiveWeaponGroup", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSWeaponController, ActiveWeaponGroup), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveWeaponGroup_MetaData), NewProp_ActiveWeaponGroup_MetaData) };
void Z_Construct_UClass_URTSWeaponController_Statics::NewProp_bAutoTarget_SetBit(void* Obj)
{
	((URTSWeaponController*)Obj)->bAutoTarget = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSWeaponController_Statics::NewProp_bAutoTarget = { "bAutoTarget", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSWeaponController), &Z_Construct_UClass_URTSWeaponController_Statics::NewProp_bAutoTarget_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoTarget_MetaData), NewProp_bAutoTarget_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSWeaponController_Statics::NewProp_AutoTargetRange = { "AutoTargetRange", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSWeaponController, AutoTargetRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AutoTargetRange_MetaData), NewProp_AutoTargetRange_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSWeaponController_Statics::NewProp_OnTargetChanged = { "OnTargetChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSWeaponController, OnTargetChanged), Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTargetChanged_MetaData), NewProp_OnTargetChanged_MetaData) }; // 1553877399
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSWeaponController_Statics::NewProp_OnFiringStateChanged = { "OnFiringStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSWeaponController, OnFiringStateChanged), Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFiringStateChanged_MetaData), NewProp_OnFiringStateChanged_MetaData) }; // 443916114
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSWeaponController_Statics::NewProp_OnWeaponFired = { "OnWeaponFired", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSWeaponController, OnWeaponFired), Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWeaponFired_MetaData), NewProp_OnWeaponFired_MetaData) }; // 1234739888
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSWeaponController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSWeaponController_Statics::NewProp_WeaponComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSWeaponController_Statics::NewProp_WeaponComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSWeaponController_Statics::NewProp_CurrentTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSWeaponController_Statics::NewProp_bIsFiring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSWeaponController_Statics::NewProp_ActiveWeaponGroup,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSWeaponController_Statics::NewProp_bAutoTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSWeaponController_Statics::NewProp_AutoTargetRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSWeaponController_Statics::NewProp_OnTargetChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSWeaponController_Statics::NewProp_OnFiringStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSWeaponController_Statics::NewProp_OnWeaponFired,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSWeaponController_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSWeaponController_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSWeaponController_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSWeaponController_Statics::ClassParams = {
	&URTSWeaponController::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSWeaponController_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSWeaponController_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSWeaponController_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSWeaponController_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSWeaponController()
{
	if (!Z_Registration_Info_UClass_URTSWeaponController.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSWeaponController.OuterSingleton, Z_Construct_UClass_URTSWeaponController_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSWeaponController.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSWeaponController);
URTSWeaponController::~URTSWeaponController() {}
// ********** End Class URTSWeaponController *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSWeaponController, URTSWeaponController::StaticClass, TEXT("URTSWeaponController"), &Z_Registration_Info_UClass_URTSWeaponController, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSWeaponController), 2991612774U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h__Script_ArmorWars_3712452040(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSWeaponController_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

/MANIFEST:NO
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.38.33130/lib/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/ucrt/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/um/x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NOEXP
"F:/ArmorWars/Intermediate/Build/Win64/x64/ArmorWarsEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.1.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.2.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.3.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.4.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.5.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.6.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.7.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.8.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.9.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.10.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.11.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.12.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.13.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.14.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Module.ArmorWars.15.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/ArmorWars.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAIController.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAIDebugHUD.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAircraftFlightComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAirMovementComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAirUnit.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAITestingSystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSAITestScenario.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSArmorComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBaseActor.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBehaviorNode.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBehaviorNodes.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBehaviorTreeComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBlackboardSystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSBuilding.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSCollisionAvoidanceSystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSCombatBehaviorTree.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSCommand.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSCommandComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSCommandPrioritySystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSEconomySubsystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSEnhancedBehaviorNodes.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSFactoryComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSFormationManager.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSFormationSystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSGameInstance.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSGameMode.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSGameState.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSGroupManager.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSHUD.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSLandMovementComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSLandUnit.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSMovementBehaviorTree.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSPawn.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSPlayerController.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSPointDefenseComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSProjectile.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSReturnFireSystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSSeaMovementComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSSeaUnit.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSSelectionSystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSTacticalAIComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSTeamManager.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSUnit.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSUnitAIComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSUnitDatabase.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSWeaponComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSWeaponController.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/UnitDatabaseSubsystem.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/RTSInterfaceComponent.cpp.obj"
"F:/ArmorWars/Intermediate/Build/Win64/x64/UnrealEditor/Development/ArmorWars/Default.rc2.res"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Slate/UnrealEditor-Slate.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SlateCore/UnrealEditor-SlateCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UMG/UnrealEditor-UMG.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Engine/UnrealEditor-Engine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Core/UnrealEditor-Core.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/CoreUObject/UnrealEditor-CoreUObject.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/InputCore/UnrealEditor-InputCore.lib"
"../Plugins/EnhancedInput/Intermediate/Build/Win64/x64/UnrealEditor/Development/EnhancedInput/UnrealEditor-EnhancedInput.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTags/UnrealEditor-GameplayTags.lib"
"../Plugins/Runtime/InstancedActors/Intermediate/Build/Win64/x64/UnrealEditor/Development/InstancedActors/UnrealEditor-InstancedActors.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AIModule/UnrealEditor-AIModule.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/NavigationSystem/UnrealEditor-NavigationSystem.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
/OUT:"F:/ArmorWars/Binaries/Win64/UnrealEditor-ArmorWars.dll"
/PDB:"F:/ArmorWars/Binaries/Win64/UnrealEditor-ArmorWars.pdb"
/ignore:4078
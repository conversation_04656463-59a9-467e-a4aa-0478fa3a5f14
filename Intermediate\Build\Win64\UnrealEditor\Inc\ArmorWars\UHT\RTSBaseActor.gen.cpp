// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSBaseActor.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSBaseActor() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSActorType();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSAircraftType();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSTechLevel();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSUnitDomain();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSActorType *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSActorType;
static UEnum* ERTSActorType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSActorType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSActorType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSActorType, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSActorType"));
	}
	return Z_Registration_Info_UEnum_ERTSActorType.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSActorType>()
{
	return ERTSActorType_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSActorType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Building.DisplayName", "Building" },
		{ "Building.Name", "ERTSActorType::Building" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum to distinguish between units and buildings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum to distinguish between units and buildings" },
#endif
		{ "Unit.DisplayName", "Unit" },
		{ "Unit.Name", "ERTSActorType::Unit" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSActorType::Unit", (int64)ERTSActorType::Unit },
		{ "ERTSActorType::Building", (int64)ERTSActorType::Building },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSActorType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSActorType",
	"ERTSActorType",
	Z_Construct_UEnum_ArmorWars_ERTSActorType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSActorType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSActorType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSActorType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSActorType()
{
	if (!Z_Registration_Info_UEnum_ERTSActorType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSActorType.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSActorType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSActorType.InnerSingleton;
}
// ********** End Enum ERTSActorType ***************************************************************

// ********** Begin Enum ERTSUnitDomain ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSUnitDomain;
static UEnum* ERTSUnitDomain_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSUnitDomain.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSUnitDomain.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSUnitDomain, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSUnitDomain"));
	}
	return Z_Registration_Info_UEnum_ERTSUnitDomain.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSUnitDomain>()
{
	return ERTSUnitDomain_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSUnitDomain_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Air.DisplayName", "Air" },
		{ "Air.Name", "ERTSUnitDomain::Air" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum for unit domains (movement types)\n" },
#endif
		{ "Land.DisplayName", "Land" },
		{ "Land.Name", "ERTSUnitDomain::Land" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ERTSUnitDomain::None" },
		{ "Sea.DisplayName", "Sea" },
		{ "Sea.Name", "ERTSUnitDomain::Sea" },
		{ "Subnautical.DisplayName", "Subnautical" },
		{ "Subnautical.Name", "ERTSUnitDomain::Subnautical" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for unit domains (movement types)" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSUnitDomain::None", (int64)ERTSUnitDomain::None },
		{ "ERTSUnitDomain::Land", (int64)ERTSUnitDomain::Land },
		{ "ERTSUnitDomain::Air", (int64)ERTSUnitDomain::Air },
		{ "ERTSUnitDomain::Sea", (int64)ERTSUnitDomain::Sea },
		{ "ERTSUnitDomain::Subnautical", (int64)ERTSUnitDomain::Subnautical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSUnitDomain_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSUnitDomain",
	"ERTSUnitDomain",
	Z_Construct_UEnum_ArmorWars_ERTSUnitDomain_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSUnitDomain_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSUnitDomain_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSUnitDomain_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSUnitDomain()
{
	if (!Z_Registration_Info_UEnum_ERTSUnitDomain.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSUnitDomain.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSUnitDomain_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSUnitDomain.InnerSingleton;
}
// ********** End Enum ERTSUnitDomain **************************************************************

// ********** Begin Enum ERTSAircraftType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSAircraftType;
static UEnum* ERTSAircraftType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSAircraftType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSAircraftType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSAircraftType, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSAircraftType"));
	}
	return Z_Registration_Info_UEnum_ERTSAircraftType.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSAircraftType>()
{
	return ERTSAircraftType_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSAircraftType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum for aircraft types (sub-category of Air domain)\n" },
#endif
		{ "FixedWing.DisplayName", "Fixed Wing" },
		{ "FixedWing.Name", "ERTSAircraftType::FixedWing" },
		{ "Hybrid.DisplayName", "Hybrid (VTOL + Fixed Wing)" },
		{ "Hybrid.Name", "ERTSAircraftType::Hybrid" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ERTSAircraftType::None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for aircraft types (sub-category of Air domain)" },
#endif
		{ "VTOL.DisplayName", "VTOL (Vertical Take-Off and Landing)" },
		{ "VTOL.Name", "ERTSAircraftType::VTOL" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSAircraftType::None", (int64)ERTSAircraftType::None },
		{ "ERTSAircraftType::VTOL", (int64)ERTSAircraftType::VTOL },
		{ "ERTSAircraftType::FixedWing", (int64)ERTSAircraftType::FixedWing },
		{ "ERTSAircraftType::Hybrid", (int64)ERTSAircraftType::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSAircraftType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSAircraftType",
	"ERTSAircraftType",
	Z_Construct_UEnum_ArmorWars_ERTSAircraftType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSAircraftType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSAircraftType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSAircraftType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSAircraftType()
{
	if (!Z_Registration_Info_UEnum_ERTSAircraftType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSAircraftType.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSAircraftType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSAircraftType.InnerSingleton;
}
// ********** End Enum ERTSAircraftType ************************************************************

// ********** Begin Enum ERTSTechLevel *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSTechLevel;
static UEnum* ERTSTechLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSTechLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSTechLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSTechLevel, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSTechLevel"));
	}
	return Z_Registration_Info_UEnum_ERTSTechLevel.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSTechLevel>()
{
	return ERTSTechLevel_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSTechLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum for technology levels\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
		{ "Tech1.DisplayName", "Tech Level 1" },
		{ "Tech1.Name", "ERTSTechLevel::Tech1" },
		{ "Tech2.DisplayName", "Tech Level 2" },
		{ "Tech2.Name", "ERTSTechLevel::Tech2" },
		{ "Tech3.DisplayName", "Tech Level 3" },
		{ "Tech3.Name", "ERTSTechLevel::Tech3" },
		{ "Tech4.DisplayName", "Tech Level 4" },
		{ "Tech4.Name", "ERTSTechLevel::Tech4" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for technology levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSTechLevel::Tech1", (int64)ERTSTechLevel::Tech1 },
		{ "ERTSTechLevel::Tech2", (int64)ERTSTechLevel::Tech2 },
		{ "ERTSTechLevel::Tech3", (int64)ERTSTechLevel::Tech3 },
		{ "ERTSTechLevel::Tech4", (int64)ERTSTechLevel::Tech4 },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSTechLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSTechLevel",
	"ERTSTechLevel",
	Z_Construct_UEnum_ArmorWars_ERTSTechLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSTechLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSTechLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSTechLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSTechLevel()
{
	if (!Z_Registration_Info_UEnum_ERTSTechLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSTechLevel.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSTechLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSTechLevel.InnerSingleton;
}
// ********** End Enum ERTSTechLevel ***************************************************************

// ********** Begin Delegate FOnHealthChanged ******************************************************
struct Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics
{
	struct RTSBaseActor_eventOnHealthChanged_Parms
	{
		float NewHealth;
		float MaxHealth;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::NewProp_NewHealth = { "NewHealth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventOnHealthChanged_Parms, NewHealth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventOnHealthChanged_Parms, MaxHealth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::NewProp_NewHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::NewProp_MaxHealth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "OnHealthChanged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::RTSBaseActor_eventOnHealthChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::RTSBaseActor_eventOnHealthChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSBaseActor::FOnHealthChanged_DelegateWrapper(const FMulticastScriptDelegate& OnHealthChanged, float NewHealth, float MaxHealth)
{
	struct RTSBaseActor_eventOnHealthChanged_Parms
	{
		float NewHealth;
		float MaxHealth;
	};
	RTSBaseActor_eventOnHealthChanged_Parms Parms;
	Parms.NewHealth=NewHealth;
	Parms.MaxHealth=MaxHealth;
	OnHealthChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnHealthChanged ********************************************************

// ********** Begin Delegate FOnDamageReceived *****************************************************
struct Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics
{
	struct RTSBaseActor_eventOnDamageReceived_Parms
	{
		float DamageAmount;
		AActor* DamageSource;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageSource;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventOnDamageReceived_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::NewProp_DamageSource = { "DamageSource", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventOnDamageReceived_Parms, DamageSource), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::NewProp_DamageSource,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "OnDamageReceived__DelegateSignature", Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::RTSBaseActor_eventOnDamageReceived_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::RTSBaseActor_eventOnDamageReceived_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSBaseActor::FOnDamageReceived_DelegateWrapper(const FMulticastScriptDelegate& OnDamageReceived, float DamageAmount, AActor* DamageSource)
{
	struct RTSBaseActor_eventOnDamageReceived_Parms
	{
		float DamageAmount;
		AActor* DamageSource;
	};
	RTSBaseActor_eventOnDamageReceived_Parms Parms;
	Parms.DamageAmount=DamageAmount;
	Parms.DamageSource=DamageSource;
	OnDamageReceived.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDamageReceived *******************************************************

// ********** Begin Delegate FOnActorDeath *********************************************************
struct Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics
{
	struct RTSBaseActor_eventOnActorDeath_Parms
	{
		ARTSBaseActor* DeadActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DeadActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::NewProp_DeadActor = { "DeadActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventOnActorDeath_Parms, DeadActor), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::NewProp_DeadActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "OnActorDeath__DelegateSignature", Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::RTSBaseActor_eventOnActorDeath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::RTSBaseActor_eventOnActorDeath_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSBaseActor::FOnActorDeath_DelegateWrapper(const FMulticastScriptDelegate& OnActorDeath, ARTSBaseActor* DeadActor)
{
	struct RTSBaseActor_eventOnActorDeath_Parms
	{
		ARTSBaseActor* DeadActor;
	};
	RTSBaseActor_eventOnActorDeath_Parms Parms;
	Parms.DeadActor=DeadActor;
	OnActorDeath.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnActorDeath ***********************************************************

// ********** Begin Delegate FOnSelectionChanged ***************************************************
struct Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics
{
	struct RTSBaseActor_eventOnSelectionChanged_Parms
	{
		ARTSBaseActor* Actor;
		bool bSelected;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_bSelected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSelected;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventOnSelectionChanged_Parms, Actor), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::NewProp_bSelected_SetBit(void* Obj)
{
	((RTSBaseActor_eventOnSelectionChanged_Parms*)Obj)->bSelected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::NewProp_bSelected = { "bSelected", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventOnSelectionChanged_Parms), &Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::NewProp_bSelected_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::NewProp_bSelected,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "OnSelectionChanged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::RTSBaseActor_eventOnSelectionChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::RTSBaseActor_eventOnSelectionChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSBaseActor::FOnSelectionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSelectionChanged, ARTSBaseActor* Actor, bool bSelected)
{
	struct RTSBaseActor_eventOnSelectionChanged_Parms
	{
		ARTSBaseActor* Actor;
		bool bSelected;
	};
	RTSBaseActor_eventOnSelectionChanged_Parms Parms;
	Parms.Actor=Actor;
	Parms.bSelected=bSelected ? true : false;
	OnSelectionChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSelectionChanged *****************************************************

// ********** Begin Class ARTSBaseActor Function AddGameplayTag ************************************
struct Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics
{
	struct RTSBaseActor_eventAddGameplayTag_Parms
	{
		FGameplayTag Tag;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tags" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventAddGameplayTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) }; // 133831994
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::NewProp_Tag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "AddGameplayTag", Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::RTSBaseActor_eventAddGameplayTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::RTSBaseActor_eventAddGameplayTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execAddGameplayTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddGameplayTag(Z_Param_Out_Tag);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function AddGameplayTag **************************************

// ********** Begin Class ARTSBaseActor Function AddGameplayTags ***********************************
struct Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics
{
	struct RTSBaseActor_eventAddGameplayTags_Parms
	{
		FGameplayTagContainer TagContainer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tags" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TagContainer_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TagContainer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::NewProp_TagContainer = { "TagContainer", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventAddGameplayTags_Parms, TagContainer), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TagContainer_MetaData), NewProp_TagContainer_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::NewProp_TagContainer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "AddGameplayTags", Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::RTSBaseActor_eventAddGameplayTags_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::RTSBaseActor_eventAddGameplayTags_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execAddGameplayTags)
{
	P_GET_STRUCT_REF(FGameplayTagContainer,Z_Param_Out_TagContainer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddGameplayTags(Z_Param_Out_TagContainer);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function AddGameplayTags *************************************

// ********** Begin Class ARTSBaseActor Function CanBeSelected *************************************
struct Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics
{
	struct RTSBaseActor_eventCanBeSelected_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Selection" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventCanBeSelected_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventCanBeSelected_Parms), &Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "CanBeSelected", Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::RTSBaseActor_eventCanBeSelected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::RTSBaseActor_eventCanBeSelected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_CanBeSelected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_CanBeSelected_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execCanBeSelected)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanBeSelected();
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function CanBeSelected ***************************************

// ********** Begin Class ARTSBaseActor Function CanProduceTechLevel *******************************
struct Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics
{
	struct RTSBaseActor_eventCanProduceTechLevel_Parms
	{
		ERTSTechLevel RequiredTechLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tech" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RequiredTechLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RequiredTechLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::NewProp_RequiredTechLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::NewProp_RequiredTechLevel = { "RequiredTechLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventCanProduceTechLevel_Parms, RequiredTechLevel), Z_Construct_UEnum_ArmorWars_ERTSTechLevel, METADATA_PARAMS(0, nullptr) }; // 544529483
void Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventCanProduceTechLevel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventCanProduceTechLevel_Parms), &Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::NewProp_RequiredTechLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::NewProp_RequiredTechLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "CanProduceTechLevel", Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::RTSBaseActor_eventCanProduceTechLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::RTSBaseActor_eventCanProduceTechLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execCanProduceTechLevel)
{
	P_GET_ENUM(ERTSTechLevel,Z_Param_RequiredTechLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanProduceTechLevel(ERTSTechLevel(Z_Param_RequiredTechLevel));
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function CanProduceTechLevel *********************************

// ********** Begin Class ARTSBaseActor Function GetActorTeamID ************************************
struct Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics
{
	struct RTSBaseActor_eventGetActorTeamID_Parms
	{
		const AActor* Actor;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Get team ID from any actor (RTSBaseActor or RTSUnit)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get team ID from any actor (RTSBaseActor or RTSUnit)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Actor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventGetActorTeamID_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Actor_MetaData), NewProp_Actor_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventGetActorTeamID_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "GetActorTeamID", Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::RTSBaseActor_eventGetActorTeamID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::RTSBaseActor_eventGetActorTeamID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execGetActorTeamID)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=ARTSBaseActor::GetActorTeamID(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function GetActorTeamID **************************************

// ********** Begin Class ARTSBaseActor Function GetHealthPercentage *******************************
struct Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics
{
	struct RTSBaseActor_eventGetHealthPercentage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Health" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventGetHealthPercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "GetHealthPercentage", Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::RTSBaseActor_eventGetHealthPercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::RTSBaseActor_eventGetHealthPercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execGetHealthPercentage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetHealthPercentage();
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function GetHealthPercentage *********************************

// ********** Begin Class ARTSBaseActor Function GetTechLevel **************************************
struct Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics
{
	struct RTSBaseActor_eventGetTechLevel_Parms
	{
		ERTSTechLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tech" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tech level functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tech level functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventGetTechLevel_Parms, ReturnValue), Z_Construct_UEnum_ArmorWars_ERTSTechLevel, METADATA_PARAMS(0, nullptr) }; // 544529483
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "GetTechLevel", Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::RTSBaseActor_eventGetTechLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::RTSBaseActor_eventGetTechLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_GetTechLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_GetTechLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execGetTechLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ERTSTechLevel*)Z_Param__Result=P_THIS->GetTechLevel();
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function GetTechLevel ****************************************

// ********** Begin Class ARTSBaseActor Function HasAllGameplayTags ********************************
struct Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics
{
	struct RTSBaseActor_eventHasAllGameplayTags_Parms
	{
		FGameplayTagContainer TagContainer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tags" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TagContainer_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TagContainer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::NewProp_TagContainer = { "TagContainer", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventHasAllGameplayTags_Parms, TagContainer), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TagContainer_MetaData), NewProp_TagContainer_MetaData) }; // 2104890724
void Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventHasAllGameplayTags_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventHasAllGameplayTags_Parms), &Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::NewProp_TagContainer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "HasAllGameplayTags", Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::RTSBaseActor_eventHasAllGameplayTags_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::RTSBaseActor_eventHasAllGameplayTags_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execHasAllGameplayTags)
{
	P_GET_STRUCT_REF(FGameplayTagContainer,Z_Param_Out_TagContainer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasAllGameplayTags(Z_Param_Out_TagContainer);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function HasAllGameplayTags **********************************

// ********** Begin Class ARTSBaseActor Function HasAnyGameplayTags ********************************
struct Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics
{
	struct RTSBaseActor_eventHasAnyGameplayTags_Parms
	{
		FGameplayTagContainer TagContainer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tags" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TagContainer_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TagContainer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::NewProp_TagContainer = { "TagContainer", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventHasAnyGameplayTags_Parms, TagContainer), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TagContainer_MetaData), NewProp_TagContainer_MetaData) }; // 2104890724
void Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventHasAnyGameplayTags_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventHasAnyGameplayTags_Parms), &Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::NewProp_TagContainer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "HasAnyGameplayTags", Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::RTSBaseActor_eventHasAnyGameplayTags_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::RTSBaseActor_eventHasAnyGameplayTags_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execHasAnyGameplayTags)
{
	P_GET_STRUCT_REF(FGameplayTagContainer,Z_Param_Out_TagContainer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasAnyGameplayTags(Z_Param_Out_TagContainer);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function HasAnyGameplayTags **********************************

// ********** Begin Class ARTSBaseActor Function HasGameplayTag ************************************
struct Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics
{
	struct RTSBaseActor_eventHasGameplayTag_Parms
	{
		FGameplayTag Tag;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gameplay tag functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gameplay tag functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventHasGameplayTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) }; // 133831994
void Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventHasGameplayTag_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventHasGameplayTag_Parms), &Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "HasGameplayTag", Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::RTSBaseActor_eventHasGameplayTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::RTSBaseActor_eventHasGameplayTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execHasGameplayTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasGameplayTag(Z_Param_Out_Tag);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function HasGameplayTag **************************************

// ********** Begin Class ARTSBaseActor Function Heal **********************************************
struct Z_Construct_UFunction_ARTSBaseActor_Heal_Statics
{
	struct RTSBaseActor_eventHeal_Parms
	{
		float HealAmount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Health" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::NewProp_HealAmount = { "HealAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventHeal_Parms, HealAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::NewProp_HealAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "Heal", Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::RTSBaseActor_eventHeal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::RTSBaseActor_eventHeal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_Heal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_Heal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execHeal)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_HealAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Heal(Z_Param_HealAmount);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function Heal ************************************************

// ********** Begin Class ARTSBaseActor Function IsAlive *******************************************
struct Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics
{
	struct RTSBaseActor_eventIsAlive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Health" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventIsAlive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventIsAlive_Parms), &Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "IsAlive", Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::RTSBaseActor_eventIsAlive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::RTSBaseActor_eventIsAlive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_IsAlive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_IsAlive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execIsAlive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAlive();
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function IsAlive *********************************************

// ********** Begin Class ARTSBaseActor Function IsBuilding ****************************************
struct Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics
{
	struct RTSBaseActor_eventIsBuilding_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Type" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventIsBuilding_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventIsBuilding_Parms), &Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "IsBuilding", Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::RTSBaseActor_eventIsBuilding_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::RTSBaseActor_eventIsBuilding_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_IsBuilding()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_IsBuilding_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execIsBuilding)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsBuilding();
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function IsBuilding ******************************************

// ********** Begin Class ARTSBaseActor Function IsDead ********************************************
struct Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics
{
	struct RTSBaseActor_eventIsDead_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Health" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventIsDead_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventIsDead_Parms), &Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "IsDead", Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::RTSBaseActor_eventIsDead_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::RTSBaseActor_eventIsDead_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_IsDead()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_IsDead_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execIsDead)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDead();
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function IsDead **********************************************

// ********** Begin Class ARTSBaseActor Function IsEnemy *******************************************
struct Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics
{
	struct RTSBaseActor_eventIsEnemy_Parms
	{
		const ARTSBaseActor* OtherActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Team" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherActor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventIsEnemy_Parms, OtherActor), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherActor_MetaData), NewProp_OtherActor_MetaData) };
void Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventIsEnemy_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventIsEnemy_Parms), &Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "IsEnemy", Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::RTSBaseActor_eventIsEnemy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::RTSBaseActor_eventIsEnemy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_IsEnemy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_IsEnemy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execIsEnemy)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_OtherActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsEnemy(Z_Param_OtherActor);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function IsEnemy *********************************************

// ********** Begin Class ARTSBaseActor Function IsEnemyActor **************************************
struct Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics
{
	struct RTSBaseActor_eventIsEnemyActor_Parms
	{
		const AActor* OtherActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Team" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherActor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventIsEnemyActor_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherActor_MetaData), NewProp_OtherActor_MetaData) };
void Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventIsEnemyActor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventIsEnemyActor_Parms), &Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "IsEnemyActor", Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::RTSBaseActor_eventIsEnemyActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::RTSBaseActor_eventIsEnemyActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execIsEnemyActor)
{
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsEnemyActor(Z_Param_OtherActor);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function IsEnemyActor ****************************************

// ********** Begin Class ARTSBaseActor Function IsOnSameTeam **************************************
struct Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics
{
	struct RTSBaseActor_eventIsOnSameTeam_Parms
	{
		const ARTSBaseActor* OtherActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Team functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherActor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventIsOnSameTeam_Parms, OtherActor), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherActor_MetaData), NewProp_OtherActor_MetaData) };
void Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventIsOnSameTeam_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventIsOnSameTeam_Parms), &Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "IsOnSameTeam", Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::RTSBaseActor_eventIsOnSameTeam_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::RTSBaseActor_eventIsOnSameTeam_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execIsOnSameTeam)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_OtherActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsOnSameTeam(Z_Param_OtherActor);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function IsOnSameTeam ****************************************

// ********** Begin Class ARTSBaseActor Function IsOnSameTeamAsActor *******************************
struct Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics
{
	struct RTSBaseActor_eventIsOnSameTeamAsActor_Parms
	{
		const AActor* OtherActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Team functions that work with any actor (including RTSUnit pawns)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team functions that work with any actor (including RTSUnit pawns)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherActor_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventIsOnSameTeamAsActor_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherActor_MetaData), NewProp_OtherActor_MetaData) };
void Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventIsOnSameTeamAsActor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventIsOnSameTeamAsActor_Parms), &Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "IsOnSameTeamAsActor", Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::RTSBaseActor_eventIsOnSameTeamAsActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::RTSBaseActor_eventIsOnSameTeamAsActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execIsOnSameTeamAsActor)
{
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsOnSameTeamAsActor(Z_Param_OtherActor);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function IsOnSameTeamAsActor *********************************

// ********** Begin Class ARTSBaseActor Function IsUnit ********************************************
struct Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics
{
	struct RTSBaseActor_eventIsUnit_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Type" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Type checking functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Type checking functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBaseActor_eventIsUnit_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventIsUnit_Parms), &Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "IsUnit", Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::RTSBaseActor_eventIsUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::RTSBaseActor_eventIsUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_IsUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_IsUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execIsUnit)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsUnit();
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function IsUnit **********************************************

// ********** Begin Class ARTSBaseActor Function OnDeath *******************************************
static FName NAME_ARTSBaseActor_OnDeath = FName(TEXT("OnDeath"));
void ARTSBaseActor::OnDeath()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSBaseActor_OnDeath);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSBaseActor_OnDeath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Health" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called when health reaches zero\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when health reaches zero" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_OnDeath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "OnDeath", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_OnDeath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_OnDeath_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBaseActor_OnDeath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_OnDeath_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBaseActor Function OnDeath *********************************************

// ********** Begin Class ARTSBaseActor Function OnHealed ******************************************
struct RTSBaseActor_eventOnHealed_Parms
{
	float HealAmount;
};
static FName NAME_ARTSBaseActor_OnHealed = FName(TEXT("OnHealed"));
void ARTSBaseActor::OnHealed(float HealAmount)
{
	RTSBaseActor_eventOnHealed_Parms Parms;
	Parms.HealAmount=HealAmount;
	UFunction* Func = FindFunctionChecked(NAME_ARTSBaseActor_OnHealed);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Health" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called when healed\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when healed" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics::NewProp_HealAmount = { "HealAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventOnHealed_Parms, HealAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics::NewProp_HealAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "OnHealed", Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics::PropPointers), sizeof(RTSBaseActor_eventOnHealed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSBaseActor_eventOnHealed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_OnHealed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_OnHealed_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBaseActor Function OnHealed ********************************************

// ********** Begin Class ARTSBaseActor Function OnSelectionChangedEvent ***************************
struct RTSBaseActor_eventOnSelectionChangedEvent_Parms
{
	bool bSelected;
};
static FName NAME_ARTSBaseActor_OnSelectionChangedEvent = FName(TEXT("OnSelectionChangedEvent"));
void ARTSBaseActor::OnSelectionChangedEvent(bool bSelected)
{
	RTSBaseActor_eventOnSelectionChangedEvent_Parms Parms;
	Parms.bSelected=bSelected ? true : false;
	UFunction* Func = FindFunctionChecked(NAME_ARTSBaseActor_OnSelectionChangedEvent);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called when selected/deselected\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when selected/deselected" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSelected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSelected;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::NewProp_bSelected_SetBit(void* Obj)
{
	((RTSBaseActor_eventOnSelectionChangedEvent_Parms*)Obj)->bSelected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::NewProp_bSelected = { "bSelected", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventOnSelectionChangedEvent_Parms), &Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::NewProp_bSelected_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::NewProp_bSelected,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "OnSelectionChangedEvent", Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::PropPointers), sizeof(RTSBaseActor_eventOnSelectionChangedEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSBaseActor_eventOnSelectionChangedEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBaseActor Function OnSelectionChangedEvent *****************************

// ********** Begin Class ARTSBaseActor Function OnTakeDamage **************************************
struct RTSBaseActor_eventOnTakeDamage_Parms
{
	float DamageAmount;
	AActor* DamageSource;
};
static FName NAME_ARTSBaseActor_OnTakeDamage = FName(TEXT("OnTakeDamage"));
void ARTSBaseActor::OnTakeDamage(float DamageAmount, AActor* DamageSource)
{
	RTSBaseActor_eventOnTakeDamage_Parms Parms;
	Parms.DamageAmount=DamageAmount;
	Parms.DamageSource=DamageSource;
	UFunction* Func = FindFunctionChecked(NAME_ARTSBaseActor_OnTakeDamage);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Health" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called when taking damage\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when taking damage" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageSource;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventOnTakeDamage_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::NewProp_DamageSource = { "DamageSource", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventOnTakeDamage_Parms, DamageSource), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::NewProp_DamageSource,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "OnTakeDamage", Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::PropPointers), sizeof(RTSBaseActor_eventOnTakeDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSBaseActor_eventOnTakeDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBaseActor Function OnTakeDamage ****************************************

// ********** Begin Class ARTSBaseActor Function RemoveGameplayTag *********************************
struct Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics
{
	struct RTSBaseActor_eventRemoveGameplayTag_Parms
	{
		FGameplayTag Tag;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tags" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventRemoveGameplayTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) }; // 133831994
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::NewProp_Tag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "RemoveGameplayTag", Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::RTSBaseActor_eventRemoveGameplayTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::RTSBaseActor_eventRemoveGameplayTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execRemoveGameplayTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveGameplayTag(Z_Param_Out_Tag);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function RemoveGameplayTag ***********************************

// ********** Begin Class ARTSBaseActor Function RemoveGameplayTags ********************************
struct Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics
{
	struct RTSBaseActor_eventRemoveGameplayTags_Parms
	{
		FGameplayTagContainer TagContainer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tags" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TagContainer_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TagContainer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::NewProp_TagContainer = { "TagContainer", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventRemoveGameplayTags_Parms, TagContainer), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TagContainer_MetaData), NewProp_TagContainer_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::NewProp_TagContainer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "RemoveGameplayTags", Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::RTSBaseActor_eventRemoveGameplayTags_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::RTSBaseActor_eventRemoveGameplayTags_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execRemoveGameplayTags)
{
	P_GET_STRUCT_REF(FGameplayTagContainer,Z_Param_Out_TagContainer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveGameplayTags(Z_Param_Out_TagContainer);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function RemoveGameplayTags **********************************

// ********** Begin Class ARTSBaseActor Function SetHealth *****************************************
struct Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics
{
	struct RTSBaseActor_eventSetHealth_Parms
	{
		float NewHealth;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Health" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewHealth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::NewProp_NewHealth = { "NewHealth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventSetHealth_Parms, NewHealth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::NewProp_NewHealth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "SetHealth", Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::RTSBaseActor_eventSetHealth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::RTSBaseActor_eventSetHealth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_SetHealth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_SetHealth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execSetHealth)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewHealth);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetHealth(Z_Param_NewHealth);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function SetHealth *******************************************

// ********** Begin Class ARTSBaseActor Function SetSelected ***************************************
struct Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics
{
	struct RTSBaseActor_eventSetSelected_Parms
	{
		bool bSelected;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSelected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSelected;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::NewProp_bSelected_SetBit(void* Obj)
{
	((RTSBaseActor_eventSetSelected_Parms*)Obj)->bSelected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::NewProp_bSelected = { "bSelected", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBaseActor_eventSetSelected_Parms), &Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::NewProp_bSelected_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::NewProp_bSelected,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "SetSelected", Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::RTSBaseActor_eventSetSelected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::RTSBaseActor_eventSetSelected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_SetSelected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_SetSelected_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execSetSelected)
{
	P_GET_UBOOL(Z_Param_bSelected);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSelected(Z_Param_bSelected);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function SetSelected *****************************************

// ********** Begin Class ARTSBaseActor Function SetTechLevel **************************************
struct Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics
{
	struct RTSBaseActor_eventSetTechLevel_Parms
	{
		ERTSTechLevel NewTechLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Tech" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewTechLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewTechLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::NewProp_NewTechLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::NewProp_NewTechLevel = { "NewTechLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventSetTechLevel_Parms, NewTechLevel), Z_Construct_UEnum_ArmorWars_ERTSTechLevel, METADATA_PARAMS(0, nullptr) }; // 544529483
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::NewProp_NewTechLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::NewProp_NewTechLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "SetTechLevel", Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::RTSBaseActor_eventSetTechLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::RTSBaseActor_eventSetTechLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_SetTechLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_SetTechLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execSetTechLevel)
{
	P_GET_ENUM(ERTSTechLevel,Z_Param_NewTechLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTechLevel(ERTSTechLevel(Z_Param_NewTechLevel));
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function SetTechLevel ****************************************

// ********** Begin Class ARTSBaseActor Function TakeDamageSimple **********************************
struct Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics
{
	struct RTSBaseActor_eventTakeDamageSimple_Parms
	{
		float DamageAmount;
		AActor* DamageSource;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Health" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simplified damage function for Blueprint use\n" },
#endif
		{ "CPP_Default_DamageSource", "None" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simplified damage function for Blueprint use" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageSource;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventTakeDamageSimple_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::NewProp_DamageSource = { "DamageSource", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBaseActor_eventTakeDamageSimple_Parms, DamageSource), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::NewProp_DamageSource,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBaseActor, nullptr, "TakeDamageSimple", Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::RTSBaseActor_eventTakeDamageSimple_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::RTSBaseActor_eventTakeDamageSimple_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBaseActor::execTakeDamageSimple)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageAmount);
	P_GET_OBJECT(AActor,Z_Param_DamageSource);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TakeDamageSimple(Z_Param_DamageAmount,Z_Param_DamageSource);
	P_NATIVE_END;
}
// ********** End Class ARTSBaseActor Function TakeDamageSimple ************************************

// ********** Begin Class ARTSBaseActor ************************************************************
void ARTSBaseActor::StaticRegisterNativesARTSBaseActor()
{
	UClass* Class = ARTSBaseActor::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddGameplayTag", &ARTSBaseActor::execAddGameplayTag },
		{ "AddGameplayTags", &ARTSBaseActor::execAddGameplayTags },
		{ "CanBeSelected", &ARTSBaseActor::execCanBeSelected },
		{ "CanProduceTechLevel", &ARTSBaseActor::execCanProduceTechLevel },
		{ "GetActorTeamID", &ARTSBaseActor::execGetActorTeamID },
		{ "GetHealthPercentage", &ARTSBaseActor::execGetHealthPercentage },
		{ "GetTechLevel", &ARTSBaseActor::execGetTechLevel },
		{ "HasAllGameplayTags", &ARTSBaseActor::execHasAllGameplayTags },
		{ "HasAnyGameplayTags", &ARTSBaseActor::execHasAnyGameplayTags },
		{ "HasGameplayTag", &ARTSBaseActor::execHasGameplayTag },
		{ "Heal", &ARTSBaseActor::execHeal },
		{ "IsAlive", &ARTSBaseActor::execIsAlive },
		{ "IsBuilding", &ARTSBaseActor::execIsBuilding },
		{ "IsDead", &ARTSBaseActor::execIsDead },
		{ "IsEnemy", &ARTSBaseActor::execIsEnemy },
		{ "IsEnemyActor", &ARTSBaseActor::execIsEnemyActor },
		{ "IsOnSameTeam", &ARTSBaseActor::execIsOnSameTeam },
		{ "IsOnSameTeamAsActor", &ARTSBaseActor::execIsOnSameTeamAsActor },
		{ "IsUnit", &ARTSBaseActor::execIsUnit },
		{ "RemoveGameplayTag", &ARTSBaseActor::execRemoveGameplayTag },
		{ "RemoveGameplayTags", &ARTSBaseActor::execRemoveGameplayTags },
		{ "SetHealth", &ARTSBaseActor::execSetHealth },
		{ "SetSelected", &ARTSBaseActor::execSetSelected },
		{ "SetTechLevel", &ARTSBaseActor::execSetTechLevel },
		{ "TakeDamageSimple", &ARTSBaseActor::execTakeDamageSimple },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ARTSBaseActor;
UClass* ARTSBaseActor::GetPrivateStaticClass()
{
	using TClass = ARTSBaseActor;
	if (!Z_Registration_Info_UClass_ARTSBaseActor.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSBaseActor"),
			Z_Registration_Info_UClass_ARTSBaseActor.InnerSingleton,
			StaticRegisterNativesARTSBaseActor,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ARTSBaseActor.InnerSingleton;
}
UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister()
{
	return ARTSBaseActor::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ARTSBaseActor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Base actor class for all RTS units and buildings\n * Provides common functionality including gameplay tags, actor type identification,\n * and basic stats that can be used by both units and buildings\n */" },
#endif
		{ "IncludePath", "RTSBaseActor.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base actor class for all RTS units and buildings\nProvides common functionality including gameplay tags, actor type identification,\nand basic stats that can be used by both units and buildings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorType_MetaData[] = {
		{ "Category", "RTS|Type" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor type - Unit or Building\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor type - Unit or Building" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameplayTags_MetaData[] = {
		{ "Category", "RTS|Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gameplay tags for this actor\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gameplay tags for this actor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "Category", "RTS|Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Display name for this actor\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Display name for this actor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "RTS|Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Description of this actor\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Description of this actor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[] = {
		{ "Category", "RTS|Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Icon for UI representation\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Icon for UI representation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamID_MetaData[] = {
		{ "Category", "RTS|Team" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Team/faction this actor belongs to\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team/faction this actor belongs to" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TechLevel_MetaData[] = {
		{ "Category", "RTS|Tech" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Technology level of this actor\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Technology level of this actor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "RTS|Stats" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Maximum health\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum health" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealth_MetaData[] = {
		{ "Category", "RTS|Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current health\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current health" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeSelected_MetaData[] = {
		{ "Category", "RTS|Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this actor can be selected\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this actor can be selected" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSelected_MetaData[] = {
		{ "Category", "RTS|Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this actor is currently selected\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this actor is currently selected" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnHealthChanged_MetaData[] = {
		{ "Category", "RTS|Events" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDamageReceived_MetaData[] = {
		{ "Category", "RTS|Events" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnActorDeath_MetaData[] = {
		{ "Category", "RTS|Events" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSelectionChanged_MetaData[] = {
		{ "Category", "RTS|Events" },
		{ "ModuleRelativePath", "Public/RTSBaseActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ActorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ActorType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GameplayTags;
	static const UECodeGen_Private::FTextPropertyParams NewProp_DisplayName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Description;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Icon;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TechLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TechLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static void NewProp_bCanBeSelected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeSelected;
	static void NewProp_bIsSelected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSelected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnHealthChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDamageReceived;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnActorDeath;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSelectionChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARTSBaseActor_AddGameplayTag, "AddGameplayTag" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_AddGameplayTags, "AddGameplayTags" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_CanBeSelected, "CanBeSelected" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_CanProduceTechLevel, "CanProduceTechLevel" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_GetActorTeamID, "GetActorTeamID" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_GetHealthPercentage, "GetHealthPercentage" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_GetTechLevel, "GetTechLevel" }, // 281678463
		{ &Z_Construct_UFunction_ARTSBaseActor_HasAllGameplayTags, "HasAllGameplayTags" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_HasAnyGameplayTags, "HasAnyGameplayTags" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_HasGameplayTag, "HasGameplayTag" }, // 414998124
		{ &Z_Construct_UFunction_ARTSBaseActor_Heal, "Heal" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_IsAlive, "IsAlive" }, // 516960374
		{ &Z_Construct_UFunction_ARTSBaseActor_IsBuilding, "IsBuilding" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_IsDead, "IsDead" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_IsEnemy, "IsEnemy" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_IsEnemyActor, "IsEnemyActor" }, // 544930396
		{ &Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeam, "IsOnSameTeam" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_IsOnSameTeamAsActor, "IsOnSameTeamAsActor" }, // 3065191544
		{ &Z_Construct_UFunction_ARTSBaseActor_IsUnit, "IsUnit" }, // **********
		{ &Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature, "OnActorDeath__DelegateSignature" }, // 510101349
		{ &Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature, "OnDamageReceived__DelegateSignature" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_OnDeath, "OnDeath" }, // 907663752
		{ &Z_Construct_UFunction_ARTSBaseActor_OnHealed, "OnHealed" }, // **********
		{ &Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature, "OnHealthChanged__DelegateSignature" }, // **********
		{ &Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature, "OnSelectionChanged__DelegateSignature" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_OnSelectionChangedEvent, "OnSelectionChangedEvent" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_OnTakeDamage, "OnTakeDamage" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTag, "RemoveGameplayTag" }, // 783717564
		{ &Z_Construct_UFunction_ARTSBaseActor_RemoveGameplayTags, "RemoveGameplayTags" }, // 183999173
		{ &Z_Construct_UFunction_ARTSBaseActor_SetHealth, "SetHealth" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_SetSelected, "SetSelected" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_SetTechLevel, "SetTechLevel" }, // **********
		{ &Z_Construct_UFunction_ARTSBaseActor_TakeDamageSimple, "TakeDamageSimple" }, // **********
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARTSBaseActor>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_ActorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_ActorType = { "ActorType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, ActorType), Z_Construct_UEnum_ArmorWars_ERTSActorType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorType_MetaData), NewProp_ActorType_MetaData) }; // 2004642588
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_GameplayTags = { "GameplayTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, GameplayTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameplayTags_MetaData), NewProp_GameplayTags_MetaData) }; // 2104890724
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, Icon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Icon_MetaData), NewProp_Icon_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, TeamID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamID_MetaData), NewProp_TeamID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_TechLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_TechLevel = { "TechLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, TechLevel), Z_Construct_UEnum_ArmorWars_ERTSTechLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TechLevel_MetaData), NewProp_TechLevel_MetaData) }; // 544529483
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, CurrentHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealth_MetaData), NewProp_CurrentHealth_MetaData) };
void Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_bCanBeSelected_SetBit(void* Obj)
{
	((ARTSBaseActor*)Obj)->bCanBeSelected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_bCanBeSelected = { "bCanBeSelected", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSBaseActor), &Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_bCanBeSelected_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeSelected_MetaData), NewProp_bCanBeSelected_MetaData) };
void Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_bIsSelected_SetBit(void* Obj)
{
	((ARTSBaseActor*)Obj)->bIsSelected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_bIsSelected = { "bIsSelected", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSBaseActor), &Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_bIsSelected_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSelected_MetaData), NewProp_bIsSelected_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_OnHealthChanged = { "OnHealthChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, OnHealthChanged), Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnHealthChanged_MetaData), NewProp_OnHealthChanged_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_OnDamageReceived = { "OnDamageReceived", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, OnDamageReceived), Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDamageReceived_MetaData), NewProp_OnDamageReceived_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_OnActorDeath = { "OnActorDeath", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, OnActorDeath), Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnActorDeath_MetaData), NewProp_OnActorDeath_MetaData) }; // 510101349
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_OnSelectionChanged = { "OnSelectionChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBaseActor, OnSelectionChanged), Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSelectionChanged_MetaData), NewProp_OnSelectionChanged_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARTSBaseActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_ActorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_ActorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_GameplayTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_Icon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_TechLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_TechLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_bCanBeSelected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_bIsSelected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_OnHealthChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_OnDamageReceived,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_OnActorDeath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBaseActor_Statics::NewProp_OnSelectionChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSBaseActor_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARTSBaseActor_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSBaseActor_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARTSBaseActor_Statics::ClassParams = {
	&ARTSBaseActor::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARTSBaseActor_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARTSBaseActor_Statics::PropPointers),
	0,
	0x009001A5u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSBaseActor_Statics::Class_MetaDataParams), Z_Construct_UClass_ARTSBaseActor_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARTSBaseActor()
{
	if (!Z_Registration_Info_UClass_ARTSBaseActor.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARTSBaseActor.OuterSingleton, Z_Construct_UClass_ARTSBaseActor_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARTSBaseActor.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARTSBaseActor);
ARTSBaseActor::~ARTSBaseActor() {}
// ********** End Class ARTSBaseActor **************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSActorType_StaticEnum, TEXT("ERTSActorType"), &Z_Registration_Info_UEnum_ERTSActorType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2004642588U) },
		{ ERTSUnitDomain_StaticEnum, TEXT("ERTSUnitDomain"), &Z_Registration_Info_UEnum_ERTSUnitDomain, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3001890894U) },
		{ ERTSAircraftType_StaticEnum, TEXT("ERTSAircraftType"), &Z_Registration_Info_UEnum_ERTSAircraftType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 896497609U) },
		{ ERTSTechLevel_StaticEnum, TEXT("ERTSTechLevel"), &Z_Registration_Info_UEnum_ERTSTechLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 544529483U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARTSBaseActor, ARTSBaseActor::StaticClass, TEXT("ARTSBaseActor"), &Z_Registration_Info_UClass_ARTSBaseActor, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARTSBaseActor), 2613503578U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h__Script_ArmorWars_4195053633(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBaseActor_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

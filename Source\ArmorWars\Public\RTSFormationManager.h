#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "RTSFormationSystem.h"
#include "RTSCommand.h"
#include "RTSFormationManager.generated.h"

class ARTSUnit;

// Formation data for a group of units
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSFormationGroup
{
    GENERATED_BODY()

    // Unique ID for this formation group
    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    int32 GroupID = -1;

    // Formation leader (elected automatically)
    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    TWeakObjectPtr<ARTSUnit> Leader;

    // All units in the formation
    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    TArray<TSoftObjectPtr<ARTSUnit>> Units;

    // Formation type
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    ERTSFormationType FormationType = ERTSFormationType::Line;

    // Formation center (leader's target location)
    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    FVector FormationCenter = FVector::ZeroVector;

    // Formation direction (forward vector)
    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    FVector FormationDirection = FVector::ForwardVector;

    // Unit spacing
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    float UnitSpacing = 200.0f;

    // Whether the formation is active
    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    bool bIsActive = false;

    // Formation creation time
    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    float CreationTime = 0.0f;

    // Constructor
    FRTSFormationGroup()
    {
        GroupID = -1;
        FormationType = ERTSFormationType::Line;
        FormationCenter = FVector::ZeroVector;
        FormationDirection = FVector::ForwardVector;
        UnitSpacing = 200.0f;
        bIsActive = false;
        CreationTime = 0.0f;
    }

    // Helper functions
    bool IsValid() const
    {
        return GroupID >= 0 && Leader.IsValid() && Units.Num() > 0;
    }

    int32 GetUnitCount() const
    {
        // Count only valid units
        int32 Count = 0;
        for (const TSoftObjectPtr<ARTSUnit>& Unit : Units)
        {
            if (Unit.IsValid())
            {
                Count++;
            }
        }
        return Count;
    }

    void CleanupInvalidUnits()
    {
        Units.RemoveAll([](const TSoftObjectPtr<ARTSUnit>& Unit)
        {
            return !Unit.IsValid();
        });
    }
};

// Delegate for formation events
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFormationCreated, int32, GroupID, const FRTSFormationGroup&, FormationGroup);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnFormationDisbanded, int32, GroupID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFormationLeaderChanged, int32, GroupID, ARTSUnit*, NewLeader);

/**
 * Formation Manager subsystem for managing unit formations
 * Handles squad leader election, formation coordination, and collision avoidance
 */
UCLASS(BlueprintType)
class ARMORWARS_API URTSFormationManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    URTSFormationManager();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    void Tick(float DeltaTime);
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override { return true; }

protected:
    // Formation groups storage
    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    TMap<int32, FRTSFormationGroup> FormationGroups;

    // Next group ID
    int32 NextGroupID = 0;

    // Update interval for formation management
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    float FormationUpdateInterval = 0.1f;

    // Last update time
    float LastUpdateTime = 0.0f;

    // Debug logging
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    bool bEnableDebugLogging = false;

public:
    // Formation creation and management
    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    int32 CreateFormation(const TArray<ARTSUnit*>& Units, ERTSFormationType FormationType = ERTSFormationType::Line, float UnitSpacing = 200.0f);

    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    bool DisbandFormation(int32 GroupID);

    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    bool AddUnitToFormation(int32 GroupID, ARTSUnit* Unit);

    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    bool RemoveUnitFromFormation(int32 GroupID, ARTSUnit* Unit);

    // Formation queries
    UFUNCTION(BlueprintPure, Category = "RTS|Formation")
    bool DoesFormationExist(int32 GroupID) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Formation")
    FRTSFormationGroup GetFormationGroup(int32 GroupID) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Formation")
    int32 GetFormationForUnit(ARTSUnit* Unit) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Formation")
    ARTSUnit* GetFormationLeader(int32 GroupID) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Formation")
    TArray<ARTSUnit*> GetFormationUnits(int32 GroupID) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Formation")
    TArray<int32> GetAllFormationIDs() const;

    // Formation movement
    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    bool MoveFormation(int32 GroupID, const FVector& TargetLocation, ERTSCommandPriority Priority = ERTSCommandPriority::Normal);

    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    bool SetFormationType(int32 GroupID, ERTSFormationType NewFormationType);

    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    bool SetFormationSpacing(int32 GroupID, float NewSpacing);

    // Formation position calculation
    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    TArray<FVector> CalculateFormationPositions(int32 GroupID, const FVector& FormationCenter) const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    FVector CalculateUnitFormationOffset(int32 GroupID, ARTSUnit* Unit) const;

    // Squad leader election
    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    ARTSUnit* ElectSquadLeader(const TArray<ARTSUnit*>& Units) const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    bool ChangeFormationLeader(int32 GroupID, ARTSUnit* NewLeader);

    // Formation maintenance
    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    void UpdateAllFormations();

    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    void UpdateFormation(int32 GroupID);

    UFUNCTION(BlueprintCallable, Category = "RTS|Formation")
    void CleanupInvalidFormations();

protected:
    // Internal formation management
    virtual void UpdateFormationPositions(FRTSFormationGroup& FormationGroup);
    virtual void ValidateFormationLeader(FRTSFormationGroup& FormationGroup);
    virtual void IssueFormationCommands(FRTSFormationGroup& FormationGroup);
    virtual FVector CalculateFormationCenter(const FRTSFormationGroup& FormationGroup) const;
    virtual FVector CalculateFormationDirection(const FRTSFormationGroup& FormationGroup) const;

    // Squad leader election criteria
    virtual float CalculateLeadershipScore(ARTSUnit* Unit) const;
    virtual bool IsValidLeader(ARTSUnit* Unit) const;

    // Utility functions
    virtual int32 GenerateGroupID();
    virtual void BroadcastFormationCreated(int32 GroupID, const FRTSFormationGroup& FormationGroup);
    virtual void BroadcastFormationDisbanded(int32 GroupID);
    virtual void BroadcastFormationLeaderChanged(int32 GroupID, ARTSUnit* NewLeader);

public:
    // Events
    UPROPERTY(BlueprintAssignable, Category = "RTS|Formation")
    FOnFormationCreated OnFormationCreated;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Formation")
    FOnFormationDisbanded OnFormationDisbanded;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Formation")
    FOnFormationLeaderChanged OnFormationLeaderChanged;
};

{"Version": "1.2", "Data": {"Source": "f:\\armorwars\\source\\armorwars\\private\\rtshud.cpp", "ProvidedModule": "", "PCH": "f:\\armorwars\\intermediate\\build\\win64\\x64\\armorwarseditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["f:\\armorwars\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\armorwars\\definitions.armorwars.h", "f:\\armorwars\\source\\armorwars\\public\\rtshud.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\hud.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\hudhitbox.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\debugtextinfo.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\debugtextinfo.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hud.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtshud.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsplayercontroller.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputactionvalue.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputactionvalue.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsplayercontroller.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtseconomysubsystem.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtseconomysubsystem.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsunit.h", "f:\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsbaseactor.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsbaseactor.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsunitaicomponent.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunitaicomponent.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtscommand.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscommand.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunit.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\canvas.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\public\\canvasitem.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\font.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\fontimportoptions.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\fontimportoptions.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\slatecore\\public\\fonts\\fontproviderinterface.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\slatecore\\uht\\fontproviderinterface.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\font.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\public\\canvastypes.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\canvastypes.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\canvas.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\blueprint\\userwidget.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\blueprint\\umgsequenceplaymode.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\umgsequenceplaymode.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\blueprint\\widgetchild.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetchild.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\objectsavecontext.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\cookenums.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\objectsaveoverride.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\cooker\\cookdependency.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\slatewrappertypes.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\slatewrappertypes.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\widget.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\binding\\states\\widgetstatebitfield.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetstatebitfield.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\fieldnotification\\public\\fieldnotificationdeclaration.h", "f:\\ue_5.6\\engine\\source\\runtime\\fieldnotification\\public\\ifieldnotificationclassdescriptor.h", "f:\\ue_5.6\\engine\\source\\runtime\\fieldnotification\\public\\inotifyfieldvaluechanged.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\fieldnotification\\uht\\inotifyfieldvaluechanged.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\visual.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\visual.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\slate\\widgettransform.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgettransform.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\blueprint\\widgetnavigation.h", "f:\\ue_5.6\\engine\\source\\runtime\\slatecore\\public\\types\\navigationmetadata.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetnavigation.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widget.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\components\\namedslotinterface.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\namedslotinterface.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\slate\\public\\widgets\\layout\\anchors.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\slate\\uht\\anchors.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\animation\\widgetanimationevents.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetanimationevents.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\animation\\widgetanimationhandle.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetanimationhandle.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\animation\\widgetanimationstate.h", "f:\\ue_5.6\\engine\\source\\runtime\\umg\\public\\animation\\umgsequencetickmanager.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\moviescenelatentactionmanager.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\umgsequencetickmanager.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviesceneplayback.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviescenesequencetransform.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\moviescenefwd.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenefwd.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviescenetimetransform.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenetimetransform.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviescenetimewarping.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenetimewarping.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\variants\\moviescenetimewarpvariant.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\variants\\moviescenenumericvariant.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenenumericvariant.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenetimewarpvariant.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenesequencetransform.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\moviescenetimehelpers.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviesceneplaybackmanager.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\moviescenesharedplaybackstate.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\compilation\\moviescenecompileddataid.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\moviescenesequenceinstancehandle.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\moviescenecomponentdebug.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\moviesceneentitysystemtypes.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\moviesceneentityids.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\inlinevalue.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviesceneevaluationoperand.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\moviescenesequenceid.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviescenesequenceid.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\moviescene\\uht\\moviesceneevaluationoperand.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviesceneplaybackcapabilities.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\imoviesceneplaybackcapability.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\entitysystem\\relativeptr.h", "f:\\ue_5.6\\engine\\source\\runtime\\moviescene\\public\\evaluation\\moviescenepreanimatedstate.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\userwidget.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}
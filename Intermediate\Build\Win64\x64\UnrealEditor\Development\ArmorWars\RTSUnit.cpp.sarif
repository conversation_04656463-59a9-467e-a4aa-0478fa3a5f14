{"version": "2.1.0", "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.5.json", "runs": [{"results": [{"ruleId": "C4996", "message": {"text": "'Cast::FStaticWarningMsg_159::condition': Attempting to use Cast<> on types that are not related"}, "analysisTarget": {"uri": "file:///F:/ArmorWars/Source/ArmorWars/Private/RTSUnit.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///F:/UE_5.6/Engine/Source/Runtime/CoreUObject/Public/Templates/Casts.h"}, "region": {"startLine": 159, "startColumn": 6, "snippet": {"text": "\t\t\t\t\tUE_STATIC_ASSERT_WARN((std::is_base_of_v<From, To>), \"Attempting to use Cast<> on types that are not related\");"}}}}], "relatedLocations": [{"id": 0, "physicalLocation": {"artifactLocation": {"uri": "file:///F:/UE_5.6/Engine/Source/Runtime/CoreUObject/Public/Templates/Casts.h"}, "region": {"startLine": 159, "startColumn": 6, "snippet": {"text": "\t\t\t\t\tUE_STATIC_ASSERT_WARN((std::is_base_of_v<From, To>), \"Attempting to use Cast<> on types that are not related\");"}}}, "message": {"text": "the template instantiation context (the oldest one first) is"}}, {"id": 1, "physicalLocation": {"artifactLocation": {"uri": "file:///F:/ArmorWars/Source/ArmorWars/Private/RTSUnit.cpp"}, "region": {"startLine": 740, "startColumn": 29, "snippet": {"text": "            if (RTSActor == Cast<ARTSBaseActor>(this) || !RTSActor->IsAlive())"}}}, "message": {"text": "see reference to function template instantiation 'const ARTSBaseActor *Cast<ARTSBaseActor,const ARTSUnit>(From *)' being compiled\n        with\n        [\n            From=const ARTSUnit\n        ]"}, "properties": {"nestingLevel": 1}}]}], "tool": {"driver": {"name": "MSVC", "shortDescription": {"text": "Microsoft Visual C++ Compiler Warnings/Errors"}, "informationUri": "https://docs.microsoft.com/cpp/error-messages/compiler-errors-1/c-cpp-build-errors"}}}]}
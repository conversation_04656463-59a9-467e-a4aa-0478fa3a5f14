#include "RTSCollisionAvoidanceSystem.h"
#include "RTSUnit.h"
#include "RTSCommandComponent.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"
#include "DrawDebugHelpers.h"

// URTSCollisionAvoidanceComponent Implementation

URTSCollisionAvoidanceComponent::URTSCollisionAvoidanceComponent()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f;
    
    bEnableCollisionAvoidance = true;
    bEnableDebugVisualization = false;
    bRespectFormationPositions = true;
}

void URTSCollisionAvoidanceComponent::BeginPlay()
{
    Super::BeginPlay();
    
    // Register with collision avoidance manager
    if (UWorld* World = GetWorld())
    {
        if (URTSCollisionAvoidanceManager* Manager = World->GetSubsystem<URTSCollisionAvoidanceManager>())
        {
            if (ARTSUnit* Unit = GetOwnerUnit())
            {
                Manager->RegisterUnit(Unit);
            }
        }
    }
}

void URTSCollisionAvoidanceComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    if (!bEnableCollisionAvoidance)
    {
        return;
    }
    
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Update avoidance data periodically
    if (CurrentTime - AvoidanceData.LastUpdateTime >= AvoidanceParams.UpdateInterval)
    {
        UpdateAvoidanceData();
        AvoidanceData.LastUpdateTime = CurrentTime;
    }
    
    // Draw debug visualization if enabled
    if (bEnableDebugVisualization)
    {
        DrawDebugVisualization();
    }
}

FVector URTSCollisionAvoidanceComponent::CalculateAvoidanceForce()
{
    if (!bEnableCollisionAvoidance)
    {
        return FVector::ZeroVector;
    }
    
    // Calculate individual force components
    AvoidanceData.SeparationForce = CalculateSeparationForce();
    AvoidanceData.AlignmentForce = CalculateAlignmentForce();
    AvoidanceData.CohesionForce = CalculateCohesionForce();
    AvoidanceData.ObstacleAvoidanceForce = CalculateObstacleAvoidanceForce();
    
    // Combine forces with weights
    FVector TotalForce = FVector::ZeroVector;
    TotalForce += AvoidanceData.SeparationForce * AvoidanceParams.SeparationWeight;
    TotalForce += AvoidanceData.AlignmentForce * AvoidanceParams.AlignmentWeight;
    TotalForce += AvoidanceData.CohesionForce * AvoidanceParams.CohesionWeight;
    TotalForce += AvoidanceData.ObstacleAvoidanceForce * AvoidanceParams.ObstacleAvoidanceWeight;
    
    AvoidanceData.TotalAvoidanceForce = TotalForce;
    return TotalForce.GetSafeNormal();
}

FVector URTSCollisionAvoidanceComponent::GetAdjustedMovementDirection(const FVector& DesiredDirection)
{
    if (!bEnableCollisionAvoidance)
    {
        return DesiredDirection;
    }
    
    FVector AvoidanceForce = CalculateAvoidanceForce();
    FVector AdjustedDirection = (DesiredDirection + AvoidanceForce).GetSafeNormal();
    
    return AdjustedDirection;
}

void URTSCollisionAvoidanceComponent::UpdateAvoidanceData()
{
    // Find nearby units
    TArray<ARTSUnit*> NearbyUnits = FindNearbyUnits();
    
    // Update nearby units list
    AvoidanceData.NearbyUnits.Empty();
    for (ARTSUnit* Unit : NearbyUnits)
    {
        if (Unit)
        {
            AvoidanceData.NearbyUnits.Add(TSoftObjectPtr<ARTSUnit>(Unit));
        }
    }
}

FVector URTSCollisionAvoidanceComponent::CalculateSeparationForce()
{
    FVector SeparationForce = FVector::ZeroVector;
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    
    if (!OwnerUnit)
    {
        return SeparationForce;
    }
    
    FVector OwnerLocation = OwnerUnit->GetActorLocation();
    int32 SeparationCount = 0;
    
    for (const TSoftObjectPtr<ARTSUnit>& UnitPtr : AvoidanceData.NearbyUnits)
    {
        if (ARTSUnit* OtherUnit = UnitPtr.Get())
        {
            if (OtherUnit == OwnerUnit)
            {
                continue;
            }

            FVector ToOther = OtherUnit->GetActorLocation() - OwnerLocation;
            float Distance = ToOther.Size();

            if (Distance > 0.0f && Distance < AvoidanceParams.AvoidanceRadius)
            {
                // Stronger separation force when closer
                float SeparationStrength = (AvoidanceParams.AvoidanceRadius - Distance) / AvoidanceParams.AvoidanceRadius;

                // Extra strong force for personal space violations
                if (Distance < AvoidanceParams.PersonalSpaceRadius)
                {
                    SeparationStrength *= 3.0f;
                }

                // Apply formation awareness
                if (bRespectFormationPositions && IsFormationMate(OtherUnit))
                {
                    SeparationStrength *= 0.5f; // Reduced separation for formation mates
                }

                FVector SeparationDirection = -ToOther.GetSafeNormal();
                SeparationForce += SeparationDirection * SeparationStrength;
                SeparationCount++;
            }
        }
    }
    
    if (SeparationCount > 0)
    {
        SeparationForce /= SeparationCount;
    }
    
    return SeparationForce.GetSafeNormal();
}

FVector URTSCollisionAvoidanceComponent::CalculateAlignmentForce()
{
    if (!AvoidanceParams.bUseVelocityMatching)
    {
        return FVector::ZeroVector;
    }
    
    FVector AverageVelocity = FVector::ZeroVector;
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    
    if (!OwnerUnit)
    {
        return FVector::ZeroVector;
    }
    
    int32 AlignmentCount = 0;
    
    for (const TWeakObjectPtr<ARTSUnit>& UnitPtr : AvoidanceData.NearbyUnits)
    {
        if (ARTSUnit* OtherUnit = UnitPtr.Get())
        {
            if (OtherUnit == OwnerUnit)
            {
                continue;
            }
            
            float Distance = GetDistanceToUnit(OtherUnit);
            if (Distance <= AvoidanceParams.AvoidanceRadius)
            {
                AverageVelocity += OtherUnit->GetVelocity();
                AlignmentCount++;
            }
        }
    }
    
    if (AlignmentCount > 0)
    {
        AverageVelocity /= AlignmentCount;
        return AverageVelocity.GetSafeNormal();
    }
    
    return FVector::ZeroVector;
}

FVector URTSCollisionAvoidanceComponent::CalculateCohesionForce()
{
    FVector CenterOfMass = FVector::ZeroVector;
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    
    if (!OwnerUnit)
    {
        return FVector::ZeroVector;
    }
    
    FVector OwnerLocation = OwnerUnit->GetActorLocation();
    int32 CohesionCount = 0;
    
    for (const TWeakObjectPtr<ARTSUnit>& UnitPtr : AvoidanceData.NearbyUnits)
    {
        if (ARTSUnit* OtherUnit = UnitPtr.Get())
        {
            if (OtherUnit == OwnerUnit)
            {
                continue;
            }
            
            float Distance = GetDistanceToUnit(OtherUnit);
            if (Distance <= AvoidanceParams.AvoidanceRadius)
            {
                CenterOfMass += OtherUnit->GetActorLocation();
                CohesionCount++;
            }
        }
    }
    
    if (CohesionCount > 0)
    {
        CenterOfMass /= CohesionCount;
        FVector ToCenterOfMass = CenterOfMass - OwnerLocation;
        return ToCenterOfMass.GetSafeNormal();
    }
    
    return FVector::ZeroVector;
}

FVector URTSCollisionAvoidanceComponent::CalculateObstacleAvoidanceForce()
{
    FVector ObstacleForce = FVector::ZeroVector;
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    
    if (!OwnerUnit)
    {
        return ObstacleForce;
    }
    
    FVector OwnerLocation = OwnerUnit->GetActorLocation();
    FVector OwnerVelocity = OwnerUnit->GetVelocity();
    
    if (OwnerVelocity.IsNearlyZero())
    {
        return ObstacleForce;
    }
    
    FVector LookAheadDirection = OwnerVelocity.GetSafeNormal();
    
    // Check for potential collisions along movement path
    for (const TWeakObjectPtr<ARTSUnit>& UnitPtr : AvoidanceData.NearbyUnits)
    {
        if (ARTSUnit* OtherUnit = UnitPtr.Get())
        {
            if (OtherUnit == OwnerUnit)
            {
                continue;
            }
            
            FVector PredictedCollisionPoint = PredictCollision(OtherUnit);
            if (!PredictedCollisionPoint.IsZero())
            {
                FVector AvoidanceDirection = (OwnerLocation - PredictedCollisionPoint).GetSafeNormal();
                float CollisionDistance = FVector::Dist(OwnerLocation, PredictedCollisionPoint);
                float AvoidanceStrength = FMath::Max(0.0f, (AvoidanceParams.LookAheadDistance - CollisionDistance) / AvoidanceParams.LookAheadDistance);
                
                ObstacleForce += AvoidanceDirection * AvoidanceStrength;
            }
        }
    }
    
    return ObstacleForce.GetSafeNormal();
}

TArray<ARTSUnit*> URTSCollisionAvoidanceComponent::FindNearbyUnits()
{
    TArray<ARTSUnit*> NearbyUnits;
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    
    if (!OwnerUnit)
    {
        return NearbyUnits;
    }
    
    // Use collision avoidance manager for optimized queries
    if (UWorld* World = GetWorld())
    {
        if (URTSCollisionAvoidanceManager* Manager = World->GetSubsystem<URTSCollisionAvoidanceManager>())
        {
            NearbyUnits = Manager->GetNearbyUnits(OwnerUnit, AvoidanceParams.AvoidanceRadius);
        }
    }
    
    // Fallback to manual search if manager not available
    if (NearbyUnits.Num() == 0)
    {
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(GetWorld(), ARTSUnit::StaticClass(), FoundActors);
        
        FVector OwnerLocation = OwnerUnit->GetActorLocation();
        
        for (AActor* Actor : FoundActors)
        {
            if (ARTSUnit* Unit = Cast<ARTSUnit>(Actor))
            {
                if (Unit != OwnerUnit && Unit->IsAlive())
                {
                    float Distance = FVector::Dist(OwnerLocation, Unit->GetActorLocation());
                    if (Distance <= AvoidanceParams.AvoidanceRadius && NearbyUnits.Num() < AvoidanceParams.MaxNearbyUnits)
                    {
                        NearbyUnits.Add(Unit);
                    }
                }
            }
        }
    }
    
    return NearbyUnits;
}

bool URTSCollisionAvoidanceComponent::IsUnitInPersonalSpace(ARTSUnit* OtherUnit) const
{
    if (!OtherUnit)
    {
        return false;
    }

    return GetDistanceToUnit(OtherUnit) <= AvoidanceParams.PersonalSpaceRadius;
}

bool URTSCollisionAvoidanceComponent::IsUnitInAvoidanceRange(ARTSUnit* OtherUnit) const
{
    if (!OtherUnit)
    {
        return false;
    }

    return GetDistanceToUnit(OtherUnit) <= AvoidanceParams.AvoidanceRadius;
}

float URTSCollisionAvoidanceComponent::GetDistanceToUnit(ARTSUnit* OtherUnit) const
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit || !OtherUnit)
    {
        return TNumericLimits<float>::Max();
    }

    return FVector::Dist(OwnerUnit->GetActorLocation(), OtherUnit->GetActorLocation());
}

ARTSUnit* URTSCollisionAvoidanceComponent::GetOwnerUnit() const
{
    return Cast<ARTSUnit>(GetOwner());
}

void URTSCollisionAvoidanceComponent::DrawDebugVisualization()
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit || !GetWorld())
    {
        return;
    }

    FVector OwnerLocation = OwnerUnit->GetActorLocation();

    // Draw avoidance radius
    DrawDebugCircle(GetWorld(), OwnerLocation, AvoidanceParams.AvoidanceRadius, 32, FColor::Yellow, false, -1.0f, 0, 2.0f, FVector::ForwardVector, FVector::RightVector);

    // Draw personal space radius
    DrawDebugCircle(GetWorld(), OwnerLocation, AvoidanceParams.PersonalSpaceRadius, 16, FColor::Red, false, -1.0f, 0, 3.0f, FVector::ForwardVector, FVector::RightVector);

    // Draw avoidance forces
    if (!AvoidanceData.TotalAvoidanceForce.IsNearlyZero())
    {
        FVector ForceEnd = OwnerLocation + AvoidanceData.TotalAvoidanceForce * 200.0f;
        DrawDebugDirectionalArrow(GetWorld(), OwnerLocation, ForceEnd, 50.0f, FColor::Cyan, false, -1.0f, 0, 5.0f);
    }

    // Draw connections to nearby units
    for (const TWeakObjectPtr<ARTSUnit>& UnitPtr : AvoidanceData.NearbyUnits)
    {
        if (ARTSUnit* OtherUnit = UnitPtr.Get())
        {
            FColor LineColor = IsUnitInPersonalSpace(OtherUnit) ? FColor::Red : FColor::Orange;
            DrawDebugLine(GetWorld(), OwnerLocation, OtherUnit->GetActorLocation(), LineColor, false, -1.0f, 0, 1.0f);
        }
    }
}

bool URTSCollisionAvoidanceComponent::ShouldAvoidUnit(ARTSUnit* OtherUnit) const
{
    if (!OtherUnit || OtherUnit == GetOwnerUnit())
    {
        return false;
    }

    if (!OtherUnit->IsAlive())
    {
        return false;
    }

    return true;
}

float URTSCollisionAvoidanceComponent::GetAvoidanceWeight(ARTSUnit* OtherUnit) const
{
    if (!ShouldAvoidUnit(OtherUnit))
    {
        return 0.0f;
    }

    float Weight = 1.0f;

    // Reduce avoidance weight for formation mates
    if (IsFormationMate(OtherUnit))
    {
        Weight *= 0.3f;
    }

    // Increase weight for units in personal space
    if (IsUnitInPersonalSpace(OtherUnit))
    {
        Weight *= 3.0f;
    }

    return Weight;
}

FVector URTSCollisionAvoidanceComponent::PredictCollision(ARTSUnit* OtherUnit) const
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit || !OtherUnit)
    {
        return FVector::ZeroVector;
    }

    FVector OwnerLocation = OwnerUnit->GetActorLocation();
    FVector OwnerVelocity = OwnerUnit->GetVelocity();
    FVector OtherLocation = OtherUnit->GetActorLocation();
    FVector OtherVelocity = OtherUnit->GetVelocity();

    // Simple collision prediction based on current velocities
    float PredictionTime = 2.0f; // Predict 2 seconds ahead

    FVector OwnerFutureLocation = OwnerLocation + OwnerVelocity * PredictionTime;
    FVector OtherFutureLocation = OtherLocation + OtherVelocity * PredictionTime;

    float FutureDistance = FVector::Dist(OwnerFutureLocation, OtherFutureLocation);

    // If units will be too close, return collision point
    if (FutureDistance < AvoidanceParams.PersonalSpaceRadius * 2.0f)
    {
        return (OwnerFutureLocation + OtherFutureLocation) * 0.5f;
    }

    return FVector::ZeroVector;
}

bool URTSCollisionAvoidanceComponent::IsFormationMate(ARTSUnit* OtherUnit) const
{
    if (!bRespectFormationPositions || !OtherUnit)
    {
        return false;
    }

    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        return false;
    }

    // Check if both units are in the same formation
    URTSCommandComponent* OwnerCommandComp = OwnerUnit->GetCommandComponent();
    URTSCommandComponent* OtherCommandComp = OtherUnit->GetCommandComponent();

    if (!OwnerCommandComp || !OtherCommandComp)
    {
        return false;
    }

    if (!OwnerCommandComp->IsInFormation() || !OtherCommandComp->IsInFormation())
    {
        return false;
    }

    // Same formation leader means they're formation mates
    return OwnerCommandComp->GetFormationLeader() == OtherCommandComp->GetFormationLeader();
}

// URTSCollisionAvoidanceManager Implementation

URTSCollisionAvoidanceManager::URTSCollisionAvoidanceManager()
{
    GridCellSize = 500.0f;
    GlobalUpdateInterval = 0.05f;
    bUseOptimizedCalculations = true;
    bEnableDebugLogging = false;
    LastGlobalUpdate = 0.0f;
}

void URTSCollisionAvoidanceManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCollisionAvoidanceManager: Initialized"));
    }
}

void URTSCollisionAvoidanceManager::Deinitialize()
{
    RegisteredUnits.Empty();
    SpatialGrid.Empty();

    Super::Deinitialize();

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCollisionAvoidanceManager: Deinitialized"));
    }
}

void URTSCollisionAvoidanceManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    if (CurrentTime - LastGlobalUpdate >= GlobalUpdateInterval)
    {
        CleanupInvalidUnits();

        if (bUseOptimizedCalculations)
        {
            UpdateSpatialGrid();
        }

        LastGlobalUpdate = CurrentTime;
    }
}

void URTSCollisionAvoidanceManager::RegisterUnit(ARTSUnit* Unit)
{
    if (!Unit)
    {
        return;
    }

    // Check if already registered
    for (const TWeakObjectPtr<ARTSUnit>& RegisteredUnit : RegisteredUnits)
    {
        if (RegisteredUnit.Get() == Unit)
        {
            return; // Already registered
        }
    }

    RegisteredUnits.Add(Unit);

    if (bUseOptimizedCalculations)
    {
        AddUnitToSpatialGrid(Unit);
    }

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCollisionAvoidanceManager: Registered unit %s"), *Unit->GetName());
    }
}

void URTSCollisionAvoidanceManager::UnregisterUnit(ARTSUnit* Unit)
{
    if (!Unit)
    {
        return;
    }

    RegisteredUnits.RemoveAll([Unit](const TSoftObjectPtr<ARTSUnit>& RegisteredUnit)
    {
        return RegisteredUnit.Get() == Unit;
    });

    if (bUseOptimizedCalculations)
    {
        RemoveUnitFromSpatialGrid(Unit);
    }

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCollisionAvoidanceManager: Unregistered unit %s"), *Unit->GetName());
    }
}

TArray<ARTSUnit*> URTSCollisionAvoidanceManager::GetUnitsInRadius(const FVector& Location, float Radius)
{
    TArray<ARTSUnit*> UnitsInRadius;

    if (bUseOptimizedCalculations)
    {
        // Use spatial grid for optimized search
        TArray<FIntPoint> CellsToCheck = GetCellsInRadius(Location, Radius);

        for (const FIntPoint& Cell : CellsToCheck)
        {
            if (const FRTSSpatialGridCell* CellData = SpatialGrid.Find(Cell))
            {
                for (const TWeakObjectPtr<ARTSUnit>& UnitPtr : CellData->Units)
                {
                    if (ARTSUnit* Unit = UnitPtr.Get())
                    {
                        float Distance = FVector::Dist(Location, Unit->GetActorLocation());
                        if (Distance <= Radius)
                        {
                            UnitsInRadius.AddUnique(Unit);
                        }
                    }
                }
            }
        }
    }
    else
    {
        // Brute force search
        for (const TWeakObjectPtr<ARTSUnit>& UnitPtr : RegisteredUnits)
        {
            if (ARTSUnit* Unit = UnitPtr.Get())
            {
                float Distance = FVector::Dist(Location, Unit->GetActorLocation());
                if (Distance <= Radius)
                {
                    UnitsInRadius.Add(Unit);
                }
            }
        }
    }

    return UnitsInRadius;
}

TArray<ARTSUnit*> URTSCollisionAvoidanceManager::GetUnitsInCell(const FVector& Location)
{
    TArray<ARTSUnit*> UnitsInCell;

    FIntPoint GridCell = WorldLocationToGridCell(Location);

    if (const FRTSSpatialGridCell* CellData = SpatialGrid.Find(GridCell))
    {
        for (const TWeakObjectPtr<ARTSUnit>& UnitPtr : CellData->Units)
        {
            if (ARTSUnit* Unit = UnitPtr.Get())
            {
                UnitsInCell.Add(Unit);
            }
        }
    }

    return UnitsInCell;
}

TArray<ARTSUnit*> URTSCollisionAvoidanceManager::GetNearbyUnits(ARTSUnit* Unit, float Radius)
{
    if (!Unit)
    {
        return TArray<ARTSUnit*>();
    }

    TArray<ARTSUnit*> NearbyUnits = GetUnitsInRadius(Unit->GetActorLocation(), Radius);

    // Remove the unit itself from the list
    NearbyUnits.Remove(Unit);

    return NearbyUnits;
}

void URTSCollisionAvoidanceManager::UpdateSpatialGrid()
{
    // Clear existing grid
    SpatialGrid.Empty();

    // Add all registered units to grid
    for (const TWeakObjectPtr<ARTSUnit>& UnitPtr : RegisteredUnits)
    {
        if (ARTSUnit* Unit = UnitPtr.Get())
        {
            AddUnitToSpatialGrid(Unit);
        }
    }
}

void URTSCollisionAvoidanceManager::CleanupInvalidUnits()
{
    RegisteredUnits.RemoveAll([](const TWeakObjectPtr<ARTSUnit>& UnitPtr)
    {
        return !UnitPtr.IsValid();
    });
}

FIntPoint URTSCollisionAvoidanceManager::WorldLocationToGridCell(const FVector& WorldLocation) const
{
    int32 X = FMath::FloorToInt(WorldLocation.X / GridCellSize);
    int32 Y = FMath::FloorToInt(WorldLocation.Y / GridCellSize);
    return FIntPoint(X, Y);
}

FVector URTSCollisionAvoidanceManager::GridCellToWorldLocation(const FIntPoint& GridCell) const
{
    float X = GridCell.X * GridCellSize + (GridCellSize * 0.5f);
    float Y = GridCell.Y * GridCellSize + (GridCellSize * 0.5f);
    return FVector(X, Y, 0.0f);
}

int32 URTSCollisionAvoidanceManager::GetRegisteredUnitCount() const
{
    return RegisteredUnits.Num();
}

void URTSCollisionAvoidanceManager::AddUnitToSpatialGrid(ARTSUnit* Unit)
{
    if (!Unit)
    {
        return;
    }

    FIntPoint GridCell = WorldLocationToGridCell(Unit->GetActorLocation());

    if (!SpatialGrid.Contains(GridCell))
    {
        SpatialGrid.Add(GridCell, FRTSSpatialGridCell());
    }

    SpatialGrid[GridCell].Units.AddUnique(Unit);
}

void URTSCollisionAvoidanceManager::RemoveUnitFromSpatialGrid(ARTSUnit* Unit)
{
    if (!Unit)
    {
        return;
    }

    FIntPoint GridCell = WorldLocationToGridCell(Unit->GetActorLocation());

    if (FRTSSpatialGridCell* Cell = SpatialGrid.Find(GridCell))
    {
        Cell->Units.RemoveAll([Unit](const TWeakObjectPtr<ARTSUnit>& UnitPtr)
        {
            return UnitPtr.Get() == Unit;
        });

        // Remove empty cells
        if (Cell->Units.Num() == 0)
        {
            SpatialGrid.Remove(GridCell);
        }
    }
}

TArray<FIntPoint> URTSCollisionAvoidanceManager::GetCellsInRadius(const FVector& Location, float Radius) const
{
    TArray<FIntPoint> Cells;

    FIntPoint CenterCell = WorldLocationToGridCell(Location);
    int32 CellRadius = FMath::CeilToInt(Radius / GridCellSize);

    for (int32 X = CenterCell.X - CellRadius; X <= CenterCell.X + CellRadius; X++)
    {
        for (int32 Y = CenterCell.Y - CellRadius; Y <= CenterCell.Y + CellRadius; Y++)
        {
            FIntPoint Cell(X, Y);
            FVector CellCenter = GridCellToWorldLocation(Cell);

            if (FVector::Dist2D(Location, CellCenter) <= Radius + GridCellSize)
            {
                Cells.Add(Cell);
            }
        }
    }

    return Cells;
}

void URTSCollisionAvoidanceManager::UpdateSpatialGrid()
{
    // Clear existing grid
    SpatialGrid.Empty();

    // Add all registered units to grid
    for (const TWeakObjectPtr<ARTSUnit>& UnitPtr : RegisteredUnits)
    {
        if (ARTSUnit* Unit = UnitPtr.Get())
        {
            AddUnitToSpatialGrid(Unit);
        }
    }
}

void URTSCollisionAvoidanceManager::CleanupInvalidUnits()
{
    RegisteredUnits.RemoveAll([](const TWeakObjectPtr<ARTSUnit>& UnitPtr)
    {
        return !UnitPtr.IsValid();
    });
}

FIntPoint URTSCollisionAvoidanceManager::WorldLocationToGridCell(const FVector& WorldLocation) const
{
    int32 X = FMath::FloorToInt(WorldLocation.X / GridCellSize);
    int32 Y = FMath::FloorToInt(WorldLocation.Y / GridCellSize);
    return FIntPoint(X, Y);
}

FVector URTSCollisionAvoidanceManager::GridCellToWorldLocation(const FIntPoint& GridCell) const
{
    float X = GridCell.X * GridCellSize + (GridCellSize * 0.5f);
    float Y = GridCell.Y * GridCellSize + (GridCellSize * 0.5f);
    return FVector(X, Y, 0.0f);
}

int32 URTSCollisionAvoidanceManager::GetRegisteredUnitCount() const
{
    return RegisteredUnits.Num();
}

void URTSCollisionAvoidanceManager::AddUnitToSpatialGrid(ARTSUnit* Unit)
{
    if (!Unit)
    {
        return;
    }

    FIntPoint GridCell = WorldLocationToGridCell(Unit->GetActorLocation());

    if (!SpatialGrid.Contains(GridCell))
    {
        SpatialGrid.Add(GridCell, TArray<TWeakObjectPtr<ARTSUnit>>());
    }

    SpatialGrid[GridCell].AddUnique(Unit);
}

void URTSCollisionAvoidanceManager::RemoveUnitFromSpatialGrid(ARTSUnit* Unit)
{
    if (!Unit)
    {
        return;
    }

    FIntPoint GridCell = WorldLocationToGridCell(Unit->GetActorLocation());

    if (TArray<TWeakObjectPtr<ARTSUnit>>* CellUnits = SpatialGrid.Find(GridCell))
    {
        CellUnits->RemoveAll([Unit](const TWeakObjectPtr<ARTSUnit>& UnitPtr)
        {
            return UnitPtr.Get() == Unit;
        });

        // Remove empty cells
        if (CellUnits->Num() == 0)
        {
            SpatialGrid.Remove(GridCell);
        }
    }
}

TArray<FIntPoint> URTSCollisionAvoidanceManager::GetCellsInRadius(const FVector& Location, float Radius) const
{
    TArray<FIntPoint> Cells;

    FIntPoint CenterCell = WorldLocationToGridCell(Location);
    int32 CellRadius = FMath::CeilToInt(Radius / GridCellSize);

    for (int32 X = CenterCell.X - CellRadius; X <= CenterCell.X + CellRadius; X++)
    {
        for (int32 Y = CenterCell.Y - CellRadius; Y <= CenterCell.Y + CellRadius; Y++)
        {
            FIntPoint Cell(X, Y);
            FVector CellCenter = GridCellToWorldLocation(Cell);

            if (FVector::Dist2D(Location, CellCenter) <= Radius + GridCellSize)
            {
                Cells.Add(Cell);
            }
        }
    }

    return Cells;
}

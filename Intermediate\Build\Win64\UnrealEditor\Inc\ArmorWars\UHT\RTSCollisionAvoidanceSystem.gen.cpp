// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSCollisionAvoidanceSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSCollisionAvoidanceSystem() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceComponent();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceManager();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCollisionAvoidanceManager_NoRegister();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSSpatialGridCell();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntPoint();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FRTSCollisionAvoidanceParams **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceParams;
class UScriptStruct* FRTSCollisionAvoidanceParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSCollisionAvoidanceParams"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision avoidance parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision avoidance parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PersonalSpaceRadius_MetaData[] = {
		{ "Category", "Collision Avoidance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Avoidance radii\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Avoidance radii" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvoidanceRadius_MetaData[] = {
		{ "Category", "Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LookAheadDistance_MetaData[] = {
		{ "Category", "Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeparationWeight_MetaData[] = {
		{ "Category", "Collision Avoidance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Force weights\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Force weights" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlignmentWeight_MetaData[] = {
		{ "Category", "Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CohesionWeight_MetaData[] = {
		{ "Category", "Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObstacleAvoidanceWeight_MetaData[] = {
		{ "Category", "Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxNearbyUnits_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFormationAwareness_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVelocityMatching_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PersonalSpaceRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AvoidanceRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LookAheadDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeparationWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AlignmentWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CohesionWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObstacleAvoidanceWeight;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxNearbyUnits;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateInterval;
	static void NewProp_bUseFormationAwareness_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFormationAwareness;
	static void NewProp_bUseVelocityMatching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVelocityMatching;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSCollisionAvoidanceParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_PersonalSpaceRadius = { "PersonalSpaceRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceParams, PersonalSpaceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PersonalSpaceRadius_MetaData), NewProp_PersonalSpaceRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_AvoidanceRadius = { "AvoidanceRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceParams, AvoidanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvoidanceRadius_MetaData), NewProp_AvoidanceRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_LookAheadDistance = { "LookAheadDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceParams, LookAheadDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LookAheadDistance_MetaData), NewProp_LookAheadDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_SeparationWeight = { "SeparationWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceParams, SeparationWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeparationWeight_MetaData), NewProp_SeparationWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_AlignmentWeight = { "AlignmentWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceParams, AlignmentWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlignmentWeight_MetaData), NewProp_AlignmentWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_CohesionWeight = { "CohesionWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceParams, CohesionWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CohesionWeight_MetaData), NewProp_CohesionWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_ObstacleAvoidanceWeight = { "ObstacleAvoidanceWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceParams, ObstacleAvoidanceWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObstacleAvoidanceWeight_MetaData), NewProp_ObstacleAvoidanceWeight_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_MaxNearbyUnits = { "MaxNearbyUnits", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceParams, MaxNearbyUnits), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxNearbyUnits_MetaData), NewProp_MaxNearbyUnits_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_UpdateInterval = { "UpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceParams, UpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateInterval_MetaData), NewProp_UpdateInterval_MetaData) };
void Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_bUseFormationAwareness_SetBit(void* Obj)
{
	((FRTSCollisionAvoidanceParams*)Obj)->bUseFormationAwareness = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_bUseFormationAwareness = { "bUseFormationAwareness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSCollisionAvoidanceParams), &Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_bUseFormationAwareness_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFormationAwareness_MetaData), NewProp_bUseFormationAwareness_MetaData) };
void Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_bUseVelocityMatching_SetBit(void* Obj)
{
	((FRTSCollisionAvoidanceParams*)Obj)->bUseVelocityMatching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_bUseVelocityMatching = { "bUseVelocityMatching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSCollisionAvoidanceParams), &Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_bUseVelocityMatching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVelocityMatching_MetaData), NewProp_bUseVelocityMatching_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_PersonalSpaceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_AvoidanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_LookAheadDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_SeparationWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_AlignmentWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_CohesionWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_ObstacleAvoidanceWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_MaxNearbyUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_UpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_bUseFormationAwareness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewProp_bUseVelocityMatching,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSCollisionAvoidanceParams",
	Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::PropPointers),
	sizeof(FRTSCollisionAvoidanceParams),
	alignof(FRTSCollisionAvoidanceParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceParams.InnerSingleton, Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceParams.InnerSingleton;
}
// ********** End ScriptStruct FRTSCollisionAvoidanceParams ****************************************

// ********** Begin ScriptStruct FRTSSpatialGridCell ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSSpatialGridCell;
class UScriptStruct* FRTSSpatialGridCell::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSSpatialGridCell.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSSpatialGridCell.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSSpatialGridCell, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSSpatialGridCell"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSSpatialGridCell.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial grid cell data\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial grid cell data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Units_MetaData[] = {
		{ "Category", "Spatial Grid" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Units_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Units;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSSpatialGridCell>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::NewProp_Units_Inner = { "Units", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::NewProp_Units = { "Units", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSSpatialGridCell, Units), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Units_MetaData), NewProp_Units_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::NewProp_Units_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::NewProp_Units,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSSpatialGridCell",
	Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::PropPointers),
	sizeof(FRTSSpatialGridCell),
	alignof(FRTSSpatialGridCell),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSSpatialGridCell()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSSpatialGridCell.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSSpatialGridCell.InnerSingleton, Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSSpatialGridCell.InnerSingleton;
}
// ********** End ScriptStruct FRTSSpatialGridCell *************************************************

// ********** Begin ScriptStruct FRTSCollisionAvoidanceData ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceData;
class UScriptStruct* FRTSCollisionAvoidanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSCollisionAvoidanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision avoidance data for a unit\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision avoidance data for a unit" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SeparationForce_MetaData[] = {
		{ "Category", "Avoidance Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current avoidance forces\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current avoidance forces" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlignmentForce_MetaData[] = {
		{ "Category", "Avoidance Data" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CohesionForce_MetaData[] = {
		{ "Category", "Avoidance Data" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObstacleAvoidanceForce_MetaData[] = {
		{ "Category", "Avoidance Data" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalAvoidanceForce_MetaData[] = {
		{ "Category", "Avoidance Data" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NearbyUnits_MetaData[] = {
		{ "Category", "Avoidance Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Nearby units\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nearby units" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Avoidance Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Update tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update tracking" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SeparationForce;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AlignmentForce;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CohesionForce;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObstacleAvoidanceForce;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TotalAvoidanceForce;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_NearbyUnits_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NearbyUnits;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSCollisionAvoidanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_SeparationForce = { "SeparationForce", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceData, SeparationForce), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SeparationForce_MetaData), NewProp_SeparationForce_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_AlignmentForce = { "AlignmentForce", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceData, AlignmentForce), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlignmentForce_MetaData), NewProp_AlignmentForce_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_CohesionForce = { "CohesionForce", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceData, CohesionForce), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CohesionForce_MetaData), NewProp_CohesionForce_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_ObstacleAvoidanceForce = { "ObstacleAvoidanceForce", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceData, ObstacleAvoidanceForce), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObstacleAvoidanceForce_MetaData), NewProp_ObstacleAvoidanceForce_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_TotalAvoidanceForce = { "TotalAvoidanceForce", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceData, TotalAvoidanceForce), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalAvoidanceForce_MetaData), NewProp_TotalAvoidanceForce_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_NearbyUnits_Inner = { "NearbyUnits", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_NearbyUnits = { "NearbyUnits", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceData, NearbyUnits), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NearbyUnits_MetaData), NewProp_NearbyUnits_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCollisionAvoidanceData, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_SeparationForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_AlignmentForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_CohesionForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_ObstacleAvoidanceForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_TotalAvoidanceForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_NearbyUnits_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_NearbyUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSCollisionAvoidanceData",
	Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::PropPointers),
	sizeof(FRTSCollisionAvoidanceData),
	alignof(FRTSCollisionAvoidanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceData.InnerSingleton, Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceData.InnerSingleton;
}
// ********** End ScriptStruct FRTSCollisionAvoidanceData ******************************************

// ********** Begin Class URTSCollisionAvoidanceComponent Function CalculateAlignmentForce *********
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics
{
	struct RTSCollisionAvoidanceComponent_eventCalculateAlignmentForce_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventCalculateAlignmentForce_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "CalculateAlignmentForce", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::RTSCollisionAvoidanceComponent_eventCalculateAlignmentForce_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::RTSCollisionAvoidanceComponent_eventCalculateAlignmentForce_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execCalculateAlignmentForce)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateAlignmentForce();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function CalculateAlignmentForce ***********

// ********** Begin Class URTSCollisionAvoidanceComponent Function CalculateAvoidanceForce *********
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics
{
	struct RTSCollisionAvoidanceComponent_eventCalculateAvoidanceForce_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main avoidance functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main avoidance functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventCalculateAvoidanceForce_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "CalculateAvoidanceForce", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::RTSCollisionAvoidanceComponent_eventCalculateAvoidanceForce_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::RTSCollisionAvoidanceComponent_eventCalculateAvoidanceForce_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execCalculateAvoidanceForce)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateAvoidanceForce();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function CalculateAvoidanceForce ***********

// ********** Begin Class URTSCollisionAvoidanceComponent Function CalculateCohesionForce **********
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics
{
	struct RTSCollisionAvoidanceComponent_eventCalculateCohesionForce_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventCalculateCohesionForce_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "CalculateCohesionForce", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::RTSCollisionAvoidanceComponent_eventCalculateCohesionForce_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::RTSCollisionAvoidanceComponent_eventCalculateCohesionForce_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execCalculateCohesionForce)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateCohesionForce();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function CalculateCohesionForce ************

// ********** Begin Class URTSCollisionAvoidanceComponent Function CalculateObstacleAvoidanceForce *
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics
{
	struct RTSCollisionAvoidanceComponent_eventCalculateObstacleAvoidanceForce_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventCalculateObstacleAvoidanceForce_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "CalculateObstacleAvoidanceForce", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::RTSCollisionAvoidanceComponent_eventCalculateObstacleAvoidanceForce_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::RTSCollisionAvoidanceComponent_eventCalculateObstacleAvoidanceForce_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execCalculateObstacleAvoidanceForce)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateObstacleAvoidanceForce();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function CalculateObstacleAvoidanceForce ***

// ********** Begin Class URTSCollisionAvoidanceComponent Function CalculateSeparationForce ********
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics
{
	struct RTSCollisionAvoidanceComponent_eventCalculateSeparationForce_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Force calculation functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Force calculation functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventCalculateSeparationForce_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "CalculateSeparationForce", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::RTSCollisionAvoidanceComponent_eventCalculateSeparationForce_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::RTSCollisionAvoidanceComponent_eventCalculateSeparationForce_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execCalculateSeparationForce)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateSeparationForce();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function CalculateSeparationForce **********

// ********** Begin Class URTSCollisionAvoidanceComponent Function FindNearbyUnits *****************
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics
{
	struct RTSCollisionAvoidanceComponent_eventFindNearbyUnits_Parms
	{
		TArray<ARTSUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventFindNearbyUnits_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "FindNearbyUnits", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::RTSCollisionAvoidanceComponent_eventFindNearbyUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::RTSCollisionAvoidanceComponent_eventFindNearbyUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execFindNearbyUnits)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSUnit*>*)Z_Param__Result=P_THIS->FindNearbyUnits();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function FindNearbyUnits *******************

// ********** Begin Class URTSCollisionAvoidanceComponent Function GetAdjustedMovementDirection ****
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics
{
	struct RTSCollisionAvoidanceComponent_eventGetAdjustedMovementDirection_Parms
	{
		FVector DesiredDirection;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesiredDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DesiredDirection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::NewProp_DesiredDirection = { "DesiredDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventGetAdjustedMovementDirection_Parms, DesiredDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesiredDirection_MetaData), NewProp_DesiredDirection_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventGetAdjustedMovementDirection_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::NewProp_DesiredDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "GetAdjustedMovementDirection", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::RTSCollisionAvoidanceComponent_eventGetAdjustedMovementDirection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::RTSCollisionAvoidanceComponent_eventGetAdjustedMovementDirection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execGetAdjustedMovementDirection)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_DesiredDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetAdjustedMovementDirection(Z_Param_Out_DesiredDirection);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function GetAdjustedMovementDirection ******

// ********** Begin Class URTSCollisionAvoidanceComponent Function GetDistanceToUnit ***************
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics
{
	struct RTSCollisionAvoidanceComponent_eventGetDistanceToUnit_Parms
	{
		ARTSUnit* OtherUnit;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherUnit;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::NewProp_OtherUnit = { "OtherUnit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventGetDistanceToUnit_Parms, OtherUnit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventGetDistanceToUnit_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::NewProp_OtherUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "GetDistanceToUnit", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::RTSCollisionAvoidanceComponent_eventGetDistanceToUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::RTSCollisionAvoidanceComponent_eventGetDistanceToUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execGetDistanceToUnit)
{
	P_GET_OBJECT(ARTSUnit,Z_Param_OtherUnit);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetDistanceToUnit(Z_Param_OtherUnit);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function GetDistanceToUnit *****************

// ********** Begin Class URTSCollisionAvoidanceComponent Function GetOwnerUnit ********************
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics
{
	struct RTSCollisionAvoidanceComponent_eventGetOwnerUnit_Parms
	{
		ARTSUnit* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventGetOwnerUnit_Parms, ReturnValue), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "GetOwnerUnit", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::RTSCollisionAvoidanceComponent_eventGetOwnerUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::RTSCollisionAvoidanceComponent_eventGetOwnerUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execGetOwnerUnit)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSUnit**)Z_Param__Result=P_THIS->GetOwnerUnit();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function GetOwnerUnit **********************

// ********** Begin Class URTSCollisionAvoidanceComponent Function IsUnitInAvoidanceRange **********
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics
{
	struct RTSCollisionAvoidanceComponent_eventIsUnitInAvoidanceRange_Parms
	{
		ARTSUnit* OtherUnit;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherUnit;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::NewProp_OtherUnit = { "OtherUnit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventIsUnitInAvoidanceRange_Parms, OtherUnit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCollisionAvoidanceComponent_eventIsUnitInAvoidanceRange_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCollisionAvoidanceComponent_eventIsUnitInAvoidanceRange_Parms), &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::NewProp_OtherUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "IsUnitInAvoidanceRange", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::RTSCollisionAvoidanceComponent_eventIsUnitInAvoidanceRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::RTSCollisionAvoidanceComponent_eventIsUnitInAvoidanceRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execIsUnitInAvoidanceRange)
{
	P_GET_OBJECT(ARTSUnit,Z_Param_OtherUnit);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsUnitInAvoidanceRange(Z_Param_OtherUnit);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function IsUnitInAvoidanceRange ************

// ********** Begin Class URTSCollisionAvoidanceComponent Function IsUnitInPersonalSpace ***********
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics
{
	struct RTSCollisionAvoidanceComponent_eventIsUnitInPersonalSpace_Parms
	{
		ARTSUnit* OtherUnit;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherUnit;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::NewProp_OtherUnit = { "OtherUnit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceComponent_eventIsUnitInPersonalSpace_Parms, OtherUnit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCollisionAvoidanceComponent_eventIsUnitInPersonalSpace_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCollisionAvoidanceComponent_eventIsUnitInPersonalSpace_Parms), &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::NewProp_OtherUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "IsUnitInPersonalSpace", Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::RTSCollisionAvoidanceComponent_eventIsUnitInPersonalSpace_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::RTSCollisionAvoidanceComponent_eventIsUnitInPersonalSpace_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execIsUnitInPersonalSpace)
{
	P_GET_OBJECT(ARTSUnit,Z_Param_OtherUnit);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsUnitInPersonalSpace(Z_Param_OtherUnit);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function IsUnitInPersonalSpace *************

// ********** Begin Class URTSCollisionAvoidanceComponent Function UpdateAvoidanceData *************
struct Z_Construct_UFunction_URTSCollisionAvoidanceComponent_UpdateAvoidanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceComponent_UpdateAvoidanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceComponent, nullptr, "UpdateAvoidanceData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceComponent_UpdateAvoidanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceComponent_UpdateAvoidanceData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceComponent_UpdateAvoidanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceComponent_UpdateAvoidanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceComponent::execUpdateAvoidanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAvoidanceData();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceComponent Function UpdateAvoidanceData ***************

// ********** Begin Class URTSCollisionAvoidanceComponent ******************************************
void URTSCollisionAvoidanceComponent::StaticRegisterNativesURTSCollisionAvoidanceComponent()
{
	UClass* Class = URTSCollisionAvoidanceComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateAlignmentForce", &URTSCollisionAvoidanceComponent::execCalculateAlignmentForce },
		{ "CalculateAvoidanceForce", &URTSCollisionAvoidanceComponent::execCalculateAvoidanceForce },
		{ "CalculateCohesionForce", &URTSCollisionAvoidanceComponent::execCalculateCohesionForce },
		{ "CalculateObstacleAvoidanceForce", &URTSCollisionAvoidanceComponent::execCalculateObstacleAvoidanceForce },
		{ "CalculateSeparationForce", &URTSCollisionAvoidanceComponent::execCalculateSeparationForce },
		{ "FindNearbyUnits", &URTSCollisionAvoidanceComponent::execFindNearbyUnits },
		{ "GetAdjustedMovementDirection", &URTSCollisionAvoidanceComponent::execGetAdjustedMovementDirection },
		{ "GetDistanceToUnit", &URTSCollisionAvoidanceComponent::execGetDistanceToUnit },
		{ "GetOwnerUnit", &URTSCollisionAvoidanceComponent::execGetOwnerUnit },
		{ "IsUnitInAvoidanceRange", &URTSCollisionAvoidanceComponent::execIsUnitInAvoidanceRange },
		{ "IsUnitInPersonalSpace", &URTSCollisionAvoidanceComponent::execIsUnitInPersonalSpace },
		{ "UpdateAvoidanceData", &URTSCollisionAvoidanceComponent::execUpdateAvoidanceData },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSCollisionAvoidanceComponent;
UClass* URTSCollisionAvoidanceComponent::GetPrivateStaticClass()
{
	using TClass = URTSCollisionAvoidanceComponent;
	if (!Z_Registration_Info_UClass_URTSCollisionAvoidanceComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSCollisionAvoidanceComponent"),
			Z_Registration_Info_UClass_URTSCollisionAvoidanceComponent.InnerSingleton,
			StaticRegisterNativesURTSCollisionAvoidanceComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSCollisionAvoidanceComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSCollisionAvoidanceComponent_NoRegister()
{
	return URTSCollisionAvoidanceComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Component for individual unit collision avoidance\n * Handles local collision avoidance calculations and force application\n */" },
#endif
		{ "IncludePath", "RTSCollisionAvoidanceSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Component for individual unit collision avoidance\nHandles local collision avoidance calculations and force application" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvoidanceParams_MetaData[] = {
		{ "Category", "Collision Avoidance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Avoidance parameters\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Avoidance parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvoidanceData_MetaData[] = {
		{ "Category", "Collision Avoidance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current avoidance data\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current avoidance data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCollisionAvoidance_MetaData[] = {
		{ "Category", "Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Component settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Component settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugVisualization_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRespectFormationPositions_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AvoidanceParams;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AvoidanceData;
	static void NewProp_bEnableCollisionAvoidance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCollisionAvoidance;
	static void NewProp_bEnableDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugVisualization;
	static void NewProp_bRespectFormationPositions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRespectFormationPositions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAlignmentForce, "CalculateAlignmentForce" }, // 962919838
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateAvoidanceForce, "CalculateAvoidanceForce" }, // 2291595345
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateCohesionForce, "CalculateCohesionForce" }, // 876429550
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateObstacleAvoidanceForce, "CalculateObstacleAvoidanceForce" }, // 3491110399
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_CalculateSeparationForce, "CalculateSeparationForce" }, // 848179033
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_FindNearbyUnits, "FindNearbyUnits" }, // 2970836627
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetAdjustedMovementDirection, "GetAdjustedMovementDirection" }, // 3140630089
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetDistanceToUnit, "GetDistanceToUnit" }, // 1230736731
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_GetOwnerUnit, "GetOwnerUnit" }, // 2010463189
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInAvoidanceRange, "IsUnitInAvoidanceRange" }, // 394534804
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_IsUnitInPersonalSpace, "IsUnitInPersonalSpace" }, // 2343363309
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceComponent_UpdateAvoidanceData, "UpdateAvoidanceData" }, // 3814865286
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSCollisionAvoidanceComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_AvoidanceParams = { "AvoidanceParams", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceComponent, AvoidanceParams), Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvoidanceParams_MetaData), NewProp_AvoidanceParams_MetaData) }; // 1586078789
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_AvoidanceData = { "AvoidanceData", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceComponent, AvoidanceData), Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvoidanceData_MetaData), NewProp_AvoidanceData_MetaData) }; // 3543739760
void Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bEnableCollisionAvoidance_SetBit(void* Obj)
{
	((URTSCollisionAvoidanceComponent*)Obj)->bEnableCollisionAvoidance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bEnableCollisionAvoidance = { "bEnableCollisionAvoidance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSCollisionAvoidanceComponent), &Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bEnableCollisionAvoidance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCollisionAvoidance_MetaData), NewProp_bEnableCollisionAvoidance_MetaData) };
void Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bEnableDebugVisualization_SetBit(void* Obj)
{
	((URTSCollisionAvoidanceComponent*)Obj)->bEnableDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bEnableDebugVisualization = { "bEnableDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSCollisionAvoidanceComponent), &Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bEnableDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugVisualization_MetaData), NewProp_bEnableDebugVisualization_MetaData) };
void Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bRespectFormationPositions_SetBit(void* Obj)
{
	((URTSCollisionAvoidanceComponent*)Obj)->bRespectFormationPositions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bRespectFormationPositions = { "bRespectFormationPositions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSCollisionAvoidanceComponent), &Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bRespectFormationPositions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRespectFormationPositions_MetaData), NewProp_bRespectFormationPositions_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_AvoidanceParams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_AvoidanceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bEnableCollisionAvoidance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bEnableDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::NewProp_bRespectFormationPositions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::ClassParams = {
	&URTSCollisionAvoidanceComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSCollisionAvoidanceComponent()
{
	if (!Z_Registration_Info_UClass_URTSCollisionAvoidanceComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSCollisionAvoidanceComponent.OuterSingleton, Z_Construct_UClass_URTSCollisionAvoidanceComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSCollisionAvoidanceComponent.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSCollisionAvoidanceComponent);
URTSCollisionAvoidanceComponent::~URTSCollisionAvoidanceComponent() {}
// ********** End Class URTSCollisionAvoidanceComponent ********************************************

// ********** Begin Class URTSCollisionAvoidanceManager Function CleanupInvalidUnits ***************
struct Z_Construct_UFunction_URTSCollisionAvoidanceManager_CleanupInvalidUnits_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance Manager" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_CleanupInvalidUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceManager, nullptr, "CleanupInvalidUnits", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_CleanupInvalidUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceManager_CleanupInvalidUnits_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceManager_CleanupInvalidUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceManager_CleanupInvalidUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceManager::execCleanupInvalidUnits)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupInvalidUnits();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceManager Function CleanupInvalidUnits *****************

// ********** Begin Class URTSCollisionAvoidanceManager Function GetNearbyUnits ********************
struct Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics
{
	struct RTSCollisionAvoidanceManager_eventGetNearbyUnits_Parms
	{
		ARTSUnit* Unit;
		float Radius;
		TArray<ARTSUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance Manager" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGetNearbyUnits_Parms, Unit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGetNearbyUnits_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGetNearbyUnits_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceManager, nullptr, "GetNearbyUnits", Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::RTSCollisionAvoidanceManager_eventGetNearbyUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::RTSCollisionAvoidanceManager_eventGetNearbyUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceManager::execGetNearbyUnits)
{
	P_GET_OBJECT(ARTSUnit,Z_Param_Unit);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSUnit*>*)Z_Param__Result=P_THIS->GetNearbyUnits(Z_Param_Unit,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceManager Function GetNearbyUnits **********************

// ********** Begin Class URTSCollisionAvoidanceManager Function GetRegisteredUnitCount ************
struct Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics
{
	struct RTSCollisionAvoidanceManager_eventGetRegisteredUnitCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance Manager" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGetRegisteredUnitCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceManager, nullptr, "GetRegisteredUnitCount", Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::RTSCollisionAvoidanceManager_eventGetRegisteredUnitCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::RTSCollisionAvoidanceManager_eventGetRegisteredUnitCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceManager::execGetRegisteredUnitCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetRegisteredUnitCount();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceManager Function GetRegisteredUnitCount **************

// ********** Begin Class URTSCollisionAvoidanceManager Function GetUnitsInCell ********************
struct Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics
{
	struct RTSCollisionAvoidanceManager_eventGetUnitsInCell_Parms
	{
		FVector Location;
		TArray<ARTSUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance Manager" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGetUnitsInCell_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGetUnitsInCell_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceManager, nullptr, "GetUnitsInCell", Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::RTSCollisionAvoidanceManager_eventGetUnitsInCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::RTSCollisionAvoidanceManager_eventGetUnitsInCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceManager::execGetUnitsInCell)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSUnit*>*)Z_Param__Result=P_THIS->GetUnitsInCell(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceManager Function GetUnitsInCell **********************

// ********** Begin Class URTSCollisionAvoidanceManager Function GetUnitsInRadius ******************
struct Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics
{
	struct RTSCollisionAvoidanceManager_eventGetUnitsInRadius_Parms
	{
		FVector Location;
		float Radius;
		TArray<ARTSUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial queries\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGetUnitsInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGetUnitsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGetUnitsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceManager, nullptr, "GetUnitsInRadius", Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::RTSCollisionAvoidanceManager_eventGetUnitsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::RTSCollisionAvoidanceManager_eventGetUnitsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceManager::execGetUnitsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSUnit*>*)Z_Param__Result=P_THIS->GetUnitsInRadius(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceManager Function GetUnitsInRadius ********************

// ********** Begin Class URTSCollisionAvoidanceManager Function GridCellToWorldLocation ***********
struct Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics
{
	struct RTSCollisionAvoidanceManager_eventGridCellToWorldLocation_Parms
	{
		FIntPoint GridCell;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance Manager" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridCell_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_GridCell;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::NewProp_GridCell = { "GridCell", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGridCellToWorldLocation_Parms, GridCell), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridCell_MetaData), NewProp_GridCell_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventGridCellToWorldLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::NewProp_GridCell,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceManager, nullptr, "GridCellToWorldLocation", Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::RTSCollisionAvoidanceManager_eventGridCellToWorldLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::RTSCollisionAvoidanceManager_eventGridCellToWorldLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceManager::execGridCellToWorldLocation)
{
	P_GET_STRUCT_REF(FIntPoint,Z_Param_Out_GridCell);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GridCellToWorldLocation(Z_Param_Out_GridCell);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceManager Function GridCellToWorldLocation *************

// ********** Begin Class URTSCollisionAvoidanceManager Function RegisterUnit **********************
struct Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics
{
	struct RTSCollisionAvoidanceManager_eventRegisterUnit_Parms
	{
		ARTSUnit* Unit;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unit registration\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unit registration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventRegisterUnit_Parms, Unit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::NewProp_Unit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceManager, nullptr, "RegisterUnit", Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::RTSCollisionAvoidanceManager_eventRegisterUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::RTSCollisionAvoidanceManager_eventRegisterUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceManager::execRegisterUnit)
{
	P_GET_OBJECT(ARTSUnit,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterUnit(Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceManager Function RegisterUnit ************************

// ********** Begin Class URTSCollisionAvoidanceManager Function UnregisterUnit ********************
struct Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics
{
	struct RTSCollisionAvoidanceManager_eventUnregisterUnit_Parms
	{
		ARTSUnit* Unit;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance Manager" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventUnregisterUnit_Parms, Unit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::NewProp_Unit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceManager, nullptr, "UnregisterUnit", Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::RTSCollisionAvoidanceManager_eventUnregisterUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::RTSCollisionAvoidanceManager_eventUnregisterUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceManager::execUnregisterUnit)
{
	P_GET_OBJECT(ARTSUnit,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterUnit(Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceManager Function UnregisterUnit **********************

// ********** Begin Class URTSCollisionAvoidanceManager Function UpdateSpatialGrid *****************
struct Z_Construct_UFunction_URTSCollisionAvoidanceManager_UpdateSpatialGrid_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Optimization functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_UpdateSpatialGrid_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceManager, nullptr, "UpdateSpatialGrid", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_UpdateSpatialGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceManager_UpdateSpatialGrid_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceManager_UpdateSpatialGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceManager_UpdateSpatialGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceManager::execUpdateSpatialGrid)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSpatialGrid();
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceManager Function UpdateSpatialGrid *******************

// ********** Begin Class URTSCollisionAvoidanceManager Function WorldLocationToGridCell ***********
struct Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics
{
	struct RTSCollisionAvoidanceManager_eventWorldLocationToGridCell_Parms
	{
		FVector WorldLocation;
		FIntPoint ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Collision Avoidance Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventWorldLocationToGridCell_Parms, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCollisionAvoidanceManager_eventWorldLocationToGridCell_Parms, ReturnValue), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCollisionAvoidanceManager, nullptr, "WorldLocationToGridCell", Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::RTSCollisionAvoidanceManager_eventWorldLocationToGridCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::RTSCollisionAvoidanceManager_eventWorldLocationToGridCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCollisionAvoidanceManager::execWorldLocationToGridCell)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_WorldLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FIntPoint*)Z_Param__Result=P_THIS->WorldLocationToGridCell(Z_Param_Out_WorldLocation);
	P_NATIVE_END;
}
// ********** End Class URTSCollisionAvoidanceManager Function WorldLocationToGridCell *************

// ********** Begin Class URTSCollisionAvoidanceManager ********************************************
void URTSCollisionAvoidanceManager::StaticRegisterNativesURTSCollisionAvoidanceManager()
{
	UClass* Class = URTSCollisionAvoidanceManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CleanupInvalidUnits", &URTSCollisionAvoidanceManager::execCleanupInvalidUnits },
		{ "GetNearbyUnits", &URTSCollisionAvoidanceManager::execGetNearbyUnits },
		{ "GetRegisteredUnitCount", &URTSCollisionAvoidanceManager::execGetRegisteredUnitCount },
		{ "GetUnitsInCell", &URTSCollisionAvoidanceManager::execGetUnitsInCell },
		{ "GetUnitsInRadius", &URTSCollisionAvoidanceManager::execGetUnitsInRadius },
		{ "GridCellToWorldLocation", &URTSCollisionAvoidanceManager::execGridCellToWorldLocation },
		{ "RegisterUnit", &URTSCollisionAvoidanceManager::execRegisterUnit },
		{ "UnregisterUnit", &URTSCollisionAvoidanceManager::execUnregisterUnit },
		{ "UpdateSpatialGrid", &URTSCollisionAvoidanceManager::execUpdateSpatialGrid },
		{ "WorldLocationToGridCell", &URTSCollisionAvoidanceManager::execWorldLocationToGridCell },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSCollisionAvoidanceManager;
UClass* URTSCollisionAvoidanceManager::GetPrivateStaticClass()
{
	using TClass = URTSCollisionAvoidanceManager;
	if (!Z_Registration_Info_UClass_URTSCollisionAvoidanceManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSCollisionAvoidanceManager"),
			Z_Registration_Info_UClass_URTSCollisionAvoidanceManager.InnerSingleton,
			StaticRegisterNativesURTSCollisionAvoidanceManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSCollisionAvoidanceManager.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSCollisionAvoidanceManager_NoRegister()
{
	return URTSCollisionAvoidanceManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World subsystem for managing global collision avoidance\n * Optimizes collision avoidance calculations across all units\n */" },
#endif
		{ "IncludePath", "RTSCollisionAvoidanceSystem.h" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World subsystem for managing global collision avoidance\nOptimizes collision avoidance calculations across all units" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredUnits_MetaData[] = {
		{ "Category", "Collision Avoidance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Registered units for collision avoidance\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registered units for collision avoidance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpatialGrid_MetaData[] = {
		{ "Category", "Collision Avoidance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spatial partitioning for optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spatial partitioning for optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridCellSize_MetaData[] = {
		{ "Category", "Manager Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalUpdateInterval_MetaData[] = {
		{ "Category", "Manager Settings" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseOptimizedCalculations_MetaData[] = {
		{ "Category", "Manager Settings" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Manager Settings" },
		{ "ModuleRelativePath", "Public/RTSCollisionAvoidanceSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_RegisteredUnits_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RegisteredUnits;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpatialGrid_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpatialGrid_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SpatialGrid;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GridCellSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalUpdateInterval;
	static void NewProp_bUseOptimizedCalculations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseOptimizedCalculations;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceManager_CleanupInvalidUnits, "CleanupInvalidUnits" }, // 2199296610
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetNearbyUnits, "GetNearbyUnits" }, // 1617792027
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetRegisteredUnitCount, "GetRegisteredUnitCount" }, // 1464972138
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInCell, "GetUnitsInCell" }, // 4199044471
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceManager_GetUnitsInRadius, "GetUnitsInRadius" }, // 2813059961
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceManager_GridCellToWorldLocation, "GridCellToWorldLocation" }, // 925708076
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceManager_RegisterUnit, "RegisterUnit" }, // 4011227875
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceManager_UnregisterUnit, "UnregisterUnit" }, // 3565138191
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceManager_UpdateSpatialGrid, "UpdateSpatialGrid" }, // 1197128624
		{ &Z_Construct_UFunction_URTSCollisionAvoidanceManager_WorldLocationToGridCell, "WorldLocationToGridCell" }, // 1238018887
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSCollisionAvoidanceManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_RegisteredUnits_Inner = { "RegisteredUnits", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_RegisteredUnits = { "RegisteredUnits", nullptr, (EPropertyFlags)0x0024080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceManager, RegisteredUnits), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredUnits_MetaData), NewProp_RegisteredUnits_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_SpatialGrid_ValueProp = { "SpatialGrid", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FRTSSpatialGridCell, METADATA_PARAMS(0, nullptr) }; // 2928180273
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_SpatialGrid_Key_KeyProp = { "SpatialGrid_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_SpatialGrid = { "SpatialGrid", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceManager, SpatialGrid), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpatialGrid_MetaData), NewProp_SpatialGrid_MetaData) }; // 2928180273
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_GridCellSize = { "GridCellSize", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceManager, GridCellSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridCellSize_MetaData), NewProp_GridCellSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_GlobalUpdateInterval = { "GlobalUpdateInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCollisionAvoidanceManager, GlobalUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalUpdateInterval_MetaData), NewProp_GlobalUpdateInterval_MetaData) };
void Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_bUseOptimizedCalculations_SetBit(void* Obj)
{
	((URTSCollisionAvoidanceManager*)Obj)->bUseOptimizedCalculations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_bUseOptimizedCalculations = { "bUseOptimizedCalculations", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSCollisionAvoidanceManager), &Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_bUseOptimizedCalculations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseOptimizedCalculations_MetaData), NewProp_bUseOptimizedCalculations_MetaData) };
void Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((URTSCollisionAvoidanceManager*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSCollisionAvoidanceManager), &Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_RegisteredUnits_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_RegisteredUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_SpatialGrid_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_SpatialGrid_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_SpatialGrid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_GridCellSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_GlobalUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_bUseOptimizedCalculations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::NewProp_bEnableDebugLogging,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::ClassParams = {
	&URTSCollisionAvoidanceManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSCollisionAvoidanceManager()
{
	if (!Z_Registration_Info_UClass_URTSCollisionAvoidanceManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSCollisionAvoidanceManager.OuterSingleton, Z_Construct_UClass_URTSCollisionAvoidanceManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSCollisionAvoidanceManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSCollisionAvoidanceManager);
URTSCollisionAvoidanceManager::~URTSCollisionAvoidanceManager() {}
// ********** End Class URTSCollisionAvoidanceManager **********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h__Script_ArmorWars_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRTSCollisionAvoidanceParams::StaticStruct, Z_Construct_UScriptStruct_FRTSCollisionAvoidanceParams_Statics::NewStructOps, TEXT("RTSCollisionAvoidanceParams"), &Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSCollisionAvoidanceParams), 1586078789U) },
		{ FRTSSpatialGridCell::StaticStruct, Z_Construct_UScriptStruct_FRTSSpatialGridCell_Statics::NewStructOps, TEXT("RTSSpatialGridCell"), &Z_Registration_Info_UScriptStruct_FRTSSpatialGridCell, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSSpatialGridCell), 2928180273U) },
		{ FRTSCollisionAvoidanceData::StaticStruct, Z_Construct_UScriptStruct_FRTSCollisionAvoidanceData_Statics::NewStructOps, TEXT("RTSCollisionAvoidanceData"), &Z_Registration_Info_UScriptStruct_FRTSCollisionAvoidanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSCollisionAvoidanceData), 3543739760U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSCollisionAvoidanceComponent, URTSCollisionAvoidanceComponent::StaticClass, TEXT("URTSCollisionAvoidanceComponent"), &Z_Registration_Info_UClass_URTSCollisionAvoidanceComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSCollisionAvoidanceComponent), 2785217239U) },
		{ Z_Construct_UClass_URTSCollisionAvoidanceManager, URTSCollisionAvoidanceManager::StaticClass, TEXT("URTSCollisionAvoidanceManager"), &Z_Registration_Info_UClass_URTSCollisionAvoidanceManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSCollisionAvoidanceManager), 2315896160U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h__Script_ArmorWars_322440040(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h__Script_ArmorWars_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h__Script_ArmorWars_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCollisionAvoidanceSystem_h__Script_ArmorWars_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

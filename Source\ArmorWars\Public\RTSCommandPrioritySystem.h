#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Components/ActorComponent.h"
#include "RTSCommand.h"
#include "RTSCommandPrioritySystem.generated.h"

class ARTSUnit;
class URTSCommandComponent;
class URTSBehaviorTreeComponent;

// Command source types for priority determination
UENUM(BlueprintType)
enum class ERTSCommandSource : uint8
{
    Player          UMETA(DisplayName = "Player"),
    AIController    UMETA(DisplayName = "AI Controller"),
    Formation       UMETA(DisplayName = "Formation"),
    Autonomous      UMETA(DisplayName = "Autonomous"),
    Emergency       UMETA(DisplayName = "Emergency"),
    System          UMETA(DisplayName = "System")
};

// Command execution context
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSCommandContext
{
    GENERATED_BODY()

    // Command metadata
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command Context")
    ERTSCommandSource Source = ERTSCommandSource::Autonomous;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command Context")
    float IssuedTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command Context")
    float ExecutionDeadline = 0.0f; // 0 = no deadline

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command Context")
    bool bCanBeInterrupted = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command Context")
    bool bRequiresImmediate = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command Context")
    int32 SequenceNumber = 0; // For ordering commands of same priority

    // Issuer information
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command Context")
    TWeakObjectPtr<AActor> CommandIssuer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command Context")
    FString IssuerID;

    // Constructor
    FRTSCommandContext()
    {
        Source = ERTSCommandSource::Autonomous;
        IssuedTime = 0.0f;
        ExecutionDeadline = 0.0f;
        bCanBeInterrupted = true;
        bRequiresImmediate = false;
        SequenceNumber = 0;
    }
};

// Enhanced command with priority context
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSPriorityCommand
{
    GENERATED_BODY()

    // Base command
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority Command")
    FRTSCommand Command;

    // Priority context
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority Command")
    FRTSCommandContext Context;

    // Calculated priority score
    UPROPERTY(BlueprintReadOnly, Category = "Priority Command")
    float PriorityScore = 0.0f;

    // Constructor
    FRTSPriorityCommand()
    {
        PriorityScore = 0.0f;
    }

    FRTSPriorityCommand(const FRTSCommand& InCommand, const FRTSCommandContext& InContext)
    {
        Command = InCommand;
        Context = InContext;
        PriorityScore = 0.0f;
    }

    // Comparison operators for sorting
    bool operator<(const FRTSPriorityCommand& Other) const
    {
        if (PriorityScore != Other.PriorityScore)
        {
            return PriorityScore > Other.PriorityScore; // Higher priority first
        }
        
        // Same priority, use sequence number
        return Context.SequenceNumber < Other.Context.SequenceNumber;
    }

    bool operator>(const FRTSPriorityCommand& Other) const
    {
        return Other < *this;
    }
};

/**
 * Command priority system for managing command execution order
 * Handles command queuing, priority calculation, and interruption logic
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class ARMORWARS_API URTSCommandPrioritySystem : public UActorComponent
{
    GENERATED_BODY()

public:
    URTSCommandPrioritySystem();

protected:
    virtual void BeginPlay() override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Priority queue for commands
    UPROPERTY(BlueprintReadOnly, Category = "Command Priority")
    TArray<FRTSPriorityCommand> PriorityQueue;

    // Priority system settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority System")
    bool bEnableDebugLogging = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority System")
    float PriorityUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority System")
    int32 MaxQueueSize = 20;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority System")
    bool bAutoExecuteHighestPriority = true;

    // Priority weights for different factors
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority Weights")
    float SourcePriorityWeight = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority Weights")
    float CommandPriorityWeight = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority Weights")
    float UrgencyWeight = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority Weights")
    float DeadlineWeight = 4.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority Weights")
    float ImmediateWeight = 10.0f;

public:
    // Command management
    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    bool AddCommand(const FRTSCommand& Command, const FRTSCommandContext& Context);

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    bool AddPlayerCommand(const FRTSCommand& Command, AActor* Player);

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    bool AddAICommand(const FRTSCommand& Command, AActor* AIController);

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    bool AddEmergencyCommand(const FRTSCommand& Command);

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    bool AddFormationCommand(const FRTSCommand& Command, AActor* FormationLeader);

    // Queue management
    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    void ClearQueue();

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    bool RemoveCommand(int32 Index);

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    bool RemoveCommandsBySource(ERTSCommandSource Source);

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    bool RemoveCommandsByIssuer(AActor* Issuer);

    // Priority queries
    UFUNCTION(BlueprintPure, Category = "RTS Command Priority")
    FRTSPriorityCommand GetHighestPriorityCommand() const;

    UFUNCTION(BlueprintPure, Category = "RTS Command Priority")
    TArray<FRTSPriorityCommand> GetCommandsByPriority() const;

    UFUNCTION(BlueprintPure, Category = "RTS Command Priority")
    TArray<FRTSPriorityCommand> GetCommandsBySource(ERTSCommandSource Source) const;

    UFUNCTION(BlueprintPure, Category = "RTS Command Priority")
    bool HasCommands() const;

    UFUNCTION(BlueprintPure, Category = "RTS Command Priority")
    int32 GetQueueSize() const;

    UFUNCTION(BlueprintPure, Category = "RTS Command Priority")
    bool CanInterruptCurrentCommand(const FRTSCommand& NewCommand, const FRTSCommandContext& NewContext) const;

    // Execution control
    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    bool ExecuteHighestPriorityCommand();

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    bool ExecuteCommand(const FRTSPriorityCommand& PriorityCommand);

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    void UpdatePriorities();

    // Priority calculation
    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    float CalculateCommandPriority(const FRTSCommand& Command, const FRTSCommandContext& Context) const;

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    float GetSourcePriorityMultiplier(ERTSCommandSource Source) const;

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    float GetCommandTypePriorityMultiplier(ERTSCommandType CommandType) const;

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    float CalculateUrgencyFactor(const FRTSCommandContext& Context) const;

    UFUNCTION(BlueprintCallable, Category = "RTS Command Priority")
    float CalculateDeadlineFactor(const FRTSCommandContext& Context) const;

protected:
    // Internal state
    int32 NextSequenceNumber = 0;
    float LastPriorityUpdate = 0.0f;
    FRTSPriorityCommand CurrentExecutingCommand;
    bool bIsExecutingCommand = false;

    // Helper functions
    virtual void SortQueueByPriority();
    virtual void CleanupExpiredCommands();
    virtual bool IsCommandValid(const FRTSCommand& Command) const;
    virtual bool IsCommandExpired(const FRTSPriorityCommand& PriorityCommand) const;
    virtual void BroadcastCommandAdded(const FRTSPriorityCommand& PriorityCommand);
    virtual void BroadcastCommandExecuted(const FRTSPriorityCommand& PriorityCommand);
    virtual void BroadcastCommandRemoved(const FRTSPriorityCommand& PriorityCommand);

    // Component integration
    virtual URTSCommandComponent* GetOwnerCommandComponent() const;
    virtual URTSBehaviorTreeComponent* GetOwnerBehaviorTreeComponent() const;
    virtual ARTSUnit* GetOwnerUnit() const;

public:
    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCommandAdded, const FRTSPriorityCommand&, PriorityCommand);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCommandExecuted, const FRTSPriorityCommand&, PriorityCommand);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCommandRemoved, const FRTSPriorityCommand&, PriorityCommand);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnQueueChanged);

    UPROPERTY(BlueprintAssignable, Category = "RTS Command Priority")
    FOnCommandAdded OnCommandAdded;

    UPROPERTY(BlueprintAssignable, Category = "RTS Command Priority")
    FOnCommandExecuted OnCommandExecuted;

    UPROPERTY(BlueprintAssignable, Category = "RTS Command Priority")
    FOnCommandRemoved OnCommandRemoved;

    UPROPERTY(BlueprintAssignable, Category = "RTS Command Priority")
    FOnQueueChanged OnQueueChanged;
};

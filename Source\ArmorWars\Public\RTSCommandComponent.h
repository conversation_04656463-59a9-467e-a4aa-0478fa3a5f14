#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "RTSCommand.h"
#include "RTSCommandComponent.generated.h"

class ARTSUnit;
class ARTSAIController;

// Delegate for command events
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCommandReceived, const FRTSCommand&, Command, bool, bInterrupted);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCommandCompleted, const FRTSCommand&, Command, ERTSCommandStatus, Status);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCommandQueueChanged, int32, QueueSize);

/**
 * Component that handles command processing for RTS units
 * Manages command queues, priorities, and execution
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class ARMORWARS_API URTSCommandComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    URTSCommandComponent();

protected:
    virtual void BeginPlay() override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Command queue management
    UPROPERTY(BlueprintReadOnly, Category = "Commands")
    FRTSCommandQueue CommandQueue;

    // Component settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command System")
    bool bEnableDebugLogging = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command System")
    float CommandUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command System")
    bool bAutoExecuteCommands = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command System")
    ERTSCommandPriority MinimumInterruptPriority = ERTSCommandPriority::High;

public:
    // Command issuing functions
    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool IssueCommand(const FRTSCommand& Command, bool bClearQueue = false);

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool IssueMoveCommand(const FVector& TargetLocation, ERTSCommandPriority Priority = ERTSCommandPriority::Normal, bool bClearQueue = true);

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool IssueAttackCommand(ARTSBaseActor* Target, ERTSCommandPriority Priority = ERTSCommandPriority::Normal, bool bClearQueue = true);

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool IssueAttackMoveCommand(const FVector& TargetLocation, ERTSCommandPriority Priority = ERTSCommandPriority::Normal, bool bClearQueue = true);

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool IssueStopCommand(ERTSCommandPriority Priority = ERTSCommandPriority::High);

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool IssueHoldCommand(ERTSCommandPriority Priority = ERTSCommandPriority::Normal);

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool IssuePatrolCommand(const TArray<FVector>& PatrolPoints, ERTSCommandPriority Priority = ERTSCommandPriority::Normal, bool bClearQueue = true);

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool IssueFollowCommand(ARTSBaseActor* Target, ERTSCommandPriority Priority = ERTSCommandPriority::Normal, bool bClearQueue = true);

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool IssueFormationCommand(ERTSFormationCommandType FormationType, const FVector& FormationCenter, const FVector& FormationOffset, ARTSUnit* FormationLeader = nullptr);

    // Command queue management
    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    void ClearCommandQueue();

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool CancelCurrentCommand();

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    bool RemoveCommand(int32 CommandIndex);

    UFUNCTION(BlueprintPure, Category = "RTS Commands")
    FRTSCommand GetCurrentCommand() const;

    UFUNCTION(BlueprintPure, Category = "RTS Commands")
    bool HasCommands() const;

    UFUNCTION(BlueprintPure, Category = "RTS Commands")
    int32 GetCommandQueueSize() const;

    UFUNCTION(BlueprintPure, Category = "RTS Commands")
    TArray<FRTSCommand> GetAllCommands() const;

    // Command execution
    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    void ExecuteCurrentCommand();

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    void CompleteCurrentCommand();

    UFUNCTION(BlueprintCallable, Category = "RTS Commands")
    void FailCurrentCommand();

    // Command validation
    UFUNCTION(BlueprintPure, Category = "RTS Commands")
    bool CanExecuteCommand(const FRTSCommand& Command) const;

    UFUNCTION(BlueprintPure, Category = "RTS Commands")
    bool CanInterruptCurrentCommand(ERTSCommandPriority NewCommandPriority) const;

    // Formation support
    UFUNCTION(BlueprintCallable, Category = "RTS Commands|Formation")
    void SetFormationData(const FVector& Offset, ARTSUnit* Leader, int32 Index);

    UFUNCTION(BlueprintPure, Category = "RTS Commands|Formation")
    bool IsInFormation() const;

    UFUNCTION(BlueprintPure, Category = "RTS Commands|Formation")
    FVector GetFormationOffset() const;

    UFUNCTION(BlueprintPure, Category = "RTS Commands|Formation")
    ARTSUnit* GetFormationLeader() const;

protected:
    // Internal command processing
    virtual void UpdateCommandExecution(float DeltaTime);
    virtual void ProcessCurrentCommand(float DeltaTime);
    virtual bool StartCommandExecution(FRTSCommand& Command);
    virtual void HandleCommandTimeout(FRTSCommand& Command);

    // Command execution handlers
    virtual void ExecuteMoveCommand(const FRTSCommand& Command);
    virtual void ExecuteAttackCommand(const FRTSCommand& Command);
    virtual void ExecuteAttackMoveCommand(const FRTSCommand& Command);
    virtual void ExecuteStopCommand(const FRTSCommand& Command);
    virtual void ExecuteHoldCommand(const FRTSCommand& Command);
    virtual void ExecutePatrolCommand(const FRTSCommand& Command);
    virtual void ExecuteFollowCommand(const FRTSCommand& Command);
    virtual void ExecuteFormationCommand(const FRTSCommand& Command);

    // Helper functions
    virtual ARTSUnit* GetOwnerUnit() const;
    virtual ARTSAIController* GetAIController() const;
    virtual bool IsCommandValid(const FRTSCommand& Command) const;
    virtual void BroadcastCommandReceived(const FRTSCommand& Command, bool bInterrupted);
    virtual void BroadcastCommandCompleted(const FRTSCommand& Command, ERTSCommandStatus Status);

protected:
    // Internal state
    float LastCommandUpdateTime = 0.0f;
    bool bIsExecutingCommand = false;

    // Formation data
    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    FVector CurrentFormationOffset = FVector::ZeroVector;

    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    TWeakObjectPtr<ARTSUnit> CurrentFormationLeader;

    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    int32 CurrentFormationIndex = -1;

public:
    // Events
    UPROPERTY(BlueprintAssignable, Category = "RTS Commands")
    FOnCommandReceived OnCommandReceived;

    UPROPERTY(BlueprintAssignable, Category = "RTS Commands")
    FOnCommandCompleted OnCommandCompleted;

    UPROPERTY(BlueprintAssignable, Category = "RTS Commands")
    FOnCommandQueueChanged OnCommandQueueChanged;
};

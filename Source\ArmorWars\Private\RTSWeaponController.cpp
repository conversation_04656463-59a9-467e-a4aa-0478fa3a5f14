#include "RTSWeaponController.h"
#include "RTSWeaponComponent.h"
#include "RTSUnit.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"

URTSWeaponController::URTSWeaponController()
{
    PrimaryComponentTick.bCanEverTick = true;
}

void URTSWeaponController::BeginPlay()
{
    Super::BeginPlay();
    
    // Auto-discover weapon components on the owner
    if (AActor* Owner = GetOwner())
    {
        TArray<URTSWeaponComponent*> FoundWeapons;
        Owner->GetComponents<URTSWeaponComponent>(FoundWeapons);
        
        for (URTSWeaponComponent* Weapon : FoundWeapons)
        {
            AddWeapon(Weapon);
        }
    }
}

void URTSWeaponController::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    UpdateTargeting(DeltaTime);
    UpdateFiring(DeltaTime);
    UpdateWeaponCoordination(DeltaTime);
}

// Weapon Management Functions
void URTSWeaponController::AddWeapon(URTSWeaponComponent* WeaponComponent)
{
    if (WeaponComponent && !WeaponComponents.Contains(WeaponComponent))
    {
        WeaponComponents.Add(WeaponComponent);
        
        // Bind to weapon events
        WeaponComponent->OnWeaponFired.AddDynamic(this, &URTSWeaponController::OnWeaponFiredInternal);
    }
}

void URTSWeaponController::RemoveWeapon(URTSWeaponComponent* WeaponComponent)
{
    if (WeaponComponent)
    {
        // Unbind from weapon events
        WeaponComponent->OnWeaponFired.RemoveDynamic(this, &URTSWeaponController::OnWeaponFiredInternal);
        
        WeaponComponents.Remove(WeaponComponent);
    }
}

void URTSWeaponController::ClearAllWeapons()
{
    for (URTSWeaponComponent* Weapon : WeaponComponents)
    {
        if (Weapon)
        {
            Weapon->OnWeaponFired.RemoveDynamic(this, &URTSWeaponController::OnWeaponFiredInternal);
        }
    }
    WeaponComponents.Empty();
}

// Targeting Functions
void URTSWeaponController::SetTarget(ARTSBaseActor* Target)
{
    if (CurrentTarget.Get() != Target)
    {
        ARTSBaseActor* OldTarget = CurrentTarget.Get();
        CurrentTarget = Target;
        
        // Update all weapon targets
        for (URTSWeaponComponent* Weapon : GetActiveWeapons())
        {
            if (Weapon)
            {
                Weapon->SetTarget(Target);
            }
        }
        
        OnTargetChanged.Broadcast(this, Target);
        
        if (OldTarget)
        {
            OnTargetLost(OldTarget);
        }
        
        if (Target)
        {
            OnTargetAcquired(Target);
        }
    }
}

void URTSWeaponController::ClearTarget()
{
    SetTarget(nullptr);
}

bool URTSWeaponController::HasValidTarget() const
{
    return CurrentTarget.IsValid() && CurrentTarget->IsAlive() && IsValidTarget(CurrentTarget.Get());
}

ARTSBaseActor* URTSWeaponController::FindBestTarget(float SearchRange) const
{
    if (SearchRange <= 0.0f)
    {
        SearchRange = (AutoTargetRange > 0.0f) ? AutoTargetRange : GetMaxRange();
    }

    if (!GetOwner() || !GetWorld())
    {
        return nullptr;
    }

    ARTSBaseActor* BestTarget = nullptr;
    float BestPriority = -1.0f;
    FVector MyLocation = GetOwner()->GetActorLocation();

    // Search for RTSBaseActor targets (buildings)
    TArray<AActor*> FoundBaseActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ARTSBaseActor::StaticClass(), FoundBaseActors);

    for (AActor* Actor : FoundBaseActors)
    {
        if (ARTSBaseActor* PotentialTarget = Cast<ARTSBaseActor>(Actor))
        {
            if (IsValidTarget(PotentialTarget))
            {
                float Distance = FVector::Dist(MyLocation, PotentialTarget->GetActorLocation());
                if (Distance <= SearchRange)
                {
                    float Priority = CalculateTargetPriority(PotentialTarget);
                    if (Priority > BestPriority)
                    {
                        BestPriority = Priority;
                        BestTarget = PotentialTarget;
                    }
                }
            }
        }
    }

    return BestTarget;
}

// Firing Functions
void URTSWeaponController::StartFiring()
{
    if (!bIsFiring)
    {
        bIsFiring = true;
        OnFiringStateChanged.Broadcast(this, true);
        OnFiringStarted();
    }
}

void URTSWeaponController::StopFiring()
{
    if (bIsFiring)
    {
        bIsFiring = false;
        
        // Stop all weapons
        for (URTSWeaponComponent* Weapon : WeaponComponents)
        {
            if (Weapon)
            {
                Weapon->StopFiring();
            }
        }
        
        OnFiringStateChanged.Broadcast(this, false);
        OnFiringStopped();
    }
}

bool URTSWeaponController::FireAtTarget(ARTSBaseActor* Target)
{
    if (!Target)
    {
        return false;
    }
    
    SetTarget(Target);
    
    if (!HasValidTarget())
    {
        return false;
    }
    
    bool bAnyWeaponFired = false;
    TArray<URTSWeaponComponent*> SuitableWeapons = SelectWeaponsForTarget(Target);
    
    for (URTSWeaponComponent* Weapon : SuitableWeapons)
    {
        if (Weapon && Weapon->CanFire())
        {
            if (Weapon->FireAtTarget(Target))
            {
                bAnyWeaponFired = true;
            }
        }
    }
    
    return bAnyWeaponFired;
}

bool URTSWeaponController::FireAtLocation(const FVector& TargetLocation)
{
    bool bAnyWeaponFired = false;
    
    for (URTSWeaponComponent* Weapon : GetActiveWeapons())
    {
        if (Weapon && Weapon->CanFire())
        {
            if (Weapon->FireAtLocation(TargetLocation))
            {
                bAnyWeaponFired = true;
            }
        }
    }
    
    return bAnyWeaponFired;
}

bool URTSWeaponController::CanFire() const
{
    for (URTSWeaponComponent* Weapon : GetActiveWeapons())
    {
        if (Weapon && Weapon->CanFire())
        {
            return true;
        }
    }
    return false;
}

// Weapon Selection Functions
void URTSWeaponController::SetActiveWeaponGroup(int32 GroupIndex)
{
    ActiveWeaponGroup = GroupIndex;
}

void URTSWeaponController::SetAllWeaponsActive(bool bActive)
{
    for (URTSWeaponComponent* Weapon : WeaponComponents)
    {
        if (Weapon)
        {
            // Set weapon active state (this would need to be implemented in URTSWeaponComponent)
            // Weapon->SetActive(bActive);
        }
    }
}

TArray<URTSWeaponComponent*> URTSWeaponController::GetActiveWeapons() const
{
    if (ActiveWeaponGroup == -1)
    {
        return WeaponComponents; // All weapons
    }
    
    // TODO: Implement weapon grouping system
    return WeaponComponents;
}

// Combat Statistics
bool URTSWeaponController::HasWeapons() const
{
    return WeaponComponents.Num() > 0;
}

float URTSWeaponController::GetMaxRange() const
{
    float MaxRange = 0.0f;
    
    for (URTSWeaponComponent* Weapon : WeaponComponents)
    {
        if (Weapon)
        {
            MaxRange = FMath::Max(MaxRange, Weapon->WeaponStats.Range);
        }
    }
    
    return MaxRange;
}

float URTSWeaponController::GetTotalDamagePerSecond() const
{
    float TotalDPS = 0.0f;
    
    for (URTSWeaponComponent* Weapon : WeaponComponents)
    {
        if (Weapon)
        {
            TotalDPS += Weapon->WeaponStats.Damage * Weapon->WeaponStats.RateOfFire;
        }
    }
    
    return TotalDPS;
}

bool URTSWeaponController::CanTargetDomain(ERTSUnitDomain Domain) const
{
    for (URTSWeaponComponent* Weapon : WeaponComponents)
    {
        if (Weapon)
        {
            ERTSTargetingCapability Capabilities = static_cast<ERTSTargetingCapability>(Weapon->TargetingCapabilities);
            
            switch (Domain)
            {
                case ERTSUnitDomain::Land:
                    if (EnumHasAnyFlags(Capabilities, ERTSTargetingCapability::CanTargetLand))
                        return true;
                    break;
                case ERTSUnitDomain::Air:
                    if (EnumHasAnyFlags(Capabilities, ERTSTargetingCapability::CanTargetAir))
                        return true;
                    break;
                case ERTSUnitDomain::Sea:
                    if (EnumHasAnyFlags(Capabilities, ERTSTargetingCapability::CanTargetSea))
                        return true;
                    break;
                case ERTSUnitDomain::Subnautical:
                    if (EnumHasAnyFlags(Capabilities, ERTSTargetingCapability::CanTargetSubnautical))
                        return true;
                    break;
            }
        }
    }
    
    return false;
}

// Protected Functions
void URTSWeaponController::UpdateTargeting(float DeltaTime)
{
    if (!bAutoTarget)
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();

    // Only search for targets periodically
    if (CurrentTime - LastTargetSearchTime < TargetSearchInterval)
    {
        return;
    }

    LastTargetSearchTime = CurrentTime;

    // If we don't have a valid target, find one
    if (!HasValidTarget())
    {
        AutoAcquireTarget();
    }
}

void URTSWeaponController::UpdateFiring(float DeltaTime)
{
    if (!bIsFiring || !HasValidTarget())
    {
        return;
    }

    CoordinateWeaponFiring();
}

void URTSWeaponController::UpdateWeaponCoordination(float DeltaTime)
{
    // Update weapon coordination logic here
    // This could include things like:
    // - Rotating turrets to face targets
    // - Managing firing sequences
    // - Coordinating multiple barrels
}

void URTSWeaponController::AutoAcquireTarget()
{
    ARTSBaseActor* NewTarget = FindBestTarget();
    if (NewTarget)
    {
        SetTarget(NewTarget);
    }
}

bool URTSWeaponController::IsValidTarget(const ARTSBaseActor* Target) const
{
    if (!Target || !Target->IsAlive())
    {
        return false;
    }

    // Check if we're on the same team
    AActor* Owner = GetOwner();
    if (Owner)
    {
        // Check if owner is an RTSBaseActor (building)
        if (ARTSBaseActor* OwnerBaseActor = Cast<ARTSBaseActor>(Owner))
        {
            if (OwnerBaseActor->IsOnSameTeam(Target))
            {
                return false;
            }
        }
        // Check if owner is an RTSUnit (pawn)
        else if (ARTSUnit* OwnerUnit = Cast<ARTSUnit>(Owner))
        {
            if (OwnerUnit->IsOnSameTeam(Target))
            {
                return false;
            }
        }
    }

    // Check if any weapon can target this unit (ARTSUnit is a Pawn, not ARTSBaseActor)
    // For now, assume all RTSBaseActors can be targeted
    // In a real implementation, you'd check the target's domain or type

    // Buildings can be targeted if we can target land units
    if (Target->IsBuilding())
    {
        return CanTargetDomain(ERTSUnitDomain::Land);
    }

    return true;
}

float URTSWeaponController::CalculateTargetPriority(const ARTSBaseActor* Target) const
{
    if (!Target)
    {
        return 0.0f;
    }

    float Priority = 1.0f;

    // Prioritize by distance (closer targets have higher priority)
    if (GetOwner())
    {
        float Distance = FVector::Dist(GetOwner()->GetActorLocation(), Target->GetActorLocation());
        Priority += 1000.0f / FMath::Max(Distance, 1.0f);
    }

    // Prioritize by health (lower health targets have higher priority)
    float HealthPercentage = Target->GetHealthPercentage();
    Priority += (1.0f - HealthPercentage) * 100.0f;

    // Prioritize based on target type
    // For now, give buildings lower priority than units would have
    if (Target->IsBuilding())
    {
        Priority += 50.0f; // Buildings get moderate priority
    }

    return Priority;
}

void URTSWeaponController::CoordinateWeaponFiring()
{
    if (!HasValidTarget())
    {
        return;
    }

    TArray<URTSWeaponComponent*> SuitableWeapons = SelectWeaponsForTarget(CurrentTarget.Get());

    for (URTSWeaponComponent* Weapon : SuitableWeapons)
    {
        if (Weapon && Weapon->CanFire())
        {
            Weapon->StartFiring();
        }
    }
}

TArray<URTSWeaponComponent*> URTSWeaponController::SelectWeaponsForTarget(const ARTSBaseActor* Target) const
{
    TArray<URTSWeaponComponent*> SuitableWeapons;

    if (!Target)
    {
        return SuitableWeapons;
    }

    for (URTSWeaponComponent* Weapon : GetActiveWeapons())
    {
        if (Weapon && Weapon->CanTargetActor(Target) && Weapon->IsTargetInRange(Target))
        {
            SuitableWeapons.Add(Weapon);
        }
    }

    return SuitableWeapons;
}

// Event handler for weapon fired events
void URTSWeaponController::OnWeaponFiredInternal(URTSWeaponComponent* Weapon, const FVector& MuzzleLocation, const FVector& TargetLocation)
{
    OnWeaponFired.Broadcast(this, Weapon, CurrentTarget.Get());
}

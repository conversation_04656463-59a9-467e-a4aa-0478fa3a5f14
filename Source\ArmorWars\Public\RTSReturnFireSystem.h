#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "RTSReturnFireSystem.generated.h"

class ARTSUnit;
class ARTSBaseActor;
class URTSWeaponController;

// Return fire behavior types
UENUM(BlueprintType)
enum class ERTSReturnFireBehavior : uint8
{
    None                UMETA(DisplayName = "No Return Fire"),
    Opportunistic       UMETA(DisplayName = "Opportunistic"),
    Aggressive          UMETA(DisplayName = "Aggressive"),
    Defensive           UMETA(DisplayName = "Defensive Only"),
    AlwaysReturn        UMETA(DisplayName = "Always Return Fire")
};

// Return fire target priority
UENUM(BlueprintType)
enum class ERTSReturnFirePriority : uint8
{
    NearestEnemy        UMETA(DisplayName = "Nearest Enemy"),
    AttackingEnemy      UMETA(DisplayName = "Attacking Enemy"),
    WeakestEnemy        UMETA(DisplayName = "Weakest Enemy"),
    StrongestThreat     UMETA(DisplayName = "Strongest Threat"),
    LastAttacker        UMETA(DisplayName = "Last Attacker")
};

// Return fire configuration
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSReturnFireConfig
{
    GENERATED_BODY()

    // Behavior settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    ERTSReturnFireBehavior Behavior = ERTSReturnFireBehavior::Opportunistic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    ERTSReturnFirePriority TargetPriority = ERTSReturnFirePriority::AttackingEnemy;

    // Range and timing
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    float MaxReturnFireRange = 800.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    bool bUseWeaponRange = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    float ReturnFireDuration = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    float ReturnFireCooldown = 1.0f;

    // Movement interaction
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bAllowReturnFireWhileMoving = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bSlowDownWhileFiring = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float MovementSpeedMultiplier = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bStopToFireAccurately = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float AccuracyPenaltyWhileMoving = 0.3f;

    // Target selection
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    bool bPrioritizeAttackers = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    float AttackerMemoryDuration = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    bool bSwitchTargetsOpportunistically = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target Selection")
    float TargetSwitchCooldown = 2.0f;

    // Constructor
    FRTSReturnFireConfig()
    {
        Behavior = ERTSReturnFireBehavior::Opportunistic;
        TargetPriority = ERTSReturnFirePriority::AttackingEnemy;
        MaxReturnFireRange = 800.0f;
        bUseWeaponRange = true;
        ReturnFireDuration = 3.0f;
        ReturnFireCooldown = 1.0f;
        bAllowReturnFireWhileMoving = true;
        bSlowDownWhileFiring = true;
        MovementSpeedMultiplier = 0.7f;
        bStopToFireAccurately = false;
        AccuracyPenaltyWhileMoving = 0.3f;
        bPrioritizeAttackers = true;
        AttackerMemoryDuration = 5.0f;
        bSwitchTargetsOpportunistically = true;
        TargetSwitchCooldown = 2.0f;
    }
};

// Return fire state data
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSReturnFireState
{
    GENERATED_BODY()

    // Current state
    UPROPERTY(BlueprintReadOnly, Category = "Return Fire State")
    bool bIsReturningFire = false;

    UPROPERTY(BlueprintReadOnly, Category = "Return Fire State")
    TWeakObjectPtr<ARTSBaseActor> CurrentTarget;

    UPROPERTY(BlueprintReadOnly, Category = "Return Fire State")
    float ReturnFireStartTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Return Fire State")
    float LastReturnFireTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Return Fire State")
    float LastTargetSwitchTime = 0.0f;

    // Attacker tracking
    UPROPERTY(BlueprintReadOnly, Category = "Return Fire State")
    TArray<TSoftObjectPtr<ARTSBaseActor>> RecentAttackers;

    UPROPERTY(BlueprintReadOnly, Category = "Return Fire State")
    TMap<TSoftObjectPtr<ARTSBaseActor>, float> AttackerTimestamps;

    // Movement state
    UPROPERTY(BlueprintReadOnly, Category = "Return Fire State")
    bool bWasMovingWhenFiring = false;

    UPROPERTY(BlueprintReadOnly, Category = "Return Fire State")
    FVector OriginalMoveTarget = FVector::ZeroVector;

    // Constructor
    FRTSReturnFireState()
    {
        bIsReturningFire = false;
        ReturnFireStartTime = 0.0f;
        LastReturnFireTime = 0.0f;
        LastTargetSwitchTime = 0.0f;
        bWasMovingWhenFiring = false;
        OriginalMoveTarget = FVector::ZeroVector;
    }
};

/**
 * Component that handles return fire while moving behavior
 * Allows units to engage enemies while maintaining movement commands
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class ARMORWARS_API URTSReturnFireComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    URTSReturnFireComponent();

protected:
    virtual void BeginPlay() override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Return Fire")
    FRTSReturnFireConfig Config;

    // Current state
    UPROPERTY(BlueprintReadOnly, Category = "Return Fire")
    FRTSReturnFireState State;

    // Component settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    bool bEnableReturnFire = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    bool bEnableDebugLogging = false;

public:
    // Main return fire functions
    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    void StartReturnFire(ARTSBaseActor* Attacker = nullptr);

    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    void StopReturnFire();

    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    void ProcessReturnFire(float DeltaTime);

    // Target management
    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    ARTSBaseActor* SelectReturnFireTarget();

    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    bool ShouldSwitchTarget(ARTSBaseActor* NewTarget);

    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    void SetReturnFireTarget(ARTSBaseActor* Target);

    // Attacker tracking
    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    void RegisterAttacker(ARTSBaseActor* Attacker);

    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    void CleanupOldAttackers();

    UFUNCTION(BlueprintPure, Category = "RTS Return Fire")
    TArray<ARTSBaseActor*> GetRecentAttackers() const;

    UFUNCTION(BlueprintPure, Category = "RTS Return Fire")
    ARTSBaseActor* GetMostRecentAttacker() const;

    // State queries
    UFUNCTION(BlueprintPure, Category = "RTS Return Fire")
    bool IsReturningFire() const { return State.bIsReturningFire; }

    UFUNCTION(BlueprintPure, Category = "RTS Return Fire")
    ARTSBaseActor* GetCurrentTarget() const { return State.CurrentTarget.Get(); }

    UFUNCTION(BlueprintPure, Category = "RTS Return Fire")
    bool CanReturnFire() const;

    UFUNCTION(BlueprintPure, Category = "RTS Return Fire")
    bool ShouldReturnFire() const;

    // Movement integration
    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    float GetMovementSpeedMultiplier() const;

    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    bool ShouldStopToFire() const;

    UFUNCTION(BlueprintCallable, Category = "RTS Return Fire")
    void HandleMovementWhileFiring();

    // Utility functions
    UFUNCTION(BlueprintPure, Category = "RTS Return Fire")
    ARTSUnit* GetOwnerUnit() const;

    UFUNCTION(BlueprintPure, Category = "RTS Return Fire")
    URTSWeaponController* GetWeaponController() const;

    UFUNCTION(BlueprintPure, Category = "RTS Return Fire")
    float GetEffectiveReturnFireRange() const;

protected:
    // Internal processing
    virtual void UpdateReturnFireState(float DeltaTime);
    virtual void ProcessTargetSelection();
    virtual void ProcessWeaponFiring();
    virtual void ProcessMovementAdjustment();

    // Target selection helpers
    virtual ARTSBaseActor* FindNearestEnemy();
    virtual ARTSBaseActor* FindAttackingEnemy();
    virtual ARTSBaseActor* FindWeakestEnemy();
    virtual ARTSBaseActor* FindStrongestThreat();
    virtual float CalculateTargetPriority(ARTSBaseActor* Target);

    // Validation helpers
    virtual bool IsValidTarget(ARTSBaseActor* Target) const;
    virtual bool IsTargetInRange(ARTSBaseActor* Target) const;
    virtual bool CanEngageTarget(ARTSBaseActor* Target) const;

public:
    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnReturnFireStarted, ARTSBaseActor*, Target);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnReturnFireStopped);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTargetChanged, ARTSBaseActor*, OldTarget, ARTSBaseActor*, NewTarget);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAttackerRegistered, ARTSBaseActor*, Attacker);

    UPROPERTY(BlueprintAssignable, Category = "RTS Return Fire")
    FOnReturnFireStarted OnReturnFireStarted;

    UPROPERTY(BlueprintAssignable, Category = "RTS Return Fire")
    FOnReturnFireStopped OnReturnFireStopped;

    UPROPERTY(BlueprintAssignable, Category = "RTS Return Fire")
    FOnTargetChanged OnTargetChanged;

    UPROPERTY(BlueprintAssignable, Category = "RTS Return Fire")
    FOnAttackerRegistered OnAttackerRegistered;
};

// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSCommandComponent.h"
#include "RTSCommand.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSCommandComponent() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandComponent();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandComponent_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandPriority();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandStatus();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSCommand();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSCommandQueue();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Delegate FOnCommandReceived ****************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnCommandReceived_Parms
	{
		FRTSCommand Command;
		bool bInterrupted;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate for command events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate for command events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static void NewProp_bInterrupted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInterrupted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnCommandReceived_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
void Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::NewProp_bInterrupted_SetBit(void* Obj)
{
	((_Script_ArmorWars_eventOnCommandReceived_Parms*)Obj)->bInterrupted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::NewProp_bInterrupted = { "bInterrupted", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_ArmorWars_eventOnCommandReceived_Parms), &Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::NewProp_bInterrupted_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::NewProp_bInterrupted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnCommandReceived__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::_Script_ArmorWars_eventOnCommandReceived_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::_Script_ArmorWars_eventOnCommandReceived_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnCommandReceived_DelegateWrapper(const FMulticastScriptDelegate& OnCommandReceived, FRTSCommand const& Command, bool bInterrupted)
{
	struct _Script_ArmorWars_eventOnCommandReceived_Parms
	{
		FRTSCommand Command;
		bool bInterrupted;
	};
	_Script_ArmorWars_eventOnCommandReceived_Parms Parms;
	Parms.Command=Command;
	Parms.bInterrupted=bInterrupted ? true : false;
	OnCommandReceived.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCommandReceived ******************************************************

// ********** Begin Delegate FOnCommandCompleted ***************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnCommandCompleted_Parms
	{
		FRTSCommand Command;
		ERTSCommandStatus Status;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Status_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Status;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnCommandCompleted_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::NewProp_Status_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::NewProp_Status = { "Status", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnCommandCompleted_Parms, Status), Z_Construct_UEnum_ArmorWars_ERTSCommandStatus, METADATA_PARAMS(0, nullptr) }; // 3736025705
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::NewProp_Status_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::NewProp_Status,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnCommandCompleted__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::_Script_ArmorWars_eventOnCommandCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::_Script_ArmorWars_eventOnCommandCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnCommandCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnCommandCompleted, FRTSCommand const& Command, ERTSCommandStatus Status)
{
	struct _Script_ArmorWars_eventOnCommandCompleted_Parms
	{
		FRTSCommand Command;
		ERTSCommandStatus Status;
	};
	_Script_ArmorWars_eventOnCommandCompleted_Parms Parms;
	Parms.Command=Command;
	Parms.Status=Status;
	OnCommandCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCommandCompleted *****************************************************

// ********** Begin Delegate FOnCommandQueueChanged ************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnCommandQueueChanged_Parms
	{
		int32 QueueSize;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_QueueSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::NewProp_QueueSize = { "QueueSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnCommandQueueChanged_Parms, QueueSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::NewProp_QueueSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnCommandQueueChanged__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnCommandQueueChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnCommandQueueChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnCommandQueueChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCommandQueueChanged, int32 QueueSize)
{
	struct _Script_ArmorWars_eventOnCommandQueueChanged_Parms
	{
		int32 QueueSize;
	};
	_Script_ArmorWars_eventOnCommandQueueChanged_Parms Parms;
	Parms.QueueSize=QueueSize;
	OnCommandQueueChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCommandQueueChanged **************************************************

// ********** Begin Class URTSCommandComponent Function CancelCurrentCommand ***********************
struct Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics
{
	struct RTSCommandComponent_eventCancelCurrentCommand_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventCancelCurrentCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventCancelCurrentCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "CancelCurrentCommand", Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::RTSCommandComponent_eventCancelCurrentCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::RTSCommandComponent_eventCancelCurrentCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execCancelCurrentCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelCurrentCommand();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function CancelCurrentCommand *************************

// ********** Begin Class URTSCommandComponent Function CanExecuteCommand **************************
struct Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics
{
	struct RTSCommandComponent_eventCanExecuteCommand_Parms
	{
		FRTSCommand Command;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command validation\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventCanExecuteCommand_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
void Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventCanExecuteCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventCanExecuteCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "CanExecuteCommand", Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::RTSCommandComponent_eventCanExecuteCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::RTSCommandComponent_eventCanExecuteCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execCanExecuteCommand)
{
	P_GET_STRUCT_REF(FRTSCommand,Z_Param_Out_Command);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanExecuteCommand(Z_Param_Out_Command);
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function CanExecuteCommand ****************************

// ********** Begin Class URTSCommandComponent Function CanInterruptCurrentCommand *****************
struct Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics
{
	struct RTSCommandComponent_eventCanInterruptCurrentCommand_Parms
	{
		ERTSCommandPriority NewCommandPriority;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewCommandPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewCommandPriority;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::NewProp_NewCommandPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::NewProp_NewCommandPriority = { "NewCommandPriority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventCanInterruptCurrentCommand_Parms, NewCommandPriority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventCanInterruptCurrentCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventCanInterruptCurrentCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::NewProp_NewCommandPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::NewProp_NewCommandPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "CanInterruptCurrentCommand", Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::RTSCommandComponent_eventCanInterruptCurrentCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::RTSCommandComponent_eventCanInterruptCurrentCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execCanInterruptCurrentCommand)
{
	P_GET_ENUM(ERTSCommandPriority,Z_Param_NewCommandPriority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanInterruptCurrentCommand(ERTSCommandPriority(Z_Param_NewCommandPriority));
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function CanInterruptCurrentCommand *******************

// ********** Begin Class URTSCommandComponent Function ClearCommandQueue **************************
struct Z_Construct_UFunction_URTSCommandComponent_ClearCommandQueue_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command queue management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command queue management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_ClearCommandQueue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "ClearCommandQueue", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_ClearCommandQueue_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_ClearCommandQueue_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSCommandComponent_ClearCommandQueue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_ClearCommandQueue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execClearCommandQueue)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearCommandQueue();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function ClearCommandQueue ****************************

// ********** Begin Class URTSCommandComponent Function CompleteCurrentCommand *********************
struct Z_Construct_UFunction_URTSCommandComponent_CompleteCurrentCommand_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_CompleteCurrentCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "CompleteCurrentCommand", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_CompleteCurrentCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_CompleteCurrentCommand_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSCommandComponent_CompleteCurrentCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_CompleteCurrentCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execCompleteCurrentCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompleteCurrentCommand();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function CompleteCurrentCommand ***********************

// ********** Begin Class URTSCommandComponent Function ExecuteCurrentCommand **********************
struct Z_Construct_UFunction_URTSCommandComponent_ExecuteCurrentCommand_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command execution\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command execution" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_ExecuteCurrentCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "ExecuteCurrentCommand", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_ExecuteCurrentCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_ExecuteCurrentCommand_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSCommandComponent_ExecuteCurrentCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_ExecuteCurrentCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execExecuteCurrentCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExecuteCurrentCommand();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function ExecuteCurrentCommand ************************

// ********** Begin Class URTSCommandComponent Function FailCurrentCommand *************************
struct Z_Construct_UFunction_URTSCommandComponent_FailCurrentCommand_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_FailCurrentCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "FailCurrentCommand", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_FailCurrentCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_FailCurrentCommand_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSCommandComponent_FailCurrentCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_FailCurrentCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execFailCurrentCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->FailCurrentCommand();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function FailCurrentCommand ***************************

// ********** Begin Class URTSCommandComponent Function GetAllCommands *****************************
struct Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics
{
	struct RTSCommandComponent_eventGetAllCommands_Parms
	{
		TArray<FRTSCommand> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(0, nullptr) }; // 1544416680
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventGetAllCommands_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1544416680
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "GetAllCommands", Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::RTSCommandComponent_eventGetAllCommands_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::RTSCommandComponent_eventGetAllCommands_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_GetAllCommands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_GetAllCommands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execGetAllCommands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FRTSCommand>*)Z_Param__Result=P_THIS->GetAllCommands();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function GetAllCommands *******************************

// ********** Begin Class URTSCommandComponent Function GetCommandQueueSize ************************
struct Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics
{
	struct RTSCommandComponent_eventGetCommandQueueSize_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventGetCommandQueueSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "GetCommandQueueSize", Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::RTSCommandComponent_eventGetCommandQueueSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::RTSCommandComponent_eventGetCommandQueueSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execGetCommandQueueSize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetCommandQueueSize();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function GetCommandQueueSize **************************

// ********** Begin Class URTSCommandComponent Function GetCurrentCommand **************************
struct Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics
{
	struct RTSCommandComponent_eventGetCurrentCommand_Parms
	{
		FRTSCommand ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventGetCurrentCommand_Parms, ReturnValue), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(0, nullptr) }; // 1544416680
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "GetCurrentCommand", Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::RTSCommandComponent_eventGetCurrentCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::RTSCommandComponent_eventGetCurrentCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execGetCurrentCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FRTSCommand*)Z_Param__Result=P_THIS->GetCurrentCommand();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function GetCurrentCommand ****************************

// ********** Begin Class URTSCommandComponent Function GetFormationLeader *************************
struct Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics
{
	struct RTSCommandComponent_eventGetFormationLeader_Parms
	{
		ARTSUnit* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands|Formation" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventGetFormationLeader_Parms, ReturnValue), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "GetFormationLeader", Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::RTSCommandComponent_eventGetFormationLeader_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::RTSCommandComponent_eventGetFormationLeader_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execGetFormationLeader)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSUnit**)Z_Param__Result=P_THIS->GetFormationLeader();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function GetFormationLeader ***************************

// ********** Begin Class URTSCommandComponent Function GetFormationOffset *************************
struct Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics
{
	struct RTSCommandComponent_eventGetFormationOffset_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands|Formation" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventGetFormationOffset_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "GetFormationOffset", Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::RTSCommandComponent_eventGetFormationOffset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::RTSCommandComponent_eventGetFormationOffset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execGetFormationOffset)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetFormationOffset();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function GetFormationOffset ***************************

// ********** Begin Class URTSCommandComponent Function HasCommands ********************************
struct Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics
{
	struct RTSCommandComponent_eventHasCommands_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventHasCommands_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventHasCommands_Parms), &Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "HasCommands", Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::RTSCommandComponent_eventHasCommands_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::RTSCommandComponent_eventHasCommands_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_HasCommands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_HasCommands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execHasCommands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasCommands();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function HasCommands **********************************

// ********** Begin Class URTSCommandComponent Function IsInFormation ******************************
struct Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics
{
	struct RTSCommandComponent_eventIsInFormation_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands|Formation" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIsInFormation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIsInFormation_Parms), &Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "IsInFormation", Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::RTSCommandComponent_eventIsInFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::RTSCommandComponent_eventIsInFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_IsInFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_IsInFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execIsInFormation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInFormation();
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function IsInFormation ********************************

// ********** Begin Class URTSCommandComponent Function IssueAttackCommand *************************
struct Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics
{
	struct RTSCommandComponent_eventIssueAttackCommand_Parms
	{
		ARTSBaseActor* Target;
		ERTSCommandPriority Priority;
		bool bClearQueue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "CPP_Default_bClearQueue", "true" },
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bClearQueue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClearQueue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueAttackCommand_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueAttackCommand_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_bClearQueue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueAttackCommand_Parms*)Obj)->bClearQueue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_bClearQueue = { "bClearQueue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueAttackCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_bClearQueue_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueAttackCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueAttackCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_bClearQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "IssueAttackCommand", Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::RTSCommandComponent_eventIssueAttackCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::RTSCommandComponent_eventIssueAttackCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execIssueAttackCommand)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_GET_UBOOL(Z_Param_bClearQueue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueAttackCommand(Z_Param_Target,ERTSCommandPriority(Z_Param_Priority),Z_Param_bClearQueue);
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function IssueAttackCommand ***************************

// ********** Begin Class URTSCommandComponent Function IssueAttackMoveCommand *********************
struct Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics
{
	struct RTSCommandComponent_eventIssueAttackMoveCommand_Parms
	{
		FVector TargetLocation;
		ERTSCommandPriority Priority;
		bool bClearQueue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "CPP_Default_bClearQueue", "true" },
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bClearQueue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClearQueue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueAttackMoveCommand_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueAttackMoveCommand_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_bClearQueue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueAttackMoveCommand_Parms*)Obj)->bClearQueue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_bClearQueue = { "bClearQueue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueAttackMoveCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_bClearQueue_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueAttackMoveCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueAttackMoveCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_bClearQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "IssueAttackMoveCommand", Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::RTSCommandComponent_eventIssueAttackMoveCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::RTSCommandComponent_eventIssueAttackMoveCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execIssueAttackMoveCommand)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TargetLocation);
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_GET_UBOOL(Z_Param_bClearQueue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueAttackMoveCommand(Z_Param_Out_TargetLocation,ERTSCommandPriority(Z_Param_Priority),Z_Param_bClearQueue);
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function IssueAttackMoveCommand ***********************

// ********** Begin Class URTSCommandComponent Function IssueCommand *******************************
struct Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics
{
	struct RTSCommandComponent_eventIssueCommand_Parms
	{
		FRTSCommand Command;
		bool bClearQueue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command issuing functions\n" },
#endif
		{ "CPP_Default_bClearQueue", "false" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command issuing functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static void NewProp_bClearQueue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClearQueue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueCommand_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
void Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::NewProp_bClearQueue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueCommand_Parms*)Obj)->bClearQueue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::NewProp_bClearQueue = { "bClearQueue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::NewProp_bClearQueue_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::NewProp_bClearQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "IssueCommand", Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::RTSCommandComponent_eventIssueCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::RTSCommandComponent_eventIssueCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_IssueCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_IssueCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execIssueCommand)
{
	P_GET_STRUCT_REF(FRTSCommand,Z_Param_Out_Command);
	P_GET_UBOOL(Z_Param_bClearQueue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueCommand(Z_Param_Out_Command,Z_Param_bClearQueue);
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function IssueCommand *********************************

// ********** Begin Class URTSCommandComponent Function IssueFollowCommand *************************
struct Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics
{
	struct RTSCommandComponent_eventIssueFollowCommand_Parms
	{
		ARTSBaseActor* Target;
		ERTSCommandPriority Priority;
		bool bClearQueue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "CPP_Default_bClearQueue", "true" },
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bClearQueue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClearQueue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueFollowCommand_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueFollowCommand_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_bClearQueue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueFollowCommand_Parms*)Obj)->bClearQueue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_bClearQueue = { "bClearQueue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueFollowCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_bClearQueue_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueFollowCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueFollowCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_bClearQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "IssueFollowCommand", Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::RTSCommandComponent_eventIssueFollowCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::RTSCommandComponent_eventIssueFollowCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execIssueFollowCommand)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_GET_UBOOL(Z_Param_bClearQueue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueFollowCommand(Z_Param_Target,ERTSCommandPriority(Z_Param_Priority),Z_Param_bClearQueue);
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function IssueFollowCommand ***************************

// ********** Begin Class URTSCommandComponent Function IssueFormationCommand **********************
struct Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics
{
	struct RTSCommandComponent_eventIssueFormationCommand_Parms
	{
		ERTSFormationCommandType FormationType;
		FVector FormationCenter;
		FVector FormationOffset;
		ARTSUnit* FormationLeader;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "CPP_Default_FormationLeader", "None" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationCenter_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationOffset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_FormationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FormationType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FormationCenter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FormationOffset;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FormationLeader;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_FormationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_FormationType = { "FormationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueFormationCommand_Parms, FormationType), Z_Construct_UEnum_ArmorWars_ERTSFormationCommandType, METADATA_PARAMS(0, nullptr) }; // 2474596395
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_FormationCenter = { "FormationCenter", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueFormationCommand_Parms, FormationCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationCenter_MetaData), NewProp_FormationCenter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_FormationOffset = { "FormationOffset", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueFormationCommand_Parms, FormationOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationOffset_MetaData), NewProp_FormationOffset_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_FormationLeader = { "FormationLeader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueFormationCommand_Parms, FormationLeader), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueFormationCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueFormationCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_FormationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_FormationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_FormationCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_FormationOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_FormationLeader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "IssueFormationCommand", Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::RTSCommandComponent_eventIssueFormationCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::RTSCommandComponent_eventIssueFormationCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execIssueFormationCommand)
{
	P_GET_ENUM(ERTSFormationCommandType,Z_Param_FormationType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_FormationCenter);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_FormationOffset);
	P_GET_OBJECT(ARTSUnit,Z_Param_FormationLeader);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueFormationCommand(ERTSFormationCommandType(Z_Param_FormationType),Z_Param_Out_FormationCenter,Z_Param_Out_FormationOffset,Z_Param_FormationLeader);
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function IssueFormationCommand ************************

// ********** Begin Class URTSCommandComponent Function IssueHoldCommand ***************************
struct Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics
{
	struct RTSCommandComponent_eventIssueHoldCommand_Parms
	{
		ERTSCommandPriority Priority;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueHoldCommand_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueHoldCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueHoldCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "IssueHoldCommand", Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::RTSCommandComponent_eventIssueHoldCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::RTSCommandComponent_eventIssueHoldCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execIssueHoldCommand)
{
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueHoldCommand(ERTSCommandPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function IssueHoldCommand *****************************

// ********** Begin Class URTSCommandComponent Function IssueMoveCommand ***************************
struct Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics
{
	struct RTSCommandComponent_eventIssueMoveCommand_Parms
	{
		FVector TargetLocation;
		ERTSCommandPriority Priority;
		bool bClearQueue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "CPP_Default_bClearQueue", "true" },
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bClearQueue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClearQueue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueMoveCommand_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueMoveCommand_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_bClearQueue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueMoveCommand_Parms*)Obj)->bClearQueue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_bClearQueue = { "bClearQueue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueMoveCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_bClearQueue_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueMoveCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueMoveCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_bClearQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "IssueMoveCommand", Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::RTSCommandComponent_eventIssueMoveCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::RTSCommandComponent_eventIssueMoveCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execIssueMoveCommand)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TargetLocation);
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_GET_UBOOL(Z_Param_bClearQueue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueMoveCommand(Z_Param_Out_TargetLocation,ERTSCommandPriority(Z_Param_Priority),Z_Param_bClearQueue);
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function IssueMoveCommand *****************************

// ********** Begin Class URTSCommandComponent Function IssuePatrolCommand *************************
struct Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics
{
	struct RTSCommandComponent_eventIssuePatrolCommand_Parms
	{
		TArray<FVector> PatrolPoints;
		ERTSCommandPriority Priority;
		bool bClearQueue;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "CPP_Default_bClearQueue", "true" },
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolPoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatrolPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PatrolPoints;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bClearQueue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClearQueue;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_PatrolPoints_Inner = { "PatrolPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_PatrolPoints = { "PatrolPoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssuePatrolCommand_Parms, PatrolPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolPoints_MetaData), NewProp_PatrolPoints_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssuePatrolCommand_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_bClearQueue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssuePatrolCommand_Parms*)Obj)->bClearQueue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_bClearQueue = { "bClearQueue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssuePatrolCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_bClearQueue_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssuePatrolCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssuePatrolCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_PatrolPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_PatrolPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_bClearQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "IssuePatrolCommand", Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::RTSCommandComponent_eventIssuePatrolCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::RTSCommandComponent_eventIssuePatrolCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execIssuePatrolCommand)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_PatrolPoints);
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_GET_UBOOL(Z_Param_bClearQueue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssuePatrolCommand(Z_Param_Out_PatrolPoints,ERTSCommandPriority(Z_Param_Priority),Z_Param_bClearQueue);
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function IssuePatrolCommand ***************************

// ********** Begin Class URTSCommandComponent Function IssueStopCommand ***************************
struct Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics
{
	struct RTSCommandComponent_eventIssueStopCommand_Parms
	{
		ERTSCommandPriority Priority;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "CPP_Default_Priority", "High" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventIssueStopCommand_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventIssueStopCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventIssueStopCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "IssueStopCommand", Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::RTSCommandComponent_eventIssueStopCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::RTSCommandComponent_eventIssueStopCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execIssueStopCommand)
{
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IssueStopCommand(ERTSCommandPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function IssueStopCommand *****************************

// ********** Begin Class URTSCommandComponent Function RemoveCommand ******************************
struct Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics
{
	struct RTSCommandComponent_eventRemoveCommand_Parms
	{
		int32 CommandIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CommandIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::NewProp_CommandIndex = { "CommandIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventRemoveCommand_Parms, CommandIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandComponent_eventRemoveCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandComponent_eventRemoveCommand_Parms), &Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::NewProp_CommandIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "RemoveCommand", Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::RTSCommandComponent_eventRemoveCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::RTSCommandComponent_eventRemoveCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_RemoveCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_RemoveCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execRemoveCommand)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CommandIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveCommand(Z_Param_CommandIndex);
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function RemoveCommand ********************************

// ********** Begin Class URTSCommandComponent Function SetFormationData ***************************
struct Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics
{
	struct RTSCommandComponent_eventSetFormationData_Parms
	{
		FVector Offset;
		ARTSUnit* Leader;
		int32 Index;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Commands|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation support\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation support" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Offset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Leader;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Index;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventSetFormationData_Parms, Offset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Offset_MetaData), NewProp_Offset_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::NewProp_Leader = { "Leader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventSetFormationData_Parms, Leader), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::NewProp_Index = { "Index", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandComponent_eventSetFormationData_Parms, Index), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::NewProp_Offset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::NewProp_Leader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::NewProp_Index,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandComponent, nullptr, "SetFormationData", Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::RTSCommandComponent_eventSetFormationData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::RTSCommandComponent_eventSetFormationData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandComponent_SetFormationData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandComponent_SetFormationData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandComponent::execSetFormationData)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Offset);
	P_GET_OBJECT(ARTSUnit,Z_Param_Leader);
	P_GET_PROPERTY(FIntProperty,Z_Param_Index);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFormationData(Z_Param_Out_Offset,Z_Param_Leader,Z_Param_Index);
	P_NATIVE_END;
}
// ********** End Class URTSCommandComponent Function SetFormationData *****************************

// ********** Begin Class URTSCommandComponent *****************************************************
void URTSCommandComponent::StaticRegisterNativesURTSCommandComponent()
{
	UClass* Class = URTSCommandComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CancelCurrentCommand", &URTSCommandComponent::execCancelCurrentCommand },
		{ "CanExecuteCommand", &URTSCommandComponent::execCanExecuteCommand },
		{ "CanInterruptCurrentCommand", &URTSCommandComponent::execCanInterruptCurrentCommand },
		{ "ClearCommandQueue", &URTSCommandComponent::execClearCommandQueue },
		{ "CompleteCurrentCommand", &URTSCommandComponent::execCompleteCurrentCommand },
		{ "ExecuteCurrentCommand", &URTSCommandComponent::execExecuteCurrentCommand },
		{ "FailCurrentCommand", &URTSCommandComponent::execFailCurrentCommand },
		{ "GetAllCommands", &URTSCommandComponent::execGetAllCommands },
		{ "GetCommandQueueSize", &URTSCommandComponent::execGetCommandQueueSize },
		{ "GetCurrentCommand", &URTSCommandComponent::execGetCurrentCommand },
		{ "GetFormationLeader", &URTSCommandComponent::execGetFormationLeader },
		{ "GetFormationOffset", &URTSCommandComponent::execGetFormationOffset },
		{ "HasCommands", &URTSCommandComponent::execHasCommands },
		{ "IsInFormation", &URTSCommandComponent::execIsInFormation },
		{ "IssueAttackCommand", &URTSCommandComponent::execIssueAttackCommand },
		{ "IssueAttackMoveCommand", &URTSCommandComponent::execIssueAttackMoveCommand },
		{ "IssueCommand", &URTSCommandComponent::execIssueCommand },
		{ "IssueFollowCommand", &URTSCommandComponent::execIssueFollowCommand },
		{ "IssueFormationCommand", &URTSCommandComponent::execIssueFormationCommand },
		{ "IssueHoldCommand", &URTSCommandComponent::execIssueHoldCommand },
		{ "IssueMoveCommand", &URTSCommandComponent::execIssueMoveCommand },
		{ "IssuePatrolCommand", &URTSCommandComponent::execIssuePatrolCommand },
		{ "IssueStopCommand", &URTSCommandComponent::execIssueStopCommand },
		{ "RemoveCommand", &URTSCommandComponent::execRemoveCommand },
		{ "SetFormationData", &URTSCommandComponent::execSetFormationData },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSCommandComponent;
UClass* URTSCommandComponent::GetPrivateStaticClass()
{
	using TClass = URTSCommandComponent;
	if (!Z_Registration_Info_UClass_URTSCommandComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSCommandComponent"),
			Z_Registration_Info_UClass_URTSCommandComponent.InnerSingleton,
			StaticRegisterNativesURTSCommandComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSCommandComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSCommandComponent_NoRegister()
{
	return URTSCommandComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSCommandComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Component that handles command processing for RTS units\n * Manages command queues, priorities, and execution\n */" },
#endif
		{ "IncludePath", "RTSCommandComponent.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Component that handles command processing for RTS units\nManages command queues, priorities, and execution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommandQueue_MetaData[] = {
		{ "Category", "Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command queue management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command queue management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Command System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Component settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Component settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommandUpdateInterval_MetaData[] = {
		{ "Category", "Command System" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoExecuteCommands_MetaData[] = {
		{ "Category", "Command System" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumInterruptPriority_MetaData[] = {
		{ "Category", "Command System" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFormationOffset_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation data\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFormationLeader_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFormationIndex_MetaData[] = {
		{ "Category", "Formation" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCommandReceived_MetaData[] = {
		{ "Category", "RTS Commands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCommandCompleted_MetaData[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCommandQueueChanged_MetaData[] = {
		{ "Category", "RTS Commands" },
		{ "ModuleRelativePath", "Public/RTSCommandComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CommandQueue;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CommandUpdateInterval;
	static void NewProp_bAutoExecuteCommands_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoExecuteCommands;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinimumInterruptPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinimumInterruptPriority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentFormationOffset;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_CurrentFormationLeader;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentFormationIndex;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCommandReceived;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCommandCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCommandQueueChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSCommandComponent_CancelCurrentCommand, "CancelCurrentCommand" }, // 4274293807
		{ &Z_Construct_UFunction_URTSCommandComponent_CanExecuteCommand, "CanExecuteCommand" }, // 1959209531
		{ &Z_Construct_UFunction_URTSCommandComponent_CanInterruptCurrentCommand, "CanInterruptCurrentCommand" }, // 2338514378
		{ &Z_Construct_UFunction_URTSCommandComponent_ClearCommandQueue, "ClearCommandQueue" }, // 84296063
		{ &Z_Construct_UFunction_URTSCommandComponent_CompleteCurrentCommand, "CompleteCurrentCommand" }, // 1927596174
		{ &Z_Construct_UFunction_URTSCommandComponent_ExecuteCurrentCommand, "ExecuteCurrentCommand" }, // 4029626094
		{ &Z_Construct_UFunction_URTSCommandComponent_FailCurrentCommand, "FailCurrentCommand" }, // 1667709220
		{ &Z_Construct_UFunction_URTSCommandComponent_GetAllCommands, "GetAllCommands" }, // 903128140
		{ &Z_Construct_UFunction_URTSCommandComponent_GetCommandQueueSize, "GetCommandQueueSize" }, // 4081349388
		{ &Z_Construct_UFunction_URTSCommandComponent_GetCurrentCommand, "GetCurrentCommand" }, // 2580690204
		{ &Z_Construct_UFunction_URTSCommandComponent_GetFormationLeader, "GetFormationLeader" }, // 1012699229
		{ &Z_Construct_UFunction_URTSCommandComponent_GetFormationOffset, "GetFormationOffset" }, // 2870688870
		{ &Z_Construct_UFunction_URTSCommandComponent_HasCommands, "HasCommands" }, // 1416930369
		{ &Z_Construct_UFunction_URTSCommandComponent_IsInFormation, "IsInFormation" }, // 2771790545
		{ &Z_Construct_UFunction_URTSCommandComponent_IssueAttackCommand, "IssueAttackCommand" }, // 714370278
		{ &Z_Construct_UFunction_URTSCommandComponent_IssueAttackMoveCommand, "IssueAttackMoveCommand" }, // 3166729263
		{ &Z_Construct_UFunction_URTSCommandComponent_IssueCommand, "IssueCommand" }, // 904502958
		{ &Z_Construct_UFunction_URTSCommandComponent_IssueFollowCommand, "IssueFollowCommand" }, // 794575672
		{ &Z_Construct_UFunction_URTSCommandComponent_IssueFormationCommand, "IssueFormationCommand" }, // 1039347320
		{ &Z_Construct_UFunction_URTSCommandComponent_IssueHoldCommand, "IssueHoldCommand" }, // 3940021913
		{ &Z_Construct_UFunction_URTSCommandComponent_IssueMoveCommand, "IssueMoveCommand" }, // 3652518276
		{ &Z_Construct_UFunction_URTSCommandComponent_IssuePatrolCommand, "IssuePatrolCommand" }, // 2515036537
		{ &Z_Construct_UFunction_URTSCommandComponent_IssueStopCommand, "IssueStopCommand" }, // 1931921781
		{ &Z_Construct_UFunction_URTSCommandComponent_RemoveCommand, "RemoveCommand" }, // 3407039958
		{ &Z_Construct_UFunction_URTSCommandComponent_SetFormationData, "SetFormationData" }, // 2723582885
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSCommandComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_CommandQueue = { "CommandQueue", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandComponent, CommandQueue), Z_Construct_UScriptStruct_FRTSCommandQueue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommandQueue_MetaData), NewProp_CommandQueue_MetaData) }; // 3307480973
void Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((URTSCommandComponent*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSCommandComponent), &Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_CommandUpdateInterval = { "CommandUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandComponent, CommandUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommandUpdateInterval_MetaData), NewProp_CommandUpdateInterval_MetaData) };
void Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_bAutoExecuteCommands_SetBit(void* Obj)
{
	((URTSCommandComponent*)Obj)->bAutoExecuteCommands = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_bAutoExecuteCommands = { "bAutoExecuteCommands", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSCommandComponent), &Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_bAutoExecuteCommands_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoExecuteCommands_MetaData), NewProp_bAutoExecuteCommands_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_MinimumInterruptPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_MinimumInterruptPriority = { "MinimumInterruptPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandComponent, MinimumInterruptPriority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumInterruptPriority_MetaData), NewProp_MinimumInterruptPriority_MetaData) }; // 136588220
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_CurrentFormationOffset = { "CurrentFormationOffset", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandComponent, CurrentFormationOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFormationOffset_MetaData), NewProp_CurrentFormationOffset_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_CurrentFormationLeader = { "CurrentFormationLeader", nullptr, (EPropertyFlags)0x0024080000000014, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandComponent, CurrentFormationLeader), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFormationLeader_MetaData), NewProp_CurrentFormationLeader_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_CurrentFormationIndex = { "CurrentFormationIndex", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandComponent, CurrentFormationIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFormationIndex_MetaData), NewProp_CurrentFormationIndex_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_OnCommandReceived = { "OnCommandReceived", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandComponent, OnCommandReceived), Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCommandReceived_MetaData), NewProp_OnCommandReceived_MetaData) }; // 1297105292
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_OnCommandCompleted = { "OnCommandCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandComponent, OnCommandCompleted), Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCommandCompleted_MetaData), NewProp_OnCommandCompleted_MetaData) }; // 860944600
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_OnCommandQueueChanged = { "OnCommandQueueChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandComponent, OnCommandQueueChanged), Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCommandQueueChanged_MetaData), NewProp_OnCommandQueueChanged_MetaData) }; // 536605311
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSCommandComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_CommandQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_CommandUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_bAutoExecuteCommands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_MinimumInterruptPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_MinimumInterruptPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_CurrentFormationOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_CurrentFormationLeader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_CurrentFormationIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_OnCommandReceived,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_OnCommandCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandComponent_Statics::NewProp_OnCommandQueueChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSCommandComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSCommandComponent_Statics::ClassParams = {
	&URTSCommandComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSCommandComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSCommandComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSCommandComponent()
{
	if (!Z_Registration_Info_UClass_URTSCommandComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSCommandComponent.OuterSingleton, Z_Construct_UClass_URTSCommandComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSCommandComponent.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSCommandComponent);
URTSCommandComponent::~URTSCommandComponent() {}
// ********** End Class URTSCommandComponent *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSCommandComponent, URTSCommandComponent::StaticClass, TEXT("URTSCommandComponent"), &Z_Registration_Info_UClass_URTSCommandComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSCommandComponent), 808087152U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h__Script_ArmorWars_3730666988(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

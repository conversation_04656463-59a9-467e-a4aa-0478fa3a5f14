// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "UnitDatabaseSubsystem.h"

#ifdef ARMORWARS_UnitDatabaseSubsystem_generated_h
#error "UnitDatabaseSubsystem.generated.h already included, missing '#pragma once' in UnitDatabaseSubsystem.h"
#endif
#define ARMORWARS_UnitDatabaseSubsystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UWorld;
struct FRTSResourceCost;
struct FRTSUnitDefinition;

// ********** Begin ScriptStruct FRTSUnitDefinition ************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_UnitDatabaseSubsystem_h_15_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSUnitDefinition_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FTableRowBase Super;


struct FRTSUnitDefinition;
// ********** End ScriptStruct FRTSUnitDefinition **************************************************

// ********** Begin Class UUnitDatabaseSubsystem ***************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_UnitDatabaseSubsystem_h_89_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execStartUnitProduction); \
	DECLARE_FUNCTION(execSpawnUnitWithEconomy); \
	DECLARE_FUNCTION(execCanAffordUnit); \
	DECLARE_FUNCTION(execGetUnitMaintenanceCost); \
	DECLARE_FUNCTION(execGetUnitResourceCost); \
	DECLARE_FUNCTION(execSpawnUnitSimple); \
	DECLARE_FUNCTION(execSpawnUnit); \
	DECLARE_FUNCTION(execIsUnitTemplateReady); \
	DECLARE_FUNCTION(execPrepareUnitTemplate); \
	DECLARE_FUNCTION(execGetUnitDefinitionsByTag); \
	DECLARE_FUNCTION(execGetAllUnitDefinitions); \
	DECLARE_FUNCTION(execGetUnitDefinition);


ARMORWARS_API UClass* Z_Construct_UClass_UUnitDatabaseSubsystem_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_UnitDatabaseSubsystem_h_89_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUnitDatabaseSubsystem(); \
	friend struct Z_Construct_UClass_UUnitDatabaseSubsystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_UUnitDatabaseSubsystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UUnitDatabaseSubsystem, UGameInstanceSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_UUnitDatabaseSubsystem_NoRegister) \
	DECLARE_SERIALIZER(UUnitDatabaseSubsystem)


#define FID_ArmorWars_Source_ArmorWars_Public_UnitDatabaseSubsystem_h_89_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UUnitDatabaseSubsystem(); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UUnitDatabaseSubsystem(UUnitDatabaseSubsystem&&) = delete; \
	UUnitDatabaseSubsystem(const UUnitDatabaseSubsystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUnitDatabaseSubsystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUnitDatabaseSubsystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UUnitDatabaseSubsystem) \
	NO_API virtual ~UUnitDatabaseSubsystem();


#define FID_ArmorWars_Source_ArmorWars_Public_UnitDatabaseSubsystem_h_86_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_UnitDatabaseSubsystem_h_89_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_UnitDatabaseSubsystem_h_89_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_UnitDatabaseSubsystem_h_89_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_UnitDatabaseSubsystem_h_89_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UUnitDatabaseSubsystem;

// ********** End Class UUnitDatabaseSubsystem *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_UnitDatabaseSubsystem_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

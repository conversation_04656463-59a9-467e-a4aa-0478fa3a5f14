// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSSelectionSystem.h"

#ifdef ARMORWARS_RTSSelectionSystem_generated_h
#error "RTSSelectionSystem.generated.h already included, missing '#pragma once' in RTSSelectionSystem.h"
#endif
#define ARMORWARS_RTSSelectionSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSBaseActor;
class ARTSPlayerController;
enum class ERTSSelectionFilter : uint8;
struct FRTSSelectionGroup;

// ********** Begin Delegate FOnRTSSelectionChanged ************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_12_DELEGATE \
ARMORWARS_API void FOnRTSSelectionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnRTSSelectionChanged, TArray<ARTSBaseActor*> const& NewSelection, TArray<ARTSBaseActor*> const& PreviousSelection);


// ********** End Delegate FOnRTSSelectionChanged **************************************************

// ********** Begin Class URTSSelectionSystem ******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_21_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execFindUnitsInRadius); \
	DECLARE_FUNCTION(execFindUnitsInSelectionBox); \
	DECLARE_FUNCTION(execGetPlayerController); \
	DECLARE_FUNCTION(execSetPlayerController); \
	DECLARE_FUNCTION(execOnSelectionChanged); \
	DECLARE_FUNCTION(execUpdateSelectionHighlights); \
	DECLARE_FUNCTION(execSetSelectionHighlightVisibility); \
	DECLARE_FUNCTION(execFilterSelection); \
	DECLARE_FUNCTION(execSelectAllMilitaryUnits); \
	DECLARE_FUNCTION(execSelectAllIdleUnits); \
	DECLARE_FUNCTION(execSelectAllUnitsOfType); \
	DECLARE_FUNCTION(execGetAllSelectionGroups); \
	DECLARE_FUNCTION(execClearSelectionGroup); \
	DECLARE_FUNCTION(execGetSelectionGroup); \
	DECLARE_FUNCTION(execLoadSelectionGroup); \
	DECLARE_FUNCTION(execSaveSelectionGroup); \
	DECLARE_FUNCTION(execIsUnitSelected); \
	DECLARE_FUNCTION(execGetSelectedUnitCount); \
	DECLARE_FUNCTION(execGetSelectedUnits); \
	DECLARE_FUNCTION(execClearSelection); \
	DECLARE_FUNCTION(execSelectUnitsInRadius); \
	DECLARE_FUNCTION(execSelectUnitsInBox); \
	DECLARE_FUNCTION(execSelectUnits); \
	DECLARE_FUNCTION(execDeselectUnit); \
	DECLARE_FUNCTION(execSelectUnit); \
	DECLARE_FUNCTION(execInitializeSelectionSystem);


#define FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_21_CALLBACK_WRAPPERS
ARMORWARS_API UClass* Z_Construct_UClass_URTSSelectionSystem_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_21_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSSelectionSystem(); \
	friend struct Z_Construct_UClass_URTSSelectionSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSSelectionSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSSelectionSystem, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSSelectionSystem_NoRegister) \
	DECLARE_SERIALIZER(URTSSelectionSystem) \
	virtual UObject* _getUObject() const override { return const_cast<URTSSelectionSystem*>(this); }


#define FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_21_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSSelectionSystem(URTSSelectionSystem&&) = delete; \
	URTSSelectionSystem(const URTSSelectionSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSSelectionSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSSelectionSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSSelectionSystem) \
	NO_API virtual ~URTSSelectionSystem();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_18_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_21_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_21_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_21_CALLBACK_WRAPPERS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_21_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h_21_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSSelectionSystem;

// ********** End Class URTSSelectionSystem ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

#include "RTSBaseActor.h"
#include "Engine/Engine.h"
#include "Engine/DamageEvents.h"

ARTSBaseActor::ARTSBaseActor()
{
    PrimaryActorTick.bCanEverTick = false;
    
    // Initialize health
    CurrentHealth = MaxHealth;
}

void ARTSBaseActor::BeginPlay()
{
    Super::BeginPlay();
    
    // Ensure current health matches max health at start
    CurrentHealth = MaxHealth;
}

// Gameplay Tag Functions
bool ARTSBaseActor::HasGameplayTag(const FGameplayTag& Tag) const
{
    return GameplayTags.HasTag(Tag);
}

bool ARTSBaseActor::HasAnyGameplayTags(const FGameplayTagContainer& TagContainer) const
{
    return GameplayTags.HasAny(TagContainer);
}

bool ARTSBaseActor::HasAllGameplayTags(const FGameplayTagContainer& TagContainer) const
{
    return GameplayTags.HasAll(TagContainer);
}

void ARTSBaseActor::AddGameplayTag(const FGameplayTag& Tag)
{
    GameplayTags.AddTag(Tag);
}

void ARTSBaseActor::RemoveGameplayTag(const FGameplayTag& Tag)
{
    GameplayTags.RemoveTag(Tag);
}

void ARTSBaseActor::AddGameplayTags(const FGameplayTagContainer& TagContainer)
{
    GameplayTags.AppendTags(TagContainer);
}

void ARTSBaseActor::RemoveGameplayTags(const FGameplayTagContainer& TagContainer)
{
    GameplayTags.RemoveTags(TagContainer);
}

// Health Functions
float ARTSBaseActor::TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser)
{
    if (DamageAmount <= 0.0f || IsDead())
    {
        return 0.0f;
    }

    float OldHealth = CurrentHealth;
    CurrentHealth = FMath::Clamp(CurrentHealth - DamageAmount, 0.0f, MaxHealth);

    // Broadcast events
    OnHealthChanged.Broadcast(CurrentHealth, MaxHealth);
    OnDamageReceived.Broadcast(DamageAmount, DamageCauser);
    OnTakeDamage(DamageAmount, DamageCauser);

    // Check for death
    if (CurrentHealth <= 0.0f && OldHealth > 0.0f)
    {
        HandleDeath();
    }

    return DamageAmount;
}

void ARTSBaseActor::TakeDamageSimple(float DamageAmount, AActor* DamageSource)
{
    FDamageEvent DamageEvent;
    TakeDamage(DamageAmount, DamageEvent, nullptr, DamageSource);
}

void ARTSBaseActor::Heal(float HealAmount)
{
    if (HealAmount <= 0.0f || IsDead())
    {
        return;
    }

    CurrentHealth = FMath::Clamp(CurrentHealth + HealAmount, 0.0f, MaxHealth);
    
    // Broadcast events
    OnHealthChanged.Broadcast(CurrentHealth, MaxHealth);
    OnHealed(HealAmount);
}

void ARTSBaseActor::SetHealth(float NewHealth)
{
    float OldHealth = CurrentHealth;
    CurrentHealth = FMath::Clamp(NewHealth, 0.0f, MaxHealth);
    
    // Broadcast health changed event
    OnHealthChanged.Broadcast(CurrentHealth, MaxHealth);
    
    // Check for death
    if (CurrentHealth <= 0.0f && OldHealth > 0.0f)
    {
        HandleDeath();
    }
}

float ARTSBaseActor::GetHealthPercentage() const
{
    return MaxHealth > 0.0f ? (CurrentHealth / MaxHealth) : 0.0f;
}

bool ARTSBaseActor::IsAlive() const
{
    return CurrentHealth > 0.0f;
}

bool ARTSBaseActor::IsDead() const
{
    return CurrentHealth <= 0.0f;
}

// Selection Functions
void ARTSBaseActor::SetSelected(bool bSelected)
{
    if (!bCanBeSelected)
    {
        return;
    }

    bool bWasSelected = bIsSelected;
    bIsSelected = bSelected;

    if (bWasSelected != bIsSelected)
    {
        OnSelectionChanged.Broadcast(this, bIsSelected);
        OnSelectionChangedEvent(bIsSelected);
    }
}

bool ARTSBaseActor::CanBeSelected() const
{
    return bCanBeSelected && IsAlive();
}

// Type Checking Functions
bool ARTSBaseActor::IsUnit() const
{
    return ActorType == ERTSActorType::Unit;
}

bool ARTSBaseActor::IsBuilding() const
{
    return ActorType == ERTSActorType::Building;
}

// Team Functions
bool ARTSBaseActor::IsOnSameTeam(const ARTSBaseActor* OtherActor) const
{
    return OtherActor && (TeamID == OtherActor->TeamID);
}

bool ARTSBaseActor::IsEnemy(const ARTSBaseActor* OtherActor) const
{
    return OtherActor && (TeamID != OtherActor->TeamID);
}

bool ARTSBaseActor::IsOnSameTeamAsActor(const AActor* OtherActor) const
{
    if (!OtherActor)
    {
        return false;
    }

    int32 OtherTeamID = GetActorTeamID(OtherActor);
    return TeamID == OtherTeamID;
}

bool ARTSBaseActor::IsEnemyActor(const AActor* OtherActor) const
{
    if (!OtherActor)
    {
        return false;
    }

    int32 OtherTeamID = GetActorTeamID(OtherActor);
    return TeamID != OtherTeamID;
}

int32 ARTSBaseActor::GetActorTeamID(const AActor* Actor)
{
    if (!Actor)
    {
        return -1; // Invalid team ID
    }

    // Check if it's an RTSBaseActor
    if (const ARTSBaseActor* RTSActor = Cast<ARTSBaseActor>(Actor))
    {
        return RTSActor->TeamID;
    }

    // Check if it's an RTSUnit (pawn)
    // We need to include RTSUnit.h for this, but to avoid circular dependencies,
    // we'll use a different approach - check for a specific interface or component
    // For now, we'll use reflection to get the TeamID property
    if (const UClass* ActorClass = Actor->GetClass())
    {
        if (const FProperty* TeamIDProperty = ActorClass->FindPropertyByName(TEXT("TeamID")))
        {
            if (const FIntProperty* IntProperty = CastField<FIntProperty>(TeamIDProperty))
            {
                return IntProperty->GetPropertyValue_InContainer(Actor);
            }
        }
    }

    return 0; // Default team ID
}

// Tech Level Functions
void ARTSBaseActor::SetTechLevel(ERTSTechLevel NewTechLevel)
{
    TechLevel = NewTechLevel;
}

bool ARTSBaseActor::CanProduceTechLevel(ERTSTechLevel RequiredTechLevel) const
{
    // Can produce units/buildings of the same tech level or lower
    return static_cast<uint8>(TechLevel) >= static_cast<uint8>(RequiredTechLevel);
}

// Protected Functions
void ARTSBaseActor::HandleDeath()
{
    // Deselect if selected
    if (bIsSelected)
    {
        SetSelected(false);
    }

    // Broadcast death event
    OnActorDeath.Broadcast(this);
    OnDeath();
    
    // Log death for debugging
    UE_LOG(LogTemp, Log, TEXT("%s has died"), *GetName());
}

// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSArmorTypes.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSArmorTypes() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSArmorFacing();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSArmorType();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSArmorType *************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSArmorType;
static UEnum* ERTSArmorType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSArmorType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSArmorType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSArmorType, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSArmorType"));
	}
	return Z_Registration_Info_UEnum_ERTSArmorType.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSArmorType>()
{
	return ERTSArmorType_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSArmorType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum for different armor types used in penetration calculations\n * Each type has different effectiveness against various damage types\n */" },
#endif
		{ "Composite.Comment", "/** Composite armor - layered armor with multiple materials */" },
		{ "Composite.DisplayName", "Composite" },
		{ "Composite.Name", "ERTSArmorType::Composite" },
		{ "Composite.ToolTip", "Composite armor - layered armor with multiple materials" },
		{ "Heavy.Comment", "/** Heavy armor - maximum protection, reduced mobility */" },
		{ "Heavy.DisplayName", "Heavy" },
		{ "Heavy.Name", "ERTSArmorType::Heavy" },
		{ "Heavy.ToolTip", "Heavy armor - maximum protection, reduced mobility" },
		{ "Light.Comment", "/** Light armor - minimal protection, high mobility */" },
		{ "Light.DisplayName", "Light" },
		{ "Light.Name", "ERTSArmorType::Light" },
		{ "Light.ToolTip", "Light armor - minimal protection, high mobility" },
		{ "Medium.Comment", "/** Medium armor - balanced protection and mobility */" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "ERTSArmorType::Medium" },
		{ "Medium.ToolTip", "Medium armor - balanced protection and mobility" },
		{ "ModuleRelativePath", "Public/RTSArmorTypes.h" },
		{ "Reactive.Comment", "/** Reactive armor - explosive reactive armor, effective against shaped charges */" },
		{ "Reactive.DisplayName", "Reactive" },
		{ "Reactive.Name", "ERTSArmorType::Reactive" },
		{ "Reactive.ToolTip", "Reactive armor - explosive reactive armor, effective against shaped charges" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for different armor types used in penetration calculations\nEach type has different effectiveness against various damage types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSArmorType::Light", (int64)ERTSArmorType::Light },
		{ "ERTSArmorType::Medium", (int64)ERTSArmorType::Medium },
		{ "ERTSArmorType::Heavy", (int64)ERTSArmorType::Heavy },
		{ "ERTSArmorType::Reactive", (int64)ERTSArmorType::Reactive },
		{ "ERTSArmorType::Composite", (int64)ERTSArmorType::Composite },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSArmorType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSArmorType",
	"ERTSArmorType",
	Z_Construct_UEnum_ArmorWars_ERTSArmorType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSArmorType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSArmorType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSArmorType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSArmorType()
{
	if (!Z_Registration_Info_UEnum_ERTSArmorType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSArmorType.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSArmorType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSArmorType.InnerSingleton;
}
// ********** End Enum ERTSArmorType ***************************************************************

// ********** Begin Enum ERTSArmorFacing ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSArmorFacing;
static UEnum* ERTSArmorFacing_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSArmorFacing.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSArmorFacing.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSArmorFacing, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSArmorFacing"));
	}
	return Z_Registration_Info_UEnum_ERTSArmorFacing.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSArmorFacing>()
{
	return ERTSArmorFacing_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSArmorFacing_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Bottom.Comment", "/** Bottom facing - protection against mines and ground attacks */" },
		{ "Bottom.DisplayName", "Bottom" },
		{ "Bottom.Name", "ERTSArmorFacing::Bottom" },
		{ "Bottom.ToolTip", "Bottom facing - protection against mines and ground attacks" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum for armor facing directions used in directional armor calculations\n * Different facings can have different armor thickness and types\n */" },
#endif
		{ "Front.Comment", "/** Front facing - typically the strongest armor */" },
		{ "Front.DisplayName", "Front" },
		{ "Front.Name", "ERTSArmorFacing::Front" },
		{ "Front.ToolTip", "Front facing - typically the strongest armor" },
		{ "ModuleRelativePath", "Public/RTSArmorTypes.h" },
		{ "Rear.Comment", "/** Rear facing - typically the weakest armor */" },
		{ "Rear.DisplayName", "Rear" },
		{ "Rear.Name", "ERTSArmorFacing::Rear" },
		{ "Rear.ToolTip", "Rear facing - typically the weakest armor" },
		{ "Side.Comment", "/** Side facing - moderate armor protection */" },
		{ "Side.DisplayName", "Side" },
		{ "Side.Name", "ERTSArmorFacing::Side" },
		{ "Side.ToolTip", "Side facing - moderate armor protection" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for armor facing directions used in directional armor calculations\nDifferent facings can have different armor thickness and types" },
#endif
		{ "Top.Comment", "/** Top facing - protection against air attacks */" },
		{ "Top.DisplayName", "Top" },
		{ "Top.Name", "ERTSArmorFacing::Top" },
		{ "Top.ToolTip", "Top facing - protection against air attacks" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSArmorFacing::Front", (int64)ERTSArmorFacing::Front },
		{ "ERTSArmorFacing::Side", (int64)ERTSArmorFacing::Side },
		{ "ERTSArmorFacing::Rear", (int64)ERTSArmorFacing::Rear },
		{ "ERTSArmorFacing::Top", (int64)ERTSArmorFacing::Top },
		{ "ERTSArmorFacing::Bottom", (int64)ERTSArmorFacing::Bottom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSArmorFacing_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSArmorFacing",
	"ERTSArmorFacing",
	Z_Construct_UEnum_ArmorWars_ERTSArmorFacing_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSArmorFacing_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSArmorFacing_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSArmorFacing_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSArmorFacing()
{
	if (!Z_Registration_Info_UEnum_ERTSArmorFacing.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSArmorFacing.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSArmorFacing_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSArmorFacing.InnerSingleton;
}
// ********** End Enum ERTSArmorFacing *************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSArmorTypes_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSArmorType_StaticEnum, TEXT("ERTSArmorType"), &Z_Registration_Info_UEnum_ERTSArmorType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 980994462U) },
		{ ERTSArmorFacing_StaticEnum, TEXT("ERTSArmorFacing"), &Z_Registration_Info_UEnum_ERTSArmorFacing, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 623479837U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSArmorTypes_h__Script_ArmorWars_3292793721(TEXT("/Script/ArmorWars"),
	nullptr, 0,
	nullptr, 0,
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSArmorTypes_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSArmorTypes_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSBuilding.h"

#ifdef ARMORWARS_RTSBuilding_generated_h
#error "RTSBuilding.generated.h already included, missing '#pragma once' in RTSBuilding.h"
#endif
#define ARMORWARS_RTSBuilding_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSBaseActor;
class ARTSBuilding;
class URTSFactoryComponent;
enum class ERTSBuildingState : uint8;
enum class ERTSBuildingType : uint8;
enum class ERTSUnitDomain : uint8;

// ********** Begin Delegate FOnBuildingStateChanged ***********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_243_DELEGATE \
static void FOnBuildingStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnBuildingStateChanged, ARTSBuilding* Building, ERTSBuildingState NewState);


// ********** End Delegate FOnBuildingStateChanged *************************************************

// ********** Begin Delegate FOnConstructionProgress ***********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_244_DELEGATE \
static void FOnConstructionProgress_DelegateWrapper(const FMulticastScriptDelegate& OnConstructionProgress, ARTSBuilding* Building, float Progress);


// ********** End Delegate FOnConstructionProgress *************************************************

// ********** Begin Delegate FOnBuildingCombat *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_245_DELEGATE \
static void FOnBuildingCombat_DelegateWrapper(const FMulticastScriptDelegate& OnBuildingCombat, ARTSBuilding* Building, ARTSBaseActor* Target);


// ********** End Delegate FOnBuildingCombat *******************************************************

// ********** Begin Class ARTSBuilding *************************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_43_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCanProduceUnits); \
	DECLARE_FUNCTION(execHasFactory); \
	DECLARE_FUNCTION(execGetFactoryComponent); \
	DECLARE_FUNCTION(execIsConsumingPower); \
	DECLARE_FUNCTION(execIsGeneratingPower); \
	DECLARE_FUNCTION(execGetNetPowerGeneration); \
	DECLARE_FUNCTION(execHasWeapons); \
	DECLARE_FUNCTION(execGetBuildingType); \
	DECLARE_FUNCTION(execIsResourceBuilding); \
	DECLARE_FUNCTION(execIsProductionBuilding); \
	DECLARE_FUNCTION(execIsDefensiveBuilding); \
	DECLARE_FUNCTION(execFindNearestEnemy); \
	DECLARE_FUNCTION(execCanTargetDomain); \
	DECLARE_FUNCTION(execGetTotalDamagePerSecond); \
	DECLARE_FUNCTION(execGetMaxAttackRange); \
	DECLARE_FUNCTION(execGetCurrentTarget); \
	DECLARE_FUNCTION(execIsAttacking); \
	DECLARE_FUNCTION(execIsInAttackRange); \
	DECLARE_FUNCTION(execCanAttackTarget); \
	DECLARE_FUNCTION(execStopAttacking); \
	DECLARE_FUNCTION(execAttackTarget); \
	DECLARE_FUNCTION(execGetBuildingState); \
	DECLARE_FUNCTION(execSetBuildingState); \
	DECLARE_FUNCTION(execGetConstructionTimeRemaining); \
	DECLARE_FUNCTION(execIsOperational); \
	DECLARE_FUNCTION(execIsUnderConstruction); \
	DECLARE_FUNCTION(execCompleteConstruction); \
	DECLARE_FUNCTION(execResumeConstruction); \
	DECLARE_FUNCTION(execPauseConstruction); \
	DECLARE_FUNCTION(execStartConstruction);


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_43_CALLBACK_WRAPPERS
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBuilding_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_43_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARTSBuilding(); \
	friend struct Z_Construct_UClass_ARTSBuilding_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_ARTSBuilding_NoRegister(); \
public: \
	DECLARE_CLASS2(ARTSBuilding, ARTSBaseActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_ARTSBuilding_NoRegister) \
	DECLARE_SERIALIZER(ARTSBuilding)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_43_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ARTSBuilding(ARTSBuilding&&) = delete; \
	ARTSBuilding(const ARTSBuilding&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ARTSBuilding); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARTSBuilding); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ARTSBuilding) \
	NO_API virtual ~ARTSBuilding();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_40_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_43_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_43_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_43_CALLBACK_WRAPPERS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_43_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h_43_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ARTSBuilding;

// ********** End Class ARTSBuilding ***************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h

// ********** Begin Enum ERTSBuildingState *********************************************************
#define FOREACH_ENUM_ERTSBUILDINGSTATE(op) \
	op(ERTSBuildingState::UnderConstruction) \
	op(ERTSBuildingState::Operational) \
	op(ERTSBuildingState::Damaged) \
	op(ERTSBuildingState::Destroyed) \
	op(ERTSBuildingState::Disabled) 

enum class ERTSBuildingState : uint8;
template<> struct TIsUEnumClass<ERTSBuildingState> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSBuildingState>();
// ********** End Enum ERTSBuildingState ***********************************************************

// ********** Begin Enum ERTSBuildingType **********************************************************
#define FOREACH_ENUM_ERTSBUILDINGTYPE(op) \
	op(ERTSBuildingType::Base) \
	op(ERTSBuildingType::Production) \
	op(ERTSBuildingType::Defense) \
	op(ERTSBuildingType::Resource) \
	op(ERTSBuildingType::Research) \
	op(ERTSBuildingType::Support) \
	op(ERTSBuildingType::Special) 

enum class ERTSBuildingType : uint8;
template<> struct TIsUEnumClass<ERTSBuildingType> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSBuildingType>();
// ********** End Enum ERTSBuildingType ************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

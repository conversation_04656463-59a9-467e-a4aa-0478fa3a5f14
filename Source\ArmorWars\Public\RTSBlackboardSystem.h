#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Components/ActorComponent.h"
#include "RTSCommand.h"
#include "RTSBlackboardSystem.generated.h"

class ARTSUnit;
class ARTSBaseActor;
class URTSCommandComponent;

// Blackboard key types for type safety
UENUM(BlueprintType)
enum class ERTSBlackboardKeyType : uint8
{
    Bool        UMETA(DisplayName = "Boolean"),
    Int         UMETA(DisplayName = "Integer"),
    Float       UMETA(DisplayName = "Float"),
    String      UMETA(DisplayName = "String"),
    Vector      UMETA(DisplayName = "Vector"),
    Rotator     UMETA(DisplayName = "Rotator"),
    Object      UMETA(DisplayName = "Object"),
    Class       UMETA(DisplayName = "Class"),
    Enum        UMETA(DisplayName = "Enum")
};

// Blackboard entry structure
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSBlackboardEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    FString Key;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    ERTSBlackboardKeyType KeyType = ERTSBlackboardKeyType::String;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    FString StringValue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    bool BoolValue = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    int32 IntValue = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    float FloatValue = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    FVector VectorValue = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    FRotator RotatorValue = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    TWeakObjectPtr<UObject> ObjectValue;

    // Constructor
    FRTSBlackboardEntry()
    {
        Key = TEXT("");
        KeyType = ERTSBlackboardKeyType::String;
        StringValue = TEXT("");
        BoolValue = false;
        IntValue = 0;
        FloatValue = 0.0f;
        VectorValue = FVector::ZeroVector;
        RotatorValue = FRotator::ZeroRotator;
        ObjectValue = nullptr;
    }

    FRTSBlackboardEntry(const FString& InKey, ERTSBlackboardKeyType InType)
    {
        Key = InKey;
        KeyType = InType;
        StringValue = TEXT("");
        BoolValue = false;
        IntValue = 0;
        FloatValue = 0.0f;
        VectorValue = FVector::ZeroVector;
        RotatorValue = FRotator::ZeroRotator;
        ObjectValue = nullptr;
    }
};

/**
 * Enhanced blackboard system for RTS AI
 * Stores shared AI data and provides type-safe access
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class ARMORWARS_API URTSBlackboardComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    URTSBlackboardComponent();

protected:
    virtual void BeginPlay() override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Blackboard data storage
    UPROPERTY(BlueprintReadOnly, Category = "Blackboard")
    TMap<FString, FRTSBlackboardEntry> BlackboardData;

    // Predefined keys for common AI data
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard Keys")
    TArray<FRTSBlackboardEntry> PredefinedKeys;

    // Auto-update settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    bool bAutoUpdateUnitData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    bool bAutoUpdateCommandData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    bool bAutoUpdateFormationData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    bool bAutoUpdateCombatData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    float UpdateInterval = 0.1f;

public:
    // Generic value setters
    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void SetBool(const FString& Key, bool Value);

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void SetInt(const FString& Key, int32 Value);

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void SetFloat(const FString& Key, float Value);

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void SetString(const FString& Key, const FString& Value);

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void SetVector(const FString& Key, const FVector& Value);

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void SetRotator(const FString& Key, const FRotator& Value);

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void SetObject(const FString& Key, UObject* Value);

    // Generic value getters
    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    bool GetBool(const FString& Key, bool DefaultValue = false) const;

    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    int32 GetInt(const FString& Key, int32 DefaultValue = 0) const;

    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    float GetFloat(const FString& Key, float DefaultValue = 0.0f) const;

    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    FString GetString(const FString& Key, const FString& DefaultValue = TEXT("")) const;

    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    FVector GetVector(const FString& Key, const FVector& DefaultValue = FVector::ZeroVector) const;

    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    FRotator GetRotator(const FString& Key, const FRotator& DefaultValue = FRotator::ZeroRotator) const;

    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    UObject* GetObject(const FString& Key) const;

    // Specialized getters for common types
    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    ARTSBaseActor* GetActor(const FString& Key) const;

    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    ARTSUnit* GetUnit(const FString& Key) const;

    // Key management
    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    bool HasKey(const FString& Key) const;

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void RemoveKey(const FString& Key);

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void ClearAllKeys();

    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    TArray<FString> GetAllKeys() const;

    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    ERTSBlackboardKeyType GetKeyType(const FString& Key) const;

    // Predefined key management
    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void InitializePredefinedKeys();

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void AddPredefinedKey(const FString& Key, ERTSBlackboardKeyType KeyType);

    // Auto-update functions
    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void UpdateUnitData();

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void UpdateCommandData();

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void UpdateFormationData();

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void UpdateCombatData();

    UFUNCTION(BlueprintCallable, Category = "RTS Blackboard")
    void UpdateAllData();

    // Utility functions
    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    ARTSUnit* GetOwnerUnit() const;

    UFUNCTION(BlueprintPure, Category = "RTS Blackboard")
    URTSCommandComponent* GetOwnerCommandComponent() const;

protected:
    // Internal update tracking
    float LastUpdateTime = 0.0f;

    // Helper functions
    virtual void SetValue(const FString& Key, const FRTSBlackboardEntry& Entry);
    virtual FRTSBlackboardEntry* GetEntry(const FString& Key);
    virtual const FRTSBlackboardEntry* GetEntry(const FString& Key) const;
    virtual void CreateKeyIfNotExists(const FString& Key, ERTSBlackboardKeyType KeyType);

    // Auto-update helpers
    virtual void UpdateBasicUnitInfo();
    virtual void UpdateUnitStats();
    virtual void UpdateCurrentCommand();
    virtual void UpdateCommandQueue();
    virtual void UpdateFormationInfo();
    virtual void UpdateCombatTargets();
    virtual void UpdateEnvironmentalData();

public:
    // Common blackboard keys as constants
    static const FString KEY_UNIT_HEALTH;
    static const FString KEY_UNIT_HEALTH_PERCENTAGE;
    static const FString KEY_UNIT_POSITION;
    static const FString KEY_UNIT_ROTATION;
    static const FString KEY_UNIT_VELOCITY;
    static const FString KEY_UNIT_SPEED;
    static const FString KEY_UNIT_MAX_SPEED;
    static const FString KEY_UNIT_ATTACK_RANGE;
    static const FString KEY_UNIT_TEAM_ID;
    
    static const FString KEY_CURRENT_COMMAND;
    static const FString KEY_COMMAND_TYPE;
    static const FString KEY_COMMAND_TARGET_LOCATION;
    static const FString KEY_COMMAND_TARGET_ACTOR;
    static const FString KEY_COMMAND_PRIORITY;
    static const FString KEY_HAS_COMMANDS;
    static const FString KEY_COMMAND_QUEUE_SIZE;
    
    static const FString KEY_IN_FORMATION;
    static const FString KEY_FORMATION_LEADER;
    static const FString KEY_FORMATION_OFFSET;
    static const FString KEY_FORMATION_INDEX;
    static const FString KEY_FORMATION_TYPE;
    
    static const FString KEY_CURRENT_TARGET;
    static const FString KEY_NEAREST_ENEMY;
    static const FString KEY_ENEMY_COUNT;
    static const FString KEY_UNDER_ATTACK;
    static const FString KEY_LAST_ATTACKER;
    static const FString KEY_TACTICAL_ADVANTAGE;
    
    static const FString KEY_MOVE_TARGET;
    static const FString KEY_PATH_BLOCKED;
    static const FString KEY_ALTERNATIVE_PATH;
    static const FString KEY_COLLISION_AVOIDANCE;
};

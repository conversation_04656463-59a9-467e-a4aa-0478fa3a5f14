#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "GameplayTagContainer.h"
#include "RTSCommand.generated.h"

class ARTSBaseActor;
class ARTSUnit;

// Command types for RTS units
UENUM(BlueprintType)
enum class ERTSCommandType : uint8
{
    None            UMETA(DisplayName = "None"),
    Move            UMETA(DisplayName = "Move"),
    AttackMove      UMETA(DisplayName = "Attack Move"),
    AttackTarget    UMETA(DisplayName = "Attack Target"),
    Stop            UMETA(DisplayName = "Stop"),
    Hold            UMETA(DisplayName = "Hold Position"),
    Patrol          UMETA(DisplayName = "Patrol"),
    Follow          UMETA(DisplayName = "Follow"),
    Guard           UMETA(DisplayName = "Guard"),
    Formation       UMETA(DisplayName = "Formation"),
    Retreat         UMETA(DisplayName = "Retreat"),
    SpecialAbility  UMETA(DisplayName = "Special Ability")
};

// Command priority levels
UENUM(BlueprintType)
enum class ERTSCommandPriority : uint8
{
    Low         UMETA(DisplayName = "Low"),
    Normal      UMETA(DisplayName = "Normal"),
    High        UMETA(DisplayName = "High"),
    Critical    UMETA(DisplayName = "Critical"),
    Override    UMETA(DisplayName = "Override")
};

// Command execution status
UENUM(BlueprintType)
enum class ERTSCommandStatus : uint8
{
    Pending     UMETA(DisplayName = "Pending"),
    Executing   UMETA(DisplayName = "Executing"),
    Completed   UMETA(DisplayName = "Completed"),
    Failed      UMETA(DisplayName = "Failed"),
    Cancelled   UMETA(DisplayName = "Cancelled"),
    Interrupted UMETA(DisplayName = "Interrupted")
};

// Formation types for group commands
UENUM(BlueprintType)
enum class ERTSFormationCommandType : uint8
{
    Line        UMETA(DisplayName = "Line"),
    Wedge       UMETA(DisplayName = "Wedge"),
    Circle      UMETA(DisplayName = "Circle"),
    Column      UMETA(DisplayName = "Column"),
    Square      UMETA(DisplayName = "Square"),
    Scattered   UMETA(DisplayName = "Scattered"),
    Custom      UMETA(DisplayName = "Custom")
};

/**
 * Base structure for RTS commands
 * Contains all necessary information for command execution
 */
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSCommand
{
    GENERATED_BODY()

    // Command identification
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    ERTSCommandType CommandType = ERTSCommandType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    ERTSCommandPriority Priority = ERTSCommandPriority::Normal;

    UPROPERTY(BlueprintReadOnly, Category = "Command")
    ERTSCommandStatus Status = ERTSCommandStatus::Pending;

    // Command parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    FVector TargetLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    TWeakObjectPtr<ARTSBaseActor> TargetActor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    TArray<FVector> WaypointLocations;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    ERTSFormationCommandType FormationType = ERTSFormationCommandType::Line;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    float FormationSpacing = 200.0f;

    // Command metadata
    UPROPERTY(BlueprintReadOnly, Category = "Command")
    float CommandTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Command")
    float ExecutionStartTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    float TimeoutDuration = 0.0f; // 0 = no timeout

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    bool bQueueCommand = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    bool bInterruptible = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command")
    FGameplayTagContainer CommandTags;

    // Formation-specific data
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    FVector FormationOffset = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    TWeakObjectPtr<ARTSUnit> FormationLeader;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    int32 FormationIndex = -1;

    // Special ability data
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Special")
    FName AbilityName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Special")
    TMap<FString, FString> AbilityParameters;

    // Constructor
    FRTSCommand()
    {
        CommandType = ERTSCommandType::None;
        Priority = ERTSCommandPriority::Normal;
        Status = ERTSCommandStatus::Pending;
        TargetLocation = FVector::ZeroVector;
        FormationType = ERTSFormationCommandType::Line;
        FormationSpacing = 200.0f;
        CommandTime = 0.0f;
        ExecutionStartTime = 0.0f;
        TimeoutDuration = 0.0f;
        bQueueCommand = false;
        bInterruptible = true;
        FormationOffset = FVector::ZeroVector;
        FormationIndex = -1;
    }

    // Helper functions
    bool IsValid() const
    {
        return CommandType != ERTSCommandType::None;
    }

    bool RequiresTarget() const
    {
        return CommandType == ERTSCommandType::AttackTarget || 
               CommandType == ERTSCommandType::Follow || 
               CommandType == ERTSCommandType::Guard;
    }

    bool RequiresLocation() const
    {
        return CommandType == ERTSCommandType::Move || 
               CommandType == ERTSCommandType::AttackMove || 
               CommandType == ERTSCommandType::Patrol;
    }

    bool IsMovementCommand() const
    {
        return CommandType == ERTSCommandType::Move || 
               CommandType == ERTSCommandType::AttackMove || 
               CommandType == ERTSCommandType::Patrol || 
               CommandType == ERTSCommandType::Follow;
    }

    bool IsCombatCommand() const
    {
        return CommandType == ERTSCommandType::AttackTarget || 
               CommandType == ERTSCommandType::AttackMove;
    }

    bool IsFormationCommand() const
    {
        return CommandType == ERTSCommandType::Formation;
    }

    bool CanInterrupt(const FRTSCommand& OtherCommand) const
    {
        if (!bInterruptible)
        {
            return false;
        }
        
        return static_cast<uint8>(OtherCommand.Priority) > static_cast<uint8>(Priority);
    }

    bool HasTimedOut(float CurrentTime) const
    {
        if (TimeoutDuration <= 0.0f || ExecutionStartTime <= 0.0f)
        {
            return false;
        }
        
        return (CurrentTime - ExecutionStartTime) > TimeoutDuration;
    }
};

/**
 * Command queue for managing multiple commands per unit
 */
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSCommandQueue
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Command Queue")
    TArray<FRTSCommand> Commands;

    UPROPERTY(BlueprintReadOnly, Category = "Command Queue")
    int32 CurrentCommandIndex = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Command Queue")
    int32 MaxQueueSize = 10;

    // Queue management
    void AddCommand(const FRTSCommand& Command, bool bClearQueue = false);
    void InsertCommand(const FRTSCommand& Command, int32 Index = 0);
    bool RemoveCommand(int32 Index);
    void ClearQueue();
    
    FRTSCommand* GetCurrentCommand();
    const FRTSCommand* GetCurrentCommand() const;
    FRTSCommand* GetNextCommand();
    
    bool AdvanceToNextCommand();
    bool HasCommands() const;
    int32 GetCommandCount() const;
    
    void SetCurrentCommandStatus(ERTSCommandStatus NewStatus);
    void CompleteCurrentCommand();
    void FailCurrentCommand();
    void CancelCurrentCommand();
};

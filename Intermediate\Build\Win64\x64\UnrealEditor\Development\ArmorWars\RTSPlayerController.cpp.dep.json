{"Version": "1.2", "Data": {"Source": "f:\\armorwars\\source\\armorwars\\private\\rtsplayercontroller.cpp", "ProvidedModule": "", "PCH": "f:\\armorwars\\intermediate\\build\\win64\\x64\\armorwarseditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["f:\\armorwars\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\armorwars\\definitions.armorwars.h", "f:\\armorwars\\source\\armorwars\\public\\rtsplayercontroller.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputactionvalue.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputactionvalue.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsplayercontroller.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsselectionsystem.h", "f:\\armorwars\\source\\armorwars\\public\\interfaces\\rtsunitselectioninterface.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunitselectioninterface.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsbaseactor.h", "f:\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsbaseactor.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsselectionsystem.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsunit.h", "f:\\armorwars\\source\\armorwars\\public\\rtsunitaicomponent.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunitaicomponent.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtscommand.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "f:\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscommand.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunit.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsbuilding.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsbuilding.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtscommandcomponent.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtscommandcomponent.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsformationmanager.h", "f:\\armorwars\\source\\armorwars\\public\\rtsformationsystem.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsformationsystem.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsformationmanager.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtshud.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\hud.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\hudhitbox.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\debugtextinfo.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\debugtextinfo.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hud.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtshud.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtspawn.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtspawn.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputcomponent.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputaction.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputmodifiers.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputmodifiers.generated.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputtriggers.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputtriggers.generated.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputaction.generated.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputcomponent.generated.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputsubsystems.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputsubsysteminterface.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedplayerinput.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\playerinput.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\public\\gesturerecognizer.h", "f:\\ue_5.6\\engine\\source\\runtime\\engine\\public\\keystate.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\playerinput.generated.h", "f:\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\public\\nativegameplaytags.h", "f:\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagsmanager.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagsmanager.generated.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedplayerinput.generated.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputsubsysteminterface.generated.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputsubsystems.generated.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputmappingcontext.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedactionkeymapping.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedactionkeymapping.generated.h", "f:\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputmappingcontext.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}
// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSBuilding.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSBuilding() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBuilding();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBuilding_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFactoryComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSWeaponController_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSBuildingState();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSBuildingType();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSUnitDomain();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSBuildingState *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSBuildingState;
static UEnum* ERTSBuildingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSBuildingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSBuildingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSBuildingState, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSBuildingState"));
	}
	return Z_Registration_Info_UEnum_ERTSBuildingState.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSBuildingState>()
{
	return ERTSBuildingState_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSBuildingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum for building states\n" },
#endif
		{ "Damaged.DisplayName", "Damaged" },
		{ "Damaged.Name", "ERTSBuildingState::Damaged" },
		{ "Destroyed.DisplayName", "Destroyed" },
		{ "Destroyed.Name", "ERTSBuildingState::Destroyed" },
		{ "Disabled.DisplayName", "Disabled" },
		{ "Disabled.Name", "ERTSBuildingState::Disabled" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
		{ "Operational.DisplayName", "Operational" },
		{ "Operational.Name", "ERTSBuildingState::Operational" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for building states" },
#endif
		{ "UnderConstruction.DisplayName", "Under Construction" },
		{ "UnderConstruction.Name", "ERTSBuildingState::UnderConstruction" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSBuildingState::UnderConstruction", (int64)ERTSBuildingState::UnderConstruction },
		{ "ERTSBuildingState::Operational", (int64)ERTSBuildingState::Operational },
		{ "ERTSBuildingState::Damaged", (int64)ERTSBuildingState::Damaged },
		{ "ERTSBuildingState::Destroyed", (int64)ERTSBuildingState::Destroyed },
		{ "ERTSBuildingState::Disabled", (int64)ERTSBuildingState::Disabled },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSBuildingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSBuildingState",
	"ERTSBuildingState",
	Z_Construct_UEnum_ArmorWars_ERTSBuildingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSBuildingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSBuildingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSBuildingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSBuildingState()
{
	if (!Z_Registration_Info_UEnum_ERTSBuildingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSBuildingState.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSBuildingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSBuildingState.InnerSingleton;
}
// ********** End Enum ERTSBuildingState ***********************************************************

// ********** Begin Enum ERTSBuildingType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSBuildingType;
static UEnum* ERTSBuildingType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSBuildingType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSBuildingType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSBuildingType, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSBuildingType"));
	}
	return Z_Registration_Info_UEnum_ERTSBuildingType.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSBuildingType>()
{
	return ERTSBuildingType_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSBuildingType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Base.DisplayName", "Base" },
		{ "Base.Name", "ERTSBuildingType::Base" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enum for building types\n" },
#endif
		{ "Defense.DisplayName", "Defense" },
		{ "Defense.Name", "ERTSBuildingType::Defense" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
		{ "Production.DisplayName", "Production" },
		{ "Production.Name", "ERTSBuildingType::Production" },
		{ "Research.DisplayName", "Research" },
		{ "Research.Name", "ERTSBuildingType::Research" },
		{ "Resource.DisplayName", "Resource" },
		{ "Resource.Name", "ERTSBuildingType::Resource" },
		{ "Special.DisplayName", "Special" },
		{ "Special.Name", "ERTSBuildingType::Special" },
		{ "Support.DisplayName", "Support" },
		{ "Support.Name", "ERTSBuildingType::Support" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for building types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSBuildingType::Base", (int64)ERTSBuildingType::Base },
		{ "ERTSBuildingType::Production", (int64)ERTSBuildingType::Production },
		{ "ERTSBuildingType::Defense", (int64)ERTSBuildingType::Defense },
		{ "ERTSBuildingType::Resource", (int64)ERTSBuildingType::Resource },
		{ "ERTSBuildingType::Research", (int64)ERTSBuildingType::Research },
		{ "ERTSBuildingType::Support", (int64)ERTSBuildingType::Support },
		{ "ERTSBuildingType::Special", (int64)ERTSBuildingType::Special },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSBuildingType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSBuildingType",
	"ERTSBuildingType",
	Z_Construct_UEnum_ArmorWars_ERTSBuildingType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSBuildingType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSBuildingType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSBuildingType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSBuildingType()
{
	if (!Z_Registration_Info_UEnum_ERTSBuildingType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSBuildingType.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSBuildingType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSBuildingType.InnerSingleton;
}
// ********** End Enum ERTSBuildingType ************************************************************

// ********** Begin Delegate FOnBuildingStateChanged ***********************************************
struct Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics
{
	struct RTSBuilding_eventOnBuildingStateChanged_Parms
	{
		ARTSBuilding* Building;
		ERTSBuildingState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Building;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::NewProp_Building = { "Building", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventOnBuildingStateChanged_Parms, Building), Z_Construct_UClass_ARTSBuilding_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventOnBuildingStateChanged_Parms, NewState), Z_Construct_UEnum_ArmorWars_ERTSBuildingState, METADATA_PARAMS(0, nullptr) }; // 1008412471
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::NewProp_Building,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "OnBuildingStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::RTSBuilding_eventOnBuildingStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::RTSBuilding_eventOnBuildingStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSBuilding::FOnBuildingStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnBuildingStateChanged, ARTSBuilding* Building, ERTSBuildingState NewState)
{
	struct RTSBuilding_eventOnBuildingStateChanged_Parms
	{
		ARTSBuilding* Building;
		ERTSBuildingState NewState;
	};
	RTSBuilding_eventOnBuildingStateChanged_Parms Parms;
	Parms.Building=Building;
	Parms.NewState=NewState;
	OnBuildingStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBuildingStateChanged *************************************************

// ********** Begin Delegate FOnConstructionProgress ***********************************************
struct Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics
{
	struct RTSBuilding_eventOnConstructionProgress_Parms
	{
		ARTSBuilding* Building;
		float Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Building;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::NewProp_Building = { "Building", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventOnConstructionProgress_Parms, Building), Z_Construct_UClass_ARTSBuilding_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventOnConstructionProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::NewProp_Building,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "OnConstructionProgress__DelegateSignature", Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::RTSBuilding_eventOnConstructionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::RTSBuilding_eventOnConstructionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSBuilding::FOnConstructionProgress_DelegateWrapper(const FMulticastScriptDelegate& OnConstructionProgress, ARTSBuilding* Building, float Progress)
{
	struct RTSBuilding_eventOnConstructionProgress_Parms
	{
		ARTSBuilding* Building;
		float Progress;
	};
	RTSBuilding_eventOnConstructionProgress_Parms Parms;
	Parms.Building=Building;
	Parms.Progress=Progress;
	OnConstructionProgress.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnConstructionProgress *************************************************

// ********** Begin Delegate FOnBuildingCombat *****************************************************
struct Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics
{
	struct RTSBuilding_eventOnBuildingCombat_Parms
	{
		ARTSBuilding* Building;
		ARTSBaseActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Building;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::NewProp_Building = { "Building", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventOnBuildingCombat_Parms, Building), Z_Construct_UClass_ARTSBuilding_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventOnBuildingCombat_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::NewProp_Building,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "OnBuildingCombat__DelegateSignature", Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::RTSBuilding_eventOnBuildingCombat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::RTSBuilding_eventOnBuildingCombat_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ARTSBuilding::FOnBuildingCombat_DelegateWrapper(const FMulticastScriptDelegate& OnBuildingCombat, ARTSBuilding* Building, ARTSBaseActor* Target)
{
	struct RTSBuilding_eventOnBuildingCombat_Parms
	{
		ARTSBuilding* Building;
		ARTSBaseActor* Target;
	};
	RTSBuilding_eventOnBuildingCombat_Parms Parms;
	Parms.Building=Building;
	Parms.Target=Target;
	OnBuildingCombat.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBuildingCombat *******************************************************

// ********** Begin Class ARTSBuilding Function AttackTarget ***************************************
struct Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics
{
	struct RTSBuilding_eventAttackTarget_Parms
	{
		ARTSBaseActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Combat functions (for defensive buildings)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat functions (for defensive buildings)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventAttackTarget_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "AttackTarget", Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::RTSBuilding_eventAttackTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::RTSBuilding_eventAttackTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_AttackTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_AttackTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execAttackTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AttackTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function AttackTarget *****************************************

// ********** Begin Class ARTSBuilding Function CanAttackTarget ************************************
struct Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics
{
	struct RTSBuilding_eventCanAttackTarget_Parms
	{
		const ARTSBaseActor* Target;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Target_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventCanAttackTarget_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Target_MetaData), NewProp_Target_MetaData) };
void Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventCanAttackTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventCanAttackTarget_Parms), &Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "CanAttackTarget", Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::RTSBuilding_eventCanAttackTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::RTSBuilding_eventCanAttackTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_CanAttackTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_CanAttackTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execCanAttackTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanAttackTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function CanAttackTarget **************************************

// ********** Begin Class ARTSBuilding Function CanProduceUnits ************************************
struct Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics
{
	struct RTSBuilding_eventCanProduceUnits_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Factory" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventCanProduceUnits_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventCanProduceUnits_Parms), &Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "CanProduceUnits", Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::RTSBuilding_eventCanProduceUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::RTSBuilding_eventCanProduceUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_CanProduceUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_CanProduceUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execCanProduceUnits)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanProduceUnits();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function CanProduceUnits **************************************

// ********** Begin Class ARTSBuilding Function CanTargetDomain ************************************
struct Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics
{
	struct RTSBuilding_eventCanTargetDomain_Parms
	{
		ERTSUnitDomain Domain;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Domain_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Domain;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::NewProp_Domain_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::NewProp_Domain = { "Domain", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventCanTargetDomain_Parms, Domain), Z_Construct_UEnum_ArmorWars_ERTSUnitDomain, METADATA_PARAMS(0, nullptr) }; // 3001890894
void Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventCanTargetDomain_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventCanTargetDomain_Parms), &Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::NewProp_Domain_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::NewProp_Domain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "CanTargetDomain", Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::RTSBuilding_eventCanTargetDomain_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::RTSBuilding_eventCanTargetDomain_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_CanTargetDomain()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_CanTargetDomain_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execCanTargetDomain)
{
	P_GET_ENUM(ERTSUnitDomain,Z_Param_Domain);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanTargetDomain(ERTSUnitDomain(Z_Param_Domain));
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function CanTargetDomain **************************************

// ********** Begin Class ARTSBuilding Function CompleteConstruction *******************************
struct Z_Construct_UFunction_ARTSBuilding_CompleteConstruction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_CompleteConstruction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "CompleteConstruction", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_CompleteConstruction_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_CompleteConstruction_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBuilding_CompleteConstruction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_CompleteConstruction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execCompleteConstruction)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompleteConstruction();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function CompleteConstruction *********************************

// ********** Begin Class ARTSBuilding Function FindNearestEnemy ***********************************
struct Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics
{
	struct RTSBuilding_eventFindNearestEnemy_Parms
	{
		float SearchRange;
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "CPP_Default_SearchRange", "0.000000" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SearchRange;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::NewProp_SearchRange = { "SearchRange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventFindNearestEnemy_Parms, SearchRange), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventFindNearestEnemy_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::NewProp_SearchRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "FindNearestEnemy", Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::RTSBuilding_eventFindNearestEnemy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::RTSBuilding_eventFindNearestEnemy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execFindNearestEnemy)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_SearchRange);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->FindNearestEnemy(Z_Param_SearchRange);
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function FindNearestEnemy *************************************

// ********** Begin Class ARTSBuilding Function GetBuildingState ***********************************
struct Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics
{
	struct RTSBuilding_eventGetBuildingState_Parms
	{
		ERTSBuildingState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Building" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventGetBuildingState_Parms, ReturnValue), Z_Construct_UEnum_ArmorWars_ERTSBuildingState, METADATA_PARAMS(0, nullptr) }; // 1008412471
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "GetBuildingState", Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::RTSBuilding_eventGetBuildingState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::RTSBuilding_eventGetBuildingState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_GetBuildingState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_GetBuildingState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execGetBuildingState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ERTSBuildingState*)Z_Param__Result=P_THIS->GetBuildingState();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function GetBuildingState *************************************

// ********** Begin Class ARTSBuilding Function GetBuildingType ************************************
struct Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics
{
	struct RTSBuilding_eventGetBuildingType_Parms
	{
		ERTSBuildingType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Building" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventGetBuildingType_Parms, ReturnValue), Z_Construct_UEnum_ArmorWars_ERTSBuildingType, METADATA_PARAMS(0, nullptr) }; // 467938925
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "GetBuildingType", Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::RTSBuilding_eventGetBuildingType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::RTSBuilding_eventGetBuildingType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_GetBuildingType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_GetBuildingType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execGetBuildingType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ERTSBuildingType*)Z_Param__Result=P_THIS->GetBuildingType();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function GetBuildingType **************************************

// ********** Begin Class ARTSBuilding Function GetConstructionTimeRemaining ***********************
struct Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics
{
	struct RTSBuilding_eventGetConstructionTimeRemaining_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventGetConstructionTimeRemaining_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "GetConstructionTimeRemaining", Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::RTSBuilding_eventGetConstructionTimeRemaining_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::RTSBuilding_eventGetConstructionTimeRemaining_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execGetConstructionTimeRemaining)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetConstructionTimeRemaining();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function GetConstructionTimeRemaining *************************

// ********** Begin Class ARTSBuilding Function GetCurrentTarget ***********************************
struct Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics
{
	struct RTSBuilding_eventGetCurrentTarget_Parms
	{
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventGetCurrentTarget_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "GetCurrentTarget", Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::RTSBuilding_eventGetCurrentTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::RTSBuilding_eventGetCurrentTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execGetCurrentTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->GetCurrentTarget();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function GetCurrentTarget *************************************

// ********** Begin Class ARTSBuilding Function GetFactoryComponent ********************************
struct Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics
{
	struct RTSBuilding_eventGetFactoryComponent_Parms
	{
		URTSFactoryComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Factory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Factory functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Factory functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventGetFactoryComponent_Parms, ReturnValue), Z_Construct_UClass_URTSFactoryComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "GetFactoryComponent", Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::RTSBuilding_eventGetFactoryComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::RTSBuilding_eventGetFactoryComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execGetFactoryComponent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSFactoryComponent**)Z_Param__Result=P_THIS->GetFactoryComponent();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function GetFactoryComponent **********************************

// ********** Begin Class ARTSBuilding Function GetMaxAttackRange **********************************
struct Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics
{
	struct RTSBuilding_eventGetMaxAttackRange_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventGetMaxAttackRange_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "GetMaxAttackRange", Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::RTSBuilding_eventGetMaxAttackRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::RTSBuilding_eventGetMaxAttackRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execGetMaxAttackRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMaxAttackRange();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function GetMaxAttackRange ************************************

// ********** Begin Class ARTSBuilding Function GetNetPowerGeneration ******************************
struct Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics
{
	struct RTSBuilding_eventGetNetPowerGeneration_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Power" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Power functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Power functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventGetNetPowerGeneration_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "GetNetPowerGeneration", Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::RTSBuilding_eventGetNetPowerGeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::RTSBuilding_eventGetNetPowerGeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execGetNetPowerGeneration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetNetPowerGeneration();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function GetNetPowerGeneration ********************************

// ********** Begin Class ARTSBuilding Function GetTotalDamagePerSecond ****************************
struct Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics
{
	struct RTSBuilding_eventGetTotalDamagePerSecond_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventGetTotalDamagePerSecond_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "GetTotalDamagePerSecond", Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::RTSBuilding_eventGetTotalDamagePerSecond_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::RTSBuilding_eventGetTotalDamagePerSecond_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execGetTotalDamagePerSecond)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalDamagePerSecond();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function GetTotalDamagePerSecond ******************************

// ********** Begin Class ARTSBuilding Function HasFactory *****************************************
struct Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics
{
	struct RTSBuilding_eventHasFactory_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Factory" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventHasFactory_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventHasFactory_Parms), &Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "HasFactory", Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::RTSBuilding_eventHasFactory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::RTSBuilding_eventHasFactory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_HasFactory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_HasFactory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execHasFactory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasFactory();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function HasFactory *******************************************

// ********** Begin Class ARTSBuilding Function HasWeapons *****************************************
struct Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics
{
	struct RTSBuilding_eventHasWeapons_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventHasWeapons_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventHasWeapons_Parms), &Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "HasWeapons", Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::RTSBuilding_eventHasWeapons_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::RTSBuilding_eventHasWeapons_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_HasWeapons()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_HasWeapons_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execHasWeapons)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasWeapons();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function HasWeapons *******************************************

// ********** Begin Class ARTSBuilding Function IsAttacking ****************************************
struct Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics
{
	struct RTSBuilding_eventIsAttacking_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventIsAttacking_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventIsAttacking_Parms), &Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "IsAttacking", Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::RTSBuilding_eventIsAttacking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::RTSBuilding_eventIsAttacking_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_IsAttacking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_IsAttacking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execIsAttacking)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAttacking();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function IsAttacking ******************************************

// ********** Begin Class ARTSBuilding Function IsConsumingPower ***********************************
struct Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics
{
	struct RTSBuilding_eventIsConsumingPower_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Power" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventIsConsumingPower_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventIsConsumingPower_Parms), &Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "IsConsumingPower", Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::RTSBuilding_eventIsConsumingPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::RTSBuilding_eventIsConsumingPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_IsConsumingPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_IsConsumingPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execIsConsumingPower)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsConsumingPower();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function IsConsumingPower *************************************

// ********** Begin Class ARTSBuilding Function IsDefensiveBuilding ********************************
struct Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics
{
	struct RTSBuilding_eventIsDefensiveBuilding_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Building" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Building type functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Building type functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventIsDefensiveBuilding_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventIsDefensiveBuilding_Parms), &Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "IsDefensiveBuilding", Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::RTSBuilding_eventIsDefensiveBuilding_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::RTSBuilding_eventIsDefensiveBuilding_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execIsDefensiveBuilding)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDefensiveBuilding();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function IsDefensiveBuilding **********************************

// ********** Begin Class ARTSBuilding Function IsGeneratingPower **********************************
struct Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics
{
	struct RTSBuilding_eventIsGeneratingPower_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Power" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventIsGeneratingPower_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventIsGeneratingPower_Parms), &Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "IsGeneratingPower", Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::RTSBuilding_eventIsGeneratingPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::RTSBuilding_eventIsGeneratingPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execIsGeneratingPower)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsGeneratingPower();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function IsGeneratingPower ************************************

// ********** Begin Class ARTSBuilding Function IsInAttackRange ************************************
struct Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics
{
	struct RTSBuilding_eventIsInAttackRange_Parms
	{
		const ARTSBaseActor* Target;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Target_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventIsInAttackRange_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Target_MetaData), NewProp_Target_MetaData) };
void Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventIsInAttackRange_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventIsInAttackRange_Parms), &Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "IsInAttackRange", Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::RTSBuilding_eventIsInAttackRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::RTSBuilding_eventIsInAttackRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_IsInAttackRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_IsInAttackRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execIsInAttackRange)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInAttackRange(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function IsInAttackRange **************************************

// ********** Begin Class ARTSBuilding Function IsOperational **************************************
struct Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics
{
	struct RTSBuilding_eventIsOperational_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventIsOperational_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventIsOperational_Parms), &Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "IsOperational", Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::RTSBuilding_eventIsOperational_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::RTSBuilding_eventIsOperational_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_IsOperational()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_IsOperational_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execIsOperational)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsOperational();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function IsOperational ****************************************

// ********** Begin Class ARTSBuilding Function IsProductionBuilding *******************************
struct Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics
{
	struct RTSBuilding_eventIsProductionBuilding_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Building" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventIsProductionBuilding_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventIsProductionBuilding_Parms), &Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "IsProductionBuilding", Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::RTSBuilding_eventIsProductionBuilding_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::RTSBuilding_eventIsProductionBuilding_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execIsProductionBuilding)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsProductionBuilding();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function IsProductionBuilding *********************************

// ********** Begin Class ARTSBuilding Function IsResourceBuilding *********************************
struct Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics
{
	struct RTSBuilding_eventIsResourceBuilding_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Building" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventIsResourceBuilding_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventIsResourceBuilding_Parms), &Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "IsResourceBuilding", Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::RTSBuilding_eventIsResourceBuilding_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::RTSBuilding_eventIsResourceBuilding_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execIsResourceBuilding)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsResourceBuilding();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function IsResourceBuilding ***********************************

// ********** Begin Class ARTSBuilding Function IsUnderConstruction ********************************
struct Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics
{
	struct RTSBuilding_eventIsUnderConstruction_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSBuilding_eventIsUnderConstruction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSBuilding_eventIsUnderConstruction_Parms), &Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "IsUnderConstruction", Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::RTSBuilding_eventIsUnderConstruction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::RTSBuilding_eventIsUnderConstruction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execIsUnderConstruction)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsUnderConstruction();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function IsUnderConstruction **********************************

// ********** Begin Class ARTSBuilding Function OnAttackStarted ************************************
struct RTSBuilding_eventOnAttackStarted_Parms
{
	ARTSBaseActor* Target;
};
static FName NAME_ARTSBuilding_OnAttackStarted = FName(TEXT("OnAttackStarted"));
void ARTSBuilding::OnAttackStarted(ARTSBaseActor* Target)
{
	RTSBuilding_eventOnAttackStarted_Parms Parms;
	Parms.Target=Target;
	UFunction* Func = FindFunctionChecked(NAME_ARTSBuilding_OnAttackStarted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventOnAttackStarted_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "OnAttackStarted", Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics::PropPointers), sizeof(RTSBuilding_eventOnAttackStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSBuilding_eventOnAttackStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_OnAttackStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_OnAttackStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBuilding Function OnAttackStarted **************************************

// ********** Begin Class ARTSBuilding Function OnAttackStopped ************************************
static FName NAME_ARTSBuilding_OnAttackStopped = FName(TEXT("OnAttackStopped"));
void ARTSBuilding::OnAttackStopped()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSBuilding_OnAttackStopped);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSBuilding_OnAttackStopped_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_OnAttackStopped_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "OnAttackStopped", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnAttackStopped_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_OnAttackStopped_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBuilding_OnAttackStopped()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_OnAttackStopped_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBuilding Function OnAttackStopped **************************************

// ********** Begin Class ARTSBuilding Function OnBuildingStateChangedEvent ************************
struct RTSBuilding_eventOnBuildingStateChangedEvent_Parms
{
	ERTSBuildingState OldState;
	ERTSBuildingState NewState;
};
static FName NAME_ARTSBuilding_OnBuildingStateChangedEvent = FName(TEXT("OnBuildingStateChangedEvent"));
void ARTSBuilding::OnBuildingStateChangedEvent(ERTSBuildingState OldState, ERTSBuildingState NewState)
{
	RTSBuilding_eventOnBuildingStateChangedEvent_Parms Parms;
	Parms.OldState=OldState;
	Parms.NewState=NewState;
	UFunction* Func = FindFunctionChecked(NAME_ARTSBuilding_OnBuildingStateChangedEvent);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Building" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventOnBuildingStateChangedEvent_Parms, OldState), Z_Construct_UEnum_ArmorWars_ERTSBuildingState, METADATA_PARAMS(0, nullptr) }; // 1008412471
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventOnBuildingStateChangedEvent_Parms, NewState), Z_Construct_UEnum_ArmorWars_ERTSBuildingState, METADATA_PARAMS(0, nullptr) }; // 1008412471
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "OnBuildingStateChangedEvent", Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::PropPointers), sizeof(RTSBuilding_eventOnBuildingStateChangedEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSBuilding_eventOnBuildingStateChangedEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBuilding Function OnBuildingStateChangedEvent **************************

// ********** Begin Class ARTSBuilding Function OnConstructionCompleted ****************************
static FName NAME_ARTSBuilding_OnConstructionCompleted = FName(TEXT("OnConstructionCompleted"));
void ARTSBuilding::OnConstructionCompleted()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSBuilding_OnConstructionCompleted);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSBuilding_OnConstructionCompleted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_OnConstructionCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "OnConstructionCompleted", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnConstructionCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_OnConstructionCompleted_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBuilding_OnConstructionCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_OnConstructionCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBuilding Function OnConstructionCompleted ******************************

// ********** Begin Class ARTSBuilding Function OnConstructionPaused *******************************
static FName NAME_ARTSBuilding_OnConstructionPaused = FName(TEXT("OnConstructionPaused"));
void ARTSBuilding::OnConstructionPaused()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSBuilding_OnConstructionPaused);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSBuilding_OnConstructionPaused_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_OnConstructionPaused_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "OnConstructionPaused", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnConstructionPaused_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_OnConstructionPaused_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBuilding_OnConstructionPaused()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_OnConstructionPaused_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBuilding Function OnConstructionPaused *********************************

// ********** Begin Class ARTSBuilding Function OnConstructionResumed ******************************
static FName NAME_ARTSBuilding_OnConstructionResumed = FName(TEXT("OnConstructionResumed"));
void ARTSBuilding::OnConstructionResumed()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSBuilding_OnConstructionResumed);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSBuilding_OnConstructionResumed_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_OnConstructionResumed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "OnConstructionResumed", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnConstructionResumed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_OnConstructionResumed_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBuilding_OnConstructionResumed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_OnConstructionResumed_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBuilding Function OnConstructionResumed ********************************

// ********** Begin Class ARTSBuilding Function OnConstructionStarted ******************************
static FName NAME_ARTSBuilding_OnConstructionStarted = FName(TEXT("OnConstructionStarted"));
void ARTSBuilding::OnConstructionStarted()
{
	UFunction* Func = FindFunctionChecked(NAME_ARTSBuilding_OnConstructionStarted);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ARTSBuilding_OnConstructionStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_OnConstructionStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "OnConstructionStarted", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_OnConstructionStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_OnConstructionStarted_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBuilding_OnConstructionStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_OnConstructionStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class ARTSBuilding Function OnConstructionStarted ********************************

// ********** Begin Class ARTSBuilding Function PauseConstruction **********************************
struct Z_Construct_UFunction_ARTSBuilding_PauseConstruction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_PauseConstruction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "PauseConstruction", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_PauseConstruction_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_PauseConstruction_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBuilding_PauseConstruction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_PauseConstruction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execPauseConstruction)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PauseConstruction();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function PauseConstruction ************************************

// ********** Begin Class ARTSBuilding Function ResumeConstruction *********************************
struct Z_Construct_UFunction_ARTSBuilding_ResumeConstruction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_ResumeConstruction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "ResumeConstruction", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_ResumeConstruction_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_ResumeConstruction_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBuilding_ResumeConstruction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_ResumeConstruction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execResumeConstruction)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResumeConstruction();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function ResumeConstruction ***********************************

// ********** Begin Class ARTSBuilding Function SetBuildingState ***********************************
struct Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics
{
	struct RTSBuilding_eventSetBuildingState_Parms
	{
		ERTSBuildingState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Building" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// State functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "State functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSBuilding_eventSetBuildingState_Parms, NewState), Z_Construct_UEnum_ArmorWars_ERTSBuildingState, METADATA_PARAMS(0, nullptr) }; // 1008412471
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "SetBuildingState", Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::RTSBuilding_eventSetBuildingState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::RTSBuilding_eventSetBuildingState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARTSBuilding_SetBuildingState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_SetBuildingState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execSetBuildingState)
{
	P_GET_ENUM(ERTSBuildingState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBuildingState(ERTSBuildingState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function SetBuildingState *************************************

// ********** Begin Class ARTSBuilding Function StartConstruction **********************************
struct Z_Construct_UFunction_ARTSBuilding_StartConstruction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Construction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Construction functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Construction functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_StartConstruction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "StartConstruction", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_StartConstruction_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_StartConstruction_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBuilding_StartConstruction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_StartConstruction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execStartConstruction)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartConstruction();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function StartConstruction ************************************

// ********** Begin Class ARTSBuilding Function StopAttacking **************************************
struct Z_Construct_UFunction_ARTSBuilding_StopAttacking_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Combat" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARTSBuilding_StopAttacking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_ARTSBuilding, nullptr, "StopAttacking", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARTSBuilding_StopAttacking_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARTSBuilding_StopAttacking_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_ARTSBuilding_StopAttacking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARTSBuilding_StopAttacking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARTSBuilding::execStopAttacking)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopAttacking();
	P_NATIVE_END;
}
// ********** End Class ARTSBuilding Function StopAttacking ****************************************

// ********** Begin Class ARTSBuilding *************************************************************
void ARTSBuilding::StaticRegisterNativesARTSBuilding()
{
	UClass* Class = ARTSBuilding::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AttackTarget", &ARTSBuilding::execAttackTarget },
		{ "CanAttackTarget", &ARTSBuilding::execCanAttackTarget },
		{ "CanProduceUnits", &ARTSBuilding::execCanProduceUnits },
		{ "CanTargetDomain", &ARTSBuilding::execCanTargetDomain },
		{ "CompleteConstruction", &ARTSBuilding::execCompleteConstruction },
		{ "FindNearestEnemy", &ARTSBuilding::execFindNearestEnemy },
		{ "GetBuildingState", &ARTSBuilding::execGetBuildingState },
		{ "GetBuildingType", &ARTSBuilding::execGetBuildingType },
		{ "GetConstructionTimeRemaining", &ARTSBuilding::execGetConstructionTimeRemaining },
		{ "GetCurrentTarget", &ARTSBuilding::execGetCurrentTarget },
		{ "GetFactoryComponent", &ARTSBuilding::execGetFactoryComponent },
		{ "GetMaxAttackRange", &ARTSBuilding::execGetMaxAttackRange },
		{ "GetNetPowerGeneration", &ARTSBuilding::execGetNetPowerGeneration },
		{ "GetTotalDamagePerSecond", &ARTSBuilding::execGetTotalDamagePerSecond },
		{ "HasFactory", &ARTSBuilding::execHasFactory },
		{ "HasWeapons", &ARTSBuilding::execHasWeapons },
		{ "IsAttacking", &ARTSBuilding::execIsAttacking },
		{ "IsConsumingPower", &ARTSBuilding::execIsConsumingPower },
		{ "IsDefensiveBuilding", &ARTSBuilding::execIsDefensiveBuilding },
		{ "IsGeneratingPower", &ARTSBuilding::execIsGeneratingPower },
		{ "IsInAttackRange", &ARTSBuilding::execIsInAttackRange },
		{ "IsOperational", &ARTSBuilding::execIsOperational },
		{ "IsProductionBuilding", &ARTSBuilding::execIsProductionBuilding },
		{ "IsResourceBuilding", &ARTSBuilding::execIsResourceBuilding },
		{ "IsUnderConstruction", &ARTSBuilding::execIsUnderConstruction },
		{ "PauseConstruction", &ARTSBuilding::execPauseConstruction },
		{ "ResumeConstruction", &ARTSBuilding::execResumeConstruction },
		{ "SetBuildingState", &ARTSBuilding::execSetBuildingState },
		{ "StartConstruction", &ARTSBuilding::execStartConstruction },
		{ "StopAttacking", &ARTSBuilding::execStopAttacking },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_ARTSBuilding;
UClass* ARTSBuilding::GetPrivateStaticClass()
{
	using TClass = ARTSBuilding;
	if (!Z_Registration_Info_UClass_ARTSBuilding.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSBuilding"),
			Z_Registration_Info_UClass_ARTSBuilding.InnerSingleton,
			StaticRegisterNativesARTSBuilding,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_ARTSBuilding.InnerSingleton;
}
UClass* Z_Construct_UClass_ARTSBuilding_NoRegister()
{
	return ARTSBuilding::GetPrivateStaticClass();
}
struct Z_Construct_UClass_ARTSBuilding_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Building class for all static RTS structures\n * Inherits from ARTSBaseActor and adds building-specific functionality\n * such as construction, production, and defensive capabilities\n */" },
#endif
		{ "IncludePath", "RTSBuilding.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Building class for all static RTS structures\nInherits from ARTSBaseActor and adds building-specific functionality\nsuch as construction, production, and defensive capabilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponController_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FactoryComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuildingType_MetaData[] = {
		{ "Category", "RTS|Building" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Building type\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Building type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuildingState_MetaData[] = {
		{ "Category", "RTS|Building" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current building state\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current building state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstructionProgress_MetaData[] = {
		{ "Category", "RTS|Construction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Construction progress (0.0 to 1.0)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Construction progress (0.0 to 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstructionTime_MetaData[] = {
		{ "Category", "RTS|Construction" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Time required to complete construction (in seconds)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time required to complete construction (in seconds)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bConstructionPaused_MetaData[] = {
		{ "Category", "RTS|Construction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether construction is paused\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether construction is paused" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PowerConsumption_MetaData[] = {
		{ "Category", "RTS|Power" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Power consumption (if applicable)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Power consumption (if applicable)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PowerGeneration_MetaData[] = {
		{ "Category", "RTS|Power" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Power generation (if applicable)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Power generation (if applicable)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionRange_MetaData[] = {
		{ "Category", "RTS|Vision" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Vision range\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vision range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceCost_MetaData[] = {
		{ "Category", "RTS|Economy" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Resource cost to build this building\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource cost to build this building" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceGenerationRate_MetaData[] = {
		{ "Category", "RTS|Economy" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Resource generation rate (resources per second, if applicable)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource generation rate (resources per second, if applicable)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBuildingStateChanged_MetaData[] = {
		{ "Category", "RTS|Events" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnConstructionProgress_MetaData[] = {
		{ "Category", "RTS|Events" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCombatChanged_MetaData[] = {
		{ "Category", "RTS|Events" },
		{ "ModuleRelativePath", "Public/RTSBuilding.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponController;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FactoryComponent;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BuildingType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BuildingType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BuildingState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BuildingState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConstructionProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConstructionTime;
	static void NewProp_bConstructionPaused_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bConstructionPaused;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PowerConsumption;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PowerGeneration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisionRange;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ResourceCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResourceGenerationRate;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBuildingStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnConstructionProgress;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCombatChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARTSBuilding_AttackTarget, "AttackTarget" }, // 647643885
		{ &Z_Construct_UFunction_ARTSBuilding_CanAttackTarget, "CanAttackTarget" }, // 2286761679
		{ &Z_Construct_UFunction_ARTSBuilding_CanProduceUnits, "CanProduceUnits" }, // 3461699318
		{ &Z_Construct_UFunction_ARTSBuilding_CanTargetDomain, "CanTargetDomain" }, // 2943575428
		{ &Z_Construct_UFunction_ARTSBuilding_CompleteConstruction, "CompleteConstruction" }, // 3621721040
		{ &Z_Construct_UFunction_ARTSBuilding_FindNearestEnemy, "FindNearestEnemy" }, // 404268717
		{ &Z_Construct_UFunction_ARTSBuilding_GetBuildingState, "GetBuildingState" }, // 40913385
		{ &Z_Construct_UFunction_ARTSBuilding_GetBuildingType, "GetBuildingType" }, // 352021513
		{ &Z_Construct_UFunction_ARTSBuilding_GetConstructionTimeRemaining, "GetConstructionTimeRemaining" }, // 2132116493
		{ &Z_Construct_UFunction_ARTSBuilding_GetCurrentTarget, "GetCurrentTarget" }, // 2341449000
		{ &Z_Construct_UFunction_ARTSBuilding_GetFactoryComponent, "GetFactoryComponent" }, // 1132313498
		{ &Z_Construct_UFunction_ARTSBuilding_GetMaxAttackRange, "GetMaxAttackRange" }, // 1528909221
		{ &Z_Construct_UFunction_ARTSBuilding_GetNetPowerGeneration, "GetNetPowerGeneration" }, // 766170746
		{ &Z_Construct_UFunction_ARTSBuilding_GetTotalDamagePerSecond, "GetTotalDamagePerSecond" }, // 1926497607
		{ &Z_Construct_UFunction_ARTSBuilding_HasFactory, "HasFactory" }, // 859335270
		{ &Z_Construct_UFunction_ARTSBuilding_HasWeapons, "HasWeapons" }, // 189420030
		{ &Z_Construct_UFunction_ARTSBuilding_IsAttacking, "IsAttacking" }, // 3741846130
		{ &Z_Construct_UFunction_ARTSBuilding_IsConsumingPower, "IsConsumingPower" }, // 433774836
		{ &Z_Construct_UFunction_ARTSBuilding_IsDefensiveBuilding, "IsDefensiveBuilding" }, // 254690345
		{ &Z_Construct_UFunction_ARTSBuilding_IsGeneratingPower, "IsGeneratingPower" }, // 1316162170
		{ &Z_Construct_UFunction_ARTSBuilding_IsInAttackRange, "IsInAttackRange" }, // 3902302737
		{ &Z_Construct_UFunction_ARTSBuilding_IsOperational, "IsOperational" }, // 3643343670
		{ &Z_Construct_UFunction_ARTSBuilding_IsProductionBuilding, "IsProductionBuilding" }, // 821267747
		{ &Z_Construct_UFunction_ARTSBuilding_IsResourceBuilding, "IsResourceBuilding" }, // 4040711897
		{ &Z_Construct_UFunction_ARTSBuilding_IsUnderConstruction, "IsUnderConstruction" }, // 1571410719
		{ &Z_Construct_UFunction_ARTSBuilding_OnAttackStarted, "OnAttackStarted" }, // 1824757171
		{ &Z_Construct_UFunction_ARTSBuilding_OnAttackStopped, "OnAttackStopped" }, // 1010973067
		{ &Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature, "OnBuildingCombat__DelegateSignature" }, // 3297911845
		{ &Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature, "OnBuildingStateChanged__DelegateSignature" }, // 210276671
		{ &Z_Construct_UFunction_ARTSBuilding_OnBuildingStateChangedEvent, "OnBuildingStateChangedEvent" }, // 3841436496
		{ &Z_Construct_UFunction_ARTSBuilding_OnConstructionCompleted, "OnConstructionCompleted" }, // 2527138015
		{ &Z_Construct_UFunction_ARTSBuilding_OnConstructionPaused, "OnConstructionPaused" }, // 974193743
		{ &Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature, "OnConstructionProgress__DelegateSignature" }, // 4244266507
		{ &Z_Construct_UFunction_ARTSBuilding_OnConstructionResumed, "OnConstructionResumed" }, // 3990489608
		{ &Z_Construct_UFunction_ARTSBuilding_OnConstructionStarted, "OnConstructionStarted" }, // 584398547
		{ &Z_Construct_UFunction_ARTSBuilding_PauseConstruction, "PauseConstruction" }, // 3652291109
		{ &Z_Construct_UFunction_ARTSBuilding_ResumeConstruction, "ResumeConstruction" }, // 273612485
		{ &Z_Construct_UFunction_ARTSBuilding_SetBuildingState, "SetBuildingState" }, // 3343206739
		{ &Z_Construct_UFunction_ARTSBuilding_StartConstruction, "StartConstruction" }, // 3983654507
		{ &Z_Construct_UFunction_ARTSBuilding_StopAttacking, "StopAttacking" }, // 1353661350
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARTSBuilding>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_WeaponController = { "WeaponController", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, WeaponController), Z_Construct_UClass_URTSWeaponController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponController_MetaData), NewProp_WeaponController_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_FactoryComponent = { "FactoryComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, FactoryComponent), Z_Construct_UClass_URTSFactoryComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FactoryComponent_MetaData), NewProp_FactoryComponent_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_BuildingType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_BuildingType = { "BuildingType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, BuildingType), Z_Construct_UEnum_ArmorWars_ERTSBuildingType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuildingType_MetaData), NewProp_BuildingType_MetaData) }; // 467938925
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_BuildingState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_BuildingState = { "BuildingState", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, BuildingState), Z_Construct_UEnum_ArmorWars_ERTSBuildingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuildingState_MetaData), NewProp_BuildingState_MetaData) }; // 1008412471
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_ConstructionProgress = { "ConstructionProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, ConstructionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstructionProgress_MetaData), NewProp_ConstructionProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_ConstructionTime = { "ConstructionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, ConstructionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstructionTime_MetaData), NewProp_ConstructionTime_MetaData) };
void Z_Construct_UClass_ARTSBuilding_Statics::NewProp_bConstructionPaused_SetBit(void* Obj)
{
	((ARTSBuilding*)Obj)->bConstructionPaused = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_bConstructionPaused = { "bConstructionPaused", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARTSBuilding), &Z_Construct_UClass_ARTSBuilding_Statics::NewProp_bConstructionPaused_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bConstructionPaused_MetaData), NewProp_bConstructionPaused_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_PowerConsumption = { "PowerConsumption", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, PowerConsumption), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PowerConsumption_MetaData), NewProp_PowerConsumption_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_PowerGeneration = { "PowerGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, PowerGeneration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PowerGeneration_MetaData), NewProp_PowerGeneration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_VisionRange = { "VisionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, VisionRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionRange_MetaData), NewProp_VisionRange_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_ResourceCost = { "ResourceCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, ResourceCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceCost_MetaData), NewProp_ResourceCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_ResourceGenerationRate = { "ResourceGenerationRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, ResourceGenerationRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceGenerationRate_MetaData), NewProp_ResourceGenerationRate_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_OnBuildingStateChanged = { "OnBuildingStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, OnBuildingStateChanged), Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBuildingStateChanged_MetaData), NewProp_OnBuildingStateChanged_MetaData) }; // 210276671
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_OnConstructionProgress = { "OnConstructionProgress", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, OnConstructionProgress), Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnConstructionProgress_MetaData), NewProp_OnConstructionProgress_MetaData) }; // 4244266507
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARTSBuilding_Statics::NewProp_OnCombatChanged = { "OnCombatChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARTSBuilding, OnCombatChanged), Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCombatChanged_MetaData), NewProp_OnCombatChanged_MetaData) }; // 3297911845
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARTSBuilding_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_WeaponController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_FactoryComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_BuildingType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_BuildingType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_BuildingState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_BuildingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_ConstructionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_ConstructionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_bConstructionPaused,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_PowerConsumption,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_PowerGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_VisionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_ResourceCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_ResourceGenerationRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_OnBuildingStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_OnConstructionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARTSBuilding_Statics::NewProp_OnCombatChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSBuilding_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARTSBuilding_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ARTSBaseActor,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSBuilding_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARTSBuilding_Statics::ClassParams = {
	&ARTSBuilding::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARTSBuilding_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARTSBuilding_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARTSBuilding_Statics::Class_MetaDataParams), Z_Construct_UClass_ARTSBuilding_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARTSBuilding()
{
	if (!Z_Registration_Info_UClass_ARTSBuilding.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARTSBuilding.OuterSingleton, Z_Construct_UClass_ARTSBuilding_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARTSBuilding.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARTSBuilding);
ARTSBuilding::~ARTSBuilding() {}
// ********** End Class ARTSBuilding ***************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSBuildingState_StaticEnum, TEXT("ERTSBuildingState"), &Z_Registration_Info_UEnum_ERTSBuildingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1008412471U) },
		{ ERTSBuildingType_StaticEnum, TEXT("ERTSBuildingType"), &Z_Registration_Info_UEnum_ERTSBuildingType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 467938925U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARTSBuilding, ARTSBuilding::StaticClass, TEXT("ARTSBuilding"), &Z_Registration_Info_UClass_ARTSBuilding, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARTSBuilding), 1173048006U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h__Script_ArmorWars_2880480915(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSBuilding_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

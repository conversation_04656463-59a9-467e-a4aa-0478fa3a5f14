{"Version": "1.2", "Data": {"Source": "f:\\armorwars\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\armorwars\\module.armorwars.13.cpp", "ProvidedModule": "", "PCH": "f:\\armorwars\\intermediate\\build\\win64\\x64\\armorwarseditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["f:\\armorwars\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\armorwars\\definitions.armorwars.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunitaicomponent.gen.cpp", "f:\\armorwars\\source\\armorwars\\public\\rtsunitaicomponent.h", "f:\\armorwars\\source\\armorwars\\public\\rtsbaseactor.h", "f:\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsbaseactor.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunitaicomponent.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunitdatabase.gen.cpp", "f:\\armorwars\\source\\armorwars\\public\\rtsunitdatabase.h", "f:\\armorwars\\source\\armorwars\\public\\rtsfactorycomponent.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsfactorycomponent.generated.h", "f:\\armorwars\\source\\armorwars\\public\\rtsbuilding.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsbuilding.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunitdatabase.generated.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunitselectioninterface.gen.cpp", "f:\\armorwars\\source\\armorwars\\public\\interfaces\\rtsunitselectioninterface.h", "f:\\armorwars\\intermediate\\build\\win64\\unrealeditor\\inc\\armorwars\\uht\\rtsunitselectioninterface.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}
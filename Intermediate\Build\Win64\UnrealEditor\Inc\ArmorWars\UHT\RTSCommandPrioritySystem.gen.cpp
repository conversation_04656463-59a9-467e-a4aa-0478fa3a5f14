// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSCommandPrioritySystem.h"
#include "RTSCommand.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSCommandPrioritySystem() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandPrioritySystem();
ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandPrioritySystem_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandSource();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandType();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSCommand();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSCommandContext();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSPriorityCommand();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSCommandSource *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSCommandSource;
static UEnum* ERTSCommandSource_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSCommandSource.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSCommandSource.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSCommandSource, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSCommandSource"));
	}
	return Z_Registration_Info_UEnum_ERTSCommandSource.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSCommandSource>()
{
	return ERTSCommandSource_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSCommandSource_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AIController.DisplayName", "AI Controller" },
		{ "AIController.Name", "ERTSCommandSource::AIController" },
		{ "Autonomous.DisplayName", "Autonomous" },
		{ "Autonomous.Name", "ERTSCommandSource::Autonomous" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command source types for priority determination\n" },
#endif
		{ "Emergency.DisplayName", "Emergency" },
		{ "Emergency.Name", "ERTSCommandSource::Emergency" },
		{ "Formation.DisplayName", "Formation" },
		{ "Formation.Name", "ERTSCommandSource::Formation" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
		{ "Player.DisplayName", "Player" },
		{ "Player.Name", "ERTSCommandSource::Player" },
		{ "System.DisplayName", "System" },
		{ "System.Name", "ERTSCommandSource::System" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command source types for priority determination" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSCommandSource::Player", (int64)ERTSCommandSource::Player },
		{ "ERTSCommandSource::AIController", (int64)ERTSCommandSource::AIController },
		{ "ERTSCommandSource::Formation", (int64)ERTSCommandSource::Formation },
		{ "ERTSCommandSource::Autonomous", (int64)ERTSCommandSource::Autonomous },
		{ "ERTSCommandSource::Emergency", (int64)ERTSCommandSource::Emergency },
		{ "ERTSCommandSource::System", (int64)ERTSCommandSource::System },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSCommandSource_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSCommandSource",
	"ERTSCommandSource",
	Z_Construct_UEnum_ArmorWars_ERTSCommandSource_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSCommandSource_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSCommandSource_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSCommandSource_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandSource()
{
	if (!Z_Registration_Info_UEnum_ERTSCommandSource.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSCommandSource.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSCommandSource_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSCommandSource.InnerSingleton;
}
// ********** End Enum ERTSCommandSource ***********************************************************

// ********** Begin ScriptStruct FRTSCommandContext ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSCommandContext;
class UScriptStruct* FRTSCommandContext::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSCommandContext.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSCommandContext.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSCommandContext, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSCommandContext"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSCommandContext.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSCommandContext_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command execution context\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command execution context" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "Category", "Command Context" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command metadata\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command metadata" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IssuedTime_MetaData[] = {
		{ "Category", "Command Context" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExecutionDeadline_MetaData[] = {
		{ "Category", "Command Context" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeInterrupted_MetaData[] = {
		{ "Category", "Command Context" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 0 = no deadline\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "0 = no deadline" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresImmediate_MetaData[] = {
		{ "Category", "Command Context" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SequenceNumber_MetaData[] = {
		{ "Category", "Command Context" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommandIssuer_MetaData[] = {
		{ "Category", "Command Context" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Issuer information\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Issuer information" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IssuerID_MetaData[] = {
		{ "Category", "Command Context" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Source_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Source;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_IssuedTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExecutionDeadline;
	static void NewProp_bCanBeInterrupted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeInterrupted;
	static void NewProp_bRequiresImmediate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresImmediate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SequenceNumber;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_CommandIssuer;
	static const UECodeGen_Private::FStrPropertyParams NewProp_IssuerID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSCommandContext>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_Source_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommandContext, Source), Z_Construct_UEnum_ArmorWars_ERTSCommandSource, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) }; // 3429210587
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_IssuedTime = { "IssuedTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommandContext, IssuedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IssuedTime_MetaData), NewProp_IssuedTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_ExecutionDeadline = { "ExecutionDeadline", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommandContext, ExecutionDeadline), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExecutionDeadline_MetaData), NewProp_ExecutionDeadline_MetaData) };
void Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_bCanBeInterrupted_SetBit(void* Obj)
{
	((FRTSCommandContext*)Obj)->bCanBeInterrupted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_bCanBeInterrupted = { "bCanBeInterrupted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSCommandContext), &Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_bCanBeInterrupted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeInterrupted_MetaData), NewProp_bCanBeInterrupted_MetaData) };
void Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_bRequiresImmediate_SetBit(void* Obj)
{
	((FRTSCommandContext*)Obj)->bRequiresImmediate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_bRequiresImmediate = { "bRequiresImmediate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSCommandContext), &Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_bRequiresImmediate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresImmediate_MetaData), NewProp_bRequiresImmediate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_SequenceNumber = { "SequenceNumber", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommandContext, SequenceNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SequenceNumber_MetaData), NewProp_SequenceNumber_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_CommandIssuer = { "CommandIssuer", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommandContext, CommandIssuer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommandIssuer_MetaData), NewProp_CommandIssuer_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_IssuerID = { "IssuerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSCommandContext, IssuerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IssuerID_MetaData), NewProp_IssuerID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSCommandContext_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_Source_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_IssuedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_ExecutionDeadline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_bCanBeInterrupted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_bRequiresImmediate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_SequenceNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_CommandIssuer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewProp_IssuerID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCommandContext_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSCommandContext_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSCommandContext",
	Z_Construct_UScriptStruct_FRTSCommandContext_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCommandContext_Statics::PropPointers),
	sizeof(FRTSCommandContext),
	alignof(FRTSCommandContext),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSCommandContext_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSCommandContext_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSCommandContext()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSCommandContext.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSCommandContext.InnerSingleton, Z_Construct_UScriptStruct_FRTSCommandContext_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSCommandContext.InnerSingleton;
}
// ********** End ScriptStruct FRTSCommandContext **************************************************

// ********** Begin ScriptStruct FRTSPriorityCommand ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSPriorityCommand;
class UScriptStruct* FRTSPriorityCommand::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSPriorityCommand.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSPriorityCommand.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSPriorityCommand, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSPriorityCommand"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSPriorityCommand.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enhanced command with priority context\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced command with priority context" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "Category", "Priority Command" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Base command\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base command" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Context_MetaData[] = {
		{ "Category", "Priority Command" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority context\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority context" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PriorityScore_MetaData[] = {
		{ "Category", "Priority Command" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Calculated priority score\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calculated priority score" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Context;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PriorityScore;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSPriorityCommand>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSPriorityCommand, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSPriorityCommand, Context), Z_Construct_UScriptStruct_FRTSCommandContext, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Context_MetaData), NewProp_Context_MetaData) }; // 3643628127
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::NewProp_PriorityScore = { "PriorityScore", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSPriorityCommand, PriorityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PriorityScore_MetaData), NewProp_PriorityScore_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::NewProp_Context,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::NewProp_PriorityScore,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSPriorityCommand",
	Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::PropPointers),
	sizeof(FRTSPriorityCommand),
	alignof(FRTSPriorityCommand),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSPriorityCommand()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSPriorityCommand.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSPriorityCommand.InnerSingleton, Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSPriorityCommand.InnerSingleton;
}
// ********** End ScriptStruct FRTSPriorityCommand *************************************************

// ********** Begin Delegate FOnCommandAdded *******************************************************
struct Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics
{
	struct RTSCommandPrioritySystem_eventOnCommandAdded_Parms
	{
		FRTSPriorityCommand PriorityCommand;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PriorityCommand_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PriorityCommand;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::NewProp_PriorityCommand = { "PriorityCommand", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventOnCommandAdded_Parms, PriorityCommand), Z_Construct_UScriptStruct_FRTSPriorityCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PriorityCommand_MetaData), NewProp_PriorityCommand_MetaData) }; // 1401229335
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::NewProp_PriorityCommand,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "OnCommandAdded__DelegateSignature", Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::RTSCommandPrioritySystem_eventOnCommandAdded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::RTSCommandPrioritySystem_eventOnCommandAdded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSCommandPrioritySystem::FOnCommandAdded_DelegateWrapper(const FMulticastScriptDelegate& OnCommandAdded, FRTSPriorityCommand const& PriorityCommand)
{
	struct RTSCommandPrioritySystem_eventOnCommandAdded_Parms
	{
		FRTSPriorityCommand PriorityCommand;
	};
	RTSCommandPrioritySystem_eventOnCommandAdded_Parms Parms;
	Parms.PriorityCommand=PriorityCommand;
	OnCommandAdded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCommandAdded *********************************************************

// ********** Begin Delegate FOnCommandExecuted ****************************************************
struct Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics
{
	struct RTSCommandPrioritySystem_eventOnCommandExecuted_Parms
	{
		FRTSPriorityCommand PriorityCommand;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PriorityCommand_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PriorityCommand;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::NewProp_PriorityCommand = { "PriorityCommand", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventOnCommandExecuted_Parms, PriorityCommand), Z_Construct_UScriptStruct_FRTSPriorityCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PriorityCommand_MetaData), NewProp_PriorityCommand_MetaData) }; // 1401229335
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::NewProp_PriorityCommand,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "OnCommandExecuted__DelegateSignature", Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::RTSCommandPrioritySystem_eventOnCommandExecuted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::RTSCommandPrioritySystem_eventOnCommandExecuted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSCommandPrioritySystem::FOnCommandExecuted_DelegateWrapper(const FMulticastScriptDelegate& OnCommandExecuted, FRTSPriorityCommand const& PriorityCommand)
{
	struct RTSCommandPrioritySystem_eventOnCommandExecuted_Parms
	{
		FRTSPriorityCommand PriorityCommand;
	};
	RTSCommandPrioritySystem_eventOnCommandExecuted_Parms Parms;
	Parms.PriorityCommand=PriorityCommand;
	OnCommandExecuted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCommandExecuted ******************************************************

// ********** Begin Delegate FOnCommandRemoved *****************************************************
struct Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics
{
	struct RTSCommandPrioritySystem_eventOnCommandRemoved_Parms
	{
		FRTSPriorityCommand PriorityCommand;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PriorityCommand_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PriorityCommand;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::NewProp_PriorityCommand = { "PriorityCommand", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventOnCommandRemoved_Parms, PriorityCommand), Z_Construct_UScriptStruct_FRTSPriorityCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PriorityCommand_MetaData), NewProp_PriorityCommand_MetaData) }; // 1401229335
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::NewProp_PriorityCommand,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "OnCommandRemoved__DelegateSignature", Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::RTSCommandPrioritySystem_eventOnCommandRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::RTSCommandPrioritySystem_eventOnCommandRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSCommandPrioritySystem::FOnCommandRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnCommandRemoved, FRTSPriorityCommand const& PriorityCommand)
{
	struct RTSCommandPrioritySystem_eventOnCommandRemoved_Parms
	{
		FRTSPriorityCommand PriorityCommand;
	};
	RTSCommandPrioritySystem_eventOnCommandRemoved_Parms Parms;
	Parms.PriorityCommand=PriorityCommand;
	OnCommandRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCommandRemoved *******************************************************

// ********** Begin Delegate FOnQueueChanged *******************************************************
struct Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "OnQueueChanged__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSCommandPrioritySystem::FOnQueueChanged_DelegateWrapper(const FMulticastScriptDelegate& OnQueueChanged)
{
	OnQueueChanged.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnQueueChanged *********************************************************

// ********** Begin Class URTSCommandPrioritySystem Function AddAICommand **************************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics
{
	struct RTSCommandPrioritySystem_eventAddAICommand_Parms
	{
		FRTSCommand Command;
		AActor* AIController;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AIController;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventAddAICommand_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::NewProp_AIController = { "AIController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventAddAICommand_Parms, AIController), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventAddAICommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventAddAICommand_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::NewProp_AIController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "AddAICommand", Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::RTSCommandPrioritySystem_eventAddAICommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::RTSCommandPrioritySystem_eventAddAICommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execAddAICommand)
{
	P_GET_STRUCT_REF(FRTSCommand,Z_Param_Out_Command);
	P_GET_OBJECT(AActor,Z_Param_AIController);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddAICommand(Z_Param_Out_Command,Z_Param_AIController);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function AddAICommand ****************************

// ********** Begin Class URTSCommandPrioritySystem Function AddCommand ****************************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics
{
	struct RTSCommandPrioritySystem_eventAddCommand_Parms
	{
		FRTSCommand Command;
		FRTSCommandContext Context;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Command management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Context_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Context;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventAddCommand_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventAddCommand_Parms, Context), Z_Construct_UScriptStruct_FRTSCommandContext, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Context_MetaData), NewProp_Context_MetaData) }; // 3643628127
void Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventAddCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventAddCommand_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::NewProp_Context,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "AddCommand", Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::RTSCommandPrioritySystem_eventAddCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::RTSCommandPrioritySystem_eventAddCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execAddCommand)
{
	P_GET_STRUCT_REF(FRTSCommand,Z_Param_Out_Command);
	P_GET_STRUCT_REF(FRTSCommandContext,Z_Param_Out_Context);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddCommand(Z_Param_Out_Command,Z_Param_Out_Context);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function AddCommand ******************************

// ********** Begin Class URTSCommandPrioritySystem Function AddEmergencyCommand *******************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics
{
	struct RTSCommandPrioritySystem_eventAddEmergencyCommand_Parms
	{
		FRTSCommand Command;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventAddEmergencyCommand_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
void Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventAddEmergencyCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventAddEmergencyCommand_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "AddEmergencyCommand", Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::RTSCommandPrioritySystem_eventAddEmergencyCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::RTSCommandPrioritySystem_eventAddEmergencyCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execAddEmergencyCommand)
{
	P_GET_STRUCT_REF(FRTSCommand,Z_Param_Out_Command);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddEmergencyCommand(Z_Param_Out_Command);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function AddEmergencyCommand *********************

// ********** Begin Class URTSCommandPrioritySystem Function AddFormationCommand *******************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics
{
	struct RTSCommandPrioritySystem_eventAddFormationCommand_Parms
	{
		FRTSCommand Command;
		AActor* FormationLeader;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FormationLeader;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventAddFormationCommand_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::NewProp_FormationLeader = { "FormationLeader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventAddFormationCommand_Parms, FormationLeader), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventAddFormationCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventAddFormationCommand_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::NewProp_FormationLeader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "AddFormationCommand", Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::RTSCommandPrioritySystem_eventAddFormationCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::RTSCommandPrioritySystem_eventAddFormationCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execAddFormationCommand)
{
	P_GET_STRUCT_REF(FRTSCommand,Z_Param_Out_Command);
	P_GET_OBJECT(AActor,Z_Param_FormationLeader);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddFormationCommand(Z_Param_Out_Command,Z_Param_FormationLeader);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function AddFormationCommand *********************

// ********** Begin Class URTSCommandPrioritySystem Function AddPlayerCommand **********************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics
{
	struct RTSCommandPrioritySystem_eventAddPlayerCommand_Parms
	{
		FRTSCommand Command;
		AActor* Player;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventAddPlayerCommand_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventAddPlayerCommand_Parms, Player), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventAddPlayerCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventAddPlayerCommand_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "AddPlayerCommand", Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::RTSCommandPrioritySystem_eventAddPlayerCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::RTSCommandPrioritySystem_eventAddPlayerCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execAddPlayerCommand)
{
	P_GET_STRUCT_REF(FRTSCommand,Z_Param_Out_Command);
	P_GET_OBJECT(AActor,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddPlayerCommand(Z_Param_Out_Command,Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function AddPlayerCommand ************************

// ********** Begin Class URTSCommandPrioritySystem Function CalculateCommandPriority **************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics
{
	struct RTSCommandPrioritySystem_eventCalculateCommandPriority_Parms
	{
		FRTSCommand Command;
		FRTSCommandContext Context;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority calculation\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority calculation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Context_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Command;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Context;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventCalculateCommandPriority_Parms, Command), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) }; // 1544416680
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventCalculateCommandPriority_Parms, Context), Z_Construct_UScriptStruct_FRTSCommandContext, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Context_MetaData), NewProp_Context_MetaData) }; // 3643628127
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventCalculateCommandPriority_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::NewProp_Context,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "CalculateCommandPriority", Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::RTSCommandPrioritySystem_eventCalculateCommandPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::RTSCommandPrioritySystem_eventCalculateCommandPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execCalculateCommandPriority)
{
	P_GET_STRUCT_REF(FRTSCommand,Z_Param_Out_Command);
	P_GET_STRUCT_REF(FRTSCommandContext,Z_Param_Out_Context);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateCommandPriority(Z_Param_Out_Command,Z_Param_Out_Context);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function CalculateCommandPriority ****************

// ********** Begin Class URTSCommandPrioritySystem Function CalculateDeadlineFactor ***************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics
{
	struct RTSCommandPrioritySystem_eventCalculateDeadlineFactor_Parms
	{
		FRTSCommandContext Context;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Context_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Context;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventCalculateDeadlineFactor_Parms, Context), Z_Construct_UScriptStruct_FRTSCommandContext, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Context_MetaData), NewProp_Context_MetaData) }; // 3643628127
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventCalculateDeadlineFactor_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::NewProp_Context,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "CalculateDeadlineFactor", Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::RTSCommandPrioritySystem_eventCalculateDeadlineFactor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::RTSCommandPrioritySystem_eventCalculateDeadlineFactor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execCalculateDeadlineFactor)
{
	P_GET_STRUCT_REF(FRTSCommandContext,Z_Param_Out_Context);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateDeadlineFactor(Z_Param_Out_Context);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function CalculateDeadlineFactor *****************

// ********** Begin Class URTSCommandPrioritySystem Function CalculateUrgencyFactor ****************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics
{
	struct RTSCommandPrioritySystem_eventCalculateUrgencyFactor_Parms
	{
		FRTSCommandContext Context;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Context_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Context;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventCalculateUrgencyFactor_Parms, Context), Z_Construct_UScriptStruct_FRTSCommandContext, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Context_MetaData), NewProp_Context_MetaData) }; // 3643628127
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventCalculateUrgencyFactor_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::NewProp_Context,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "CalculateUrgencyFactor", Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::RTSCommandPrioritySystem_eventCalculateUrgencyFactor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::RTSCommandPrioritySystem_eventCalculateUrgencyFactor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execCalculateUrgencyFactor)
{
	P_GET_STRUCT_REF(FRTSCommandContext,Z_Param_Out_Context);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateUrgencyFactor(Z_Param_Out_Context);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function CalculateUrgencyFactor ******************

// ********** Begin Class URTSCommandPrioritySystem Function CanInterruptCurrentCommand ************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics
{
	struct RTSCommandPrioritySystem_eventCanInterruptCurrentCommand_Parms
	{
		FRTSCommand NewCommand;
		FRTSCommandContext NewContext;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewCommand_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewCommand;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewContext;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::NewProp_NewCommand = { "NewCommand", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventCanInterruptCurrentCommand_Parms, NewCommand), Z_Construct_UScriptStruct_FRTSCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewCommand_MetaData), NewProp_NewCommand_MetaData) }; // 1544416680
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::NewProp_NewContext = { "NewContext", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventCanInterruptCurrentCommand_Parms, NewContext), Z_Construct_UScriptStruct_FRTSCommandContext, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewContext_MetaData), NewProp_NewContext_MetaData) }; // 3643628127
void Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventCanInterruptCurrentCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventCanInterruptCurrentCommand_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::NewProp_NewCommand,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::NewProp_NewContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "CanInterruptCurrentCommand", Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::RTSCommandPrioritySystem_eventCanInterruptCurrentCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::RTSCommandPrioritySystem_eventCanInterruptCurrentCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execCanInterruptCurrentCommand)
{
	P_GET_STRUCT_REF(FRTSCommand,Z_Param_Out_NewCommand);
	P_GET_STRUCT_REF(FRTSCommandContext,Z_Param_Out_NewContext);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanInterruptCurrentCommand(Z_Param_Out_NewCommand,Z_Param_Out_NewContext);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function CanInterruptCurrentCommand **************

// ********** Begin Class URTSCommandPrioritySystem Function ClearQueue ****************************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_ClearQueue_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Queue management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Queue management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_ClearQueue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "ClearQueue", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_ClearQueue_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_ClearQueue_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_ClearQueue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_ClearQueue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execClearQueue)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearQueue();
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function ClearQueue ******************************

// ********** Begin Class URTSCommandPrioritySystem Function ExecuteCommand ************************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics
{
	struct RTSCommandPrioritySystem_eventExecuteCommand_Parms
	{
		FRTSPriorityCommand PriorityCommand;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PriorityCommand_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PriorityCommand;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::NewProp_PriorityCommand = { "PriorityCommand", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventExecuteCommand_Parms, PriorityCommand), Z_Construct_UScriptStruct_FRTSPriorityCommand, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PriorityCommand_MetaData), NewProp_PriorityCommand_MetaData) }; // 1401229335
void Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventExecuteCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventExecuteCommand_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::NewProp_PriorityCommand,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "ExecuteCommand", Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::RTSCommandPrioritySystem_eventExecuteCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::RTSCommandPrioritySystem_eventExecuteCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execExecuteCommand)
{
	P_GET_STRUCT_REF(FRTSPriorityCommand,Z_Param_Out_PriorityCommand);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecuteCommand(Z_Param_Out_PriorityCommand);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function ExecuteCommand **************************

// ********** Begin Class URTSCommandPrioritySystem Function ExecuteHighestPriorityCommand *********
struct Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics
{
	struct RTSCommandPrioritySystem_eventExecuteHighestPriorityCommand_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Execution control\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execution control" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventExecuteHighestPriorityCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventExecuteHighestPriorityCommand_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "ExecuteHighestPriorityCommand", Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::RTSCommandPrioritySystem_eventExecuteHighestPriorityCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::RTSCommandPrioritySystem_eventExecuteHighestPriorityCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execExecuteHighestPriorityCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecuteHighestPriorityCommand();
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function ExecuteHighestPriorityCommand ***********

// ********** Begin Class URTSCommandPrioritySystem Function GetCommandsByPriority *****************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics
{
	struct RTSCommandPrioritySystem_eventGetCommandsByPriority_Parms
	{
		TArray<FRTSPriorityCommand> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSPriorityCommand, METADATA_PARAMS(0, nullptr) }; // 1401229335
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventGetCommandsByPriority_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1401229335
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "GetCommandsByPriority", Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::RTSCommandPrioritySystem_eventGetCommandsByPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::RTSCommandPrioritySystem_eventGetCommandsByPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execGetCommandsByPriority)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FRTSPriorityCommand>*)Z_Param__Result=P_THIS->GetCommandsByPriority();
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function GetCommandsByPriority *******************

// ********** Begin Class URTSCommandPrioritySystem Function GetCommandsBySource *******************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics
{
	struct RTSCommandPrioritySystem_eventGetCommandsBySource_Parms
	{
		ERTSCommandSource Source;
		TArray<FRTSPriorityCommand> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Source_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Source;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::NewProp_Source_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventGetCommandsBySource_Parms, Source), Z_Construct_UEnum_ArmorWars_ERTSCommandSource, METADATA_PARAMS(0, nullptr) }; // 3429210587
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSPriorityCommand, METADATA_PARAMS(0, nullptr) }; // 1401229335
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventGetCommandsBySource_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1401229335
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::NewProp_Source_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "GetCommandsBySource", Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::RTSCommandPrioritySystem_eventGetCommandsBySource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::RTSCommandPrioritySystem_eventGetCommandsBySource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execGetCommandsBySource)
{
	P_GET_ENUM(ERTSCommandSource,Z_Param_Source);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FRTSPriorityCommand>*)Z_Param__Result=P_THIS->GetCommandsBySource(ERTSCommandSource(Z_Param_Source));
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function GetCommandsBySource *********************

// ********** Begin Class URTSCommandPrioritySystem Function GetCommandTypePriorityMultiplier ******
struct Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics
{
	struct RTSCommandPrioritySystem_eventGetCommandTypePriorityMultiplier_Parms
	{
		ERTSCommandType CommandType;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CommandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CommandType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::NewProp_CommandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::NewProp_CommandType = { "CommandType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventGetCommandTypePriorityMultiplier_Parms, CommandType), Z_Construct_UEnum_ArmorWars_ERTSCommandType, METADATA_PARAMS(0, nullptr) }; // 4246000564
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventGetCommandTypePriorityMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::NewProp_CommandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::NewProp_CommandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "GetCommandTypePriorityMultiplier", Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::RTSCommandPrioritySystem_eventGetCommandTypePriorityMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::RTSCommandPrioritySystem_eventGetCommandTypePriorityMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execGetCommandTypePriorityMultiplier)
{
	P_GET_ENUM(ERTSCommandType,Z_Param_CommandType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCommandTypePriorityMultiplier(ERTSCommandType(Z_Param_CommandType));
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function GetCommandTypePriorityMultiplier ********

// ********** Begin Class URTSCommandPrioritySystem Function GetHighestPriorityCommand *************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics
{
	struct RTSCommandPrioritySystem_eventGetHighestPriorityCommand_Parms
	{
		FRTSPriorityCommand ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority queries\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority queries" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventGetHighestPriorityCommand_Parms, ReturnValue), Z_Construct_UScriptStruct_FRTSPriorityCommand, METADATA_PARAMS(0, nullptr) }; // 1401229335
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "GetHighestPriorityCommand", Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::RTSCommandPrioritySystem_eventGetHighestPriorityCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::RTSCommandPrioritySystem_eventGetHighestPriorityCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execGetHighestPriorityCommand)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FRTSPriorityCommand*)Z_Param__Result=P_THIS->GetHighestPriorityCommand();
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function GetHighestPriorityCommand ***************

// ********** Begin Class URTSCommandPrioritySystem Function GetQueueSize **************************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics
{
	struct RTSCommandPrioritySystem_eventGetQueueSize_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventGetQueueSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "GetQueueSize", Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::RTSCommandPrioritySystem_eventGetQueueSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::RTSCommandPrioritySystem_eventGetQueueSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execGetQueueSize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetQueueSize();
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function GetQueueSize ****************************

// ********** Begin Class URTSCommandPrioritySystem Function GetSourcePriorityMultiplier ***********
struct Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics
{
	struct RTSCommandPrioritySystem_eventGetSourcePriorityMultiplier_Parms
	{
		ERTSCommandSource Source;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Source_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Source;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::NewProp_Source_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventGetSourcePriorityMultiplier_Parms, Source), Z_Construct_UEnum_ArmorWars_ERTSCommandSource, METADATA_PARAMS(0, nullptr) }; // 3429210587
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventGetSourcePriorityMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::NewProp_Source_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "GetSourcePriorityMultiplier", Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::RTSCommandPrioritySystem_eventGetSourcePriorityMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::RTSCommandPrioritySystem_eventGetSourcePriorityMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execGetSourcePriorityMultiplier)
{
	P_GET_ENUM(ERTSCommandSource,Z_Param_Source);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetSourcePriorityMultiplier(ERTSCommandSource(Z_Param_Source));
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function GetSourcePriorityMultiplier *************

// ********** Begin Class URTSCommandPrioritySystem Function HasCommands ***************************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics
{
	struct RTSCommandPrioritySystem_eventHasCommands_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventHasCommands_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventHasCommands_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "HasCommands", Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::RTSCommandPrioritySystem_eventHasCommands_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::RTSCommandPrioritySystem_eventHasCommands_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execHasCommands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasCommands();
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function HasCommands *****************************

// ********** Begin Class URTSCommandPrioritySystem Function RemoveCommand *************************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics
{
	struct RTSCommandPrioritySystem_eventRemoveCommand_Parms
	{
		int32 Index;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Index;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::NewProp_Index = { "Index", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventRemoveCommand_Parms, Index), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventRemoveCommand_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventRemoveCommand_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::NewProp_Index,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "RemoveCommand", Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::RTSCommandPrioritySystem_eventRemoveCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::RTSCommandPrioritySystem_eventRemoveCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execRemoveCommand)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Index);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveCommand(Z_Param_Index);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function RemoveCommand ***************************

// ********** Begin Class URTSCommandPrioritySystem Function RemoveCommandsByIssuer ****************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics
{
	struct RTSCommandPrioritySystem_eventRemoveCommandsByIssuer_Parms
	{
		AActor* Issuer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Issuer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::NewProp_Issuer = { "Issuer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventRemoveCommandsByIssuer_Parms, Issuer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventRemoveCommandsByIssuer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventRemoveCommandsByIssuer_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::NewProp_Issuer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "RemoveCommandsByIssuer", Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::RTSCommandPrioritySystem_eventRemoveCommandsByIssuer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::RTSCommandPrioritySystem_eventRemoveCommandsByIssuer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execRemoveCommandsByIssuer)
{
	P_GET_OBJECT(AActor,Z_Param_Issuer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveCommandsByIssuer(Z_Param_Issuer);
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function RemoveCommandsByIssuer ******************

// ********** Begin Class URTSCommandPrioritySystem Function RemoveCommandsBySource ****************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics
{
	struct RTSCommandPrioritySystem_eventRemoveCommandsBySource_Parms
	{
		ERTSCommandSource Source;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Source_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Source;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::NewProp_Source_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSCommandPrioritySystem_eventRemoveCommandsBySource_Parms, Source), Z_Construct_UEnum_ArmorWars_ERTSCommandSource, METADATA_PARAMS(0, nullptr) }; // 3429210587
void Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSCommandPrioritySystem_eventRemoveCommandsBySource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSCommandPrioritySystem_eventRemoveCommandsBySource_Parms), &Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::NewProp_Source_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "RemoveCommandsBySource", Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::RTSCommandPrioritySystem_eventRemoveCommandsBySource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::RTSCommandPrioritySystem_eventRemoveCommandsBySource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execRemoveCommandsBySource)
{
	P_GET_ENUM(ERTSCommandSource,Z_Param_Source);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveCommandsBySource(ERTSCommandSource(Z_Param_Source));
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function RemoveCommandsBySource ******************

// ********** Begin Class URTSCommandPrioritySystem Function UpdatePriorities **********************
struct Z_Construct_UFunction_URTSCommandPrioritySystem_UpdatePriorities_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSCommandPrioritySystem_UpdatePriorities_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSCommandPrioritySystem, nullptr, "UpdatePriorities", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSCommandPrioritySystem_UpdatePriorities_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSCommandPrioritySystem_UpdatePriorities_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSCommandPrioritySystem_UpdatePriorities()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSCommandPrioritySystem_UpdatePriorities_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSCommandPrioritySystem::execUpdatePriorities)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePriorities();
	P_NATIVE_END;
}
// ********** End Class URTSCommandPrioritySystem Function UpdatePriorities ************************

// ********** Begin Class URTSCommandPrioritySystem ************************************************
void URTSCommandPrioritySystem::StaticRegisterNativesURTSCommandPrioritySystem()
{
	UClass* Class = URTSCommandPrioritySystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddAICommand", &URTSCommandPrioritySystem::execAddAICommand },
		{ "AddCommand", &URTSCommandPrioritySystem::execAddCommand },
		{ "AddEmergencyCommand", &URTSCommandPrioritySystem::execAddEmergencyCommand },
		{ "AddFormationCommand", &URTSCommandPrioritySystem::execAddFormationCommand },
		{ "AddPlayerCommand", &URTSCommandPrioritySystem::execAddPlayerCommand },
		{ "CalculateCommandPriority", &URTSCommandPrioritySystem::execCalculateCommandPriority },
		{ "CalculateDeadlineFactor", &URTSCommandPrioritySystem::execCalculateDeadlineFactor },
		{ "CalculateUrgencyFactor", &URTSCommandPrioritySystem::execCalculateUrgencyFactor },
		{ "CanInterruptCurrentCommand", &URTSCommandPrioritySystem::execCanInterruptCurrentCommand },
		{ "ClearQueue", &URTSCommandPrioritySystem::execClearQueue },
		{ "ExecuteCommand", &URTSCommandPrioritySystem::execExecuteCommand },
		{ "ExecuteHighestPriorityCommand", &URTSCommandPrioritySystem::execExecuteHighestPriorityCommand },
		{ "GetCommandsByPriority", &URTSCommandPrioritySystem::execGetCommandsByPriority },
		{ "GetCommandsBySource", &URTSCommandPrioritySystem::execGetCommandsBySource },
		{ "GetCommandTypePriorityMultiplier", &URTSCommandPrioritySystem::execGetCommandTypePriorityMultiplier },
		{ "GetHighestPriorityCommand", &URTSCommandPrioritySystem::execGetHighestPriorityCommand },
		{ "GetQueueSize", &URTSCommandPrioritySystem::execGetQueueSize },
		{ "GetSourcePriorityMultiplier", &URTSCommandPrioritySystem::execGetSourcePriorityMultiplier },
		{ "HasCommands", &URTSCommandPrioritySystem::execHasCommands },
		{ "RemoveCommand", &URTSCommandPrioritySystem::execRemoveCommand },
		{ "RemoveCommandsByIssuer", &URTSCommandPrioritySystem::execRemoveCommandsByIssuer },
		{ "RemoveCommandsBySource", &URTSCommandPrioritySystem::execRemoveCommandsBySource },
		{ "UpdatePriorities", &URTSCommandPrioritySystem::execUpdatePriorities },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSCommandPrioritySystem;
UClass* URTSCommandPrioritySystem::GetPrivateStaticClass()
{
	using TClass = URTSCommandPrioritySystem;
	if (!Z_Registration_Info_UClass_URTSCommandPrioritySystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSCommandPrioritySystem"),
			Z_Registration_Info_UClass_URTSCommandPrioritySystem.InnerSingleton,
			StaticRegisterNativesURTSCommandPrioritySystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSCommandPrioritySystem.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSCommandPrioritySystem_NoRegister()
{
	return URTSCommandPrioritySystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSCommandPrioritySystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Command priority system for managing command execution order\n * Handles command queuing, priority calculation, and interruption logic\n */" },
#endif
		{ "IncludePath", "RTSCommandPrioritySystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Command priority system for managing command execution order\nHandles command queuing, priority calculation, and interruption logic" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PriorityQueue_MetaData[] = {
		{ "Category", "Command Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority queue for commands\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority queue for commands" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Priority System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority system settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority system settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PriorityUpdateInterval_MetaData[] = {
		{ "Category", "Priority System" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxQueueSize_MetaData[] = {
		{ "Category", "Priority System" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoExecuteHighestPriority_MetaData[] = {
		{ "Category", "Priority System" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourcePriorityWeight_MetaData[] = {
		{ "Category", "Priority Weights" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority weights for different factors\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority weights for different factors" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommandPriorityWeight_MetaData[] = {
		{ "Category", "Priority Weights" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UrgencyWeight_MetaData[] = {
		{ "Category", "Priority Weights" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeadlineWeight_MetaData[] = {
		{ "Category", "Priority Weights" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImmediateWeight_MetaData[] = {
		{ "Category", "Priority Weights" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCommandAdded_MetaData[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCommandExecuted_MetaData[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCommandRemoved_MetaData[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnQueueChanged_MetaData[] = {
		{ "Category", "RTS Command Priority" },
		{ "ModuleRelativePath", "Public/RTSCommandPrioritySystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PriorityQueue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PriorityQueue;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PriorityUpdateInterval;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxQueueSize;
	static void NewProp_bAutoExecuteHighestPriority_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoExecuteHighestPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SourcePriorityWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CommandPriorityWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UrgencyWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeadlineWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ImmediateWeight;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCommandAdded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCommandExecuted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCommandRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnQueueChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_AddAICommand, "AddAICommand" }, // 1554769537
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_AddCommand, "AddCommand" }, // 3670389208
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_AddEmergencyCommand, "AddEmergencyCommand" }, // 4202731038
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_AddFormationCommand, "AddFormationCommand" }, // 1235046213
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_AddPlayerCommand, "AddPlayerCommand" }, // 2996209979
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateCommandPriority, "CalculateCommandPriority" }, // 3279211478
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateDeadlineFactor, "CalculateDeadlineFactor" }, // 3495111162
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_CalculateUrgencyFactor, "CalculateUrgencyFactor" }, // 704552258
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_CanInterruptCurrentCommand, "CanInterruptCurrentCommand" }, // 45030915
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_ClearQueue, "ClearQueue" }, // 2682972973
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteCommand, "ExecuteCommand" }, // 80385189
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_ExecuteHighestPriorityCommand, "ExecuteHighestPriorityCommand" }, // 2827550058
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsByPriority, "GetCommandsByPriority" }, // 868483717
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandsBySource, "GetCommandsBySource" }, // 3870179069
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_GetCommandTypePriorityMultiplier, "GetCommandTypePriorityMultiplier" }, // 3347105363
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_GetHighestPriorityCommand, "GetHighestPriorityCommand" }, // 31009326
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_GetQueueSize, "GetQueueSize" }, // 967442023
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_GetSourcePriorityMultiplier, "GetSourcePriorityMultiplier" }, // 4239889767
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_HasCommands, "HasCommands" }, // 2925165863
		{ &Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature, "OnCommandAdded__DelegateSignature" }, // 1949372310
		{ &Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature, "OnCommandExecuted__DelegateSignature" }, // 2907193324
		{ &Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature, "OnCommandRemoved__DelegateSignature" }, // 1304596515
		{ &Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature, "OnQueueChanged__DelegateSignature" }, // 1635451925
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommand, "RemoveCommand" }, // 1248290709
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsByIssuer, "RemoveCommandsByIssuer" }, // 874759624
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_RemoveCommandsBySource, "RemoveCommandsBySource" }, // 2111635440
		{ &Z_Construct_UFunction_URTSCommandPrioritySystem_UpdatePriorities, "UpdatePriorities" }, // 1510228631
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSCommandPrioritySystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_PriorityQueue_Inner = { "PriorityQueue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSPriorityCommand, METADATA_PARAMS(0, nullptr) }; // 1401229335
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_PriorityQueue = { "PriorityQueue", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, PriorityQueue), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PriorityQueue_MetaData), NewProp_PriorityQueue_MetaData) }; // 1401229335
void Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((URTSCommandPrioritySystem*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSCommandPrioritySystem), &Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_PriorityUpdateInterval = { "PriorityUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, PriorityUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PriorityUpdateInterval_MetaData), NewProp_PriorityUpdateInterval_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_MaxQueueSize = { "MaxQueueSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, MaxQueueSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxQueueSize_MetaData), NewProp_MaxQueueSize_MetaData) };
void Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_bAutoExecuteHighestPriority_SetBit(void* Obj)
{
	((URTSCommandPrioritySystem*)Obj)->bAutoExecuteHighestPriority = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_bAutoExecuteHighestPriority = { "bAutoExecuteHighestPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSCommandPrioritySystem), &Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_bAutoExecuteHighestPriority_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoExecuteHighestPriority_MetaData), NewProp_bAutoExecuteHighestPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_SourcePriorityWeight = { "SourcePriorityWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, SourcePriorityWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourcePriorityWeight_MetaData), NewProp_SourcePriorityWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_CommandPriorityWeight = { "CommandPriorityWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, CommandPriorityWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommandPriorityWeight_MetaData), NewProp_CommandPriorityWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_UrgencyWeight = { "UrgencyWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, UrgencyWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UrgencyWeight_MetaData), NewProp_UrgencyWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_DeadlineWeight = { "DeadlineWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, DeadlineWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeadlineWeight_MetaData), NewProp_DeadlineWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_ImmediateWeight = { "ImmediateWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, ImmediateWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImmediateWeight_MetaData), NewProp_ImmediateWeight_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_OnCommandAdded = { "OnCommandAdded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, OnCommandAdded), Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCommandAdded_MetaData), NewProp_OnCommandAdded_MetaData) }; // 1949372310
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_OnCommandExecuted = { "OnCommandExecuted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, OnCommandExecuted), Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCommandExecuted_MetaData), NewProp_OnCommandExecuted_MetaData) }; // 2907193324
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_OnCommandRemoved = { "OnCommandRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, OnCommandRemoved), Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCommandRemoved_MetaData), NewProp_OnCommandRemoved_MetaData) }; // 1304596515
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_OnQueueChanged = { "OnQueueChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSCommandPrioritySystem, OnQueueChanged), Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnQueueChanged_MetaData), NewProp_OnQueueChanged_MetaData) }; // 1635451925
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSCommandPrioritySystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_PriorityQueue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_PriorityQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_PriorityUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_MaxQueueSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_bAutoExecuteHighestPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_SourcePriorityWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_CommandPriorityWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_UrgencyWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_DeadlineWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_ImmediateWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_OnCommandAdded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_OnCommandExecuted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_OnCommandRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSCommandPrioritySystem_Statics::NewProp_OnQueueChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandPrioritySystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSCommandPrioritySystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandPrioritySystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSCommandPrioritySystem_Statics::ClassParams = {
	&URTSCommandPrioritySystem::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSCommandPrioritySystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandPrioritySystem_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSCommandPrioritySystem_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSCommandPrioritySystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSCommandPrioritySystem()
{
	if (!Z_Registration_Info_UClass_URTSCommandPrioritySystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSCommandPrioritySystem.OuterSingleton, Z_Construct_UClass_URTSCommandPrioritySystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSCommandPrioritySystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSCommandPrioritySystem);
URTSCommandPrioritySystem::~URTSCommandPrioritySystem() {}
// ********** End Class URTSCommandPrioritySystem **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSCommandSource_StaticEnum, TEXT("ERTSCommandSource"), &Z_Registration_Info_UEnum_ERTSCommandSource, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3429210587U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRTSCommandContext::StaticStruct, Z_Construct_UScriptStruct_FRTSCommandContext_Statics::NewStructOps, TEXT("RTSCommandContext"), &Z_Registration_Info_UScriptStruct_FRTSCommandContext, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSCommandContext), 3643628127U) },
		{ FRTSPriorityCommand::StaticStruct, Z_Construct_UScriptStruct_FRTSPriorityCommand_Statics::NewStructOps, TEXT("RTSPriorityCommand"), &Z_Registration_Info_UScriptStruct_FRTSPriorityCommand, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSPriorityCommand), 1401229335U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSCommandPrioritySystem, URTSCommandPrioritySystem::StaticClass, TEXT("URTSCommandPrioritySystem"), &Z_Registration_Info_UClass_URTSCommandPrioritySystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSCommandPrioritySystem), 1685128362U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h__Script_ArmorWars_3709044922(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h__Script_ArmorWars_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h__Script_ArmorWars_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h__Script_ArmorWars_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSCommandPrioritySystem_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

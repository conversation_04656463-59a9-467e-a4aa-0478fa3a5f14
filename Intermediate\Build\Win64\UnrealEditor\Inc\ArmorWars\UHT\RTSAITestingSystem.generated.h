// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSAITestingSystem.h"

#ifdef ARMORWARS_RTSAITestingSystem_generated_h
#error "RTSAITestingSystem.generated.h already included, missing '#pragma once' in RTSAITestingSystem.h"
#endif
#define ARMORWARS_RTSAITestingSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSUnit;
enum class ERTSTestScenario : uint8;
struct FRTSPerformanceMetrics;
struct FRTSTestResult;

// ********** Begin ScriptStruct FRTSTestResult ****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_32_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSTestResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSTestResult;
// ********** End ScriptStruct FRTSTestResult ******************************************************

// ********** Begin ScriptStruct FRTSPerformanceMetrics ********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_75_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSPerformanceMetrics;
// ********** End ScriptStruct FRTSPerformanceMetrics **********************************************

// ********** Begin Class URTSAIDebugComponent *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_114_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetOwnerUnit); \
	DECLARE_FUNCTION(execGetAIComponentStatus); \
	DECLARE_FUNCTION(execValidateAIComponents); \
	DECLARE_FUNCTION(execDrawDebugInfo); \
	DECLARE_FUNCTION(execLogCombatState); \
	DECLARE_FUNCTION(execLogMovementState); \
	DECLARE_FUNCTION(execLogBehaviorTreeState); \
	DECLARE_FUNCTION(execLogCommandQueue); \
	DECLARE_FUNCTION(execLogCurrentState);


ARMORWARS_API UClass* Z_Construct_UClass_URTSAIDebugComponent_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_114_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSAIDebugComponent(); \
	friend struct Z_Construct_UClass_URTSAIDebugComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSAIDebugComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSAIDebugComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSAIDebugComponent_NoRegister) \
	DECLARE_SERIALIZER(URTSAIDebugComponent)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_114_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSAIDebugComponent(URTSAIDebugComponent&&) = delete; \
	URTSAIDebugComponent(const URTSAIDebugComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSAIDebugComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSAIDebugComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSAIDebugComponent) \
	NO_API virtual ~URTSAIDebugComponent();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_111_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_114_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_114_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_114_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_114_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSAIDebugComponent;

// ********** End Class URTSAIDebugComponent *******************************************************

// ********** Begin Delegate FOnTestStarted ********************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_331_DELEGATE \
static void FOnTestStarted_DelegateWrapper(const FMulticastScriptDelegate& OnTestStarted, ERTSTestScenario TestScenario);


// ********** End Delegate FOnTestStarted **********************************************************

// ********** Begin Delegate FOnTestCompleted ******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_332_DELEGATE \
static void FOnTestCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTestCompleted, ERTSTestScenario TestScenario, bool bPassed);


// ********** End Delegate FOnTestCompleted ********************************************************

// ********** Begin Delegate FOnPerformanceAlert ***************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_333_DELEGATE \
static void FOnPerformanceAlert_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceAlert, const FString& AlertMessage);


// ********** End Delegate FOnPerformanceAlert *****************************************************

// ********** Begin Class URTSAITestingManager *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_196_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetSystemValidationErrors); \
	DECLARE_FUNCTION(execValidateAISystem); \
	DECLARE_FUNCTION(execExportTestResults); \
	DECLARE_FUNCTION(execGenerateTestReport); \
	DECLARE_FUNCTION(execGetCurrentPerformanceMetrics); \
	DECLARE_FUNCTION(execGetTestResults); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execStopPerformanceMonitoring); \
	DECLARE_FUNCTION(execStartPerformanceMonitoring); \
	DECLARE_FUNCTION(execValidateTestEnvironment); \
	DECLARE_FUNCTION(execCleanupTestUnits); \
	DECLARE_FUNCTION(execSpawnTestUnits); \
	DECLARE_FUNCTION(execRunStressTest); \
	DECLARE_FUNCTION(execRunCommandPriorityTest); \
	DECLARE_FUNCTION(execRunCollisionAvoidanceTest); \
	DECLARE_FUNCTION(execRunReturnFireTest); \
	DECLARE_FUNCTION(execRunCombatEngagementTest); \
	DECLARE_FUNCTION(execRunFormationMovementTest); \
	DECLARE_FUNCTION(execRunBasicMovementTest); \
	DECLARE_FUNCTION(execRunAllTests); \
	DECLARE_FUNCTION(execStopCurrentTest); \
	DECLARE_FUNCTION(execStartTest);


ARMORWARS_API UClass* Z_Construct_UClass_URTSAITestingManager_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_196_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSAITestingManager(); \
	friend struct Z_Construct_UClass_URTSAITestingManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSAITestingManager_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSAITestingManager, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSAITestingManager_NoRegister) \
	DECLARE_SERIALIZER(URTSAITestingManager)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_196_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSAITestingManager(URTSAITestingManager&&) = delete; \
	URTSAITestingManager(const URTSAITestingManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSAITestingManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSAITestingManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSAITestingManager) \
	NO_API virtual ~URTSAITestingManager();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_193_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_196_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_196_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_196_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h_196_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSAITestingManager;

// ********** End Class URTSAITestingManager *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h

// ********** Begin Enum ERTSTestScenario **********************************************************
#define FOREACH_ENUM_ERTSTESTSCENARIO(op) \
	op(ERTSTestScenario::BasicMovement) \
	op(ERTSTestScenario::FormationMovement) \
	op(ERTSTestScenario::CombatEngagement) \
	op(ERTSTestScenario::ReturnFire) \
	op(ERTSTestScenario::CollisionAvoidance) \
	op(ERTSTestScenario::CommandPriority) \
	op(ERTSTestScenario::MixedScenario) \
	op(ERTSTestScenario::StressTest) 

enum class ERTSTestScenario : uint8;
template<> struct TIsUEnumClass<ERTSTestScenario> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSTestScenario>();
// ********** End Enum ERTSTestScenario ************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

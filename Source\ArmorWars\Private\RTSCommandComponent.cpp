#include "RTSCommandComponent.h"
#include "RTSUnit.h"
#include "RTSAIController.h"
#include "RTSBaseActor.h"
#include "Engine/World.h"
#include "Engine/Engine.h"

// Additional implementation for URTSCommandComponent

bool URTSCommandComponent::CanExecuteCommand(const FRTSCommand& Command) const
{
    if (!IsCommandValid(Command))
    {
        return false;
    }
    
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit || !OwnerUnit->IsAlive())
    {
        return false;
    }
    
    // Check if command requires a target and target is valid
    if (Command.RequiresTarget())
    {
        if (!Command.TargetActor.IsValid() || !Command.TargetActor->IsAlive())
        {
            return false;
        }
    }
    
    return true;
}

bool URTSCommandComponent::CanInterruptCurrentCommand(ERTSCommandPriority NewCommandPriority) const
{
    const FRTSCommand* CurrentCommand = CommandQueue.GetCurrentCommand();
    if (!CurrentCommand)
    {
        return true; // No current command to interrupt
    }
    
    if (!CurrentCommand->bInterruptible)
    {
        return false;
    }
    
    return static_cast<uint8>(NewCommandPriority) >= static_cast<uint8>(MinimumInterruptPriority) &&
           static_cast<uint8>(NewCommandPriority) > static_cast<uint8>(CurrentCommand->Priority);
}

void URTSCommandComponent::SetFormationData(const FVector& Offset, ARTSUnit* Leader, int32 Index)
{
    CurrentFormationOffset = Offset;
    CurrentFormationLeader = Leader;
    CurrentFormationIndex = Index;
    
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCommandComponent: Formation data set - Offset: %s, Leader: %s, Index: %d"),
            *Offset.ToString(),
            Leader ? *Leader->GetName() : TEXT("None"),
            Index);
    }
}

bool URTSCommandComponent::IsInFormation() const
{
    return CurrentFormationLeader.IsValid() && CurrentFormationIndex >= 0;
}

FVector URTSCommandComponent::GetFormationOffset() const
{
    return CurrentFormationOffset;
}

ARTSUnit* URTSCommandComponent::GetFormationLeader() const
{
    return CurrentFormationLeader.Get();
}

void URTSCommandComponent::UpdateCommandExecution(float DeltaTime)
{
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Check if enough time has passed since last update
    if (CurrentTime - LastCommandUpdateTime < CommandUpdateInterval)
    {
        return;
    }
    
    LastCommandUpdateTime = CurrentTime;
    
    // Process current command
    if (CommandQueue.HasCommands())
    {
        ProcessCurrentCommand(DeltaTime);
    }
}

void URTSCommandComponent::ProcessCurrentCommand(float DeltaTime)
{
    FRTSCommand* CurrentCommand = CommandQueue.GetCurrentCommand();
    if (!CurrentCommand)
    {
        return;
    }
    
    // Check for timeout
    if (CurrentCommand->HasTimedOut(GetWorld()->GetTimeSeconds()))
    {
        HandleCommandTimeout(*CurrentCommand);
        return;
    }
    
    // Execute command based on status
    switch (CurrentCommand->Status)
    {
        case ERTSCommandStatus::Pending:
            ExecuteCurrentCommand();
            break;
            
        case ERTSCommandStatus::Executing:
            // Continue processing the command
            switch (CurrentCommand->CommandType)
            {
                case ERTSCommandType::Move:
                    ExecuteMoveCommand(*CurrentCommand);
                    break;
                case ERTSCommandType::AttackTarget:
                    ExecuteAttackCommand(*CurrentCommand);
                    break;
                case ERTSCommandType::AttackMove:
                    ExecuteAttackMoveCommand(*CurrentCommand);
                    break;
                case ERTSCommandType::Stop:
                    ExecuteStopCommand(*CurrentCommand);
                    break;
                case ERTSCommandType::Hold:
                    ExecuteHoldCommand(*CurrentCommand);
                    break;
                case ERTSCommandType::Patrol:
                    ExecutePatrolCommand(*CurrentCommand);
                    break;
                case ERTSCommandType::Follow:
                    ExecuteFollowCommand(*CurrentCommand);
                    break;
                case ERTSCommandType::Formation:
                    ExecuteFormationCommand(*CurrentCommand);
                    break;
                default:
                    // Unknown command type, fail it
                    FailCurrentCommand();
                    break;
            }
            break;
            
        case ERTSCommandStatus::Completed:
        case ERTSCommandStatus::Failed:
        case ERTSCommandStatus::Cancelled:
            // Command is finished, advance to next
            CommandQueue.AdvanceToNextCommand();
            bIsExecutingCommand = false;
            OnCommandQueueChanged.Broadcast(CommandQueue.GetCommandCount());
            break;
            
        default:
            break;
    }
}

bool URTSCommandComponent::StartCommandExecution(FRTSCommand& Command)
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        return false;
    }
    
    // Validate command before execution
    if (!CanExecuteCommand(Command))
    {
        return false;
    }
    
    // Command-specific initialization
    switch (Command.CommandType)
    {
        case ERTSCommandType::Stop:
            // Stop command executes immediately
            if (ARTSAIController* AIController = GetAIController())
            {
                AIController->StopMovement();
            }
            OwnerUnit->StopAttacking();
            CompleteCurrentCommand();
            return true;
            
        case ERTSCommandType::Formation:
            // Set formation data
            SetFormationData(Command.FormationOffset, Command.FormationLeader.Get(), Command.FormationIndex);
            break;
            
        default:
            break;
    }
    
    return true;
}

void URTSCommandComponent::HandleCommandTimeout(FRTSCommand& Command)
{
    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Warning, TEXT("RTSCommandComponent: Command timed out: %s"), 
            *UEnum::GetValueAsString(Command.CommandType));
    }
    
    FailCurrentCommand();
}

void URTSCommandComponent::ExecuteMoveCommand(const FRTSCommand& Command)
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        FailCurrentCommand();
        return;
    }
    
    // Calculate target location with formation offset if in formation
    FVector TargetLocation = Command.TargetLocation;
    if (IsInFormation())
    {
        TargetLocation += CurrentFormationOffset;
    }
    
    // Issue move command to unit
    OwnerUnit->MoveToLocation(TargetLocation);
    
    // Check if we've reached the destination
    float DistanceToTarget = FVector::Dist(OwnerUnit->GetActorLocation(), TargetLocation);
    if (DistanceToTarget <= 100.0f) // Arrival tolerance
    {
        CompleteCurrentCommand();
    }
}

void URTSCommandComponent::ExecuteAttackCommand(const FRTSCommand& Command)
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        FailCurrentCommand();
        return;
    }
    
    // Check if target is still valid
    if (!Command.TargetActor.IsValid() || !Command.TargetActor->IsAlive())
    {
        FailCurrentCommand();
        return;
    }
    
    // Attack the target
    OwnerUnit->AttackTarget(Command.TargetActor.Get());
    
    // Check if target is dead
    if (!Command.TargetActor->IsAlive())
    {
        CompleteCurrentCommand();
    }
}

void URTSCommandComponent::ExecuteAttackMoveCommand(const FRTSCommand& Command)
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        FailCurrentCommand();
        return;
    }
    
    // Move towards target while engaging enemies
    FVector TargetLocation = Command.TargetLocation;
    if (IsInFormation())
    {
        TargetLocation += CurrentFormationOffset;
    }
    
    OwnerUnit->AttackMoveToLocation(TargetLocation);
    
    // Check if we've reached the destination
    float DistanceToTarget = FVector::Dist(OwnerUnit->GetActorLocation(), TargetLocation);
    if (DistanceToTarget <= 100.0f)
    {
        CompleteCurrentCommand();
    }
}

void URTSCommandComponent::ExecuteStopCommand(const FRTSCommand& Command)
{
    // Stop command should complete immediately in StartCommandExecution
    CompleteCurrentCommand();
}

void URTSCommandComponent::ExecuteHoldCommand(const FRTSCommand& Command)
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        FailCurrentCommand();
        return;
    }
    
    // Hold position - stop movement but continue attacking
    if (ARTSAIController* AIController = GetAIController())
    {
        AIController->StopMovement();
    }
    
    // Hold command continues indefinitely until interrupted
    // It doesn't complete on its own
}

void URTSCommandComponent::ExecutePatrolCommand(const FRTSCommand& Command)
{
    // Patrol implementation would cycle through waypoints
    // For now, treat as move command to first waypoint
    if (Command.WaypointLocations.Num() > 0)
    {
        FRTSCommand MoveCommand = Command;
        MoveCommand.CommandType = ERTSCommandType::Move;
        MoveCommand.TargetLocation = Command.WaypointLocations[0];
        ExecuteMoveCommand(MoveCommand);
    }
    else
    {
        FailCurrentCommand();
    }
}

void URTSCommandComponent::ExecuteFollowCommand(const FRTSCommand& Command)
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    if (!OwnerUnit)
    {
        FailCurrentCommand();
        return;
    }
    
    // Check if target is still valid
    if (!Command.TargetActor.IsValid())
    {
        FailCurrentCommand();
        return;
    }
    
    // Move towards the target
    FVector TargetLocation = Command.TargetActor->GetActorLocation();
    OwnerUnit->MoveToLocation(TargetLocation);
    
    // Follow command continues indefinitely
}

void URTSCommandComponent::ExecuteFormationCommand(const FRTSCommand& Command)
{
    // Formation command completes immediately after setting formation data
    CompleteCurrentCommand();
}

ARTSUnit* URTSCommandComponent::GetOwnerUnit() const
{
    return Cast<ARTSUnit>(GetOwner());
}

ARTSAIController* URTSCommandComponent::GetAIController() const
{
    ARTSUnit* OwnerUnit = GetOwnerUnit();
    return OwnerUnit ? Cast<ARTSAIController>(OwnerUnit->GetController()) : nullptr;
}

bool URTSCommandComponent::IsCommandValid(const FRTSCommand& Command) const
{
    return Command.IsValid();
}

void URTSCommandComponent::BroadcastCommandReceived(const FRTSCommand& Command, bool bInterrupted)
{
    OnCommandReceived.Broadcast(Command, bInterrupted);
}

void URTSCommandComponent::BroadcastCommandCompleted(const FRTSCommand& Command, ERTSCommandStatus Status)
{
    OnCommandCompleted.Broadcast(Command, Status);
}

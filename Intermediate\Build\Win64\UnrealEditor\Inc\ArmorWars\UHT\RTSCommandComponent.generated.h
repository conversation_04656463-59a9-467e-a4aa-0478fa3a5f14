// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSCommandComponent.h"

#ifdef ARMORWARS_RTSCommandComponent_generated_h
#error "RTSCommandComponent.generated.h already included, missing '#pragma once' in RTSCommandComponent.h"
#endif
#define ARMORWARS_RTSCommandComponent_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSBaseActor;
class ARTSUnit;
enum class ERTSCommandPriority : uint8;
enum class ERTSCommandStatus : uint8;
enum class ERTSFormationCommandType : uint8;
struct FRTSCommand;

// ********** Begin Delegate FOnCommandReceived ****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_12_DELEGATE \
ARMORWARS_API void FOnCommandReceived_DelegateWrapper(const FMulticastScriptDelegate& OnCommandReceived, FRTSCommand const& Command, bool bInterrupted);


// ********** End Delegate FOnCommandReceived ******************************************************

// ********** Begin Delegate FOnCommandCompleted ***************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_13_DELEGATE \
ARMORWARS_API void FOnCommandCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnCommandCompleted, FRTSCommand const& Command, ERTSCommandStatus Status);


// ********** End Delegate FOnCommandCompleted *****************************************************

// ********** Begin Delegate FOnCommandQueueChanged ************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_14_DELEGATE \
ARMORWARS_API void FOnCommandQueueChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCommandQueueChanged, int32 QueueSize);


// ********** End Delegate FOnCommandQueueChanged **************************************************

// ********** Begin Class URTSCommandComponent *****************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_23_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetFormationLeader); \
	DECLARE_FUNCTION(execGetFormationOffset); \
	DECLARE_FUNCTION(execIsInFormation); \
	DECLARE_FUNCTION(execSetFormationData); \
	DECLARE_FUNCTION(execCanInterruptCurrentCommand); \
	DECLARE_FUNCTION(execCanExecuteCommand); \
	DECLARE_FUNCTION(execFailCurrentCommand); \
	DECLARE_FUNCTION(execCompleteCurrentCommand); \
	DECLARE_FUNCTION(execExecuteCurrentCommand); \
	DECLARE_FUNCTION(execGetAllCommands); \
	DECLARE_FUNCTION(execGetCommandQueueSize); \
	DECLARE_FUNCTION(execHasCommands); \
	DECLARE_FUNCTION(execGetCurrentCommand); \
	DECLARE_FUNCTION(execRemoveCommand); \
	DECLARE_FUNCTION(execCancelCurrentCommand); \
	DECLARE_FUNCTION(execClearCommandQueue); \
	DECLARE_FUNCTION(execIssueFormationCommand); \
	DECLARE_FUNCTION(execIssueFollowCommand); \
	DECLARE_FUNCTION(execIssuePatrolCommand); \
	DECLARE_FUNCTION(execIssueHoldCommand); \
	DECLARE_FUNCTION(execIssueStopCommand); \
	DECLARE_FUNCTION(execIssueAttackMoveCommand); \
	DECLARE_FUNCTION(execIssueAttackCommand); \
	DECLARE_FUNCTION(execIssueMoveCommand); \
	DECLARE_FUNCTION(execIssueCommand);


ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandComponent_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_23_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSCommandComponent(); \
	friend struct Z_Construct_UClass_URTSCommandComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSCommandComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSCommandComponent_NoRegister) \
	DECLARE_SERIALIZER(URTSCommandComponent)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_23_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSCommandComponent(URTSCommandComponent&&) = delete; \
	URTSCommandComponent(const URTSCommandComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSCommandComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSCommandComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSCommandComponent) \
	NO_API virtual ~URTSCommandComponent();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_20_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_23_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_23_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_23_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h_23_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSCommandComponent;

// ********** End Class URTSCommandComponent *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSCommandComponent_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

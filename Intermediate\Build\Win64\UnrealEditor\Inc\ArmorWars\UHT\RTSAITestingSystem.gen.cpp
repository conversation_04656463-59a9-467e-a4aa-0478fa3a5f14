// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSAITestingSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSAITestingSystem() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSAIDebugComponent();
ARMORWARS_API UClass* Z_Construct_UClass_URTSAIDebugComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSAITestingManager();
ARMORWARS_API UClass* Z_Construct_UClass_URTSAITestingManager_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSTestScenario();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSPerformanceMetrics();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSTestResult();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSTestScenario **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSTestScenario;
static UEnum* ERTSTestScenario_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSTestScenario.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSTestScenario.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSTestScenario, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSTestScenario"));
	}
	return Z_Registration_Info_UEnum_ERTSTestScenario.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSTestScenario>()
{
	return ERTSTestScenario_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSTestScenario_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BasicMovement.DisplayName", "Basic Movement" },
		{ "BasicMovement.Name", "ERTSTestScenario::BasicMovement" },
		{ "BlueprintType", "true" },
		{ "CollisionAvoidance.DisplayName", "Collision Avoidance" },
		{ "CollisionAvoidance.Name", "ERTSTestScenario::CollisionAvoidance" },
		{ "CombatEngagement.DisplayName", "Combat Engagement" },
		{ "CombatEngagement.Name", "ERTSTestScenario::CombatEngagement" },
		{ "CommandPriority.DisplayName", "Command Priority" },
		{ "CommandPriority.Name", "ERTSTestScenario::CommandPriority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test scenario types\n" },
#endif
		{ "FormationMovement.DisplayName", "Formation Movement" },
		{ "FormationMovement.Name", "ERTSTestScenario::FormationMovement" },
		{ "MixedScenario.DisplayName", "Mixed Scenario" },
		{ "MixedScenario.Name", "ERTSTestScenario::MixedScenario" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
		{ "ReturnFire.DisplayName", "Return Fire" },
		{ "ReturnFire.Name", "ERTSTestScenario::ReturnFire" },
		{ "StressTest.DisplayName", "Stress Test" },
		{ "StressTest.Name", "ERTSTestScenario::StressTest" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test scenario types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSTestScenario::BasicMovement", (int64)ERTSTestScenario::BasicMovement },
		{ "ERTSTestScenario::FormationMovement", (int64)ERTSTestScenario::FormationMovement },
		{ "ERTSTestScenario::CombatEngagement", (int64)ERTSTestScenario::CombatEngagement },
		{ "ERTSTestScenario::ReturnFire", (int64)ERTSTestScenario::ReturnFire },
		{ "ERTSTestScenario::CollisionAvoidance", (int64)ERTSTestScenario::CollisionAvoidance },
		{ "ERTSTestScenario::CommandPriority", (int64)ERTSTestScenario::CommandPriority },
		{ "ERTSTestScenario::MixedScenario", (int64)ERTSTestScenario::MixedScenario },
		{ "ERTSTestScenario::StressTest", (int64)ERTSTestScenario::StressTest },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSTestScenario_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSTestScenario",
	"ERTSTestScenario",
	Z_Construct_UEnum_ArmorWars_ERTSTestScenario_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSTestScenario_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSTestScenario_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSTestScenario_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSTestScenario()
{
	if (!Z_Registration_Info_UEnum_ERTSTestScenario.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSTestScenario.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSTestScenario_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSTestScenario.InnerSingleton;
}
// ********** End Enum ERTSTestScenario ************************************************************

// ********** Begin ScriptStruct FRTSTestResult ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSTestResult;
class UScriptStruct* FRTSTestResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSTestResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSTestResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSTestResult, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSTestResult"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSTestResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSTestResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test result data\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test result data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestScenario_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTestPassed_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestDuration_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestDescription_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailureReason_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMetrics_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndTime_MetaData[] = {
		{ "Category", "Test Result" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TestScenario_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TestScenario;
	static void NewProp_bTestPassed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTestPassed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TestDuration;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestDescription;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FailureReason;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PerformanceMetrics_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PerformanceMetrics;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EndTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSTestResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_TestScenario_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_TestScenario = { "TestScenario", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSTestResult, TestScenario), Z_Construct_UEnum_ArmorWars_ERTSTestScenario, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestScenario_MetaData), NewProp_TestScenario_MetaData) }; // 3240236233
void Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_bTestPassed_SetBit(void* Obj)
{
	((FRTSTestResult*)Obj)->bTestPassed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_bTestPassed = { "bTestPassed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSTestResult), &Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_bTestPassed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTestPassed_MetaData), NewProp_bTestPassed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_TestDuration = { "TestDuration", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSTestResult, TestDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestDuration_MetaData), NewProp_TestDuration_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_TestDescription = { "TestDescription", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSTestResult, TestDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestDescription_MetaData), NewProp_TestDescription_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_FailureReason = { "FailureReason", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSTestResult, FailureReason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailureReason_MetaData), NewProp_FailureReason_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_PerformanceMetrics_Inner = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_PerformanceMetrics = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSTestResult, PerformanceMetrics), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMetrics_MetaData), NewProp_PerformanceMetrics_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSTestResult, StartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_EndTime = { "EndTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSTestResult, EndTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndTime_MetaData), NewProp_EndTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSTestResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_TestScenario_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_TestScenario,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_bTestPassed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_TestDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_TestDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_FailureReason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_PerformanceMetrics_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_PerformanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewProp_EndTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSTestResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSTestResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSTestResult",
	Z_Construct_UScriptStruct_FRTSTestResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSTestResult_Statics::PropPointers),
	sizeof(FRTSTestResult),
	alignof(FRTSTestResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSTestResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSTestResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSTestResult()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSTestResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSTestResult.InnerSingleton, Z_Construct_UScriptStruct_FRTSTestResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSTestResult.InnerSingleton;
}
// ********** End ScriptStruct FRTSTestResult ******************************************************

// ********** Begin ScriptStruct FRTSPerformanceMetrics ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSPerformanceMetrics;
class UScriptStruct* FRTSPerformanceMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSPerformanceMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSPerformanceMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSPerformanceMetrics, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSPerformanceMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSPerformanceMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance metrics tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance metrics tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFrameTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFrameTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalUnitsTracked_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveCommandsCount_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorTreeUpdatesPerSecond_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFrameTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFrameTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalUnitsTracked;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveCommandsCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BehaviorTreeUpdatesPerSecond;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSPerformanceMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_AverageFrameTime = { "AverageFrameTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSPerformanceMetrics, AverageFrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFrameTime_MetaData), NewProp_AverageFrameTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_MaxFrameTime = { "MaxFrameTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSPerformanceMetrics, MaxFrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFrameTime_MetaData), NewProp_MaxFrameTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_TotalUnitsTracked = { "TotalUnitsTracked", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSPerformanceMetrics, TotalUnitsTracked), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalUnitsTracked_MetaData), NewProp_TotalUnitsTracked_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_ActiveCommandsCount = { "ActiveCommandsCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSPerformanceMetrics, ActiveCommandsCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveCommandsCount_MetaData), NewProp_ActiveCommandsCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_BehaviorTreeUpdatesPerSecond = { "BehaviorTreeUpdatesPerSecond", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSPerformanceMetrics, BehaviorTreeUpdatesPerSecond), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorTreeUpdatesPerSecond_MetaData), NewProp_BehaviorTreeUpdatesPerSecond_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSPerformanceMetrics, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_AverageFrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_MaxFrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_TotalUnitsTracked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_ActiveCommandsCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_BehaviorTreeUpdatesPerSecond,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewProp_MemoryUsageMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSPerformanceMetrics",
	Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::PropPointers),
	sizeof(FRTSPerformanceMetrics),
	alignof(FRTSPerformanceMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSPerformanceMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSPerformanceMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSPerformanceMetrics.InnerSingleton, Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSPerformanceMetrics.InnerSingleton;
}
// ********** End ScriptStruct FRTSPerformanceMetrics **********************************************

// ********** Begin Class URTSAIDebugComponent Function DrawDebugInfo ******************************
struct Z_Construct_UFunction_URTSAIDebugComponent_DrawDebugInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Debug" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAIDebugComponent_DrawDebugInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAIDebugComponent, nullptr, "DrawDebugInfo", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_DrawDebugInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAIDebugComponent_DrawDebugInfo_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAIDebugComponent_DrawDebugInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAIDebugComponent_DrawDebugInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAIDebugComponent::execDrawDebugInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugInfo();
	P_NATIVE_END;
}
// ********** End Class URTSAIDebugComponent Function DrawDebugInfo ********************************

// ********** Begin Class URTSAIDebugComponent Function GetAIComponentStatus ***********************
struct Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics
{
	struct RTSAIDebugComponent_eventGetAIComponentStatus_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Debug" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIDebugComponent_eventGetAIComponentStatus_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAIDebugComponent, nullptr, "GetAIComponentStatus", Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::RTSAIDebugComponent_eventGetAIComponentStatus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::RTSAIDebugComponent_eventGetAIComponentStatus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAIDebugComponent::execGetAIComponentStatus)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAIComponentStatus();
	P_NATIVE_END;
}
// ********** End Class URTSAIDebugComponent Function GetAIComponentStatus *************************

// ********** Begin Class URTSAIDebugComponent Function GetOwnerUnit *******************************
struct Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics
{
	struct RTSAIDebugComponent_eventGetOwnerUnit_Parms
	{
		ARTSUnit* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Debug" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAIDebugComponent_eventGetOwnerUnit_Parms, ReturnValue), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAIDebugComponent, nullptr, "GetOwnerUnit", Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::RTSAIDebugComponent_eventGetOwnerUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::RTSAIDebugComponent_eventGetOwnerUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAIDebugComponent::execGetOwnerUnit)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSUnit**)Z_Param__Result=P_THIS->GetOwnerUnit();
	P_NATIVE_END;
}
// ********** End Class URTSAIDebugComponent Function GetOwnerUnit *********************************

// ********** Begin Class URTSAIDebugComponent Function LogBehaviorTreeState ***********************
struct Z_Construct_UFunction_URTSAIDebugComponent_LogBehaviorTreeState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Debug" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAIDebugComponent_LogBehaviorTreeState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAIDebugComponent, nullptr, "LogBehaviorTreeState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_LogBehaviorTreeState_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAIDebugComponent_LogBehaviorTreeState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAIDebugComponent_LogBehaviorTreeState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAIDebugComponent_LogBehaviorTreeState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAIDebugComponent::execLogBehaviorTreeState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogBehaviorTreeState();
	P_NATIVE_END;
}
// ********** End Class URTSAIDebugComponent Function LogBehaviorTreeState *************************

// ********** Begin Class URTSAIDebugComponent Function LogCombatState *****************************
struct Z_Construct_UFunction_URTSAIDebugComponent_LogCombatState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Debug" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAIDebugComponent_LogCombatState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAIDebugComponent, nullptr, "LogCombatState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_LogCombatState_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAIDebugComponent_LogCombatState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAIDebugComponent_LogCombatState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAIDebugComponent_LogCombatState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAIDebugComponent::execLogCombatState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogCombatState();
	P_NATIVE_END;
}
// ********** End Class URTSAIDebugComponent Function LogCombatState *******************************

// ********** Begin Class URTSAIDebugComponent Function LogCommandQueue ****************************
struct Z_Construct_UFunction_URTSAIDebugComponent_LogCommandQueue_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Debug" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAIDebugComponent_LogCommandQueue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAIDebugComponent, nullptr, "LogCommandQueue", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_LogCommandQueue_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAIDebugComponent_LogCommandQueue_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAIDebugComponent_LogCommandQueue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAIDebugComponent_LogCommandQueue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAIDebugComponent::execLogCommandQueue)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogCommandQueue();
	P_NATIVE_END;
}
// ********** End Class URTSAIDebugComponent Function LogCommandQueue ******************************

// ********** Begin Class URTSAIDebugComponent Function LogCurrentState ****************************
struct Z_Construct_UFunction_URTSAIDebugComponent_LogCurrentState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAIDebugComponent_LogCurrentState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAIDebugComponent, nullptr, "LogCurrentState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_LogCurrentState_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAIDebugComponent_LogCurrentState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAIDebugComponent_LogCurrentState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAIDebugComponent_LogCurrentState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAIDebugComponent::execLogCurrentState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogCurrentState();
	P_NATIVE_END;
}
// ********** End Class URTSAIDebugComponent Function LogCurrentState ******************************

// ********** Begin Class URTSAIDebugComponent Function LogMovementState ***************************
struct Z_Construct_UFunction_URTSAIDebugComponent_LogMovementState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Debug" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAIDebugComponent_LogMovementState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAIDebugComponent, nullptr, "LogMovementState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_LogMovementState_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAIDebugComponent_LogMovementState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAIDebugComponent_LogMovementState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAIDebugComponent_LogMovementState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAIDebugComponent::execLogMovementState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogMovementState();
	P_NATIVE_END;
}
// ********** End Class URTSAIDebugComponent Function LogMovementState *****************************

// ********** Begin Class URTSAIDebugComponent Function ValidateAIComponents ***********************
struct Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics
{
	struct RTSAIDebugComponent_eventValidateAIComponents_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAIDebugComponent_eventValidateAIComponents_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAIDebugComponent_eventValidateAIComponents_Parms), &Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAIDebugComponent, nullptr, "ValidateAIComponents", Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::RTSAIDebugComponent_eventValidateAIComponents_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::RTSAIDebugComponent_eventValidateAIComponents_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAIDebugComponent::execValidateAIComponents)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateAIComponents();
	P_NATIVE_END;
}
// ********** End Class URTSAIDebugComponent Function ValidateAIComponents *************************

// ********** Begin Class URTSAIDebugComponent *****************************************************
void URTSAIDebugComponent::StaticRegisterNativesURTSAIDebugComponent()
{
	UClass* Class = URTSAIDebugComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "DrawDebugInfo", &URTSAIDebugComponent::execDrawDebugInfo },
		{ "GetAIComponentStatus", &URTSAIDebugComponent::execGetAIComponentStatus },
		{ "GetOwnerUnit", &URTSAIDebugComponent::execGetOwnerUnit },
		{ "LogBehaviorTreeState", &URTSAIDebugComponent::execLogBehaviorTreeState },
		{ "LogCombatState", &URTSAIDebugComponent::execLogCombatState },
		{ "LogCommandQueue", &URTSAIDebugComponent::execLogCommandQueue },
		{ "LogCurrentState", &URTSAIDebugComponent::execLogCurrentState },
		{ "LogMovementState", &URTSAIDebugComponent::execLogMovementState },
		{ "ValidateAIComponents", &URTSAIDebugComponent::execValidateAIComponents },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSAIDebugComponent;
UClass* URTSAIDebugComponent::GetPrivateStaticClass()
{
	using TClass = URTSAIDebugComponent;
	if (!Z_Registration_Info_UClass_URTSAIDebugComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSAIDebugComponent"),
			Z_Registration_Info_UClass_URTSAIDebugComponent.InnerSingleton,
			StaticRegisterNativesURTSAIDebugComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSAIDebugComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSAIDebugComponent_NoRegister()
{
	return URTSAIDebugComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSAIDebugComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Component for debugging individual unit AI behavior\n * Provides detailed logging and visualization for AI debugging\n */" },
#endif
		{ "IncludePath", "RTSAITestingSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Component for debugging individual unit AI behavior\nProvides detailed logging and visualization for AI debugging" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Debug Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableVisualDebug_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogCommandExecution_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogBehaviorTreeStates_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogMovementData_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogCombatData_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DebugUpdateInterval_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static void NewProp_bEnableVisualDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableVisualDebug;
	static void NewProp_bLogCommandExecution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogCommandExecution;
	static void NewProp_bLogBehaviorTreeStates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogBehaviorTreeStates;
	static void NewProp_bLogMovementData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogMovementData;
	static void NewProp_bLogCombatData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogCombatData;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DebugUpdateInterval;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSAIDebugComponent_DrawDebugInfo, "DrawDebugInfo" }, // 317022151
		{ &Z_Construct_UFunction_URTSAIDebugComponent_GetAIComponentStatus, "GetAIComponentStatus" }, // 665829212
		{ &Z_Construct_UFunction_URTSAIDebugComponent_GetOwnerUnit, "GetOwnerUnit" }, // 985993103
		{ &Z_Construct_UFunction_URTSAIDebugComponent_LogBehaviorTreeState, "LogBehaviorTreeState" }, // 4209560276
		{ &Z_Construct_UFunction_URTSAIDebugComponent_LogCombatState, "LogCombatState" }, // 1023714983
		{ &Z_Construct_UFunction_URTSAIDebugComponent_LogCommandQueue, "LogCommandQueue" }, // 4165237339
		{ &Z_Construct_UFunction_URTSAIDebugComponent_LogCurrentState, "LogCurrentState" }, // 238129812
		{ &Z_Construct_UFunction_URTSAIDebugComponent_LogMovementState, "LogMovementState" }, // 3520657188
		{ &Z_Construct_UFunction_URTSAIDebugComponent_ValidateAIComponents, "ValidateAIComponents" }, // 1483082112
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSAIDebugComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((URTSAIDebugComponent*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSAIDebugComponent), &Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
void Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bEnableVisualDebug_SetBit(void* Obj)
{
	((URTSAIDebugComponent*)Obj)->bEnableVisualDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bEnableVisualDebug = { "bEnableVisualDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSAIDebugComponent), &Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bEnableVisualDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableVisualDebug_MetaData), NewProp_bEnableVisualDebug_MetaData) };
void Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogCommandExecution_SetBit(void* Obj)
{
	((URTSAIDebugComponent*)Obj)->bLogCommandExecution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogCommandExecution = { "bLogCommandExecution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSAIDebugComponent), &Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogCommandExecution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogCommandExecution_MetaData), NewProp_bLogCommandExecution_MetaData) };
void Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogBehaviorTreeStates_SetBit(void* Obj)
{
	((URTSAIDebugComponent*)Obj)->bLogBehaviorTreeStates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogBehaviorTreeStates = { "bLogBehaviorTreeStates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSAIDebugComponent), &Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogBehaviorTreeStates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogBehaviorTreeStates_MetaData), NewProp_bLogBehaviorTreeStates_MetaData) };
void Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogMovementData_SetBit(void* Obj)
{
	((URTSAIDebugComponent*)Obj)->bLogMovementData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogMovementData = { "bLogMovementData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSAIDebugComponent), &Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogMovementData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogMovementData_MetaData), NewProp_bLogMovementData_MetaData) };
void Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogCombatData_SetBit(void* Obj)
{
	((URTSAIDebugComponent*)Obj)->bLogCombatData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogCombatData = { "bLogCombatData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSAIDebugComponent), &Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogCombatData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogCombatData_MetaData), NewProp_bLogCombatData_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_DebugUpdateInterval = { "DebugUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAIDebugComponent, DebugUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DebugUpdateInterval_MetaData), NewProp_DebugUpdateInterval_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSAIDebugComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bEnableVisualDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogCommandExecution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogBehaviorTreeStates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogMovementData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_bLogCombatData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAIDebugComponent_Statics::NewProp_DebugUpdateInterval,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAIDebugComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSAIDebugComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAIDebugComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSAIDebugComponent_Statics::ClassParams = {
	&URTSAIDebugComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSAIDebugComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSAIDebugComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAIDebugComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSAIDebugComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSAIDebugComponent()
{
	if (!Z_Registration_Info_UClass_URTSAIDebugComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSAIDebugComponent.OuterSingleton, Z_Construct_UClass_URTSAIDebugComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSAIDebugComponent.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSAIDebugComponent);
URTSAIDebugComponent::~URTSAIDebugComponent() {}
// ********** End Class URTSAIDebugComponent *******************************************************

// ********** Begin Delegate FOnTestStarted ********************************************************
struct Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics
{
	struct RTSAITestingManager_eventOnTestStarted_Parms
	{
		ERTSTestScenario TestScenario;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TestScenario_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TestScenario;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::NewProp_TestScenario_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::NewProp_TestScenario = { "TestScenario", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventOnTestStarted_Parms, TestScenario), Z_Construct_UEnum_ArmorWars_ERTSTestScenario, METADATA_PARAMS(0, nullptr) }; // 3240236233
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::NewProp_TestScenario_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::NewProp_TestScenario,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "OnTestStarted__DelegateSignature", Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::RTSAITestingManager_eventOnTestStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::RTSAITestingManager_eventOnTestStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSAITestingManager::FOnTestStarted_DelegateWrapper(const FMulticastScriptDelegate& OnTestStarted, ERTSTestScenario TestScenario)
{
	struct RTSAITestingManager_eventOnTestStarted_Parms
	{
		ERTSTestScenario TestScenario;
	};
	RTSAITestingManager_eventOnTestStarted_Parms Parms;
	Parms.TestScenario=TestScenario;
	OnTestStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTestStarted **********************************************************

// ********** Begin Delegate FOnTestCompleted ******************************************************
struct Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics
{
	struct RTSAITestingManager_eventOnTestCompleted_Parms
	{
		ERTSTestScenario TestScenario;
		bool bPassed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TestScenario_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TestScenario;
	static void NewProp_bPassed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPassed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::NewProp_TestScenario_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::NewProp_TestScenario = { "TestScenario", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventOnTestCompleted_Parms, TestScenario), Z_Construct_UEnum_ArmorWars_ERTSTestScenario, METADATA_PARAMS(0, nullptr) }; // 3240236233
void Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::NewProp_bPassed_SetBit(void* Obj)
{
	((RTSAITestingManager_eventOnTestCompleted_Parms*)Obj)->bPassed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::NewProp_bPassed = { "bPassed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventOnTestCompleted_Parms), &Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::NewProp_bPassed_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::NewProp_TestScenario_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::NewProp_TestScenario,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::NewProp_bPassed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "OnTestCompleted__DelegateSignature", Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::RTSAITestingManager_eventOnTestCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::RTSAITestingManager_eventOnTestCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSAITestingManager::FOnTestCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTestCompleted, ERTSTestScenario TestScenario, bool bPassed)
{
	struct RTSAITestingManager_eventOnTestCompleted_Parms
	{
		ERTSTestScenario TestScenario;
		bool bPassed;
	};
	RTSAITestingManager_eventOnTestCompleted_Parms Parms;
	Parms.TestScenario=TestScenario;
	Parms.bPassed=bPassed ? true : false;
	OnTestCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTestCompleted ********************************************************

// ********** Begin Delegate FOnPerformanceAlert ***************************************************
struct Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics
{
	struct RTSAITestingManager_eventOnPerformanceAlert_Parms
	{
		FString AlertMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlertMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AlertMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::NewProp_AlertMessage = { "AlertMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventOnPerformanceAlert_Parms, AlertMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlertMessage_MetaData), NewProp_AlertMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::NewProp_AlertMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "OnPerformanceAlert__DelegateSignature", Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::RTSAITestingManager_eventOnPerformanceAlert_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::RTSAITestingManager_eventOnPerformanceAlert_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSAITestingManager::FOnPerformanceAlert_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceAlert, const FString& AlertMessage)
{
	struct RTSAITestingManager_eventOnPerformanceAlert_Parms
	{
		FString AlertMessage;
	};
	RTSAITestingManager_eventOnPerformanceAlert_Parms Parms;
	Parms.AlertMessage=AlertMessage;
	OnPerformanceAlert.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPerformanceAlert *****************************************************

// ********** Begin Class URTSAITestingManager Function CleanupTestUnits ***************************
struct Z_Construct_UFunction_URTSAITestingManager_CleanupTestUnits_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_CleanupTestUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "CleanupTestUnits", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_CleanupTestUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_CleanupTestUnits_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAITestingManager_CleanupTestUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_CleanupTestUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execCleanupTestUnits)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupTestUnits();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function CleanupTestUnits *****************************

// ********** Begin Class URTSAITestingManager Function ExportTestResults **************************
struct Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics
{
	struct RTSAITestingManager_eventExportTestResults_Parms
	{
		FString FilePath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventExportTestResults_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::NewProp_FilePath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "ExportTestResults", Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::RTSAITestingManager_eventExportTestResults_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::RTSAITestingManager_eventExportTestResults_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_ExportTestResults()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_ExportTestResults_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execExportTestResults)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportTestResults(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function ExportTestResults ****************************

// ********** Begin Class URTSAITestingManager Function GenerateTestReport *************************
struct Z_Construct_UFunction_URTSAITestingManager_GenerateTestReport_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_GenerateTestReport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "GenerateTestReport", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_GenerateTestReport_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_GenerateTestReport_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAITestingManager_GenerateTestReport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_GenerateTestReport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execGenerateTestReport)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateTestReport();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function GenerateTestReport ***************************

// ********** Begin Class URTSAITestingManager Function GetCurrentPerformanceMetrics ***************
struct Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics
{
	struct RTSAITestingManager_eventGetCurrentPerformanceMetrics_Parms
	{
		FRTSPerformanceMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventGetCurrentPerformanceMetrics_Parms, ReturnValue), Z_Construct_UScriptStruct_FRTSPerformanceMetrics, METADATA_PARAMS(0, nullptr) }; // 4093698240
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "GetCurrentPerformanceMetrics", Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::RTSAITestingManager_eventGetCurrentPerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::RTSAITestingManager_eventGetCurrentPerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execGetCurrentPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FRTSPerformanceMetrics*)Z_Param__Result=P_THIS->GetCurrentPerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function GetCurrentPerformanceMetrics *****************

// ********** Begin Class URTSAITestingManager Function GetSystemValidationErrors ******************
struct Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics
{
	struct RTSAITestingManager_eventGetSystemValidationErrors_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventGetSystemValidationErrors_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "GetSystemValidationErrors", Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::RTSAITestingManager_eventGetSystemValidationErrors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::RTSAITestingManager_eventGetSystemValidationErrors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execGetSystemValidationErrors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetSystemValidationErrors();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function GetSystemValidationErrors ********************

// ********** Begin Class URTSAITestingManager Function GetTestResults *****************************
struct Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics
{
	struct RTSAITestingManager_eventGetTestResults_Parms
	{
		TArray<FRTSTestResult> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Results and reporting\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Results and reporting" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSTestResult, METADATA_PARAMS(0, nullptr) }; // 3159634577
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventGetTestResults_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3159634577
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "GetTestResults", Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::RTSAITestingManager_eventGetTestResults_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::RTSAITestingManager_eventGetTestResults_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_GetTestResults()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_GetTestResults_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execGetTestResults)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FRTSTestResult>*)Z_Param__Result=P_THIS->GetTestResults();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function GetTestResults *******************************

// ********** Begin Class URTSAITestingManager Function RunAllTests ********************************
struct Z_Construct_UFunction_URTSAITestingManager_RunAllTests_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_RunAllTests_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "RunAllTests", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunAllTests_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_RunAllTests_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAITestingManager_RunAllTests()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_RunAllTests_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execRunAllTests)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RunAllTests();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function RunAllTests **********************************

// ********** Begin Class URTSAITestingManager Function RunBasicMovementTest ***********************
struct Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics
{
	struct RTSAITestingManager_eventRunBasicMovementTest_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test scenarios\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test scenarios" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestingManager_eventRunBasicMovementTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventRunBasicMovementTest_Parms), &Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "RunBasicMovementTest", Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::RTSAITestingManager_eventRunBasicMovementTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::RTSAITestingManager_eventRunBasicMovementTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execRunBasicMovementTest)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RunBasicMovementTest();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function RunBasicMovementTest *************************

// ********** Begin Class URTSAITestingManager Function RunCollisionAvoidanceTest ******************
struct Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics
{
	struct RTSAITestingManager_eventRunCollisionAvoidanceTest_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestingManager_eventRunCollisionAvoidanceTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventRunCollisionAvoidanceTest_Parms), &Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "RunCollisionAvoidanceTest", Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::RTSAITestingManager_eventRunCollisionAvoidanceTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::RTSAITestingManager_eventRunCollisionAvoidanceTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execRunCollisionAvoidanceTest)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RunCollisionAvoidanceTest();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function RunCollisionAvoidanceTest ********************

// ********** Begin Class URTSAITestingManager Function RunCombatEngagementTest ********************
struct Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics
{
	struct RTSAITestingManager_eventRunCombatEngagementTest_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestingManager_eventRunCombatEngagementTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventRunCombatEngagementTest_Parms), &Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "RunCombatEngagementTest", Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::RTSAITestingManager_eventRunCombatEngagementTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::RTSAITestingManager_eventRunCombatEngagementTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execRunCombatEngagementTest)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RunCombatEngagementTest();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function RunCombatEngagementTest **********************

// ********** Begin Class URTSAITestingManager Function RunCommandPriorityTest *********************
struct Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics
{
	struct RTSAITestingManager_eventRunCommandPriorityTest_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestingManager_eventRunCommandPriorityTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventRunCommandPriorityTest_Parms), &Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "RunCommandPriorityTest", Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::RTSAITestingManager_eventRunCommandPriorityTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::RTSAITestingManager_eventRunCommandPriorityTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execRunCommandPriorityTest)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RunCommandPriorityTest();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function RunCommandPriorityTest ***********************

// ********** Begin Class URTSAITestingManager Function RunFormationMovementTest *******************
struct Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics
{
	struct RTSAITestingManager_eventRunFormationMovementTest_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestingManager_eventRunFormationMovementTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventRunFormationMovementTest_Parms), &Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "RunFormationMovementTest", Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::RTSAITestingManager_eventRunFormationMovementTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::RTSAITestingManager_eventRunFormationMovementTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execRunFormationMovementTest)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RunFormationMovementTest();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function RunFormationMovementTest *********************

// ********** Begin Class URTSAITestingManager Function RunReturnFireTest **************************
struct Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics
{
	struct RTSAITestingManager_eventRunReturnFireTest_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestingManager_eventRunReturnFireTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventRunReturnFireTest_Parms), &Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "RunReturnFireTest", Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::RTSAITestingManager_eventRunReturnFireTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::RTSAITestingManager_eventRunReturnFireTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execRunReturnFireTest)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RunReturnFireTest();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function RunReturnFireTest ****************************

// ********** Begin Class URTSAITestingManager Function RunStressTest ******************************
struct Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics
{
	struct RTSAITestingManager_eventRunStressTest_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestingManager_eventRunStressTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventRunStressTest_Parms), &Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "RunStressTest", Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::RTSAITestingManager_eventRunStressTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::RTSAITestingManager_eventRunStressTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_RunStressTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_RunStressTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execRunStressTest)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RunStressTest();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function RunStressTest ********************************

// ********** Begin Class URTSAITestingManager Function SpawnTestUnits *****************************
struct Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics
{
	struct RTSAITestingManager_eventSpawnTestUnits_Parms
	{
		int32 Count;
		FVector SpawnLocation;
		float SpawnRadius;
		TArray<ARTSUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test utilities\n" },
#endif
		{ "CPP_Default_SpawnRadius", "500.000000" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test utilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnRadius;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventSpawnTestUnits_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::NewProp_SpawnLocation = { "SpawnLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventSpawnTestUnits_Parms, SpawnLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnLocation_MetaData), NewProp_SpawnLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::NewProp_SpawnRadius = { "SpawnRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventSpawnTestUnits_Parms, SpawnRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventSpawnTestUnits_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::NewProp_Count,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::NewProp_SpawnLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::NewProp_SpawnRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "SpawnTestUnits", Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::RTSAITestingManager_eventSpawnTestUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::RTSAITestingManager_eventSpawnTestUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execSpawnTestUnits)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Count);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_SpawnLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SpawnRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSUnit*>*)Z_Param__Result=P_THIS->SpawnTestUnits(Z_Param_Count,Z_Param_Out_SpawnLocation,Z_Param_SpawnRadius);
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function SpawnTestUnits *******************************

// ********** Begin Class URTSAITestingManager Function StartPerformanceMonitoring *****************
struct Z_Construct_UFunction_URTSAITestingManager_StartPerformanceMonitoring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_StartPerformanceMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "StartPerformanceMonitoring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_StartPerformanceMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_StartPerformanceMonitoring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAITestingManager_StartPerformanceMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_StartPerformanceMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execStartPerformanceMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartPerformanceMonitoring();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function StartPerformanceMonitoring *******************

// ********** Begin Class URTSAITestingManager Function StartTest **********************************
struct Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics
{
	struct RTSAITestingManager_eventStartTest_Parms
	{
		ERTSTestScenario TestScenario;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test execution\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test execution" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TestScenario_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TestScenario;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::NewProp_TestScenario_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::NewProp_TestScenario = { "TestScenario", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventStartTest_Parms, TestScenario), Z_Construct_UEnum_ArmorWars_ERTSTestScenario, METADATA_PARAMS(0, nullptr) }; // 3240236233
void Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestingManager_eventStartTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventStartTest_Parms), &Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::NewProp_TestScenario_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::NewProp_TestScenario,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "StartTest", Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::RTSAITestingManager_eventStartTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::RTSAITestingManager_eventStartTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_StartTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_StartTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execStartTest)
{
	P_GET_ENUM(ERTSTestScenario,Z_Param_TestScenario);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartTest(ERTSTestScenario(Z_Param_TestScenario));
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function StartTest ************************************

// ********** Begin Class URTSAITestingManager Function StopCurrentTest ****************************
struct Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics
{
	struct RTSAITestingManager_eventStopCurrentTest_Parms
	{
		bool bTestPassed;
		FString FailureReason;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "CPP_Default_bTestPassed", "false" },
		{ "CPP_Default_FailureReason", "" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailureReason_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static void NewProp_bTestPassed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTestPassed;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FailureReason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::NewProp_bTestPassed_SetBit(void* Obj)
{
	((RTSAITestingManager_eventStopCurrentTest_Parms*)Obj)->bTestPassed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::NewProp_bTestPassed = { "bTestPassed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventStopCurrentTest_Parms), &Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::NewProp_bTestPassed_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::NewProp_FailureReason = { "FailureReason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventStopCurrentTest_Parms, FailureReason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailureReason_MetaData), NewProp_FailureReason_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::NewProp_bTestPassed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::NewProp_FailureReason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "StopCurrentTest", Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::RTSAITestingManager_eventStopCurrentTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::RTSAITestingManager_eventStopCurrentTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execStopCurrentTest)
{
	P_GET_UBOOL(Z_Param_bTestPassed);
	P_GET_PROPERTY(FStrProperty,Z_Param_FailureReason);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopCurrentTest(Z_Param_bTestPassed,Z_Param_FailureReason);
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function StopCurrentTest ******************************

// ********** Begin Class URTSAITestingManager Function StopPerformanceMonitoring ******************
struct Z_Construct_UFunction_URTSAITestingManager_StopPerformanceMonitoring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_StopPerformanceMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "StopPerformanceMonitoring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_StopPerformanceMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_StopPerformanceMonitoring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSAITestingManager_StopPerformanceMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_StopPerformanceMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execStopPerformanceMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopPerformanceMonitoring();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function StopPerformanceMonitoring ********************

// ********** Begin Class URTSAITestingManager Function UpdatePerformanceMetrics *******************
struct Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics
{
	struct RTSAITestingManager_eventUpdatePerformanceMetrics_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSAITestingManager_eventUpdatePerformanceMetrics_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "UpdatePerformanceMetrics", Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::RTSAITestingManager_eventUpdatePerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::RTSAITestingManager_eventUpdatePerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execUpdatePerformanceMetrics)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function UpdatePerformanceMetrics *********************

// ********** Begin Class URTSAITestingManager Function ValidateAISystem ***************************
struct Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics
{
	struct RTSAITestingManager_eventValidateAISystem_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// System validation\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "System validation" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestingManager_eventValidateAISystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventValidateAISystem_Parms), &Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "ValidateAISystem", Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::RTSAITestingManager_eventValidateAISystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::RTSAITestingManager_eventValidateAISystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execValidateAISystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateAISystem();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function ValidateAISystem *****************************

// ********** Begin Class URTSAITestingManager Function ValidateTestEnvironment ********************
struct Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics
{
	struct RTSAITestingManager_eventValidateTestEnvironment_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSAITestingManager_eventValidateTestEnvironment_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSAITestingManager_eventValidateTestEnvironment_Parms), &Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSAITestingManager, nullptr, "ValidateTestEnvironment", Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::RTSAITestingManager_eventValidateTestEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::RTSAITestingManager_eventValidateTestEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSAITestingManager::execValidateTestEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateTestEnvironment();
	P_NATIVE_END;
}
// ********** End Class URTSAITestingManager Function ValidateTestEnvironment **********************

// ********** Begin Class URTSAITestingManager *****************************************************
void URTSAITestingManager::StaticRegisterNativesURTSAITestingManager()
{
	UClass* Class = URTSAITestingManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CleanupTestUnits", &URTSAITestingManager::execCleanupTestUnits },
		{ "ExportTestResults", &URTSAITestingManager::execExportTestResults },
		{ "GenerateTestReport", &URTSAITestingManager::execGenerateTestReport },
		{ "GetCurrentPerformanceMetrics", &URTSAITestingManager::execGetCurrentPerformanceMetrics },
		{ "GetSystemValidationErrors", &URTSAITestingManager::execGetSystemValidationErrors },
		{ "GetTestResults", &URTSAITestingManager::execGetTestResults },
		{ "RunAllTests", &URTSAITestingManager::execRunAllTests },
		{ "RunBasicMovementTest", &URTSAITestingManager::execRunBasicMovementTest },
		{ "RunCollisionAvoidanceTest", &URTSAITestingManager::execRunCollisionAvoidanceTest },
		{ "RunCombatEngagementTest", &URTSAITestingManager::execRunCombatEngagementTest },
		{ "RunCommandPriorityTest", &URTSAITestingManager::execRunCommandPriorityTest },
		{ "RunFormationMovementTest", &URTSAITestingManager::execRunFormationMovementTest },
		{ "RunReturnFireTest", &URTSAITestingManager::execRunReturnFireTest },
		{ "RunStressTest", &URTSAITestingManager::execRunStressTest },
		{ "SpawnTestUnits", &URTSAITestingManager::execSpawnTestUnits },
		{ "StartPerformanceMonitoring", &URTSAITestingManager::execStartPerformanceMonitoring },
		{ "StartTest", &URTSAITestingManager::execStartTest },
		{ "StopCurrentTest", &URTSAITestingManager::execStopCurrentTest },
		{ "StopPerformanceMonitoring", &URTSAITestingManager::execStopPerformanceMonitoring },
		{ "UpdatePerformanceMetrics", &URTSAITestingManager::execUpdatePerformanceMetrics },
		{ "ValidateAISystem", &URTSAITestingManager::execValidateAISystem },
		{ "ValidateTestEnvironment", &URTSAITestingManager::execValidateTestEnvironment },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSAITestingManager;
UClass* URTSAITestingManager::GetPrivateStaticClass()
{
	using TClass = URTSAITestingManager;
	if (!Z_Registration_Info_UClass_URTSAITestingManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSAITestingManager"),
			Z_Registration_Info_UClass_URTSAITestingManager.InnerSingleton,
			StaticRegisterNativesURTSAITestingManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSAITestingManager.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSAITestingManager_NoRegister()
{
	return URTSAITestingManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSAITestingManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World subsystem for comprehensive AI testing and debugging\n * Manages test scenarios, performance monitoring, and system validation\n */" },
#endif
		{ "IncludePath", "RTSAITestingSystem.h" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World subsystem for comprehensive AI testing and debugging\nManages test scenarios, performance monitoring, and system validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestResults_MetaData[] = {
		{ "Category", "Testing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRunningTest_MetaData[] = {
		{ "Category", "Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTestScenario_MetaData[] = {
		{ "Category", "Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTestStartTime_MetaData[] = {
		{ "Category", "Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMetrics_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameTimeHistory_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFrameHistorySize_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestUnits_MetaData[] = {
		{ "Category", "Testing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Test units\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Test units" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTestStarted_MetaData[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTestCompleted_MetaData[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPerformanceAlert_MetaData[] = {
		{ "Category", "RTS AI Testing" },
		{ "ModuleRelativePath", "Public/RTSAITestingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TestResults_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TestResults;
	static void NewProp_bIsRunningTest_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRunningTest;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentTestScenario_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentTestScenario;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentTestStartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentMetrics;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameTimeHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FrameTimeHistory;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxFrameHistorySize;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TestUnits_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TestUnits;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTestStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTestCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPerformanceAlert;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSAITestingManager_CleanupTestUnits, "CleanupTestUnits" }, // 11244129
		{ &Z_Construct_UFunction_URTSAITestingManager_ExportTestResults, "ExportTestResults" }, // 3705992395
		{ &Z_Construct_UFunction_URTSAITestingManager_GenerateTestReport, "GenerateTestReport" }, // 2037156765
		{ &Z_Construct_UFunction_URTSAITestingManager_GetCurrentPerformanceMetrics, "GetCurrentPerformanceMetrics" }, // 2300823826
		{ &Z_Construct_UFunction_URTSAITestingManager_GetSystemValidationErrors, "GetSystemValidationErrors" }, // 292590693
		{ &Z_Construct_UFunction_URTSAITestingManager_GetTestResults, "GetTestResults" }, // 1114800758
		{ &Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature, "OnPerformanceAlert__DelegateSignature" }, // 860460612
		{ &Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature, "OnTestCompleted__DelegateSignature" }, // 1087244989
		{ &Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature, "OnTestStarted__DelegateSignature" }, // 3187609317
		{ &Z_Construct_UFunction_URTSAITestingManager_RunAllTests, "RunAllTests" }, // 3262713030
		{ &Z_Construct_UFunction_URTSAITestingManager_RunBasicMovementTest, "RunBasicMovementTest" }, // 525778140
		{ &Z_Construct_UFunction_URTSAITestingManager_RunCollisionAvoidanceTest, "RunCollisionAvoidanceTest" }, // 822283745
		{ &Z_Construct_UFunction_URTSAITestingManager_RunCombatEngagementTest, "RunCombatEngagementTest" }, // 2821802232
		{ &Z_Construct_UFunction_URTSAITestingManager_RunCommandPriorityTest, "RunCommandPriorityTest" }, // 1099113450
		{ &Z_Construct_UFunction_URTSAITestingManager_RunFormationMovementTest, "RunFormationMovementTest" }, // 2834077432
		{ &Z_Construct_UFunction_URTSAITestingManager_RunReturnFireTest, "RunReturnFireTest" }, // 2457915271
		{ &Z_Construct_UFunction_URTSAITestingManager_RunStressTest, "RunStressTest" }, // 1997158290
		{ &Z_Construct_UFunction_URTSAITestingManager_SpawnTestUnits, "SpawnTestUnits" }, // 3750498019
		{ &Z_Construct_UFunction_URTSAITestingManager_StartPerformanceMonitoring, "StartPerformanceMonitoring" }, // 1490973191
		{ &Z_Construct_UFunction_URTSAITestingManager_StartTest, "StartTest" }, // 406727382
		{ &Z_Construct_UFunction_URTSAITestingManager_StopCurrentTest, "StopCurrentTest" }, // 1037392054
		{ &Z_Construct_UFunction_URTSAITestingManager_StopPerformanceMonitoring, "StopPerformanceMonitoring" }, // 3174369280
		{ &Z_Construct_UFunction_URTSAITestingManager_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 3793929137
		{ &Z_Construct_UFunction_URTSAITestingManager_ValidateAISystem, "ValidateAISystem" }, // 3013109245
		{ &Z_Construct_UFunction_URTSAITestingManager_ValidateTestEnvironment, "ValidateTestEnvironment" }, // 4119474173
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSAITestingManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_TestResults_Inner = { "TestResults", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSTestResult, METADATA_PARAMS(0, nullptr) }; // 3159634577
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_TestResults = { "TestResults", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAITestingManager, TestResults), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestResults_MetaData), NewProp_TestResults_MetaData) }; // 3159634577
void Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_bIsRunningTest_SetBit(void* Obj)
{
	((URTSAITestingManager*)Obj)->bIsRunningTest = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_bIsRunningTest = { "bIsRunningTest", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSAITestingManager), &Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_bIsRunningTest_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRunningTest_MetaData), NewProp_bIsRunningTest_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_CurrentTestScenario_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_CurrentTestScenario = { "CurrentTestScenario", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAITestingManager, CurrentTestScenario), Z_Construct_UEnum_ArmorWars_ERTSTestScenario, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTestScenario_MetaData), NewProp_CurrentTestScenario_MetaData) }; // 3240236233
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_CurrentTestStartTime = { "CurrentTestStartTime", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAITestingManager, CurrentTestStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTestStartTime_MetaData), NewProp_CurrentTestStartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_CurrentMetrics = { "CurrentMetrics", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAITestingManager, CurrentMetrics), Z_Construct_UScriptStruct_FRTSPerformanceMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMetrics_MetaData), NewProp_CurrentMetrics_MetaData) }; // 4093698240
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_FrameTimeHistory_Inner = { "FrameTimeHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_FrameTimeHistory = { "FrameTimeHistory", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAITestingManager, FrameTimeHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameTimeHistory_MetaData), NewProp_FrameTimeHistory_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_MaxFrameHistorySize = { "MaxFrameHistorySize", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAITestingManager, MaxFrameHistorySize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFrameHistorySize_MetaData), NewProp_MaxFrameHistorySize_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_TestUnits_Inner = { "TestUnits", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_TestUnits = { "TestUnits", nullptr, (EPropertyFlags)0x0024080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAITestingManager, TestUnits), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestUnits_MetaData), NewProp_TestUnits_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_OnTestStarted = { "OnTestStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAITestingManager, OnTestStarted), Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTestStarted_MetaData), NewProp_OnTestStarted_MetaData) }; // 3187609317
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_OnTestCompleted = { "OnTestCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAITestingManager, OnTestCompleted), Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTestCompleted_MetaData), NewProp_OnTestCompleted_MetaData) }; // 1087244989
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_OnPerformanceAlert = { "OnPerformanceAlert", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSAITestingManager, OnPerformanceAlert), Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPerformanceAlert_MetaData), NewProp_OnPerformanceAlert_MetaData) }; // 860460612
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSAITestingManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_TestResults_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_TestResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_bIsRunningTest,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_CurrentTestScenario_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_CurrentTestScenario,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_CurrentTestStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_CurrentMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_FrameTimeHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_FrameTimeHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_MaxFrameHistorySize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_TestUnits_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_TestUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_OnTestStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_OnTestCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSAITestingManager_Statics::NewProp_OnPerformanceAlert,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAITestingManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSAITestingManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAITestingManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSAITestingManager_Statics::ClassParams = {
	&URTSAITestingManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSAITestingManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSAITestingManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSAITestingManager_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSAITestingManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSAITestingManager()
{
	if (!Z_Registration_Info_UClass_URTSAITestingManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSAITestingManager.OuterSingleton, Z_Construct_UClass_URTSAITestingManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSAITestingManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSAITestingManager);
URTSAITestingManager::~URTSAITestingManager() {}
// ********** End Class URTSAITestingManager *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSTestScenario_StaticEnum, TEXT("ERTSTestScenario"), &Z_Registration_Info_UEnum_ERTSTestScenario, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3240236233U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRTSTestResult::StaticStruct, Z_Construct_UScriptStruct_FRTSTestResult_Statics::NewStructOps, TEXT("RTSTestResult"), &Z_Registration_Info_UScriptStruct_FRTSTestResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSTestResult), 3159634577U) },
		{ FRTSPerformanceMetrics::StaticStruct, Z_Construct_UScriptStruct_FRTSPerformanceMetrics_Statics::NewStructOps, TEXT("RTSPerformanceMetrics"), &Z_Registration_Info_UScriptStruct_FRTSPerformanceMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSPerformanceMetrics), 4093698240U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSAIDebugComponent, URTSAIDebugComponent::StaticClass, TEXT("URTSAIDebugComponent"), &Z_Registration_Info_UClass_URTSAIDebugComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSAIDebugComponent), 2602142112U) },
		{ Z_Construct_UClass_URTSAITestingManager, URTSAITestingManager::StaticClass, TEXT("URTSAITestingManager"), &Z_Registration_Info_UClass_URTSAITestingManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSAITestingManager), 753526955U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h__Script_ArmorWars_1653003397(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h__Script_ArmorWars_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h__Script_ArmorWars_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h__Script_ArmorWars_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSAITestingSystem_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

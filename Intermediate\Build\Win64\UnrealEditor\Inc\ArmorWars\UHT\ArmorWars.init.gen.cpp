// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeArmorWars_init() {}
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnBuildSpeedChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnResourceChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSGroupManager_OnGroupBehaviorChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSGroupManager_OnGroupMemberAdded__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSGroupManager_OnGroupMemberRemoved__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSProjectile_OnProjectileExploded__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSProjectile_OnProjectileImpact__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSAircraftFlightComponent_OnAltitudeChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSAircraftFlightComponent_OnFlightModeChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSArmorComponent_OnArmorHit__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSArmorComponent_OnArmorPenetrated__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSFactoryComponent_OnProductionCancelled__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSFactoryComponent_OnProductionCompleted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSFactoryComponent_OnProductionStarted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSFactoryComponent_OnQueueChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSPointDefenseComponent_OnProjectileIntercepted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSPointDefenseComponent_OnSystemStateChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSPointDefenseComponent_OnTargetAcquired__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSTacticalAIComponent_OnTacticalManeuverCompleted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSTacticalAIComponent_OnTacticalManeuverStarted__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSUnitAIComponent_OnBehaviorChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSUnitAIComponent_OnFormationChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSWeaponComponent_OnAmmoChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSWeaponComponent_OnTargetChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSWeaponComponent_OnWeaponFired__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature();
	ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_ArmorWars;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_ArmorWars()
	{
		if (!Z_Registration_Info_UPackage__Script_ArmorWars.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnActorDeath__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnBuildSpeedChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnCommandCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnCommandQueueChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnCommandReceived__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnDamageReceived__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnHealthChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnMovementChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnResourceChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ArmorWars_OnUnitSelectionChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSAIController_OnAIStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSAIController_OnEnemyDetected__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSAIController_OnTargetAcquired__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSAirUnit_OnAirCombatEngaged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSAirUnit_OnFormationChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestPhaseChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSAITestScenario_OnTestStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSBaseActor_OnActorDeath__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSBaseActor_OnDamageReceived__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSBaseActor_OnHealthChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSBaseActor_OnSelectionChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingCombat__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSBuilding_OnBuildingStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSBuilding_OnConstructionProgress__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSGroupManager_OnGroupBehaviorChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSGroupManager_OnGroupMemberAdded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSGroupManager_OnGroupMemberRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSLandUnit_OnTerrainChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSProjectile_OnProjectileExploded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSProjectile_OnProjectileImpact__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalCombatEngaged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSSeaUnit_OnNavalFormationChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_ARTSSeaUnit_OnSubmarineStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSAircraftFlightComponent_OnAltitudeChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSAircraftFlightComponent_OnFlightModeChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSAITestingManager_OnPerformanceAlert__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSAITestingManager_OnTestStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSArmorComponent_OnArmorHit__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSArmorComponent_OnArmorPenetrated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandAdded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandExecuted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnCommandRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSCommandPrioritySystem_OnQueueChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSFactoryComponent_OnProductionCancelled__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSFactoryComponent_OnProductionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSFactoryComponent_OnProductionStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSFactoryComponent_OnQueueChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSPointDefenseComponent_OnProjectileIntercepted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSPointDefenseComponent_OnSystemStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSPointDefenseComponent_OnTargetAcquired__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSTacticalAIComponent_OnTacticalManeuverCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSTacticalAIComponent_OnTacticalManeuverStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamCreated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSTeamManager_OnTeamRemoved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSUnitAIComponent_OnBehaviorChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSUnitAIComponent_OnFormationChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSWeaponComponent_OnAmmoChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSWeaponComponent_OnTargetChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSWeaponComponent_OnWeaponFired__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSWeaponController_OnFiringStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSWeaponController_OnTargetChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_URTSWeaponController_OnWeaponFired__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/ArmorWars",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0xE9AAC047,
				0xDDC4E9D8,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_ArmorWars.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_ArmorWars.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_ArmorWars(Z_Construct_UPackage__Script_ArmorWars, TEXT("/Script/ArmorWars"), Z_Registration_Info_UPackage__Script_ArmorWars, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xE9AAC047, 0xDDC4E9D8));
PRAGMA_ENABLE_DEPRECATION_WARNINGS

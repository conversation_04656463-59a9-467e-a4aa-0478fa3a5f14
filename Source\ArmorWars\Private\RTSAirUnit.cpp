#include "RTSAirUnit.h"
#include "RTSAirMovementComponent.h"
#include "RTSAircraftFlightComponent.h"
#include "RTSArmorComponent.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"

ARTSAirUnit::ARTSAirUnit()
{
    // Set default values for air units
    UnitDomain = ERTSUnitDomain::Air;

    // Create air movement component
    AirMovementComponent = CreateDefaultSubobject<URTSAirMovementComponent>(TEXT("AirMovementComponent"));

    // Create flight component
    FlightComponent = CreateDefaultSubobject<URTSAircraftFlightComponent>(TEXT("FlightComponent"));

    // Air units typically have lighter armor
    if (ArmorComponent)
    {
        ArmorComponent->SetUniformArmor(20.0f, ERTSArmorType::Light);
    }
}

void ARTSAirUnit::BeginPlay()
{
    Super::BeginPlay();
    
    // Ensure flight component is active for air units
    if (FlightComponent)
    {
        FlightComponent->SetActive(true);

        // Role configuration removed - behavior set modularly
    }
    
    // Store initial altitude
    PreviousAltitude = FlightComponent ? FlightComponent->GetAltitudeAGL() : GetActorLocation().Z;
}

void ARTSAirUnit::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    UpdateFormationFlying(DeltaTime);
    UpdateAirCombat(DeltaTime);
    UpdateSensors(DeltaTime);
}

// Simple Flight Control Functions (arcade-style)
void ARTSAirUnit::SetAltitude(float TargetAltitude)
{
    // Simple altitude change - just move the actor directly
    FVector CurrentLocation = GetActorLocation();
    CurrentLocation.Z = FMath::Clamp(TargetAltitude, 100.0f, ServiceCeiling);
    SetActorLocation(CurrentLocation);
}

void ARTSAirUnit::SetCruiseAltitude()
{
    SetAltitude(CruiseAltitude);
}

void ARTSAirUnit::PerformEvasiveManeuvers()
{
    // Simple evasive maneuver - quick position change
    FVector RandomDirection = FMath::VRand();
    RandomDirection.Z = FMath::RandRange(-0.3f, 0.3f); // Limit vertical component
    RandomDirection.Normalize();

    FVector CurrentLocation = GetActorLocation();
    FVector EvasiveLocation = CurrentLocation + RandomDirection * 1000.0f; // 10 meter evasive move

    // Clamp to valid altitude
    EvasiveLocation.Z = FMath::Clamp(EvasiveLocation.Z, 100.0f, ServiceCeiling);

    SetActorLocation(EvasiveLocation);
}

// Formation Functions
void ARTSAirUnit::JoinFormation(ARTSAirUnit* Leader, ERTSFormationType Formation, const FVector& Offset)
{
    if (Leader && Leader != this)
    {
        FormationLeader = Leader;
        FormationType = Formation;
        FormationOffset = Offset;
        
        OnFormationChanged.Broadcast(this, Formation);
        OnFormationJoined(Leader);
    }
}

void ARTSAirUnit::LeaveFormation()
{
    if (IsInFormation())
    {
        FormationLeader.Reset();
        FormationType = ERTSFormationType::Line; // Default to line formation when not in formation
        SetFormationData(false, FVector::ZeroVector); // Use base class method

        OnFormationChanged.Broadcast(this, ERTSFormationType::Line);
        OnFormationLeft();
    }
}

void ARTSAirUnit::SetFormationLeader(const TArray<ARTSAirUnit*>& Followers, ERTSFormationType Formation)
{
    FormationType = Formation;
    
    // Set formation positions for followers
    for (int32 i = 0; i < Followers.Num(); i++)
    {
        if (Followers[i] && Followers[i] != this)
        {
            FVector Offset = CalculateFormationOffset(Formation, i);
            Followers[i]->JoinFormation(this, Formation, Offset);
        }
    }
}

// IsInFormation() is now inherited from RTSUnit base class

bool ARTSAirUnit::IsFormationLeader() const
{
    return !FormationLeader.IsValid() && FormationType != ERTSFormationType::Line; // Line is default, so check for other formations
}

// Air Combat Functions
void ARTSAirUnit::EngageAirTarget(ARTSAirUnit* Target)
{
    if (Target && bCanEngageAirTargets && IsValidAirTarget(Target))
    {
        // TODO: Implement weapon controller engagement
        OnAirCombatEngaged.Broadcast(this, Target);
        OnAirTargetEngaged(Target);
    }
}

void ARTSAirUnit::EngageGroundTarget(ARTSUnit* Target)
{
    if (Target && bCanEngageGroundTargets && IsValidGroundTarget(Target))
    {
        // TODO: Implement weapon controller engagement
        OnAirCombatEngaged.Broadcast(this, Target);
    }
}

ARTSAirUnit* ARTSAirUnit::FindNearestAirThreat() const
{
    TArray<ARTSAirUnit*> AirTargets = GetAirTargetsInRange();
    
    ARTSAirUnit* NearestThreat = nullptr;
    float NearestDistance = FLT_MAX;
    
    for (ARTSAirUnit* Target : AirTargets)
    {
        if (IsValidAirTarget(Target))
        {
            float Distance = FVector::Dist(GetActorLocation(), Target->GetActorLocation());
            if (Distance < NearestDistance)
            {
                NearestDistance = Distance;
                NearestThreat = Target;
            }
        }
    }
    
    return NearestThreat;
}

TArray<ARTSAirUnit*> ARTSAirUnit::GetAirTargetsInRange() const
{
    TArray<ARTSAirUnit*> AirTargets;
    
    if (UWorld* World = GetWorld())
    {
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(World, ARTSAirUnit::StaticClass(), FoundActors);
        
        FVector MyLocation = GetActorLocation();
        
        for (AActor* Actor : FoundActors)
        {
            if (ARTSAirUnit* AirUnit = Cast<ARTSAirUnit>(Actor))
            {
                if (AirUnit != this && IsEnemy(AirUnit))
                {
                    float Distance = FVector::Dist(MyLocation, AirUnit->GetActorLocation());
                    if (Distance <= AirToAirRange)
                    {
                        AirTargets.Add(AirUnit);
                    }
                }
            }
        }
    }
    
    return AirTargets;
}

// Simple Fuel Functions (autonomous units don't need complex fuel management)
bool ARTSAirUnit::HasFuel() const
{
    return true; // Autonomous units always have fuel
}

// Sensor Functions
void ARTSAirUnit::ToggleRadar()
{
    bRadarActive = !bRadarActive;
}

TArray<ARTSUnit*> ARTSAirUnit::GetRadarContacts() const
{
    TArray<ARTSUnit*> Contacts;
    
    if (!bRadarActive)
    {
        return Contacts;
    }
    
    if (UWorld* World = GetWorld())
    {
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(World, ARTSUnit::StaticClass(), FoundActors);
        
        FVector MyLocation = GetActorLocation();
        
        for (AActor* Actor : FoundActors)
        {
            if (ARTSUnit* Unit = Cast<ARTSUnit>(Actor))
            {
                if (Unit != this && CanDetectTarget(Unit))
                {
                    float Distance = FVector::Dist(MyLocation, Unit->GetActorLocation());
                    if (Distance <= RadarRange)
                    {
                        Contacts.Add(Unit);
                    }
                }
            }
        }
    }
    
    return Contacts;
}

bool ARTSAirUnit::CanDetectTarget(const ARTSUnit* Target) const
{
    if (!Target || !bRadarActive)
    {
        return false;
    }
    
    // Air units are easier to detect
    if (Target->IsAirUnit())
    {
        return true;
    }
    
    // Ground units are harder to detect from altitude
    float Distance = FVector::Dist(GetActorLocation(), Target->GetActorLocation());
    float DetectionRange = RadarRange * 0.7f; // Reduced range for ground targets
    
    return Distance <= DetectionRange;
}

// State Query Functions
bool ARTSAirUnit::IsAtCruiseAltitude() const
{
    float CurrentAltitude = FlightComponent ? FlightComponent->GetAltitudeAGL() : GetActorLocation().Z;
    return FMath::Abs(CurrentAltitude - CruiseAltitude) < 100.0f; // 1 meter tolerance
}

bool ARTSAirUnit::IsClimbing() const
{
    return GetVerticalSpeed() > 50.0f; // 0.5 m/s threshold
}

bool ARTSAirUnit::IsDescending() const
{
    return GetVerticalSpeed() < -50.0f; // -0.5 m/s threshold
}

float ARTSAirUnit::GetVerticalSpeed() const
{
    float CurrentAltitude = FlightComponent ? FlightComponent->GetAltitudeAGL() : GetActorLocation().Z;
    float DeltaTime = GetWorld()->GetDeltaSeconds();

    if (DeltaTime > 0.0f)
    {
        return (CurrentAltitude - PreviousAltitude) / DeltaTime;
    }

    return 0.0f;
}

// Protected Functions

void ARTSAirUnit::UpdateFormationFlying(float DeltaTime)
{
    if (IsInFormation() && FormationLeader.IsValid())
    {
        MaintainFormation(DeltaTime);
    }
}

void ARTSAirUnit::UpdateAirCombat(float DeltaTime)
{
    // Auto-engage air threats if not already attacking
    if (!IsAttacking() && bCanEngageAirTargets)
    {
        ARTSAirUnit* NearestThreat = FindNearestAirThreat();
        if (NearestThreat)
        {
            EngageAirTarget(NearestThreat);
        }
    }
}

void ARTSAirUnit::UpdateSensors(float DeltaTime)
{
    if (bRadarActive)
    {
        // Periodically scan for new contacts
        static float LastScanTime = 0.0f;
        float CurrentTime = GetWorld()->GetTimeSeconds();

        if (CurrentTime - LastScanTime > 1.0f) // Scan every second
        {
            TArray<ARTSUnit*> Contacts = GetRadarContacts();
            for (ARTSUnit* Contact : Contacts)
            {
                OnRadarContactDetected(Contact);
            }
            LastScanTime = CurrentTime;
        }
    }
}

void ARTSAirUnit::UpdateMovement(float DeltaTime)
{
    // Simple arcade-style movement for air units
    if (FlightComponent && FlightComponent->IsFlying())
    {
        // Maintain minimum altitude
        FVector CurrentLocation = GetActorLocation();
        if (CurrentLocation.Z < 100.0f)
        {
            CurrentLocation.Z = 100.0f;
            SetActorLocation(CurrentLocation);
        }
    }

    // Use base movement for pathfinding
    Super::UpdateMovement(DeltaTime);
}

void ARTSAirUnit::UpdateCombat(float DeltaTime)
{
    // Use air-specific combat logic
    UpdateAirCombat(DeltaTime);

    // Call base combat update for weapon handling
    Super::UpdateCombat(DeltaTime);
}

FVector ARTSAirUnit::CalculateFormationPosition() const
{
    if (!FormationLeader.IsValid())
    {
        return GetActorLocation();
    }

    FVector LeaderLocation = FormationLeader->GetActorLocation();
    FVector LeaderForward = FormationLeader->GetActorForwardVector();
    FVector LeaderRight = FormationLeader->GetActorRightVector();
    FVector LeaderUp = FormationLeader->GetActorUpVector();

    // Transform offset to world space
    FVector WorldOffset = LeaderForward * FormationOffset.X +
                         LeaderRight * FormationOffset.Y +
                         LeaderUp * FormationOffset.Z;

    return LeaderLocation + WorldOffset;
}

void ARTSAirUnit::MaintainFormation(float DeltaTime)
{
    FVector TargetPosition = CalculateFormationPosition();

    // Simple formation movement - move towards target position
    FVector CurrentLocation = GetActorLocation();
    FVector Direction = (TargetPosition - CurrentLocation).GetSafeNormal();

    float FormationSpeed = MovementSpeed * 0.8f; // Slightly slower for formation flying
    FVector NewLocation = CurrentLocation + Direction * FormationSpeed * DeltaTime;

    SetActorLocation(NewLocation);
}

bool ARTSAirUnit::IsValidAirTarget(const ARTSAirUnit* Target) const
{
    if (!Target || !Target->IsAlive() || IsOnSameTeam(Target))
    {
        return false;
    }

    // Check if target is in range
    float Distance = FVector::Dist(GetActorLocation(), Target->GetActorLocation());
    return Distance <= AirToAirRange;
}

bool ARTSAirUnit::IsValidGroundTarget(const ARTSUnit* Target) const
{
    if (!Target || !Target->IsAlive() || IsOnSameTeam(Target))
    {
        return false;
    }

    // Check if target is ground-based
    if (Target->IsAirUnit())
    {
        return false;
    }

    // Check if target is in range
    float Distance = FVector::Dist(GetActorLocation(), Target->GetActorLocation());
    return Distance <= AirToGroundRange;
}

float ARTSAirUnit::CalculateInterceptCourse(const ARTSAirUnit* Target, FVector& OutInterceptPoint) const
{
    if (!Target)
    {
        OutInterceptPoint = FVector::ZeroVector;
        return 0.0f;
    }

    FVector TargetPos = Target->GetActorLocation();
    FVector TargetVelocity = Target->GetVelocity();
    FVector MyLocation = GetActorLocation();
    float MySpeed = FlightComponent ? FlightComponent->GetCurrentSpeed() : 0.0f;

    // Simple intercept calculation
    FVector RelativePosition = TargetPos - MyLocation;
    float TargetSpeed = TargetVelocity.Size();

    if (TargetSpeed < 1.0f || MySpeed < 1.0f)
    {
        OutInterceptPoint = TargetPos;
        return RelativePosition.Size() / FMath::Max(MySpeed, 1.0f);
    }

    // Solve for intercept time
    float a = TargetSpeed * TargetSpeed - MySpeed * MySpeed;
    float b = 2.0f * FVector::DotProduct(RelativePosition, TargetVelocity);
    float c = RelativePosition.SizeSquared();

    float Discriminant = b * b - 4.0f * a * c;
    if (Discriminant < 0.0f)
    {
        OutInterceptPoint = TargetPos;
        return RelativePosition.Size() / MySpeed;
    }

    float InterceptTime = (-b - FMath::Sqrt(Discriminant)) / (2.0f * a);
    if (InterceptTime < 0.0f)
    {
        InterceptTime = (-b + FMath::Sqrt(Discriminant)) / (2.0f * a);
    }

    OutInterceptPoint = TargetPos + TargetVelocity * InterceptTime;
    return InterceptTime;
}

// Role configuration function removed - behavior will be set modularly

FVector ARTSAirUnit::CalculateFormationOffset(ERTSFormationType Formation, int32 Position) const
{
    FVector Offset = FVector::ZeroVector;

    switch (Formation)
    {
        case ERTSFormationType::Line:
            Offset = FVector(0, Position * 200.0f, 0); // 2 meter spacing
            break;

        case ERTSFormationType::Column:
            Offset = FVector(-Position * 200.0f, 0, 0); // Trail formation
            break;

        case ERTSFormationType::Wedge:
            Offset = FVector(-Position * 150.0f, (Position % 2 == 0 ? 1 : -1) * (Position / 2 + 1) * 150.0f, 0);
            break;

        case ERTSFormationType::Square:
            if (Position == 0) Offset = FVector(-200.0f, 0, 0);
            else if (Position == 1) Offset = FVector(0, 200.0f, 0);
            else if (Position == 2) Offset = FVector(0, -200.0f, 0);
            else Offset = FVector(-400.0f, 0, 0);
            break;

        case ERTSFormationType::Circle:
            {
                float Angle = (2.0f * PI * Position) / 8.0f; // Assume max 8 units in circle
                float Radius = 300.0f;
                Offset = FVector(FMath::Cos(Angle) * Radius, FMath::Sin(Angle) * Radius, 0);
            }
            break;

        case ERTSFormationType::Scattered:
            // Random scattered formation
            Offset = FVector(
                FMath::RandRange(-400.0f, 400.0f),
                FMath::RandRange(-400.0f, 400.0f),
                FMath::RandRange(-100.0f, 100.0f)
            );
            break;

        default:
            break;
    }

    return Offset;
}

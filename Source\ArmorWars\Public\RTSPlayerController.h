// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include "InputActionValue.h"
#include "RTSPlayerController.generated.h"


class URTSSelectionSystem;
class ARTSUnit;
class ARTSBuilding;
class UInputMappingContext;
class UInputAction;

/**
 * Player Controller for ArmorWars RTS game
 * Handles RTS-specific input and camera controls
 */
UCLASS(BlueprintType, Blueprintable)
class ARMORWARS_API ARTSPlayerController : public APlayerController
{
	GENERATED_BODY()

public:
	ARTSPlayerController();

	// APlayerController interface
	virtual void BeginPlay() override;
	virtual void SetupInputComponent() override;
	virtual void Tick(float DeltaTime) override;

protected:
	/** Enhanced Input action for primary selection (left mouse button) */
	UFUNCTION(BlueprintCallable, Category = "RTS Input")
	void OnPrimaryAction(const FInputActionValue& Value);

	/** Enhanced Input action for primary action pressed (start selection box) */
	UFUNCTION(BlueprintCallable, Category = "RTS Input")
	void OnPrimaryActionPressed(const FInputActionValue& Value);

	/** Enhanced Input action for primary action released (end selection box) */
	UFUNCTION(BlueprintCallable, Category = "RTS Input")
	void OnPrimaryActionReleased(const FInputActionValue& Value);

	/** Enhanced Input action for secondary action (right mouse button) */
	UFUNCTION(BlueprintCallable, Category = "RTS Input")
	void OnSecondaryAction(const FInputActionValue& Value);

	/** Enhanced Input action for camera movement forward/backward */
	UFUNCTION(BlueprintCallable, Category = "RTS Input")
	void OnCameraMoveForward(const FInputActionValue& Value);

	/** Enhanced Input action for camera movement left/right */
	UFUNCTION(BlueprintCallable, Category = "RTS Input")
	void OnCameraMoveRight(const FInputActionValue& Value);

	/** Enhanced Input action for camera zoom */
	UFUNCTION(BlueprintCallable, Category = "RTS Input")
	void OnCameraZoom(const FInputActionValue& Value);

	/** Enhanced Input action for camera rotation */
	UFUNCTION(BlueprintCallable, Category = "RTS Input")
	void OnCameraRotate(const FInputActionValue& Value);

public:


	/** Get the selection system */
	UFUNCTION(BlueprintCallable, Category = "RTS Player Controller")
	URTSSelectionSystem* GetSelectionSystem() const;

	/** Get the player's team ID */
	UFUNCTION(BlueprintCallable, Category = "RTS Player Controller")
	int32 GetPlayerTeamID() const;

	/** Set the player's team ID */
	UFUNCTION(BlueprintCallable, Category = "RTS Player Controller")
	void SetPlayerTeamID(int32 NewTeamID);

	/** Start selection box at current mouse position */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	void StartSelectionBox();

	/** Update selection box to current mouse position */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	void UpdateSelectionBox();

	/** End selection box and perform selection */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	void EndSelectionBox();

	/** Get current mouse position in screen space */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	FVector2D GetCurrentMousePosition() const;

	/** Get the RTS pawn if possessed */
	UFUNCTION(BlueprintCallable, Category = "RTS Camera")
	class ARTSPawn* GetRTSPawn() const;

	/** Set camera movement speed on the RTS pawn */
	UFUNCTION(BlueprintCallable, Category = "RTS Camera")
	void SetCameraMovementSpeed(float NewSpeed);

	/** Set camera zoom speed on the RTS pawn */
	UFUNCTION(BlueprintCallable, Category = "RTS Camera")
	void SetCameraZoomSpeed(float NewSpeed);

	/** Set camera rotation speed on the RTS pawn */
	UFUNCTION(BlueprintCallable, Category = "RTS Camera")
	void SetCameraRotationSpeed(float NewSpeed);

	/** Perform box selection with current selection box bounds */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	void PerformBoxSelection();

	/** Perform single unit selection at mouse position */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	void PerformSingleSelection();

	/** Find units within screen space selection box */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	TArray<AActor*> FindUnitsInSelectionBox(const FVector2D& StartPos, const FVector2D& EndPos);

	/** Alternative method: Find units using world space frustum */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	TArray<AActor*> FindUnitsInSelectionBoxWorldSpace(const FVector2D& StartPos, const FVector2D& EndPos);

	/** Convert screen position to world position */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	bool ScreenToWorldPosition(const FVector2D& ScreenPosition, FVector& WorldPosition, FVector& WorldDirection);

	/** Get currently selected units */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	TArray<AActor*> GetSelectedUnits() const { return SelectedUnits; }

	/** Clear current selection */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	void ClearUnitSelection();

	/** Add unit to selection */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	void AddUnitToSelection(AActor* Unit);

	/** Remove unit from selection */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	void RemoveUnitFromSelection(AActor* Unit);

	/** Set selection (replaces current selection) */
	UFUNCTION(BlueprintCallable, Category = "RTS Selection")
	void SetUnitSelection(const TArray<AActor*>& Units);

	/** Issue a move command to selected units at specific location */
	UFUNCTION(BlueprintCallable, Category = "RTS Commands")
	void IssueMoveCommandToLocation(const FVector& TargetLocation);

	/** Issue an attack command to selected units */
	UFUNCTION(BlueprintCallable, Category = "RTS Commands")
	void IssueAttackCommand(AActor* TargetActor);

	/** Issue a stop command to selected units */
	UFUNCTION(BlueprintCallable, Category = "RTS Commands")
	void IssueStopCommand();

	/** Issue a move command to selected units at mouse position */
	UFUNCTION(BlueprintCallable, Category = "RTS Commands")
	void IssueMoveCommand();

	/** Issue an attack-move command to selected units */
	UFUNCTION(BlueprintCallable, Category = "RTS Commands")
	void IssueAttackMoveCommand();

	/** Get world location from mouse cursor */
	UFUNCTION(BlueprintCallable, Category = "RTS Commands")
	bool GetMouseWorldLocation(FVector& WorldLocation);

	/** Check if location is valid for movement */
	UFUNCTION(BlueprintCallable, Category = "RTS Commands")
	bool IsValidMoveLocation(const FVector& Location);

	/** Calculate formation positions for multiple units */
	UFUNCTION(BlueprintCallable, Category = "RTS Commands")
	TArray<FVector> CalculateFormationPositions(const FVector& CenterLocation, int32 UnitCount);

protected:
	/** Blueprint event called when primary action is performed */
	UFUNCTION(BlueprintImplementableEvent, Category = "RTS Input")
	void OnPrimaryActionPerformed();

	/** Blueprint event called when secondary action is performed */
	UFUNCTION(BlueprintImplementableEvent, Category = "RTS Input")
	void OnSecondaryActionPerformed();

	/** Blueprint event called when camera should move */
	UFUNCTION(BlueprintImplementableEvent, Category = "RTS Input")
	void OnCameraMoveRequested(float AxisValue);

	/** Blueprint event called when camera should zoom */
	UFUNCTION(BlueprintImplementableEvent, Category = "RTS Input")
	void OnCameraZoomRequested(float AxisValue);

	/** Blueprint event called when camera should rotate */
	UFUNCTION(BlueprintImplementableEvent, Category = "RTS Input")
	void OnCameraRotateRequested(float AxisValue);

	/** Blueprint event called when selection changes */
	UFUNCTION(BlueprintImplementableEvent, Category = "RTS Selection")
	void OnSelectionChanged(const TArray<AActor*>& NewSelection);

	/** Blueprint event called when move command is issued */
	UFUNCTION(BlueprintImplementableEvent, Category = "RTS Commands")
	void OnMoveCommandIssued(const FVector& TargetLocation, const TArray<AActor*>& Units);

	/** Blueprint event called when attack-move command is issued */
	UFUNCTION(BlueprintImplementableEvent, Category = "RTS Commands")
	void OnAttackMoveCommandIssued(const FVector& TargetLocation, const TArray<AActor*>& Units);

protected:


	/** The player's team ID */
	UPROPERTY(BlueprintReadWrite, Category = "Team")
	int32 PlayerTeamID = 0;

	/** Camera movement speed */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera")
	float CameraMovementSpeed = 1000.0f;

	/** Camera zoom speed */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera")
	float CameraZoomSpeed = 100.0f;

	/** Camera rotation speed */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera")
	float CameraRotationSpeed = 90.0f;

	/** Whether debug logging is enabled */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
	bool bEnableDebugLogging = false;

	/** Whether selection box is currently being drawn */
	UPROPERTY(BlueprintReadOnly, Category = "Selection")
	bool bIsDrawingSelectionBox = false;

	/** Start position of selection box in screen space */
	UPROPERTY(BlueprintReadOnly, Category = "Selection")
	FVector2D SelectionBoxStartPosition;

	/** Current mouse position for selection box */
	UPROPERTY(BlueprintReadOnly, Category = "Selection")
	FVector2D CurrentMousePosition;

	/** Minimum distance mouse must move to start selection box */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection")
	float SelectionBoxMinDistance = 5.0f;

	/** Enhanced Input Mapping Context */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Enhanced Input")
	TObjectPtr<UInputMappingContext> DefaultMappingContext;

	/** Enhanced Input Actions */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Enhanced Input")
	TObjectPtr<UInputAction> PrimaryAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Enhanced Input")
	TObjectPtr<UInputAction> SecondaryAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Enhanced Input")
	TObjectPtr<UInputAction> CameraMoveForwardAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Enhanced Input")
	TObjectPtr<UInputAction> CameraMoveRightAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Enhanced Input")
	TObjectPtr<UInputAction> CameraZoomAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Enhanced Input")
	TObjectPtr<UInputAction> CameraRotateAction;

private:
	/** Currently selected units */
	UPROPERTY()
	TArray<AActor*> SelectedUnits;
};

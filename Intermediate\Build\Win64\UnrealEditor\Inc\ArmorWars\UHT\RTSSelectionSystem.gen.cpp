// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSSelectionSystem.h"
#include "Interfaces/RTSUnitSelectionInterface.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSSelectionSystem() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSPlayerController_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSSelectionSystem();
ARMORWARS_API UClass* Z_Construct_UClass_URTSSelectionSystem_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSUnitSelectionInterface_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSSelectionFilter();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSSelectionGroup();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Delegate FOnRTSSelectionChanged ************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnRTSSelectionChanged_Parms
	{
		TArray<ARTSBaseActor*> NewSelection;
		TArray<ARTSBaseActor*> PreviousSelection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate for selection changed events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate for selection changed events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSelection_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviousSelection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewSelection_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NewSelection;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PreviousSelection_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PreviousSelection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_NewSelection_Inner = { "NewSelection", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_NewSelection = { "NewSelection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnRTSSelectionChanged_Parms, NewSelection), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSelection_MetaData), NewProp_NewSelection_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_PreviousSelection_Inner = { "PreviousSelection", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_PreviousSelection = { "PreviousSelection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnRTSSelectionChanged_Parms, PreviousSelection), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviousSelection_MetaData), NewProp_PreviousSelection_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_NewSelection_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_NewSelection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_PreviousSelection_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::NewProp_PreviousSelection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnRTSSelectionChanged__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnRTSSelectionChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnRTSSelectionChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRTSSelectionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnRTSSelectionChanged, TArray<ARTSBaseActor*> const& NewSelection, TArray<ARTSBaseActor*> const& PreviousSelection)
{
	struct _Script_ArmorWars_eventOnRTSSelectionChanged_Parms
	{
		TArray<ARTSBaseActor*> NewSelection;
		TArray<ARTSBaseActor*> PreviousSelection;
	};
	_Script_ArmorWars_eventOnRTSSelectionChanged_Parms Parms;
	Parms.NewSelection=NewSelection;
	Parms.PreviousSelection=PreviousSelection;
	OnRTSSelectionChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRTSSelectionChanged **************************************************

// ********** Begin Class URTSSelectionSystem Function ClearSelection ******************************
static FName NAME_URTSSelectionSystem_ClearSelection = FName(TEXT("ClearSelection"));
void URTSSelectionSystem::ClearSelection()
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_ClearSelection);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
	ProcessEvent(Func,NULL);
	}
	else
	{
		ClearSelection_Implementation();
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_ClearSelection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_ClearSelection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "ClearSelection", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_ClearSelection_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_ClearSelection_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSSelectionSystem_ClearSelection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_ClearSelection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execClearSelection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearSelection_Implementation();
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function ClearSelection ********************************

// ********** Begin Class URTSSelectionSystem Function ClearSelectionGroup *************************
struct RTSSelectionSystem_eventClearSelectionGroup_Parms
{
	int32 GroupIndex;
};
static FName NAME_URTSSelectionSystem_ClearSelectionGroup = FName(TEXT("ClearSelectionGroup"));
void URTSSelectionSystem::ClearSelectionGroup(int32 GroupIndex)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_ClearSelectionGroup);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventClearSelectionGroup_Parms Parms;
		Parms.GroupIndex=GroupIndex;
	ProcessEvent(Func,&Parms);
	}
	else
	{
		ClearSelectionGroup_Implementation(GroupIndex);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics::NewProp_GroupIndex = { "GroupIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventClearSelectionGroup_Parms, GroupIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics::NewProp_GroupIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "ClearSelectionGroup", Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics::PropPointers), sizeof(RTSSelectionSystem_eventClearSelectionGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventClearSelectionGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execClearSelectionGroup)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearSelectionGroup_Implementation(Z_Param_GroupIndex);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function ClearSelectionGroup ***************************

// ********** Begin Class URTSSelectionSystem Function DeselectUnit ********************************
struct RTSSelectionSystem_eventDeselectUnit_Parms
{
	ARTSBaseActor* Unit;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventDeselectUnit_Parms()
		: ReturnValue(false)
	{
	}
};
static FName NAME_URTSSelectionSystem_DeselectUnit = FName(TEXT("DeselectUnit"));
bool URTSSelectionSystem::DeselectUnit(ARTSBaseActor* Unit)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_DeselectUnit);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventDeselectUnit_Parms Parms;
		Parms.Unit=Unit;
	ProcessEvent(Func,&Parms);
		return !!Parms.ReturnValue;
	}
	else
	{
		return DeselectUnit_Implementation(Unit);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventDeselectUnit_Parms, Unit), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSelectionSystem_eventDeselectUnit_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSelectionSystem_eventDeselectUnit_Parms), &Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "DeselectUnit", Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::PropPointers), sizeof(RTSSelectionSystem_eventDeselectUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventDeselectUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execDeselectUnit)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DeselectUnit_Implementation(Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function DeselectUnit **********************************

// ********** Begin Class URTSSelectionSystem Function FilterSelection *****************************
struct RTSSelectionSystem_eventFilterSelection_Parms
{
	ERTSSelectionFilter Filter;
	TArray<ARTSBaseActor*> ReturnValue;
};
static FName NAME_URTSSelectionSystem_FilterSelection = FName(TEXT("FilterSelection"));
TArray<ARTSBaseActor*> URTSSelectionSystem::FilterSelection(ERTSSelectionFilter Filter) const
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_FilterSelection);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventFilterSelection_Parms Parms;
		Parms.Filter=Filter;
		const_cast<URTSSelectionSystem*>(this)->ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return const_cast<URTSSelectionSystem*>(this)->FilterSelection_Implementation(Filter);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Filter_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Filter;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::NewProp_Filter_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::NewProp_Filter = { "Filter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFilterSelection_Parms, Filter), Z_Construct_UEnum_ArmorWars_ERTSSelectionFilter, METADATA_PARAMS(0, nullptr) }; // 1280946198
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFilterSelection_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::NewProp_Filter_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::NewProp_Filter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "FilterSelection", Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::PropPointers), sizeof(RTSSelectionSystem_eventFilterSelection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x48020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventFilterSelection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_FilterSelection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_FilterSelection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execFilterSelection)
{
	P_GET_ENUM(ERTSSelectionFilter,Z_Param_Filter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->FilterSelection_Implementation(ERTSSelectionFilter(Z_Param_Filter));
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function FilterSelection *******************************

// ********** Begin Class URTSSelectionSystem Function FindUnitsInRadius ***************************
struct Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics
{
	struct RTSSelectionSystem_eventFindUnitsInRadius_Parms
	{
		FVector CenterLocation;
		float Radius;
		ERTSSelectionFilter Filter;
		TArray<ARTSBaseActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "CPP_Default_Filter", "None" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Filter_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Filter;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_CenterLocation = { "CenterLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInRadius_Parms, CenterLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterLocation_MetaData), NewProp_CenterLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Filter_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Filter = { "Filter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInRadius_Parms, Filter), Z_Construct_UEnum_ArmorWars_ERTSSelectionFilter, METADATA_PARAMS(0, nullptr) }; // 1280946198
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_CenterLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Filter_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_Filter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "FindUnitsInRadius", Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::RTSSelectionSystem_eventFindUnitsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::RTSSelectionSystem_eventFindUnitsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execFindUnitsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CenterLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_ENUM(ERTSSelectionFilter,Z_Param_Filter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->FindUnitsInRadius(Z_Param_Out_CenterLocation,Z_Param_Radius,ERTSSelectionFilter(Z_Param_Filter));
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function FindUnitsInRadius *****************************

// ********** Begin Class URTSSelectionSystem Function FindUnitsInSelectionBox *********************
struct Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics
{
	struct RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms
	{
		FVector2D StartPos;
		FVector2D EndPos;
		ERTSSelectionFilter Filter;
		TArray<ARTSBaseActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection box functionality\n" },
#endif
		{ "CPP_Default_Filter", "None" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection box functionality" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPos_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPos_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPos;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Filter_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Filter;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_StartPos = { "StartPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms, StartPos), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPos_MetaData), NewProp_StartPos_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_EndPos = { "EndPos", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms, EndPos), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPos_MetaData), NewProp_EndPos_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_Filter_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_Filter = { "Filter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms, Filter), Z_Construct_UEnum_ArmorWars_ERTSSelectionFilter, METADATA_PARAMS(0, nullptr) }; // 1280946198
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_StartPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_EndPos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_Filter_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_Filter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "FindUnitsInSelectionBox", Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::RTSSelectionSystem_eventFindUnitsInSelectionBox_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execFindUnitsInSelectionBox)
{
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_StartPos);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_EndPos);
	P_GET_ENUM(ERTSSelectionFilter,Z_Param_Filter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->FindUnitsInSelectionBox(Z_Param_Out_StartPos,Z_Param_Out_EndPos,ERTSSelectionFilter(Z_Param_Filter));
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function FindUnitsInSelectionBox ***********************

// ********** Begin Class URTSSelectionSystem Function GetAllSelectionGroups ***********************
struct RTSSelectionSystem_eventGetAllSelectionGroups_Parms
{
	TArray<FRTSSelectionGroup> ReturnValue;
};
static FName NAME_URTSSelectionSystem_GetAllSelectionGroups = FName(TEXT("GetAllSelectionGroups"));
TArray<FRTSSelectionGroup> URTSSelectionSystem::GetAllSelectionGroups() const
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_GetAllSelectionGroups);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventGetAllSelectionGroups_Parms Parms;
		const_cast<URTSSelectionSystem*>(this)->ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return const_cast<URTSSelectionSystem*>(this)->GetAllSelectionGroups_Implementation();
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRTSSelectionGroup, METADATA_PARAMS(0, nullptr) }; // 974660342
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventGetAllSelectionGroups_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 974660342
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "GetAllSelectionGroups", Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::PropPointers), sizeof(RTSSelectionSystem_eventGetAllSelectionGroups_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x48020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventGetAllSelectionGroups_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execGetAllSelectionGroups)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FRTSSelectionGroup>*)Z_Param__Result=P_THIS->GetAllSelectionGroups_Implementation();
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function GetAllSelectionGroups *************************

// ********** Begin Class URTSSelectionSystem Function GetPlayerController *************************
struct Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics
{
	struct RTSSelectionSystem_eventGetPlayerController_Parms
	{
		ARTSPlayerController* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventGetPlayerController_Parms, ReturnValue), Z_Construct_UClass_ARTSPlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "GetPlayerController", Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::RTSSelectionSystem_eventGetPlayerController_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::RTSSelectionSystem_eventGetPlayerController_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execGetPlayerController)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSPlayerController**)Z_Param__Result=P_THIS->GetPlayerController();
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function GetPlayerController ***************************

// ********** Begin Class URTSSelectionSystem Function GetSelectedUnitCount ************************
struct RTSSelectionSystem_eventGetSelectedUnitCount_Parms
{
	int32 ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventGetSelectedUnitCount_Parms()
		: ReturnValue(0)
	{
	}
};
static FName NAME_URTSSelectionSystem_GetSelectedUnitCount = FName(TEXT("GetSelectedUnitCount"));
int32 URTSSelectionSystem::GetSelectedUnitCount() const
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_GetSelectedUnitCount);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventGetSelectedUnitCount_Parms Parms;
		const_cast<URTSSelectionSystem*>(this)->ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return const_cast<URTSSelectionSystem*>(this)->GetSelectedUnitCount_Implementation();
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventGetSelectedUnitCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "GetSelectedUnitCount", Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics::PropPointers), sizeof(RTSSelectionSystem_eventGetSelectedUnitCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x48020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventGetSelectedUnitCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execGetSelectedUnitCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetSelectedUnitCount_Implementation();
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function GetSelectedUnitCount **************************

// ********** Begin Class URTSSelectionSystem Function GetSelectedUnits ****************************
struct RTSSelectionSystem_eventGetSelectedUnits_Parms
{
	TArray<ARTSBaseActor*> ReturnValue;
};
static FName NAME_URTSSelectionSystem_GetSelectedUnits = FName(TEXT("GetSelectedUnits"));
TArray<ARTSBaseActor*> URTSSelectionSystem::GetSelectedUnits() const
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_GetSelectedUnits);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventGetSelectedUnits_Parms Parms;
		const_cast<URTSSelectionSystem*>(this)->ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return const_cast<URTSSelectionSystem*>(this)->GetSelectedUnits_Implementation();
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventGetSelectedUnits_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "GetSelectedUnits", Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::PropPointers), sizeof(RTSSelectionSystem_eventGetSelectedUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x48020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventGetSelectedUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execGetSelectedUnits)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->GetSelectedUnits_Implementation();
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function GetSelectedUnits ******************************

// ********** Begin Class URTSSelectionSystem Function GetSelectionGroup ***************************
struct RTSSelectionSystem_eventGetSelectionGroup_Parms
{
	int32 GroupIndex;
	FRTSSelectionGroup ReturnValue;
};
static FName NAME_URTSSelectionSystem_GetSelectionGroup = FName(TEXT("GetSelectionGroup"));
FRTSSelectionGroup URTSSelectionSystem::GetSelectionGroup(int32 GroupIndex) const
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_GetSelectionGroup);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventGetSelectionGroup_Parms Parms;
		Parms.GroupIndex=GroupIndex;
		const_cast<URTSSelectionSystem*>(this)->ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return const_cast<URTSSelectionSystem*>(this)->GetSelectionGroup_Implementation(GroupIndex);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::NewProp_GroupIndex = { "GroupIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventGetSelectionGroup_Parms, GroupIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventGetSelectionGroup_Parms, ReturnValue), Z_Construct_UScriptStruct_FRTSSelectionGroup, METADATA_PARAMS(0, nullptr) }; // 974660342
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::NewProp_GroupIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "GetSelectionGroup", Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::PropPointers), sizeof(RTSSelectionSystem_eventGetSelectionGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x48020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventGetSelectionGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execGetSelectionGroup)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FRTSSelectionGroup*)Z_Param__Result=P_THIS->GetSelectionGroup_Implementation(Z_Param_GroupIndex);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function GetSelectionGroup *****************************

// ********** Begin Class URTSSelectionSystem Function InitializeSelectionSystem *******************
struct RTSSelectionSystem_eventInitializeSelectionSystem_Parms
{
	ARTSPlayerController* PlayerController;
};
static FName NAME_URTSSelectionSystem_InitializeSelectionSystem = FName(TEXT("InitializeSelectionSystem"));
void URTSSelectionSystem::InitializeSelectionSystem(ARTSPlayerController* PlayerController)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_InitializeSelectionSystem);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventInitializeSelectionSystem_Parms Parms;
		Parms.PlayerController=PlayerController;
	ProcessEvent(Func,&Parms);
	}
	else
	{
		InitializeSelectionSystem_Implementation(PlayerController);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// IRTSUnitSelectionInterface implementation - these are BlueprintImplementableEvents\n// We implement them as BlueprintNativeEvents to provide C++ implementation with Blueprint override capability\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "IRTSUnitSelectionInterface implementation - these are BlueprintImplementableEvents\nWe implement them as BlueprintNativeEvents to provide C++ implementation with Blueprint override capability" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventInitializeSelectionSystem_Parms, PlayerController), Z_Construct_UClass_ARTSPlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics::NewProp_PlayerController,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "InitializeSelectionSystem", Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics::PropPointers), sizeof(RTSSelectionSystem_eventInitializeSelectionSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventInitializeSelectionSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execInitializeSelectionSystem)
{
	P_GET_OBJECT(ARTSPlayerController,Z_Param_PlayerController);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeSelectionSystem_Implementation(Z_Param_PlayerController);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function InitializeSelectionSystem *********************

// ********** Begin Class URTSSelectionSystem Function IsUnitSelected ******************************
struct RTSSelectionSystem_eventIsUnitSelected_Parms
{
	ARTSBaseActor* Unit;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventIsUnitSelected_Parms()
		: ReturnValue(false)
	{
	}
};
static FName NAME_URTSSelectionSystem_IsUnitSelected = FName(TEXT("IsUnitSelected"));
bool URTSSelectionSystem::IsUnitSelected(ARTSBaseActor* Unit) const
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_IsUnitSelected);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventIsUnitSelected_Parms Parms;
		Parms.Unit=Unit;
		const_cast<URTSSelectionSystem*>(this)->ProcessEvent(Func,&Parms);
		return !!Parms.ReturnValue;
	}
	else
	{
		return const_cast<URTSSelectionSystem*>(this)->IsUnitSelected_Implementation(Unit);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventIsUnitSelected_Parms, Unit), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSelectionSystem_eventIsUnitSelected_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSelectionSystem_eventIsUnitSelected_Parms), &Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "IsUnitSelected", Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::PropPointers), sizeof(RTSSelectionSystem_eventIsUnitSelected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x48020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventIsUnitSelected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execIsUnitSelected)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsUnitSelected_Implementation(Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function IsUnitSelected ********************************

// ********** Begin Class URTSSelectionSystem Function LoadSelectionGroup **************************
struct RTSSelectionSystem_eventLoadSelectionGroup_Parms
{
	int32 GroupIndex;
	bool bAddToSelection;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventLoadSelectionGroup_Parms()
		: ReturnValue(false)
	{
	}
};
static FName NAME_URTSSelectionSystem_LoadSelectionGroup = FName(TEXT("LoadSelectionGroup"));
bool URTSSelectionSystem::LoadSelectionGroup(int32 GroupIndex, bool bAddToSelection)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_LoadSelectionGroup);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventLoadSelectionGroup_Parms Parms;
		Parms.GroupIndex=GroupIndex;
		Parms.bAddToSelection=bAddToSelection ? true : false;
	ProcessEvent(Func,&Parms);
		return !!Parms.ReturnValue;
	}
	else
	{
		return LoadSelectionGroup_Implementation(GroupIndex, bAddToSelection);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupIndex;
	static void NewProp_bAddToSelection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAddToSelection;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::NewProp_GroupIndex = { "GroupIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventLoadSelectionGroup_Parms, GroupIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::NewProp_bAddToSelection_SetBit(void* Obj)
{
	((RTSSelectionSystem_eventLoadSelectionGroup_Parms*)Obj)->bAddToSelection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::NewProp_bAddToSelection = { "bAddToSelection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSelectionSystem_eventLoadSelectionGroup_Parms), &Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::NewProp_bAddToSelection_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSelectionSystem_eventLoadSelectionGroup_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSelectionSystem_eventLoadSelectionGroup_Parms), &Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::NewProp_GroupIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::NewProp_bAddToSelection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "LoadSelectionGroup", Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::PropPointers), sizeof(RTSSelectionSystem_eventLoadSelectionGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventLoadSelectionGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execLoadSelectionGroup)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupIndex);
	P_GET_UBOOL(Z_Param_bAddToSelection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadSelectionGroup_Implementation(Z_Param_GroupIndex,Z_Param_bAddToSelection);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function LoadSelectionGroup ****************************

// ********** Begin Class URTSSelectionSystem Function OnSelectionChanged **************************
struct RTSSelectionSystem_eventOnSelectionChanged_Parms
{
	TArray<ARTSBaseActor*> NewSelection;
	TArray<ARTSBaseActor*> PreviousSelection;
};
static FName NAME_URTSSelectionSystem_OnSelectionChanged = FName(TEXT("OnSelectionChanged"));
void URTSSelectionSystem::OnSelectionChanged(TArray<ARTSBaseActor*> const& NewSelection, TArray<ARTSBaseActor*> const& PreviousSelection)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_OnSelectionChanged);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventOnSelectionChanged_Parms Parms;
		Parms.NewSelection=NewSelection;
		Parms.PreviousSelection=PreviousSelection;
	ProcessEvent(Func,&Parms);
	}
	else
	{
		OnSelectionChanged_Implementation(NewSelection, PreviousSelection);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSelection_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviousSelection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewSelection_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NewSelection;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PreviousSelection_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PreviousSelection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::NewProp_NewSelection_Inner = { "NewSelection", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::NewProp_NewSelection = { "NewSelection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventOnSelectionChanged_Parms, NewSelection), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSelection_MetaData), NewProp_NewSelection_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::NewProp_PreviousSelection_Inner = { "PreviousSelection", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::NewProp_PreviousSelection = { "PreviousSelection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventOnSelectionChanged_Parms, PreviousSelection), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviousSelection_MetaData), NewProp_PreviousSelection_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::NewProp_NewSelection_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::NewProp_NewSelection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::NewProp_PreviousSelection_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::NewProp_PreviousSelection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "OnSelectionChanged", Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::PropPointers), sizeof(RTSSelectionSystem_eventOnSelectionChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventOnSelectionChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execOnSelectionChanged)
{
	P_GET_TARRAY_REF(ARTSBaseActor*,Z_Param_Out_NewSelection);
	P_GET_TARRAY_REF(ARTSBaseActor*,Z_Param_Out_PreviousSelection);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSelectionChanged_Implementation(Z_Param_Out_NewSelection,Z_Param_Out_PreviousSelection);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function OnSelectionChanged ****************************

// ********** Begin Class URTSSelectionSystem Function SaveSelectionGroup **************************
struct RTSSelectionSystem_eventSaveSelectionGroup_Parms
{
	int32 GroupIndex;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventSaveSelectionGroup_Parms()
		: ReturnValue(false)
	{
	}
};
static FName NAME_URTSSelectionSystem_SaveSelectionGroup = FName(TEXT("SaveSelectionGroup"));
bool URTSSelectionSystem::SaveSelectionGroup(int32 GroupIndex)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_SaveSelectionGroup);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventSaveSelectionGroup_Parms Parms;
		Parms.GroupIndex=GroupIndex;
	ProcessEvent(Func,&Parms);
		return !!Parms.ReturnValue;
	}
	else
	{
		return SaveSelectionGroup_Implementation(GroupIndex);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::NewProp_GroupIndex = { "GroupIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSaveSelectionGroup_Parms, GroupIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSelectionSystem_eventSaveSelectionGroup_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSelectionSystem_eventSaveSelectionGroup_Parms), &Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::NewProp_GroupIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SaveSelectionGroup", Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::PropPointers), sizeof(RTSSelectionSystem_eventSaveSelectionGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventSaveSelectionGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSaveSelectionGroup)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveSelectionGroup_Implementation(Z_Param_GroupIndex);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SaveSelectionGroup ****************************

// ********** Begin Class URTSSelectionSystem Function SelectAllIdleUnits **************************
struct RTSSelectionSystem_eventSelectAllIdleUnits_Parms
{
	int32 ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventSelectAllIdleUnits_Parms()
		: ReturnValue(0)
	{
	}
};
static FName NAME_URTSSelectionSystem_SelectAllIdleUnits = FName(TEXT("SelectAllIdleUnits"));
int32 URTSSelectionSystem::SelectAllIdleUnits()
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_SelectAllIdleUnits);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventSelectAllIdleUnits_Parms Parms;
	ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return SelectAllIdleUnits_Implementation();
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectAllIdleUnits_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SelectAllIdleUnits", Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics::PropPointers), sizeof(RTSSelectionSystem_eventSelectAllIdleUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventSelectAllIdleUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSelectAllIdleUnits)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->SelectAllIdleUnits_Implementation();
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SelectAllIdleUnits ****************************

// ********** Begin Class URTSSelectionSystem Function SelectAllMilitaryUnits **********************
struct RTSSelectionSystem_eventSelectAllMilitaryUnits_Parms
{
	int32 ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventSelectAllMilitaryUnits_Parms()
		: ReturnValue(0)
	{
	}
};
static FName NAME_URTSSelectionSystem_SelectAllMilitaryUnits = FName(TEXT("SelectAllMilitaryUnits"));
int32 URTSSelectionSystem::SelectAllMilitaryUnits()
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_SelectAllMilitaryUnits);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventSelectAllMilitaryUnits_Parms Parms;
	ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return SelectAllMilitaryUnits_Implementation();
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectAllMilitaryUnits_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SelectAllMilitaryUnits", Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics::PropPointers), sizeof(RTSSelectionSystem_eventSelectAllMilitaryUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventSelectAllMilitaryUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSelectAllMilitaryUnits)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->SelectAllMilitaryUnits_Implementation();
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SelectAllMilitaryUnits ************************

// ********** Begin Class URTSSelectionSystem Function SelectAllUnitsOfType ************************
struct RTSSelectionSystem_eventSelectAllUnitsOfType_Parms
{
	ARTSBaseActor* ReferenceUnit;
	bool bOnScreen;
	int32 ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventSelectAllUnitsOfType_Parms()
		: ReturnValue(0)
	{
	}
};
static FName NAME_URTSSelectionSystem_SelectAllUnitsOfType = FName(TEXT("SelectAllUnitsOfType"));
int32 URTSSelectionSystem::SelectAllUnitsOfType(ARTSBaseActor* ReferenceUnit, bool bOnScreen)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_SelectAllUnitsOfType);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventSelectAllUnitsOfType_Parms Parms;
		Parms.ReferenceUnit=ReferenceUnit;
		Parms.bOnScreen=bOnScreen ? true : false;
	ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return SelectAllUnitsOfType_Implementation(ReferenceUnit, bOnScreen);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReferenceUnit;
	static void NewProp_bOnScreen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOnScreen;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::NewProp_ReferenceUnit = { "ReferenceUnit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectAllUnitsOfType_Parms, ReferenceUnit), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::NewProp_bOnScreen_SetBit(void* Obj)
{
	((RTSSelectionSystem_eventSelectAllUnitsOfType_Parms*)Obj)->bOnScreen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::NewProp_bOnScreen = { "bOnScreen", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSelectionSystem_eventSelectAllUnitsOfType_Parms), &Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::NewProp_bOnScreen_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectAllUnitsOfType_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::NewProp_ReferenceUnit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::NewProp_bOnScreen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SelectAllUnitsOfType", Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::PropPointers), sizeof(RTSSelectionSystem_eventSelectAllUnitsOfType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventSelectAllUnitsOfType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSelectAllUnitsOfType)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_ReferenceUnit);
	P_GET_UBOOL(Z_Param_bOnScreen);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->SelectAllUnitsOfType_Implementation(Z_Param_ReferenceUnit,Z_Param_bOnScreen);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SelectAllUnitsOfType **************************

// ********** Begin Class URTSSelectionSystem Function SelectUnit **********************************
struct RTSSelectionSystem_eventSelectUnit_Parms
{
	ARTSBaseActor* Unit;
	bool bAddToSelection;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventSelectUnit_Parms()
		: ReturnValue(false)
	{
	}
};
static FName NAME_URTSSelectionSystem_SelectUnit = FName(TEXT("SelectUnit"));
bool URTSSelectionSystem::SelectUnit(ARTSBaseActor* Unit, bool bAddToSelection)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_SelectUnit);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventSelectUnit_Parms Parms;
		Parms.Unit=Unit;
		Parms.bAddToSelection=bAddToSelection ? true : false;
	ProcessEvent(Func,&Parms);
		return !!Parms.ReturnValue;
	}
	else
	{
		return SelectUnit_Implementation(Unit, bAddToSelection);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static void NewProp_bAddToSelection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAddToSelection;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnit_Parms, Unit), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::NewProp_bAddToSelection_SetBit(void* Obj)
{
	((RTSSelectionSystem_eventSelectUnit_Parms*)Obj)->bAddToSelection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::NewProp_bAddToSelection = { "bAddToSelection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSelectionSystem_eventSelectUnit_Parms), &Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::NewProp_bAddToSelection_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSSelectionSystem_eventSelectUnit_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSelectionSystem_eventSelectUnit_Parms), &Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::NewProp_bAddToSelection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SelectUnit", Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::PropPointers), sizeof(RTSSelectionSystem_eventSelectUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventSelectUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SelectUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SelectUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSelectUnit)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Unit);
	P_GET_UBOOL(Z_Param_bAddToSelection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SelectUnit_Implementation(Z_Param_Unit,Z_Param_bAddToSelection);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SelectUnit ************************************

// ********** Begin Class URTSSelectionSystem Function SelectUnits *********************************
struct RTSSelectionSystem_eventSelectUnits_Parms
{
	TArray<ARTSBaseActor*> Units;
	bool bAddToSelection;
	int32 ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventSelectUnits_Parms()
		: ReturnValue(0)
	{
	}
};
static FName NAME_URTSSelectionSystem_SelectUnits = FName(TEXT("SelectUnits"));
int32 URTSSelectionSystem::SelectUnits(TArray<ARTSBaseActor*> const& Units, bool bAddToSelection)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_SelectUnits);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventSelectUnits_Parms Parms;
		Parms.Units=Units;
		Parms.bAddToSelection=bAddToSelection ? true : false;
	ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return SelectUnits_Implementation(Units, bAddToSelection);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Units_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Units_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Units;
	static void NewProp_bAddToSelection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAddToSelection;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::NewProp_Units_Inner = { "Units", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::NewProp_Units = { "Units", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnits_Parms, Units), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Units_MetaData), NewProp_Units_MetaData) };
void Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::NewProp_bAddToSelection_SetBit(void* Obj)
{
	((RTSSelectionSystem_eventSelectUnits_Parms*)Obj)->bAddToSelection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::NewProp_bAddToSelection = { "bAddToSelection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSelectionSystem_eventSelectUnits_Parms), &Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::NewProp_bAddToSelection_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnits_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::NewProp_Units_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::NewProp_Units,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::NewProp_bAddToSelection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SelectUnits", Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::PropPointers), sizeof(RTSSelectionSystem_eventSelectUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventSelectUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SelectUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SelectUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSelectUnits)
{
	P_GET_TARRAY_REF(ARTSBaseActor*,Z_Param_Out_Units);
	P_GET_UBOOL(Z_Param_bAddToSelection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->SelectUnits_Implementation(Z_Param_Out_Units,Z_Param_bAddToSelection);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SelectUnits ***********************************

// ********** Begin Class URTSSelectionSystem Function SelectUnitsInBox ****************************
struct RTSSelectionSystem_eventSelectUnitsInBox_Parms
{
	FVector2D StartPosition;
	FVector2D EndPosition;
	ERTSSelectionFilter Filter;
	int32 ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventSelectUnitsInBox_Parms()
		: ReturnValue(0)
	{
	}
};
static FName NAME_URTSSelectionSystem_SelectUnitsInBox = FName(TEXT("SelectUnitsInBox"));
int32 URTSSelectionSystem::SelectUnitsInBox(FVector2D const& StartPosition, FVector2D const& EndPosition, ERTSSelectionFilter Filter)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_SelectUnitsInBox);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventSelectUnitsInBox_Parms Parms;
		Parms.StartPosition=StartPosition;
		Parms.EndPosition=EndPosition;
		Parms.Filter=Filter;
	ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return SelectUnitsInBox_Implementation(StartPosition, EndPosition, Filter);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPosition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Filter_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Filter;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::NewProp_StartPosition = { "StartPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnitsInBox_Parms, StartPosition), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPosition_MetaData), NewProp_StartPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::NewProp_EndPosition = { "EndPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnitsInBox_Parms, EndPosition), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPosition_MetaData), NewProp_EndPosition_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::NewProp_Filter_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::NewProp_Filter = { "Filter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnitsInBox_Parms, Filter), Z_Construct_UEnum_ArmorWars_ERTSSelectionFilter, METADATA_PARAMS(0, nullptr) }; // 1280946198
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnitsInBox_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::NewProp_StartPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::NewProp_EndPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::NewProp_Filter_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::NewProp_Filter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SelectUnitsInBox", Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::PropPointers), sizeof(RTSSelectionSystem_eventSelectUnitsInBox_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventSelectUnitsInBox_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSelectUnitsInBox)
{
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_StartPosition);
	P_GET_STRUCT_REF(FVector2D,Z_Param_Out_EndPosition);
	P_GET_ENUM(ERTSSelectionFilter,Z_Param_Filter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->SelectUnitsInBox_Implementation(Z_Param_Out_StartPosition,Z_Param_Out_EndPosition,ERTSSelectionFilter(Z_Param_Filter));
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SelectUnitsInBox ******************************

// ********** Begin Class URTSSelectionSystem Function SelectUnitsInRadius *************************
struct RTSSelectionSystem_eventSelectUnitsInRadius_Parms
{
	FVector CenterLocation;
	float Radius;
	ERTSSelectionFilter Filter;
	int32 ReturnValue;

	/** Constructor, initializes return property only **/
	RTSSelectionSystem_eventSelectUnitsInRadius_Parms()
		: ReturnValue(0)
	{
	}
};
static FName NAME_URTSSelectionSystem_SelectUnitsInRadius = FName(TEXT("SelectUnitsInRadius"));
int32 URTSSelectionSystem::SelectUnitsInRadius(FVector const& CenterLocation, float Radius, ERTSSelectionFilter Filter)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_SelectUnitsInRadius);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventSelectUnitsInRadius_Parms Parms;
		Parms.CenterLocation=CenterLocation;
		Parms.Radius=Radius;
		Parms.Filter=Filter;
	ProcessEvent(Func,&Parms);
		return Parms.ReturnValue;
	}
	else
	{
		return SelectUnitsInRadius_Implementation(CenterLocation, Radius, Filter);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CenterLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CenterLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Filter_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Filter;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::NewProp_CenterLocation = { "CenterLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnitsInRadius_Parms, CenterLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CenterLocation_MetaData), NewProp_CenterLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnitsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::NewProp_Filter_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::NewProp_Filter = { "Filter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnitsInRadius_Parms, Filter), Z_Construct_UEnum_ArmorWars_ERTSSelectionFilter, METADATA_PARAMS(0, nullptr) }; // 1280946198
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSelectUnitsInRadius_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::NewProp_CenterLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::NewProp_Filter_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::NewProp_Filter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SelectUnitsInRadius", Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::PropPointers), sizeof(RTSSelectionSystem_eventSelectUnitsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventSelectUnitsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSelectUnitsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CenterLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_ENUM(ERTSSelectionFilter,Z_Param_Filter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->SelectUnitsInRadius_Implementation(Z_Param_Out_CenterLocation,Z_Param_Radius,ERTSSelectionFilter(Z_Param_Filter));
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SelectUnitsInRadius ***************************

// ********** Begin Class URTSSelectionSystem Function SetPlayerController *************************
struct Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics
{
	struct RTSSelectionSystem_eventSetPlayerController_Parms
	{
		ARTSPlayerController* PlayerController;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Additional C++ methods for direct access\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Additional C++ methods for direct access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSSelectionSystem_eventSetPlayerController_Parms, PlayerController), Z_Construct_UClass_ARTSPlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::NewProp_PlayerController,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SetPlayerController", Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::RTSSelectionSystem_eventSetPlayerController_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::RTSSelectionSystem_eventSetPlayerController_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSetPlayerController)
{
	P_GET_OBJECT(ARTSPlayerController,Z_Param_PlayerController);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPlayerController(Z_Param_PlayerController);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SetPlayerController ***************************

// ********** Begin Class URTSSelectionSystem Function SetSelectionHighlightVisibility *************
struct RTSSelectionSystem_eventSetSelectionHighlightVisibility_Parms
{
	bool bVisible;
};
static FName NAME_URTSSelectionSystem_SetSelectionHighlightVisibility = FName(TEXT("SetSelectionHighlightVisibility"));
void URTSSelectionSystem::SetSelectionHighlightVisibility(bool bVisible)
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_SetSelectionHighlightVisibility);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
		RTSSelectionSystem_eventSetSelectionHighlightVisibility_Parms Parms;
		Parms.bVisible=bVisible ? true : false;
	ProcessEvent(Func,&Parms);
	}
	else
	{
		SetSelectionHighlightVisibility_Implementation(bVisible);
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((RTSSelectionSystem_eventSetSelectionHighlightVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSSelectionSystem_eventSetSelectionHighlightVisibility_Parms), &Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "SetSelectionHighlightVisibility", Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::PropPointers), sizeof(RTSSelectionSystem_eventSetSelectionHighlightVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(RTSSelectionSystem_eventSetSelectionHighlightVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execSetSelectionHighlightVisibility)
{
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSelectionHighlightVisibility_Implementation(Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function SetSelectionHighlightVisibility ***************

// ********** Begin Class URTSSelectionSystem Function UpdateSelectionHighlights *******************
static FName NAME_URTSSelectionSystem_UpdateSelectionHighlights = FName(TEXT("UpdateSelectionHighlights"));
void URTSSelectionSystem::UpdateSelectionHighlights()
{
	UFunction* Func = FindFunctionChecked(NAME_URTSSelectionSystem_UpdateSelectionHighlights);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
	ProcessEvent(Func,NULL);
	}
	else
	{
		UpdateSelectionHighlights_Implementation();
	}
}
struct Z_Construct_UFunction_URTSSelectionSystem_UpdateSelectionHighlights_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Selection" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSSelectionSystem_UpdateSelectionHighlights_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSSelectionSystem, nullptr, "UpdateSelectionHighlights", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSSelectionSystem_UpdateSelectionHighlights_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSSelectionSystem_UpdateSelectionHighlights_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSSelectionSystem_UpdateSelectionHighlights()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSSelectionSystem_UpdateSelectionHighlights_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSSelectionSystem::execUpdateSelectionHighlights)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSelectionHighlights_Implementation();
	P_NATIVE_END;
}
// ********** End Class URTSSelectionSystem Function UpdateSelectionHighlights *********************

// ********** Begin Class URTSSelectionSystem ******************************************************
void URTSSelectionSystem::StaticRegisterNativesURTSSelectionSystem()
{
	UClass* Class = URTSSelectionSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearSelection", &URTSSelectionSystem::execClearSelection },
		{ "ClearSelectionGroup", &URTSSelectionSystem::execClearSelectionGroup },
		{ "DeselectUnit", &URTSSelectionSystem::execDeselectUnit },
		{ "FilterSelection", &URTSSelectionSystem::execFilterSelection },
		{ "FindUnitsInRadius", &URTSSelectionSystem::execFindUnitsInRadius },
		{ "FindUnitsInSelectionBox", &URTSSelectionSystem::execFindUnitsInSelectionBox },
		{ "GetAllSelectionGroups", &URTSSelectionSystem::execGetAllSelectionGroups },
		{ "GetPlayerController", &URTSSelectionSystem::execGetPlayerController },
		{ "GetSelectedUnitCount", &URTSSelectionSystem::execGetSelectedUnitCount },
		{ "GetSelectedUnits", &URTSSelectionSystem::execGetSelectedUnits },
		{ "GetSelectionGroup", &URTSSelectionSystem::execGetSelectionGroup },
		{ "InitializeSelectionSystem", &URTSSelectionSystem::execInitializeSelectionSystem },
		{ "IsUnitSelected", &URTSSelectionSystem::execIsUnitSelected },
		{ "LoadSelectionGroup", &URTSSelectionSystem::execLoadSelectionGroup },
		{ "OnSelectionChanged", &URTSSelectionSystem::execOnSelectionChanged },
		{ "SaveSelectionGroup", &URTSSelectionSystem::execSaveSelectionGroup },
		{ "SelectAllIdleUnits", &URTSSelectionSystem::execSelectAllIdleUnits },
		{ "SelectAllMilitaryUnits", &URTSSelectionSystem::execSelectAllMilitaryUnits },
		{ "SelectAllUnitsOfType", &URTSSelectionSystem::execSelectAllUnitsOfType },
		{ "SelectUnit", &URTSSelectionSystem::execSelectUnit },
		{ "SelectUnits", &URTSSelectionSystem::execSelectUnits },
		{ "SelectUnitsInBox", &URTSSelectionSystem::execSelectUnitsInBox },
		{ "SelectUnitsInRadius", &URTSSelectionSystem::execSelectUnitsInRadius },
		{ "SetPlayerController", &URTSSelectionSystem::execSetPlayerController },
		{ "SetSelectionHighlightVisibility", &URTSSelectionSystem::execSetSelectionHighlightVisibility },
		{ "UpdateSelectionHighlights", &URTSSelectionSystem::execUpdateSelectionHighlights },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSSelectionSystem;
UClass* URTSSelectionSystem::GetPrivateStaticClass()
{
	using TClass = URTSSelectionSystem;
	if (!Z_Registration_Info_UClass_URTSSelectionSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSSelectionSystem"),
			Z_Registration_Info_UClass_URTSSelectionSystem.InnerSingleton,
			StaticRegisterNativesURTSSelectionSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSSelectionSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSSelectionSystem_NoRegister()
{
	return URTSSelectionSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSSelectionSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * RTS Selection System - Manages unit selection for RTS gameplay\n * Implements the IRTSUnitSelectionInterface for Blueprint compatibility\n */" },
#endif
		{ "IncludePath", "RTSSelectionSystem.h" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RTS Selection System - Manages unit selection for RTS gameplay\nImplements the IRTSUnitSelectionInterface for Blueprint compatibility" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSelectionChangedEvent_MetaData[] = {
		{ "Category", "RTS Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedUnits_MetaData[] = {
		{ "Category", "Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Currently selected units\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Currently selected units" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectionGroups_MetaData[] = {
		{ "Category", "Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection groups (hotkeys 1-9)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection groups (hotkeys 1-9)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwningPlayerController_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Owning player controller\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Owning player controller" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHighlightSelectedUnits_MetaData[] = {
		{ "Category", "Selection Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Selection Settings" },
		{ "ModuleRelativePath", "Public/RTSSelectionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSelectionChangedEvent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SelectedUnits_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SelectedUnits;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SelectionGroups_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SelectionGroups_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SelectionGroups;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwningPlayerController;
	static void NewProp_bHighlightSelectedUnits_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHighlightSelectedUnits;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSSelectionSystem_ClearSelection, "ClearSelection" }, // 1861243068
		{ &Z_Construct_UFunction_URTSSelectionSystem_ClearSelectionGroup, "ClearSelectionGroup" }, // 2455460774
		{ &Z_Construct_UFunction_URTSSelectionSystem_DeselectUnit, "DeselectUnit" }, // 1815265043
		{ &Z_Construct_UFunction_URTSSelectionSystem_FilterSelection, "FilterSelection" }, // 2185158491
		{ &Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInRadius, "FindUnitsInRadius" }, // 1074043033
		{ &Z_Construct_UFunction_URTSSelectionSystem_FindUnitsInSelectionBox, "FindUnitsInSelectionBox" }, // 3199806915
		{ &Z_Construct_UFunction_URTSSelectionSystem_GetAllSelectionGroups, "GetAllSelectionGroups" }, // 1502259160
		{ &Z_Construct_UFunction_URTSSelectionSystem_GetPlayerController, "GetPlayerController" }, // 2753863605
		{ &Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnitCount, "GetSelectedUnitCount" }, // 1689768449
		{ &Z_Construct_UFunction_URTSSelectionSystem_GetSelectedUnits, "GetSelectedUnits" }, // 70853399
		{ &Z_Construct_UFunction_URTSSelectionSystem_GetSelectionGroup, "GetSelectionGroup" }, // 709855157
		{ &Z_Construct_UFunction_URTSSelectionSystem_InitializeSelectionSystem, "InitializeSelectionSystem" }, // 3345708791
		{ &Z_Construct_UFunction_URTSSelectionSystem_IsUnitSelected, "IsUnitSelected" }, // 277829336
		{ &Z_Construct_UFunction_URTSSelectionSystem_LoadSelectionGroup, "LoadSelectionGroup" }, // 2260522413
		{ &Z_Construct_UFunction_URTSSelectionSystem_OnSelectionChanged, "OnSelectionChanged" }, // 3693507459
		{ &Z_Construct_UFunction_URTSSelectionSystem_SaveSelectionGroup, "SaveSelectionGroup" }, // 1737652553
		{ &Z_Construct_UFunction_URTSSelectionSystem_SelectAllIdleUnits, "SelectAllIdleUnits" }, // 2371584327
		{ &Z_Construct_UFunction_URTSSelectionSystem_SelectAllMilitaryUnits, "SelectAllMilitaryUnits" }, // 3281354564
		{ &Z_Construct_UFunction_URTSSelectionSystem_SelectAllUnitsOfType, "SelectAllUnitsOfType" }, // 91555662
		{ &Z_Construct_UFunction_URTSSelectionSystem_SelectUnit, "SelectUnit" }, // 1902669172
		{ &Z_Construct_UFunction_URTSSelectionSystem_SelectUnits, "SelectUnits" }, // 4221655251
		{ &Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInBox, "SelectUnitsInBox" }, // 3539469937
		{ &Z_Construct_UFunction_URTSSelectionSystem_SelectUnitsInRadius, "SelectUnitsInRadius" }, // 2056718609
		{ &Z_Construct_UFunction_URTSSelectionSystem_SetPlayerController, "SetPlayerController" }, // 1764212049
		{ &Z_Construct_UFunction_URTSSelectionSystem_SetSelectionHighlightVisibility, "SetSelectionHighlightVisibility" }, // 175769568
		{ &Z_Construct_UFunction_URTSSelectionSystem_UpdateSelectionHighlights, "UpdateSelectionHighlights" }, // 2058078117
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static const UECodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSSelectionSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_OnSelectionChangedEvent = { "OnSelectionChangedEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSSelectionSystem, OnSelectionChangedEvent), Z_Construct_UDelegateFunction_ArmorWars_OnRTSSelectionChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSelectionChangedEvent_MetaData), NewProp_OnSelectionChangedEvent_MetaData) }; // 1719768022
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectedUnits_Inner = { "SelectedUnits", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectedUnits = { "SelectedUnits", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSSelectionSystem, SelectedUnits), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedUnits_MetaData), NewProp_SelectedUnits_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups_ValueProp = { "SelectionGroups", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FRTSSelectionGroup, METADATA_PARAMS(0, nullptr) }; // 974660342
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups_Key_KeyProp = { "SelectionGroups_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups = { "SelectionGroups", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSSelectionSystem, SelectionGroups), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectionGroups_MetaData), NewProp_SelectionGroups_MetaData) }; // 974660342
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_OwningPlayerController = { "OwningPlayerController", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSSelectionSystem, OwningPlayerController), Z_Construct_UClass_ARTSPlayerController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwningPlayerController_MetaData), NewProp_OwningPlayerController_MetaData) };
void Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bHighlightSelectedUnits_SetBit(void* Obj)
{
	((URTSSelectionSystem*)Obj)->bHighlightSelectedUnits = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bHighlightSelectedUnits = { "bHighlightSelectedUnits", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSSelectionSystem), &Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bHighlightSelectedUnits_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHighlightSelectedUnits_MetaData), NewProp_bHighlightSelectedUnits_MetaData) };
void Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((URTSSelectionSystem*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSSelectionSystem), &Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSSelectionSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_OnSelectionChangedEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectedUnits_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectedUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_SelectionGroups,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_OwningPlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bHighlightSelectedUnits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSSelectionSystem_Statics::NewProp_bEnableDebugLogging,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSSelectionSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSSelectionSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSSelectionSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_URTSSelectionSystem_Statics::InterfaceParams[] = {
	{ Z_Construct_UClass_URTSUnitSelectionInterface_NoRegister, (int32)VTABLE_OFFSET(URTSSelectionSystem, IRTSUnitSelectionInterface), false },  // 1723694110
};
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSSelectionSystem_Statics::ClassParams = {
	&URTSSelectionSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSSelectionSystem_Statics::PropPointers,
	InterfaceParams,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSSelectionSystem_Statics::PropPointers),
	UE_ARRAY_COUNT(InterfaceParams),
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSSelectionSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSSelectionSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSSelectionSystem()
{
	if (!Z_Registration_Info_UClass_URTSSelectionSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSSelectionSystem.OuterSingleton, Z_Construct_UClass_URTSSelectionSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSSelectionSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSSelectionSystem);
URTSSelectionSystem::~URTSSelectionSystem() {}
// ********** End Class URTSSelectionSystem ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h__Script_ArmorWars_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSSelectionSystem, URTSSelectionSystem::StaticClass, TEXT("URTSSelectionSystem"), &Z_Registration_Info_UClass_URTSSelectionSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSSelectionSystem), 3810658665U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h__Script_ArmorWars_115360266(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSSelectionSystem_h__Script_ArmorWars_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

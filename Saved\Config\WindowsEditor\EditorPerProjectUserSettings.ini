;METADATA=(Diff=true, UseCommands=true)
[/Script/UnrealEd.EditorPerProjectUserSettings]
bDisplayDocumentationLink=False
bDisplayActionListItemRefIds=False
bAlwaysGatherBehaviorTreeDebuggerData=False
bDisplayBlackboardKeysInAlphabeticalOrder=False
bUseSimplygonSwarm=False
SimplygonServerIP=127.0.0.1
bEnableSwarmDebugging=False
SimplygonSwarmDelay=5000
SwarmNumOfConcurrentJobs=16
SwarmMaxUploadChunkSizeInMB=100
SwarmIntermediateFolder=F:/ArmorWars/Intermediate/Simplygon/
bShowCompilerLogOnCompileError=False
DataSourceFolder=(Path="")
bAnimationReimportWarnings=False
bConfirmEditorClose=False
bSCSEditorShowFloor=False
bAlwaysBuildUAT=True
SCSViewportCameraSpeed=4
bShowSelectionSubcomponents=True
AssetViewerProfileName=
PreviewFeatureLevel=4
PreviewPlatformName=None
PreviewShaderFormatName=None
PreviewShaderPlatformName=None
bPreviewFeatureLevelActive=False
bPreviewFeatureLevelWasDefault=True
PreviewDeviceProfileName=None

[/Script/UnrealEd.EditorStyleSettings]
XAxisColor=(R=0.594000,G=0.019700,B=0.000000,A=1.000000)
YAxisColor=(R=0.134900,G=0.395900,B=0.000000,A=1.000000)
ZAxisColor=(R=0.025100,G=0.207000,B=0.850000,A=1.000000)
ApplicationScale=1.000000
bColorVisionDeficiencyCorrection=False
bColorVisionDeficiencyCorrectionPreviewWithDeficiency=False
SelectionColor=(R=0.828000,G=0.364000,B=0.003000,A=1.000000)
AdditionalSelectionColors[0]=(R=0.019382,G=0.496933,B=1.000000,A=1.000000)
AdditionalSelectionColors[1]=(R=0.356400,G=0.040915,B=0.520996,A=1.000000)
AdditionalSelectionColors[2]=(R=1.000000,G=0.168269,B=0.332452,A=1.000000)
AdditionalSelectionColors[3]=(R=1.000000,G=0.051269,B=0.051269,A=1.000000)
AdditionalSelectionColors[4]=(R=1.000000,G=0.715693,B=0.010330,A=1.000000)
AdditionalSelectionColors[5]=(R=0.258183,G=0.539479,B=0.068478,A=1.000000)
ViewportToolOverlayColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
bEnableEditorWindowBackgroundColor=False
EditorWindowBackgroundColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
MenuSearchFieldVisibilityThreshold=10
bUseGrid=True
bAntiAliasGrid=True
RegularColor=(R=0.024000,G=0.024000,B=0.024000,A=1.000000)
RuleColor=(R=0.010000,G=0.010000,B=0.010000,A=1.000000)
CenterColor=(R=0.005000,G=0.005000,B=0.005000,A=1.000000)
GridSnapSize=16
GraphBackgroundBrush=(TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),ResourceObject=None,OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=0.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False),bIsDynamicallyLoaded=False,ResourceName="")
bShowNativeComponentNames=True
AssetEditorOpenLocation=Default
bEnableColorizedEditorTabs=True
CurrentAppliedTheme=134380265FBB4A9CA00A1DC9770217B8
bEnableMiddleEllipsis=True

[/Script/OutputLog.OutputLogSettings]
LogTimestampMode=None
CategoryColorizationMode=None
bCycleToOutputLogDrawer=True
bEnableOutputLogWordWrap=False
bEnableOutputLogClearOnPIE=False
OutputLogTabFilter=(MessagesFilter=Enabled,WarningsFilter=Enabled,ErrorsFilter=Enabled,FilterText="",Categories=,bSelectNewCategories=True)

[/Script/UnrealEd.LevelEditorPlaySettings]
LaptopScreenResolutions=(Description="Apple MacBook Air 11",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Air 13\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\"",Width=1280,Height=800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\" (Retina)",Width=2560,Height=1600,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\" (Retina)",Width=2880,Height=1800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Generic 14-15.6\" Notebook",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="19\" monitor",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="20\" monitor",Width=1600,Height=900,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="22\" monitor",Width=1680,Height=1050,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="21.5-24\" monitor",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="27\" monitor",Width=2560,Height=1440,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (3rd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro3_129")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (2nd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro2_129")
TabletScreenResolutions=(Description="iPad Pro 11-inch",Width=834,Height=1194,AspectRatio="5:7",bCanSwapAspectRatio=True,ProfileName="iPadPro11")
TabletScreenResolutions=(Description="iPad Pro 10.5-inch",Width=834,Height=1112,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro105")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch",Width=1024,Height=1366,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro129")
TabletScreenResolutions=(Description="iPad Pro 9.7-inch",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro97")
TabletScreenResolutions=(Description="iPad (6th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad6")
TabletScreenResolutions=(Description="iPad (5th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad5")
TabletScreenResolutions=(Description="iPad Air 3",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir3")
TabletScreenResolutions=(Description="iPad Air 2",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir2")
TabletScreenResolutions=(Description="iPad Mini 5",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini5")
TabletScreenResolutions=(Description="iPad Mini 4",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini4")
TabletScreenResolutions=(Description="LG G Pad X 8.0",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Asus Zenpad 3s 10",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Huawei MediaPad M3",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface RT",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface Pro",Width=1080,Height=1920,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="720p (HDTV, Blu-ray)",Width=1280,Height=720,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="1080i, 1080p (HDTV, Blu-ray)",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Ultra HD",Width=3840,Height=2160,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Digital Cinema",Width=4096,Height=2160,AspectRatio="1.90:1",bCanSwapAspectRatio=True,ProfileName="")
GameGetsMouseControl=False
UseMouseForTouch=False
MouseControlLabelPosition=LabelAnchorMode_TopLeft
ViewportGetsHMDControl=False
bShouldMinimizeEditorOnNonVRPIE=False
bEmulateStereo=False
SoloAudioInFirstPIEClient=False
EnablePIEEnterAndExitSounds=False
PlayInEditorSoundQualityLevel=0
bUseNonRealtimeAudioDevice=False
bPreferToStreamLevelsInPIE=False
bPromoteOutputLogWarningsDuringPIE=False
NewWindowPosition=(X=-1,Y=-1)
PIEAlwaysOnTop=False
DisableStandaloneSound=False
AdditionalLaunchParameters=
BuildGameBeforeLaunch=PlayOnBuild_Default
LaunchConfiguration=LaunchConfig_Default
PackFilesForLaunch=NoPak
bAutoCompileBlueprintsOnLaunch=True
bLaunchSeparateServer=False
PlayNetMode=PIE_Standalone
RunUnderOneProcess=True
PlayNumberOfClients=1
PrimaryPIEClientIndex=0
ServerPort=17777
ClientWindowWidth=640
RouteGamepadToSecondWindow=False
CreateAudioDeviceForEveryPlayer=False
ClientWindowHeight=480
ServerMapNameOverride=
AdditionalServerGameOptions=
bShowServerDebugDrawingByDefault=True
ServerDebugDrawingColorTintStrength=0.000000
ServerDebugDrawingColorTint=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)
bHMDForPrimaryProcessOnly=True
AdditionalServerLaunchParameters=
ServerFixedFPS=0
NetworkEmulationSettings=(bIsNetworkEmulationEnabled=False,EmulationTarget=Server,CurrentProfile="Custom",OutPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0),InPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0))
LastSize=(X=0,Y=0)
LastExecutedLaunchDevice=Windows@DESKTOP-GBDVD69
LastExecutedLaunchName=DESKTOP-GBDVD69
LastExecutedPIEPreviewDevice=
DeviceToEmulate=
PIESafeZoneOverride=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000)

[/Script/UnrealEd.LevelEditorViewportSettings]
GridEnabled=False
CurrentPosGridSize=0
FlightCameraControlExperimentalNavigation=False
MinimumOrthographicZoom=250.000000
bAllowArcballRotate=False
bAllowScreenRotate=False
bShowActorEditorContext=True
bShowBrushMarkerPolys=False
bAllowEditWidgetAxisDisplay=True
bUseLegacyCameraMovementNotifications=False
SnapToSurface=(bEnabled=False,SnapOffsetExtent=0.000000,bSnapRotation=True)
bEnableLayerSnap=False
ActiveSnapLayerIndex=0
PreserveNonUniformScale=True
bUseLODViewLocking=False
PreviewMeshes=/Engine/EditorMeshes/ColorCalibrator/SM_ColorCalibrator.SM_ColorCalibrator
BillboardScale=1.000000
bSaveEngineStats=False
MeasuringToolUnits=MeasureUnits_Centimeters
SelectedSplinePointSizeAdjustment=0.000000
SplineLineThicknessAdjustment=0.000000
SplineTangentHandleSizeAdjustment=0.000000
SplineTangentScale=0.500000
LastInViewportMenuLocation=(X=0.000000,Y=0.000000)
MaterialForDroppedTextures=None
MaterialParamsForDroppedTextures=()
EditorViews=(("/Temp/Untitled_1.Untitled_1", (LevelViewportsInfo=((),(CamUpdated=True),(CamUpdated=True),(CamPosition=(X=3495.218032,Y=1009.419891,Z=369.006575),CamRotation=(Pitch=-32.599900,Yaw=182.216706,Roll=0.000000)),(CamUpdated=True),(CamUpdated=True),(),()))),("/Game/GridBasedBuilder/Demo/Levels/Overview.Overview", (LevelViewportsInfo=((),(CamUpdated=True),(CamUpdated=True),(CamPosition=(X=-2794.287231,Y=-1924.088615,Z=132.949374),CamRotation=(Pitch=-36.327253,Yaw=3.916832,Roll=0.000000)),(CamUpdated=True),(CamUpdated=True),(),()))))
PropertyColorationColorForMatchingObjects=(B=0,G=0,R=255,A=255)
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 2.Viewport0",ConfigSettings=(ViewportType=LVT_OrthoNegativeYZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 2.Viewport1",ConfigSettings=(ViewportType=LVT_Perspective,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=1,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=1,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=True,bShowOnScreenStats=False,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 2.Viewport2",ConfigSettings=(ViewportType=LVT_OrthoNegativeXZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 2.Viewport3",ConfigSettings=(ViewportType=LVT_OrthoXY,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport0",ConfigSettings=(ViewportType=LVT_OrthoNegativeYZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport1",ConfigSettings=(ViewportType=LVT_Perspective,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=1,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=1,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=True,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport2",ConfigSettings=(ViewportType=LVT_OrthoNegativeXZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport3",ConfigSettings=(ViewportType=LVT_OrthoXY,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))

[MRU]
MRUItem0=/Game/GridBasedBuilder/Demo/Levels/Overview

[/Script/UnrealEd.PersonaOptions]
bAutoAlignFloorToMesh=True
bAlwaysOpenAnimationAssetsInNewTab=False
bMuteAudio=False
DefaultLocalAxesSelection=2
DefaultBoneDrawSelection=1
bShowBoneColors=False
DefaultBoneColor=(R=0.000000,G=0.000000,B=0.025000,A=1.000000)
SelectedBoneColor=(R=0.200000,G=1.000000,B=0.200000,A=1.000000)
AffectedBoneColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
DisabledBoneColor=(R=0.400000,G=0.400000,B=0.400000,A=1.000000)
ParentOfSelectedBoneColor=(R=0.850000,G=0.450000,B=0.120000,A=1.000000)
VirtualBoneColor=(R=0.400000,G=0.400000,B=1.000000,A=1.000000)
SectionTimingNodeColor=(R=0.390000,G=0.390000,B=1.000000,A=0.750000)
NotifyTimingNodeColor=(R=0.800000,G=0.100000,B=0.100000,A=1.000000)
BranchingPointTimingNodeColor=(R=0.500000,G=1.000000,B=1.000000,A=1.000000)
bPauseAnimationOnCameraMove=False
bUseInlineSocketEditor=False
bFlattenSkeletonHierarchyWhenFiltering=False
bHideParentsWhenFiltering=False
bShowBoneIndexes=False
bExpandTreeOnSelection=True
bAllowPreviewMeshCollectionsToSelectFromDifferentSkeletons=True
bAllowPreviewMeshCollectionsToUseCustomAnimBP=False
bAllowMeshSectionSelection=False
NumFolderFiltersInAssetBrowser=2
AssetEditorOptions=(Context="SkeletonEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="SkeletalMeshEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="AnimationEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="AnimationBlueprintEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="PhysicsAssetEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
CurveEditorSnapInterval=0.010000
TimelineScrubSnapValue=1000
TimelineDisplayFormat=Frames
bTimelineDisplayPercentage=True
bTimelineDisplayFormatSecondary=True
bTimelineDisplayCurveKeys=False
TimelineEnabledSnaps=CompositeSegment
TimelineEnabledSnaps=MontageSection
bAllowIncompatibleSkeletonSelection=False
bUseTreeViewForAnimationCurves=False
AnimationCurveGroupingDelimiters="._/|\\"

[WidgetTemplatesExpanded]
Common=False
Audio=False
AudioMaterial=False
Editor=False
Input=False
Lists=False
Misc=False
Optimization=False
Panel=False
Primitive=False
Special Effects=False
Synth=False
Uncategorized=False
User Created=True
Advanced=False

[DetailCustomWidgetExpansion]
InterchangeGenericAssetsPipeline=InterchangeGenericAssetsPipeline.Common Meshes.Build
BoneProxy=BoneProxy.Transforms.Mesh Relative,BoneProxy.Transforms.Reference,BoneProxy.Transforms.Bone
MaterialEditorInstanceConstant=MaterialEditorInstanceConstant.ParameterGroups.Global Scalar Parameter Values,MaterialEditorInstanceConstant.ParameterGroups.Global Texture Parameter Values,MaterialEditorInstanceConstant.ParameterGroups.Global Vector Parameter Values
MaterialInstanceConstant=MaterialEditorInstanceConstant.ParameterGroups.Global Scalar Parameter Values,MaterialEditorInstanceConstant.ParameterGroups.Global Texture Parameter Values,MaterialEditorInstanceConstant.ParameterGroups.Global Vector Parameter Values
SkeletonEditingTool=SkeletonEditingProperties.Details.Transform
ProjectionProperties=SkeletonEditingProperties.Details.Transform
MirroringProperties=SkeletonEditingProperties.Details.Transform
OrientingProperties=SkeletonEditingProperties.Details.Transform
SkeletonEditingProperties=SkeletonEditingProperties.Details.Transform
MeshTopologySelectionMechanicProperties=SkeletonEditingProperties.Details.Transform
InputMappingContext=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/CameraMoveRight.CameraMoveRight,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/CameraRotate.CameraRotate,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/SecondaryAction.SecondaryAction,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/PrimaryAction.PrimaryAction,InputMappingContext.Mappings.ActionMappings.None
InputAction=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/CameraMoveRight.CameraMoveRight,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/CameraRotate.CameraRotate,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/SecondaryAction.SecondaryAction,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/PrimaryAction.PrimaryAction,InputMappingContext.Mappings.ActionMappings.None
K2Node_VariableGet=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
ArmorWarsPlayerController_C=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
Blueprint=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
SceneComponent=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
ArmorWarsHud_C=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
K2Node_EnhancedInputAction=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
BP_MediumTank_C=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
MedTankAIcontroller_C=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
UserDefinedStruct=
K2Node_CustomEvent=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
StaticMeshComponent=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
CapsuleComponent=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
LandscapeStreamingProxy=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
Actor=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick
RTSLandMovementComponent=InputMappingContext.Mappings.ActionMappings,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/MouseClick.MouseClick,InputMappingContext.Mappings.ActionMappings./Game/GameInit/EnhancedInput/RightMouseClick.RightMouseClick

[/Script/LiveCoding.LiveCodingSettings]
Startup=Automatic
bPreloadEngineModules=False
bPreloadEnginePluginModules=False
bPreloadProjectModules=True
bPreloadProjectPluginModules=True

[EditorStartup]
LastLevel=/Engine/Maps/Templates/OpenWorld

[ModuleFileTracking]
TextureGraphEngine.TimeStamp=2025.06.17-14.11.47
TextureGraphEngine.LastCompileMethod=Unknown
ArmorWars.TimeStamp=2025.06.23-10.57.49
ArmorWars.LastCompileMethod=External
TextureGraphInsight.TimeStamp=2025.06.17-14.11.47
TextureGraphInsight.LastCompileMethod=Unknown
StorageServerClient.TimeStamp=2025.06.17-14.07.01
StorageServerClient.LastCompileMethod=Unknown
CookOnTheFly.TimeStamp=2025.06.17-14.06.54
CookOnTheFly.LastCompileMethod=Unknown
StreamingFile.TimeStamp=2025.06.17-14.07.01
StreamingFile.LastCompileMethod=Unknown
NetworkFile.TimeStamp=2025.06.17-14.06.58
NetworkFile.LastCompileMethod=Unknown
PakFile.TimeStamp=2025.06.17-14.06.59
PakFile.LastCompileMethod=Unknown
RSA.TimeStamp=2025.06.17-14.06.59
RSA.LastCompileMethod=Unknown
SandboxFile.TimeStamp=2025.06.17-14.06.59
SandboxFile.LastCompileMethod=Unknown
CoreUObject.TimeStamp=2025.06.17-14.06.54
CoreUObject.LastCompileMethod=Unknown
Engine.TimeStamp=2025.06.17-14.06.55
Engine.LastCompileMethod=Unknown
UniversalObjectLocator.TimeStamp=2025.06.17-14.07.01
UniversalObjectLocator.LastCompileMethod=Unknown
Renderer.TimeStamp=2025.06.17-14.06.59
Renderer.LastCompileMethod=Unknown
AnimGraphRuntime.TimeStamp=2025.06.17-14.06.54
AnimGraphRuntime.LastCompileMethod=Unknown
SlateRHIRenderer.TimeStamp=2025.06.17-14.07.01
SlateRHIRenderer.LastCompileMethod=Unknown
Landscape.TimeStamp=2025.06.17-14.06.57
Landscape.LastCompileMethod=Unknown
RHICore.TimeStamp=2025.06.17-14.06.59
RHICore.LastCompileMethod=Unknown
RenderCore.TimeStamp=2025.06.17-14.06.59
RenderCore.LastCompileMethod=Unknown
TextureCompressor.TimeStamp=2025.06.17-14.07.01
TextureCompressor.LastCompileMethod=Unknown
OpenColorIOWrapper.TimeStamp=2025.06.17-14.06.59
OpenColorIOWrapper.LastCompileMethod=Unknown
Virtualization.TimeStamp=2025.06.17-14.07.02
Virtualization.LastCompileMethod=Unknown
MessageLog.TimeStamp=2025.06.17-14.06.57
MessageLog.LastCompileMethod=Unknown
AudioEditor.TimeStamp=2025.06.17-14.06.54
AudioEditor.LastCompileMethod=Unknown
PropertyEditor.TimeStamp=2025.06.17-14.06.59
PropertyEditor.LastCompileMethod=Unknown
AudioExtensions.TimeStamp=2025.06.17-14.06.54
AudioExtensions.LastCompileMethod=Unknown
AndroidAudioFeatures.TimeStamp=
AndroidAudioFeatures.LastCompileMethod=Unknown
IOSAudioFeatures.TimeStamp=
IOSAudioFeatures.LastCompileMethod=Unknown
LinuxAudioFeatures.TimeStamp=
LinuxAudioFeatures.LastCompileMethod=Unknown
MacAudioFeatures.TimeStamp=
MacAudioFeatures.LastCompileMethod=Unknown
TVOSAudioFeatures.TimeStamp=
TVOSAudioFeatures.LastCompileMethod=Unknown
VisionOSAudioFeatures.TimeStamp=
VisionOSAudioFeatures.LastCompileMethod=Unknown
WindowsAudioFeatures.TimeStamp=
WindowsAudioFeatures.LastCompileMethod=Unknown
SignalProcessing.TimeStamp=2025.06.17-14.07.00
SignalProcessing.LastCompileMethod=Unknown
AudioMixerCore.TimeStamp=2025.06.17-14.06.54
AudioMixerCore.LastCompileMethod=Unknown
AnimationModifiers.TimeStamp=2025.06.17-14.06.52
AnimationModifiers.LastCompileMethod=Unknown
IoStoreOnDemandCore.TimeStamp=2025.06.17-14.06.56
IoStoreOnDemandCore.LastCompileMethod=Unknown
OpusAudioDecoder.TimeStamp=2025.06.17-14.06.59
OpusAudioDecoder.LastCompileMethod=Unknown
VorbisAudioDecoder.TimeStamp=2025.06.17-14.07.02
VorbisAudioDecoder.LastCompileMethod=Unknown
AdpcmAudioDecoder.TimeStamp=2025.06.17-14.06.52
AdpcmAudioDecoder.LastCompileMethod=Unknown
BinkAudioDecoder.TimeStamp=2025.06.17-14.06.54
BinkAudioDecoder.LastCompileMethod=Unknown
RadAudioDecoder.TimeStamp=2025.06.17-14.06.59
RadAudioDecoder.LastCompileMethod=Unknown
TelemetryUtils.TimeStamp=2025.06.17-14.07.01
TelemetryUtils.LastCompileMethod=Unknown
FastBuildController.TimeStamp=2025.06.17-14.10.10
FastBuildController.LastCompileMethod=Unknown
UbaController.TimeStamp=2025.06.17-14.11.50
UbaController.LastCompileMethod=Unknown
XGEController.TimeStamp=2025.06.17-14.12.12
XGEController.LastCompileMethod=Unknown
PerforceSourceControl.TimeStamp=2025.06.17-14.09.06
PerforceSourceControl.LastCompileMethod=Unknown
SourceControl.TimeStamp=2025.06.17-14.07.01
SourceControl.LastCompileMethod=Unknown
PlasticSourceControl.TimeStamp=2025.06.17-14.09.06
PlasticSourceControl.LastCompileMethod=Unknown
PlatformCrypto.TimeStamp=2025.06.17-14.09.53
PlatformCrypto.LastCompileMethod=Unknown
PlatformCryptoTypes.TimeStamp=2025.06.17-14.09.53
PlatformCryptoTypes.LastCompileMethod=Unknown
PlatformCryptoContext.TimeStamp=2025.06.17-14.09.53
PlatformCryptoContext.LastCompileMethod=Unknown
PythonScriptPluginPreload.TimeStamp=2025.06.17-14.09.53
PythonScriptPluginPreload.LastCompileMethod=Unknown
DesktopPlatform.TimeStamp=2025.06.17-14.06.54
DesktopPlatform.LastCompileMethod=Unknown
ChaosCloth.TimeStamp=2025.06.17-14.09.04
ChaosCloth.LastCompileMethod=Unknown
PCGCompute.TimeStamp=2025.06.17-14.11.07
PCGCompute.LastCompileMethod=Unknown
AISupportModule.TimeStamp=2025.06.17-14.08.51
AISupportModule.LastCompileMethod=Unknown
ACLPlugin.TimeStamp=2025.06.17-14.08.52
ACLPlugin.LastCompileMethod=Unknown
OptimusSettings.TimeStamp=2025.06.17-14.08.56
OptimusSettings.LastCompileMethod=Unknown
PixWinPlugin.TimeStamp=2025.06.17-14.09.06
PixWinPlugin.LastCompileMethod=Unknown
RenderDocPlugin.TimeStamp=2025.06.17-14.09.07
RenderDocPlugin.LastCompileMethod=Unknown
DatasmithContent.TimeStamp=2025.06.17-14.09.13
DatasmithContent.LastCompileMethod=Unknown
GLTFExporter.TimeStamp=2025.06.17-14.09.16
GLTFExporter.LastCompileMethod=Unknown
VariantManagerContent.TimeStamp=2025.06.17-14.09.18
VariantManagerContent.LastCompileMethod=Unknown
NiagaraShader.TimeStamp=2025.06.17-14.10.12
NiagaraShader.LastCompileMethod=Unknown
NiagaraVertexFactories.TimeStamp=2025.06.17-14.10.12
NiagaraVertexFactories.LastCompileMethod=Unknown
InterchangeAssets.TimeStamp=2025.06.17-14.10.19
InterchangeAssets.LastCompileMethod=Unknown
ExrReaderGpu.TimeStamp=2025.06.17-14.10.24
ExrReaderGpu.LastCompileMethod=Unknown
WmfMedia.TimeStamp=2025.06.17-14.10.28
WmfMedia.LastCompileMethod=Unknown
Media.TimeStamp=2025.06.17-14.06.57
Media.LastCompileMethod=Unknown
EOSShared.TimeStamp=2025.06.17-14.11.03
EOSShared.LastCompileMethod=Unknown
OnlineServicesInterface.TimeStamp=2025.06.17-14.11.03
OnlineServicesInterface.LastCompileMethod=Unknown
OnlineServicesCommon.TimeStamp=2025.06.17-14.11.03
OnlineServicesCommon.LastCompileMethod=Unknown
OnlineServicesCommonEngineUtils.TimeStamp=2025.06.17-14.11.03
OnlineServicesCommonEngineUtils.LastCompileMethod=Unknown
OnlineSubsystem.TimeStamp=2025.06.17-14.11.05
OnlineSubsystem.LastCompileMethod=Unknown
HTTP.TimeStamp=2025.06.17-14.06.56
HTTP.LastCompileMethod=Unknown
SSL.TimeStamp=2025.06.17-14.07.01
SSL.LastCompileMethod=Unknown
XMPP.TimeStamp=2025.06.17-14.07.03
XMPP.LastCompileMethod=Unknown
WebSockets.TimeStamp=2025.06.17-14.07.02
WebSockets.LastCompileMethod=Unknown
OnlineSubsystemNULL.TimeStamp=2025.06.17-14.11.06
OnlineSubsystemNULL.LastCompileMethod=Unknown
Sockets.TimeStamp=2025.06.17-14.07.01
Sockets.LastCompileMethod=Unknown
OnlineSubsystemUtils.TimeStamp=2025.06.17-14.11.06
OnlineSubsystemUtils.LastCompileMethod=Unknown
OnlineBlueprintSupport.TimeStamp=2025.06.17-14.11.06
OnlineBlueprintSupport.LastCompileMethod=Unknown
ChunkDownloader.TimeStamp=2025.06.17-14.11.12
ChunkDownloader.LastCompileMethod=Unknown
ComputeFramework.TimeStamp=2025.06.17-14.11.13
ComputeFramework.LastCompileMethod=Unknown
ExampleDeviceProfileSelector.TimeStamp=2025.06.17-14.11.13
ExampleDeviceProfileSelector.LastCompileMethod=Unknown
HairStrandsCore.TimeStamp=2025.06.17-14.11.19
HairStrandsCore.LastCompileMethod=Unknown
WindowsDeviceProfileSelector.TimeStamp=2025.06.17-14.11.46
WindowsDeviceProfileSelector.LastCompileMethod=Unknown
EditorPerformance.TimeStamp=2025.06.17-14.09.32
EditorPerformance.LastCompileMethod=Unknown
StallLogSubsystem.TimeStamp=2025.06.17-14.09.32
StallLogSubsystem.LastCompileMethod=Unknown
EditorTelemetry.TimeStamp=2025.06.17-14.09.32
EditorTelemetry.LastCompileMethod=Unknown
Analytics.TimeStamp=2025.06.17-14.06.52
Analytics.LastCompileMethod=Unknown
NFORDenoise.TimeStamp=2025.06.17-14.09.44
NFORDenoise.LastCompileMethod=Unknown
RuntimeTelemetry.TimeStamp=2025.06.17-14.09.54
RuntimeTelemetry.LastCompileMethod=Unknown
NNEDenoiserShaders.TimeStamp=2025.06.17-14.11.01
NNEDenoiserShaders.LastCompileMethod=Unknown
LauncherChunkInstaller.TimeStamp=2025.06.17-14.11.10
LauncherChunkInstaller.LastCompileMethod=Unknown
CameraCalibrationCore.TimeStamp=2025.06.17-14.11.57
CameraCalibrationCore.LastCompileMethod=Unknown
CompositeCore.TimeStamp=2025.06.17-14.09.30
CompositeCore.LastCompileMethod=Unknown
D3D12RHI.TimeStamp=2025.06.17-14.06.54
D3D12RHI.LastCompileMethod=Unknown
WindowsPlatformFeatures.TimeStamp=2025.06.17-14.07.02
WindowsPlatformFeatures.LastCompileMethod=Unknown
GameplayMediaEncoder.TimeStamp=2025.06.17-14.06.55
GameplayMediaEncoder.LastCompileMethod=Unknown
AVEncoder.TimeStamp=2025.06.17-14.06.54
AVEncoder.LastCompileMethod=Unknown
Chaos.TimeStamp=2025.06.17-14.06.54
Chaos.LastCompileMethod=Unknown
GeometryCore.TimeStamp=2025.06.17-14.06.56
GeometryCore.LastCompileMethod=Unknown
ChaosSolverEngine.TimeStamp=2025.06.17-14.06.54
ChaosSolverEngine.LastCompileMethod=Unknown
DirectoryWatcher.TimeStamp=2025.06.17-14.06.55
DirectoryWatcher.LastCompileMethod=Unknown
Settings.TimeStamp=2025.06.17-14.07.00
Settings.LastCompileMethod=Unknown
InputCore.TimeStamp=2025.06.17-14.06.56
InputCore.LastCompileMethod=Unknown
TargetPlatform.TimeStamp=2025.06.17-14.07.01
TargetPlatform.LastCompileMethod=Unknown
TurnkeySupport.TimeStamp=2025.06.17-14.07.01
TurnkeySupport.LastCompileMethod=Unknown
TextureFormat.TimeStamp=2025.06.17-14.07.01
TextureFormat.LastCompileMethod=Unknown
TextureFormatASTC.TimeStamp=2025.06.17-14.07.01
TextureFormatASTC.LastCompileMethod=Unknown
TextureFormatDXT.TimeStamp=2025.06.17-14.07.01
TextureFormatDXT.LastCompileMethod=Unknown
TextureFormatETC2.TimeStamp=2025.06.17-14.07.01
TextureFormatETC2.LastCompileMethod=Unknown
TextureFormatIntelISPCTexComp.TimeStamp=2025.06.17-14.07.01
TextureFormatIntelISPCTexComp.LastCompileMethod=Unknown
TextureFormatUncompressed.TimeStamp=2025.06.17-14.07.01
TextureFormatUncompressed.LastCompileMethod=Unknown
TextureFormatOodle.TimeStamp=2025.06.17-14.09.07
TextureFormatOodle.LastCompileMethod=Unknown
AndroidTargetPlatform.TimeStamp=2025.06.17-14.06.45
AndroidTargetPlatform.LastCompileMethod=Unknown
AndroidTargetPlatformSettings.TimeStamp=2025.06.17-14.06.45
AndroidTargetPlatformSettings.LastCompileMethod=Unknown
AndroidTargetPlatformControls.TimeStamp=2025.06.17-14.06.45
AndroidTargetPlatformControls.LastCompileMethod=Unknown
IOSTargetPlatform.TimeStamp=2025.06.17-14.06.48
IOSTargetPlatform.LastCompileMethod=Unknown
IOSTargetPlatformSettings.TimeStamp=2025.06.17-14.06.48
IOSTargetPlatformSettings.LastCompileMethod=Unknown
IOSTargetPlatformControls.TimeStamp=2025.06.17-14.06.48
IOSTargetPlatformControls.LastCompileMethod=Unknown
LinuxTargetPlatform.TimeStamp=2025.06.17-14.06.48
LinuxTargetPlatform.LastCompileMethod=Unknown
LinuxTargetPlatformSettings.TimeStamp=2025.06.17-14.06.48
LinuxTargetPlatformSettings.LastCompileMethod=Unknown
LinuxTargetPlatformControls.TimeStamp=2025.06.17-14.06.48
LinuxTargetPlatformControls.LastCompileMethod=Unknown
MacTargetPlatform.TimeStamp=2025.06.17-14.06.57
MacTargetPlatform.LastCompileMethod=Unknown
MacTargetPlatformSettings.TimeStamp=2025.06.17-14.06.57
MacTargetPlatformSettings.LastCompileMethod=Unknown
MacTargetPlatformControls.TimeStamp=2025.06.17-14.06.57
MacTargetPlatformControls.LastCompileMethod=Unknown
TVOSTargetPlatform.TimeStamp=2025.06.17-14.06.48
TVOSTargetPlatform.LastCompileMethod=Unknown
TVOSTargetPlatformSettings.TimeStamp=2025.06.17-14.06.48
TVOSTargetPlatformSettings.LastCompileMethod=Unknown
TVOSTargetPlatformControls.TimeStamp=2025.06.17-14.06.48
TVOSTargetPlatformControls.LastCompileMethod=Unknown
WindowsTargetPlatform.TimeStamp=2025.06.17-14.07.02
WindowsTargetPlatform.LastCompileMethod=Unknown
WindowsTargetPlatformSettings.TimeStamp=2025.06.17-14.07.02
WindowsTargetPlatformSettings.LastCompileMethod=Unknown
WindowsTargetPlatformControls.TimeStamp=2025.06.17-14.07.02
WindowsTargetPlatformControls.LastCompileMethod=Unknown
AudioFormatOPUS.TimeStamp=2025.06.17-14.06.54
AudioFormatOPUS.LastCompileMethod=Unknown
AudioFormatOGG.TimeStamp=2025.06.17-14.06.54
AudioFormatOGG.LastCompileMethod=Unknown
AudioFormatADPCM.TimeStamp=2025.06.17-14.06.54
AudioFormatADPCM.LastCompileMethod=Unknown
AudioFormatBINK.TimeStamp=2025.06.17-14.06.54
AudioFormatBINK.LastCompileMethod=Unknown
AudioFormatRAD.TimeStamp=2025.06.17-14.06.54
AudioFormatRAD.LastCompileMethod=Unknown
ShaderFormatVectorVM.TimeStamp=2025.06.17-14.07.00
ShaderFormatVectorVM.LastCompileMethod=Unknown
ShaderFormatD3D.TimeStamp=2025.06.17-14.07.00
ShaderFormatD3D.LastCompileMethod=Unknown
ShaderFormatOpenGL.TimeStamp=2025.06.17-14.07.00
ShaderFormatOpenGL.LastCompileMethod=Unknown
VulkanShaderFormat.TimeStamp=2025.06.17-14.07.02
VulkanShaderFormat.LastCompileMethod=Unknown
MetalShaderFormat.TimeStamp=2025.06.17-14.06.57
MetalShaderFormat.LastCompileMethod=Unknown
DerivedDataCache.TimeStamp=2025.06.17-14.06.54
DerivedDataCache.LastCompileMethod=Unknown
ShaderPreprocessor.TimeStamp=2025.06.17-14.07.00
ShaderPreprocessor.LastCompileMethod=Unknown
ImageWrapper.TimeStamp=2025.06.17-14.06.56
ImageWrapper.LastCompileMethod=Unknown
NullInstallBundleManager.TimeStamp=2025.06.17-14.06.59
NullInstallBundleManager.LastCompileMethod=Unknown
AssetRegistry.TimeStamp=2025.06.17-14.06.54
AssetRegistry.LastCompileMethod=Unknown
TargetDeviceServices.TimeStamp=2025.06.17-14.07.01
TargetDeviceServices.LastCompileMethod=Unknown
MeshUtilities.TimeStamp=2025.06.17-14.06.57
MeshUtilities.LastCompileMethod=Unknown
MaterialBaking.TimeStamp=2025.06.17-14.06.57
MaterialBaking.LastCompileMethod=Unknown
MeshMergeUtilities.TimeStamp=2025.06.17-14.06.57
MeshMergeUtilities.LastCompileMethod=Unknown
MeshReductionInterface.TimeStamp=2025.06.17-14.06.57
MeshReductionInterface.LastCompileMethod=Unknown
QuadricMeshReduction.TimeStamp=2025.06.17-14.06.59
QuadricMeshReduction.LastCompileMethod=Unknown
ProxyLODMeshReduction.TimeStamp=2025.06.17-14.09.12
ProxyLODMeshReduction.LastCompileMethod=Unknown
SkeletalMeshReduction.TimeStamp=2025.06.17-14.09.54
SkeletalMeshReduction.LastCompileMethod=Unknown
MeshBoneReduction.TimeStamp=2025.06.17-14.06.57
MeshBoneReduction.LastCompileMethod=Unknown
StaticMeshDescription.TimeStamp=2025.06.17-14.07.01
StaticMeshDescription.LastCompileMethod=Unknown
GeometryProcessingInterfaces.TimeStamp=2025.06.17-14.06.56
GeometryProcessingInterfaces.LastCompileMethod=Unknown
NaniteBuilder.TimeStamp=2025.06.17-14.06.58
NaniteBuilder.LastCompileMethod=Unknown
MeshBuilder.TimeStamp=2025.06.17-14.06.57
MeshBuilder.LastCompileMethod=Unknown
KismetCompiler.TimeStamp=2025.06.17-14.06.56
KismetCompiler.LastCompileMethod=Unknown
MovieSceneTools.TimeStamp=2025.06.17-14.06.57
MovieSceneTools.LastCompileMethod=Unknown
Sequencer.TimeStamp=2025.06.17-14.07.00
Sequencer.LastCompileMethod=Unknown
CurveEditor.TimeStamp=2025.06.17-14.06.54
CurveEditor.LastCompileMethod=Unknown
SequencerCore.TimeStamp=2025.06.17-14.07.00
SequencerCore.LastCompileMethod=Unknown
AssetDefinition.TimeStamp=2025.06.17-14.06.54
AssetDefinition.LastCompileMethod=Unknown
Core.TimeStamp=2025.06.17-14.06.54
Core.LastCompileMethod=Unknown
Networking.TimeStamp=2025.06.17-14.06.59
Networking.LastCompileMethod=Unknown
LiveCoding.TimeStamp=2025.06.17-14.06.57
LiveCoding.LastCompileMethod=Unknown
HeadMountedDisplay.TimeStamp=2025.06.17-14.06.56
HeadMountedDisplay.LastCompileMethod=Unknown
SourceCodeAccess.TimeStamp=2025.06.17-14.07.01
SourceCodeAccess.LastCompileMethod=Unknown
Messaging.TimeStamp=2025.06.17-14.06.57
Messaging.LastCompileMethod=Unknown
MRMesh.TimeStamp=2025.06.17-14.06.58
MRMesh.LastCompileMethod=Unknown
UnrealEd.TimeStamp=2025.06.17-14.07.02
UnrealEd.LastCompileMethod=Unknown
LandscapeEditorUtilities.TimeStamp=2025.06.17-14.06.57
LandscapeEditorUtilities.LastCompileMethod=Unknown
SubobjectDataInterface.TimeStamp=2025.06.17-14.07.01
SubobjectDataInterface.LastCompileMethod=Unknown
SlateCore.TimeStamp=2025.06.17-14.07.01
SlateCore.LastCompileMethod=Unknown
Slate.TimeStamp=2025.06.17-14.07.00
Slate.LastCompileMethod=Unknown
SlateReflector.TimeStamp=2025.06.17-14.07.01
SlateReflector.LastCompileMethod=Unknown
EditorStyle.TimeStamp=2025.06.17-14.06.55
EditorStyle.LastCompileMethod=Unknown
UMG.TimeStamp=2025.06.17-14.07.01
UMG.LastCompileMethod=Unknown
UMGEditor.TimeStamp=2025.06.17-14.07.01
UMGEditor.LastCompileMethod=Unknown
AssetTools.TimeStamp=2025.06.17-14.06.54
AssetTools.LastCompileMethod=Unknown
ScriptableEditorWidgets.TimeStamp=2025.06.17-14.06.59
ScriptableEditorWidgets.LastCompileMethod=Unknown
CollisionAnalyzer.TimeStamp=2025.06.17-14.06.54
CollisionAnalyzer.LastCompileMethod=Unknown
WorkspaceMenuStructure.TimeStamp=2025.06.17-14.07.02
WorkspaceMenuStructure.LastCompileMethod=Unknown
FunctionalTesting.TimeStamp=2025.06.17-14.06.55
FunctionalTesting.LastCompileMethod=Unknown
BehaviorTreeEditor.TimeStamp=2025.06.17-14.06.54
BehaviorTreeEditor.LastCompileMethod=Unknown
GameplayTasksEditor.TimeStamp=2025.06.17-14.06.55
GameplayTasksEditor.LastCompileMethod=Unknown
StringTableEditor.TimeStamp=2025.06.17-14.07.01
StringTableEditor.LastCompileMethod=Unknown
VREditor.TimeStamp=2025.06.17-14.07.02
VREditor.LastCompileMethod=Unknown
MaterialEditor.TimeStamp=2025.06.17-14.06.57
MaterialEditor.LastCompileMethod=Unknown
Overlay.TimeStamp=2025.06.17-14.06.59
Overlay.LastCompileMethod=Unknown
OverlayEditor.TimeStamp=2025.06.17-14.06.59
OverlayEditor.LastCompileMethod=Unknown
MediaAssets.TimeStamp=2025.06.17-14.06.57
MediaAssets.LastCompileMethod=Unknown
ClothingSystemRuntimeNv.TimeStamp=2025.06.17-14.06.54
ClothingSystemRuntimeNv.LastCompileMethod=Unknown
ClothingSystemEditor.TimeStamp=2025.06.17-14.06.54
ClothingSystemEditor.LastCompileMethod=Unknown
AnimationDataController.TimeStamp=2025.06.17-14.06.52
AnimationDataController.LastCompileMethod=Unknown
TimeManagement.TimeStamp=2025.06.17-14.07.01
TimeManagement.LastCompileMethod=Unknown
AnimGraph.TimeStamp=2025.06.17-14.06.54
AnimGraph.LastCompileMethod=Unknown
WorldBookmark.TimeStamp=2025.06.17-14.07.02
WorldBookmark.LastCompileMethod=Unknown
WorldPartitionEditor.TimeStamp=2025.06.17-14.07.02
WorldPartitionEditor.LastCompileMethod=Unknown
PacketHandler.TimeStamp=2025.06.17-14.06.59
PacketHandler.LastCompileMethod=Unknown
NetworkReplayStreaming.TimeStamp=2025.06.17-14.06.59
NetworkReplayStreaming.LastCompileMethod=Unknown
MassEntity.TimeStamp=2025.06.17-14.06.57
MassEntity.LastCompileMethod=Unknown
MassEntityTestSuite.TimeStamp=2025.06.17-14.06.57
MassEntityTestSuite.LastCompileMethod=Unknown
AndroidFileServer.TimeStamp=2025.06.17-14.11.11
AndroidFileServer.LastCompileMethod=Unknown
WebMMoviePlayer.TimeStamp=2025.06.17-14.11.46
WebMMoviePlayer.LastCompileMethod=Unknown
WindowsMoviePlayer.TimeStamp=2025.06.17-14.11.46
WindowsMoviePlayer.LastCompileMethod=Unknown
EnhancedInput.TimeStamp=2025.06.17-14.09.13
EnhancedInput.LastCompileMethod=Unknown
InputBlueprintNodes.TimeStamp=2025.06.17-14.09.13
InputBlueprintNodes.LastCompileMethod=Unknown
BlueprintGraph.TimeStamp=2025.06.17-14.06.54
BlueprintGraph.LastCompileMethod=Unknown
MutableRuntime.TimeStamp=2025.06.17-14.11.01
MutableRuntime.LastCompileMethod=Unknown
CustomizableObject.TimeStamp=2025.06.17-14.10.59
CustomizableObject.LastCompileMethod=Unknown
MutableTools.TimeStamp=2025.06.17-14.11.01
MutableTools.LastCompileMethod=Unknown
MutableValidation.TimeStamp=2025.06.17-14.11.01
MutableValidation.LastCompileMethod=Unknown
EnvironmentQueryEditor.TimeStamp=2025.06.17-14.08.51
EnvironmentQueryEditor.LastCompileMethod=Unknown
AnimationData.TimeStamp=2025.06.17-14.08.52
AnimationData.LastCompileMethod=Unknown
AnimationModifierLibrary.TimeStamp=2025.06.17-14.08.52
AnimationModifierLibrary.LastCompileMethod=Unknown
ControlRig.TimeStamp=2025.06.17-14.08.53
ControlRig.LastCompileMethod=Unknown
Constraints.TimeStamp=2025.06.17-14.06.54
Constraints.LastCompileMethod=Unknown
ControlRigDeveloper.TimeStamp=2025.06.17-14.08.53
ControlRigDeveloper.LastCompileMethod=Unknown
OptimusCore.TimeStamp=2025.06.17-14.08.56
OptimusCore.LastCompileMethod=Unknown
OptimusDeveloper.TimeStamp=2025.06.17-14.08.56
OptimusDeveloper.LastCompileMethod=Unknown
IKRig.TimeStamp=2025.06.17-14.08.57
IKRig.LastCompileMethod=Unknown
IKRigDeveloper.TimeStamp=2025.06.17-14.08.57
IKRigDeveloper.LastCompileMethod=Unknown
RigLogicLib.TimeStamp=2025.06.17-14.09.00
RigLogicLib.LastCompileMethod=Unknown
RigLogicLibTest.TimeStamp=2025.06.17-14.09.00
RigLogicLibTest.LastCompileMethod=Unknown
RigLogicDeveloper.TimeStamp=2025.06.17-14.09.00
RigLogicDeveloper.LastCompileMethod=Unknown
AnimationSharing.TimeStamp=2025.06.17-14.09.04
AnimationSharing.LastCompileMethod=Unknown
PropertyAccessNode.TimeStamp=2025.06.17-14.09.07
PropertyAccessNode.LastCompileMethod=Unknown
AssetManagerEditor.TimeStamp=2025.06.17-14.09.07
AssetManagerEditor.LastCompileMethod=Unknown
TreeMap.TimeStamp=2025.06.17-14.07.01
TreeMap.LastCompileMethod=Unknown
ContentBrowser.TimeStamp=2025.06.17-14.06.54
ContentBrowser.LastCompileMethod=Unknown
ContentBrowserData.TimeStamp=2025.06.17-14.06.54
ContentBrowserData.LastCompileMethod=Unknown
ToolMenus.TimeStamp=2025.06.17-14.07.01
ToolMenus.LastCompileMethod=Unknown
LevelEditor.TimeStamp=2025.06.17-14.06.57
LevelEditor.LastCompileMethod=Unknown
MainFrame.TimeStamp=2025.06.17-14.06.57
MainFrame.LastCompileMethod=Unknown
HotReload.TimeStamp=2025.06.17-14.06.56
HotReload.LastCompileMethod=Unknown
CommonMenuExtensions.TimeStamp=2025.06.17-14.06.54
CommonMenuExtensions.LastCompileMethod=Unknown
PixelInspectorModule.TimeStamp=2025.06.17-14.06.59
PixelInspectorModule.LastCompileMethod=Unknown
DataValidation.TimeStamp=2025.06.17-14.09.07
DataValidation.LastCompileMethod=Unknown
FacialAnimation.TimeStamp=2025.06.17-14.09.08
FacialAnimation.LastCompileMethod=Unknown
FacialAnimationEditor.TimeStamp=2025.06.17-14.09.08
FacialAnimationEditor.LastCompileMethod=Unknown
GameplayTagsEditor.TimeStamp=2025.06.17-14.09.08
GameplayTagsEditor.LastCompileMethod=Unknown
NiagaraCore.TimeStamp=2025.06.17-14.10.10
NiagaraCore.LastCompileMethod=Unknown
Niagara.TimeStamp=2025.06.17-14.10.10
Niagara.LastCompileMethod=Unknown
NiagaraEditor.TimeStamp=2025.06.17-14.10.11
NiagaraEditor.LastCompileMethod=Unknown
LevelSequence.TimeStamp=2025.06.17-14.06.57
LevelSequence.LastCompileMethod=Unknown
NiagaraAnimNotifies.TimeStamp=2025.06.17-14.10.10
NiagaraAnimNotifies.LastCompileMethod=Unknown
NiagaraSimCaching.TimeStamp=2025.06.17-14.10.19
NiagaraSimCaching.LastCompileMethod=Unknown
NiagaraSimCachingEditor.TimeStamp=2025.06.17-14.10.19
NiagaraSimCachingEditor.LastCompileMethod=Unknown
InterchangeNodes.TimeStamp=2025.06.17-14.10.20
InterchangeNodes.LastCompileMethod=Unknown
InterchangeFactoryNodes.TimeStamp=2025.06.17-14.10.20
InterchangeFactoryNodes.LastCompileMethod=Unknown
InterchangeImport.TimeStamp=2025.06.17-14.10.20
InterchangeImport.LastCompileMethod=Unknown
InterchangePipelines.TimeStamp=2025.06.17-14.10.20
InterchangePipelines.LastCompileMethod=Unknown
ImgMediaEngine.TimeStamp=2025.06.17-14.10.25
ImgMediaEngine.LastCompileMethod=Unknown
UdpMessaging.TimeStamp=2025.06.17-14.10.28
UdpMessaging.LastCompileMethod=Unknown
TcpMessaging.TimeStamp=2025.06.17-14.10.28
TcpMessaging.LastCompileMethod=Unknown
ActorSequence.TimeStamp=2025.06.17-14.10.59
ActorSequence.LastCompileMethod=Unknown
AudioSynesthesiaCore.TimeStamp=2025.06.17-14.11.11
AudioSynesthesiaCore.LastCompileMethod=Unknown
AudioSynesthesia.TimeStamp=2025.06.17-14.11.11
AudioSynesthesia.LastCompileMethod=Unknown
AudioAnalyzer.TimeStamp=2025.06.17-14.06.54
AudioAnalyzer.LastCompileMethod=Unknown
CableComponent.TimeStamp=2025.06.17-14.11.12
CableComponent.LastCompileMethod=Unknown
CustomMeshComponent.TimeStamp=2025.06.17-14.11.13
CustomMeshComponent.LastCompileMethod=Unknown
DataRegistry.TimeStamp=2025.06.17-14.11.13
DataRegistry.LastCompileMethod=Unknown
DataRegistryEditor.TimeStamp=2025.06.17-14.11.13
DataRegistryEditor.LastCompileMethod=Unknown
GameFeatures.TimeStamp=2025.06.17-14.11.13
GameFeatures.LastCompileMethod=Unknown
GameplayAbilities.TimeStamp=2025.06.17-14.11.13
GameplayAbilities.LastCompileMethod=Unknown
GameplayDebugger.TimeStamp=2025.06.17-14.06.55
GameplayDebugger.LastCompileMethod=Unknown
GameplayAbilitiesEditor.TimeStamp=2025.06.17-14.11.13
GameplayAbilitiesEditor.LastCompileMethod=Unknown
GameplayStateTreeModule.TimeStamp=2025.06.17-14.11.14
GameplayStateTreeModule.LastCompileMethod=Unknown
LocationServicesBPLibrary.TimeStamp=2025.06.17-14.11.26
LocationServicesBPLibrary.LastCompileMethod=Unknown
MetasoundGraphCore.TimeStamp=2025.06.17-14.11.28
MetasoundGraphCore.LastCompileMethod=Unknown
MetasoundGenerator.TimeStamp=2025.06.17-14.11.28
MetasoundGenerator.LastCompileMethod=Unknown
MetasoundFrontend.TimeStamp=2025.06.17-14.11.28
MetasoundFrontend.LastCompileMethod=Unknown
MetasoundStandardNodes.TimeStamp=2025.06.17-14.11.29
MetasoundStandardNodes.LastCompileMethod=Unknown
MetasoundEngine.TimeStamp=2025.06.17-14.11.28
MetasoundEngine.LastCompileMethod=Unknown
WaveTable.TimeStamp=2025.06.17-14.11.46
WaveTable.LastCompileMethod=Unknown
MetasoundEngineTest.TimeStamp=2025.06.17-14.11.28
MetasoundEngineTest.LastCompileMethod=Unknown
MetasoundEditor.TimeStamp=2025.06.17-14.11.28
MetasoundEditor.LastCompileMethod=Unknown
AudioWidgets.TimeStamp=2025.06.17-14.11.12
AudioWidgets.LastCompileMethod=Unknown
AdvancedWidgets.TimeStamp=2025.06.17-14.06.52
AdvancedWidgets.LastCompileMethod=Unknown
ModularGameplay.TimeStamp=2025.06.17-14.11.30
ModularGameplay.LastCompileMethod=Unknown
MsQuicRuntime.TimeStamp=2025.06.17-14.11.30
MsQuicRuntime.LastCompileMethod=Unknown
ProceduralMeshComponent.TimeStamp=2025.06.17-14.11.38
ProceduralMeshComponent.LastCompileMethod=Unknown
PropertyAccessEditor.TimeStamp=2025.06.17-14.11.38
PropertyAccessEditor.LastCompileMethod=Unknown
PropertyBindingUtils.TimeStamp=2025.06.17-14.11.38
PropertyBindingUtils.LastCompileMethod=Unknown
PropertyBindingUtilsTestSuite.TimeStamp=2025.06.17-14.11.38
PropertyBindingUtilsTestSuite.LastCompileMethod=Unknown
ReplicationGraph.TimeStamp=2025.06.17-14.11.38
ReplicationGraph.LastCompileMethod=Unknown
ResonanceAudio.TimeStamp=2025.06.17-14.11.38
ResonanceAudio.LastCompileMethod=Unknown
RigVM.TimeStamp=2025.06.17-14.11.40
RigVM.LastCompileMethod=Unknown
RigVMDeveloper.TimeStamp=2025.06.17-14.11.40
RigVMDeveloper.LastCompileMethod=Unknown
ScriptableToolsFramework.TimeStamp=2025.06.17-14.11.40
ScriptableToolsFramework.LastCompileMethod=Unknown
SmartObjectsModule.TimeStamp=2025.06.17-14.11.41
SmartObjectsModule.LastCompileMethod=Unknown
SmartObjectsTestSuite.TimeStamp=2025.06.17-14.11.41
SmartObjectsTestSuite.LastCompileMethod=Unknown
SoundFields.TimeStamp=2025.06.17-14.11.41
SoundFields.LastCompileMethod=Unknown
SignificanceManager.TimeStamp=2025.06.17-14.11.41
SignificanceManager.LastCompileMethod=Unknown
Synthesis.TimeStamp=2025.06.17-14.11.41
Synthesis.LastCompileMethod=Unknown
StateTreeModule.TimeStamp=2025.06.17-14.11.41
StateTreeModule.LastCompileMethod=Unknown
TraceServices.TimeStamp=2025.06.17-14.07.01
TraceServices.LastCompileMethod=Unknown
TraceAnalysis.TimeStamp=2025.06.17-14.07.01
TraceAnalysis.LastCompileMethod=Unknown
StateTreeTestSuite.TimeStamp=2025.06.17-14.11.41
StateTreeTestSuite.LastCompileMethod=Unknown
Paper2D.TimeStamp=2025.06.17-14.08.51
Paper2D.LastCompileMethod=Unknown
EngineCameras.TimeStamp=2025.06.17-14.09.00
EngineCameras.LastCompileMethod=Unknown
GameplayCameras.TimeStamp=2025.06.17-14.09.00
GameplayCameras.LastCompileMethod=Unknown
ChaosCaching.TimeStamp=2025.06.17-14.09.25
ChaosCaching.LastCompileMethod=Unknown
ChaosCachingEditor.TimeStamp=2025.06.17-14.09.25
ChaosCachingEditor.LastCompileMethod=Unknown
TakeRecorder.TimeStamp=2025.06.17-14.12.09
TakeRecorder.LastCompileMethod=Unknown
FullBodyIK.TimeStamp=2025.06.17-14.09.32
FullBodyIK.LastCompileMethod=Unknown
PBIK.TimeStamp=2025.06.17-14.09.32
PBIK.LastCompileMethod=Unknown
PythonScriptPlugin.TimeStamp=2025.06.17-14.09.53
PythonScriptPlugin.LastCompileMethod=Unknown
SequenceNavigator.TimeStamp=2025.06.17-14.09.54
SequenceNavigator.LastCompileMethod=Unknown
NNERuntimeORT.TimeStamp=2025.06.17-14.11.02
NNERuntimeORT.LastCompileMethod=Unknown
NNEEditor.TimeStamp=2025.06.17-14.06.59
NNEEditor.LastCompileMethod=Unknown
ChaosClothEditor.TimeStamp=2025.06.17-14.09.04
ChaosClothEditor.LastCompileMethod=Unknown
ChaosInsightsAnalysis.TimeStamp=2025.06.17-14.09.04
ChaosInsightsAnalysis.LastCompileMethod=Unknown
ChaosInsightsUI.TimeStamp=2025.06.17-14.09.04
ChaosInsightsUI.LastCompileMethod=Unknown
ChaosVD.TimeStamp=2025.06.17-14.09.04
ChaosVD.LastCompileMethod=Unknown
ChaosVDBlueprint.TimeStamp=2025.06.17-14.09.04
ChaosVDBlueprint.LastCompileMethod=Unknown
ChaosVDBuiltInExtensions.TimeStamp=2025.06.17-14.09.04
ChaosVDBuiltInExtensions.LastCompileMethod=Unknown
InputEditor.TimeStamp=2025.06.17-14.09.13
InputEditor.LastCompileMethod=Unknown
IoStoreInsights.TimeStamp=2025.06.17-14.10.21
IoStoreInsights.LastCompileMethod=Unknown
TraceInsights.TimeStamp=2025.06.17-14.07.01
TraceInsights.LastCompileMethod=Unknown
TraceInsightsCore.TimeStamp=2025.06.17-14.07.01
TraceInsightsCore.LastCompileMethod=Unknown
LightWeightInstancesEditor.TimeStamp=2025.06.17-14.10.21
LightWeightInstancesEditor.LastCompileMethod=Unknown
MassInsightsAnalysis.TimeStamp=2025.06.17-14.10.21
MassInsightsAnalysis.LastCompileMethod=Unknown
MassInsightsUI.TimeStamp=2025.06.17-14.10.21
MassInsightsUI.LastCompileMethod=Unknown
MeshPaintEditorMode.TimeStamp=2025.06.17-14.10.28
MeshPaintEditorMode.LastCompileMethod=Unknown
MeshPaintingToolset.TimeStamp=2025.06.17-14.10.28
MeshPaintingToolset.LastCompileMethod=Unknown
PCG.TimeStamp=2025.06.17-14.11.07
PCG.LastCompileMethod=Unknown
PCGEditor.TimeStamp=2025.06.17-14.11.07
PCGEditor.LastCompileMethod=Unknown
RenderGraphInsights.TimeStamp=2025.06.17-14.11.11
RenderGraphInsights.LastCompileMethod=Unknown
CustomizableObjectEditor.TimeStamp=2025.06.17-14.11.00
CustomizableObjectEditor.LastCompileMethod=Unknown
TraceUtilities.TimeStamp=2025.06.17-14.11.50
TraceUtilities.LastCompileMethod=Unknown
EditorTraceUtilities.TimeStamp=2025.06.17-14.11.50
EditorTraceUtilities.LastCompileMethod=Unknown
TraceTools.TimeStamp=2025.06.17-14.07.01
TraceTools.LastCompileMethod=Unknown
TextureGraphInsightEditor.TimeStamp=2025.06.17-14.11.47
TextureGraphInsightEditor.LastCompileMethod=Unknown
TextureGraph.TimeStamp=2025.06.17-14.11.47
TextureGraph.LastCompileMethod=Unknown
WorldMetricsCore.TimeStamp=2025.06.17-14.12.12
WorldMetricsCore.LastCompileMethod=Unknown
WorldMetricsTest.TimeStamp=2025.06.17-14.12.12
WorldMetricsTest.LastCompileMethod=Unknown
CsvMetrics.TimeStamp=2025.06.17-14.12.12
CsvMetrics.LastCompileMethod=Unknown
ACLPluginEditor.TimeStamp=2025.06.17-14.08.52
ACLPluginEditor.LastCompileMethod=Unknown
BlendSpaceMotionAnalysis.TimeStamp=2025.06.17-14.08.53
BlendSpaceMotionAnalysis.LastCompileMethod=Unknown
ControlRigSpline.TimeStamp=2025.06.17-14.08.56
ControlRigSpline.LastCompileMethod=Unknown
GameplayInsights.TimeStamp=2025.06.17-14.08.56
GameplayInsights.LastCompileMethod=Unknown
AnimationBlueprintEditor.TimeStamp=2025.06.17-14.06.52
AnimationBlueprintEditor.LastCompileMethod=Unknown
GameplayInsightsEditor.TimeStamp=2025.06.17-14.08.56
GameplayInsightsEditor.LastCompileMethod=Unknown
RewindDebuggerRuntime.TimeStamp=2025.06.17-14.08.56
RewindDebuggerRuntime.LastCompileMethod=Unknown
RewindDebuggerVLogRuntime.TimeStamp=2025.06.17-14.08.57
RewindDebuggerVLogRuntime.LastCompileMethod=Unknown
RigLogicModule.TimeStamp=2025.06.17-14.09.00
RigLogicModule.LastCompileMethod=Unknown
RigLogicEditor.TimeStamp=2025.06.17-14.09.00
RigLogicEditor.LastCompileMethod=Unknown
TweeningUtils.TimeStamp=2025.06.17-14.09.00
TweeningUtils.LastCompileMethod=Unknown
TweeningUtilsEditor.TimeStamp=2025.06.17-14.09.00
TweeningUtilsEditor.LastCompileMethod=Unknown
SkeletalMeshModelingTools.TimeStamp=2025.06.17-14.09.00
SkeletalMeshModelingTools.LastCompileMethod=Unknown
SkeletalMeshEditor.TimeStamp=2025.06.17-14.07.00
SkeletalMeshEditor.LastCompileMethod=Unknown
AnimationSharingEd.TimeStamp=2025.06.17-14.09.04
AnimationSharingEd.LastCompileMethod=Unknown
CLionSourceCodeAccess.TimeStamp=2025.06.17-14.09.04
CLionSourceCodeAccess.LastCompileMethod=Unknown
DumpGPUServices.TimeStamp=2025.06.17-14.09.06
DumpGPUServices.LastCompileMethod=Unknown
GitSourceControl.TimeStamp=2025.06.17-14.09.06
GitSourceControl.LastCompileMethod=Unknown
NamingTokens.TimeStamp=2025.06.17-14.09.06
NamingTokens.LastCompileMethod=Unknown
NamingTokensUncookedOnly.TimeStamp=2025.06.17-14.09.06
NamingTokensUncookedOnly.LastCompileMethod=Unknown
PluginUtils.TimeStamp=2025.06.17-14.09.06
PluginUtils.LastCompileMethod=Unknown
ProjectLauncher.TimeStamp=2025.06.17-14.09.06
ProjectLauncher.LastCompileMethod=Unknown
CommonLaunchExtensions.TimeStamp=2025.06.17-14.09.06
CommonLaunchExtensions.LastCompileMethod=Unknown
SubversionSourceControl.TimeStamp=2025.06.17-14.09.07
SubversionSourceControl.LastCompileMethod=Unknown
UObjectPlugin.TimeStamp=2025.06.17-14.09.07
UObjectPlugin.LastCompileMethod=Unknown
VisualStudioCodeSourceCodeAccess.TimeStamp=2025.06.17-14.09.07
VisualStudioCodeSourceCodeAccess.LastCompileMethod=Unknown
VisualStudioSourceCodeAccess.TimeStamp=2025.06.17-14.09.07
VisualStudioSourceCodeAccess.LastCompileMethod=Unknown
RiderSourceCodeAccess.TimeStamp=2025.06.17-14.09.07
RiderSourceCodeAccess.LastCompileMethod=Unknown
N10XSourceCodeAccess.TimeStamp=2025.06.17-14.09.06
N10XSourceCodeAccess.LastCompileMethod=Unknown
AssetReferenceRestrictions.TimeStamp=2025.06.17-14.09.07
AssetReferenceRestrictions.LastCompileMethod=Unknown
BlueprintHeaderView.TimeStamp=2025.06.17-14.09.07
BlueprintHeaderView.LastCompileMethod=Unknown
ChangelistReview.TimeStamp=2025.06.17-14.09.07
ChangelistReview.LastCompileMethod=Unknown
CryptoKeys.TimeStamp=2025.06.17-14.09.07
CryptoKeys.LastCompileMethod=Unknown
CryptoKeysOpenSSL.TimeStamp=2025.06.17-14.09.07
CryptoKeysOpenSSL.LastCompileMethod=Unknown
ColorGradingEditor.TimeStamp=2025.06.17-14.09.07
ColorGradingEditor.LastCompileMethod=Unknown
CurveEditorTools.TimeStamp=2025.06.17-14.09.07
CurveEditorTools.LastCompileMethod=Unknown
EditorDebugTools.TimeStamp=2025.06.17-14.09.07
EditorDebugTools.LastCompileMethod=Unknown
EditorScriptingUtilities.TimeStamp=2025.06.17-14.09.08
EditorScriptingUtilities.LastCompileMethod=Unknown
MaterialAnalyzer.TimeStamp=2025.06.17-14.09.08
MaterialAnalyzer.LastCompileMethod=Unknown
MeshLODToolset.TimeStamp=2025.06.17-14.09.08
MeshLODToolset.LastCompileMethod=Unknown
MobileLauncherProfileWizard.TimeStamp=2025.06.17-14.09.08
MobileLauncherProfileWizard.LastCompileMethod=Unknown
ModelingToolsEditorMode.TimeStamp=2025.06.17-14.09.08
ModelingToolsEditorMode.LastCompileMethod=Unknown
PluginBrowser.TimeStamp=2025.06.17-14.09.09
PluginBrowser.LastCompileMethod=Unknown
SampleToolsEditorMode.TimeStamp=2025.06.17-14.09.12
SampleToolsEditorMode.LastCompileMethod=Unknown
SequencerAnimTools.TimeStamp=2025.06.17-14.09.12
SequencerAnimTools.LastCompileMethod=Unknown
ScriptableToolsEditorMode.TimeStamp=2025.06.17-14.09.12
ScriptableToolsEditorMode.LastCompileMethod=Unknown
SpeedTreeImporter.TimeStamp=2025.06.17-14.09.12
SpeedTreeImporter.LastCompileMethod=Unknown
StylusInput.TimeStamp=2025.06.17-14.09.12
StylusInput.LastCompileMethod=Unknown
StylusInputDebugWidget.TimeStamp=2025.06.17-14.09.12
StylusInputDebugWidget.LastCompileMethod=Unknown
UVEditor.TimeStamp=2025.06.17-14.09.13
UVEditor.LastCompileMethod=Unknown
UVEditorTools.TimeStamp=2025.06.17-14.09.13
UVEditorTools.LastCompileMethod=Unknown
UVEditorToolsEditorOnly.TimeStamp=2025.06.17-14.09.13
UVEditorToolsEditorOnly.LastCompileMethod=Unknown
UMGWidgetPreview.TimeStamp=2025.06.17-14.09.12
UMGWidgetPreview.LastCompileMethod=Unknown
StaticMeshEditorModeling.TimeStamp=2025.06.17-14.09.12
StaticMeshEditorModeling.LastCompileMethod=Unknown
StaticMeshEditor.TimeStamp=2025.06.17-14.07.01
StaticMeshEditor.LastCompileMethod=Unknown
AdvancedPreviewScene.TimeStamp=2025.06.17-14.06.52
AdvancedPreviewScene.LastCompileMethod=Unknown
WorldPartitionHLODUtilities.TimeStamp=2025.06.17-14.09.13
WorldPartitionHLODUtilities.LastCompileMethod=Unknown
DatasmithContentEditor.TimeStamp=2025.06.17-14.09.13
DatasmithContentEditor.LastCompileMethod=Unknown
VariantManager.TimeStamp=2025.06.17-14.09.18
VariantManager.LastCompileMethod=Unknown
VariantManagerContentEditor.TimeStamp=2025.06.17-14.09.18
VariantManagerContentEditor.LastCompileMethod=Unknown
Cascade.TimeStamp=2025.06.17-14.10.10
Cascade.LastCompileMethod=Unknown
NiagaraBlueprintNodes.TimeStamp=2025.06.17-14.10.10
NiagaraBlueprintNodes.LastCompileMethod=Unknown
NiagaraEditorWidgets.TimeStamp=2025.06.17-14.10.12
NiagaraEditorWidgets.LastCompileMethod=Unknown
InterchangeEditor.TimeStamp=2025.06.17-14.10.20
InterchangeEditor.LastCompileMethod=Unknown
InterchangeEditorPipelines.TimeStamp=2025.06.17-14.10.20
InterchangeEditorPipelines.LastCompileMethod=Unknown
InterchangeEditorUtilities.TimeStamp=2025.06.17-14.10.20
InterchangeEditorUtilities.LastCompileMethod=Unknown
GLTFCore.TimeStamp=2025.06.17-14.10.20
GLTFCore.LastCompileMethod=Unknown
InterchangeMessages.TimeStamp=2025.06.17-14.10.20
InterchangeMessages.LastCompileMethod=Unknown
InterchangeExport.TimeStamp=2025.06.17-14.10.20
InterchangeExport.LastCompileMethod=Unknown
InterchangeDispatcher.TimeStamp=2025.06.17-14.10.20
InterchangeDispatcher.LastCompileMethod=Unknown
InterchangeCommon.TimeStamp=2025.06.17-14.10.20
InterchangeCommon.LastCompileMethod=Unknown
InterchangeCommonParser.TimeStamp=2025.06.17-14.10.20
InterchangeCommonParser.LastCompileMethod=Unknown
InterchangeFbxParser.TimeStamp=2025.06.17-14.10.20
InterchangeFbxParser.LastCompileMethod=Unknown
ImgMedia.TimeStamp=2025.06.17-14.10.25
ImgMedia.LastCompileMethod=Unknown
MediaCompositing.TimeStamp=2025.06.17-14.10.25
MediaCompositing.LastCompileMethod=Unknown
MediaPlate.TimeStamp=2025.06.17-14.10.25
MediaPlate.LastCompileMethod=Unknown
MediaPlateEditor.TimeStamp=2025.06.17-14.10.25
MediaPlateEditor.LastCompileMethod=Unknown
OnlineBase.TimeStamp=2025.06.17-14.11.03
OnlineBase.LastCompileMethod=Unknown
PerformanceMonitor.TimeStamp=2025.06.17-14.11.10
PerformanceMonitor.LastCompileMethod=Unknown
SequencerScripting.TimeStamp=2025.06.17-14.10.59
SequencerScripting.LastCompileMethod=Unknown
SequencerScriptingEditor.TimeStamp=2025.06.17-14.10.59
SequencerScriptingEditor.LastCompileMethod=Unknown
TemplateSequence.TimeStamp=2025.06.17-14.10.59
TemplateSequence.LastCompileMethod=Unknown
InterchangeTests.TimeStamp=2025.06.17-14.11.46
InterchangeTests.LastCompileMethod=Unknown
InterchangeTestEditor.TimeStamp=2025.06.17-14.11.46
InterchangeTestEditor.LastCompileMethod=Unknown
ActorLayerUtilities.TimeStamp=2025.06.17-14.11.11
ActorLayerUtilities.LastCompileMethod=Unknown
ActorLayerUtilitiesEditor.TimeStamp=2025.06.17-14.11.11
ActorLayerUtilitiesEditor.LastCompileMethod=Unknown
AndroidPermission.TimeStamp=2025.06.17-14.11.11
AndroidPermission.LastCompileMethod=Unknown
AppleImageUtils.TimeStamp=2025.06.17-14.11.11
AppleImageUtils.LastCompileMethod=Unknown
AppleImageUtilsBlueprintSupport.TimeStamp=2025.06.17-14.11.11
AppleImageUtilsBlueprintSupport.LastCompileMethod=Unknown
ArchVisCharacter.TimeStamp=2025.06.17-14.11.11
ArchVisCharacter.LastCompileMethod=Unknown
AssetTags.TimeStamp=2025.06.17-14.11.11
AssetTags.LastCompileMethod=Unknown
AudioCapture.TimeStamp=2025.06.17-14.11.11
AudioCapture.LastCompileMethod=Unknown
AudioCaptureWasapi.TimeStamp=2025.06.17-14.06.54
AudioCaptureWasapi.LastCompileMethod=Unknown
AudioWidgetsEditor.TimeStamp=2025.06.17-14.11.12
AudioWidgetsEditor.LastCompileMethod=Unknown
ComputeFrameworkEditor.TimeStamp=2025.06.17-14.11.13
ComputeFrameworkEditor.LastCompileMethod=Unknown
GameFeaturesEditor.TimeStamp=2025.06.17-14.11.13
GameFeaturesEditor.LastCompileMethod=Unknown
GeometryCacheEd.TimeStamp=2025.06.17-14.11.14
GeometryCacheEd.LastCompileMethod=Unknown
GeometryCacheSequencer.TimeStamp=2025.06.17-14.11.14
GeometryCacheSequencer.LastCompileMethod=Unknown
GeometryCacheStreamer.TimeStamp=2025.06.17-14.11.14
GeometryCacheStreamer.LastCompileMethod=Unknown
GeometryCache.TimeStamp=2025.06.17-14.11.14
GeometryCache.LastCompileMethod=Unknown
GeometryCacheTracks.TimeStamp=2025.06.17-14.11.14
GeometryCacheTracks.LastCompileMethod=Unknown
GeometryAlgorithms.TimeStamp=2025.06.17-14.11.14
GeometryAlgorithms.LastCompileMethod=Unknown
DynamicMesh.TimeStamp=2025.06.17-14.11.14
DynamicMesh.LastCompileMethod=Unknown
MeshFileUtils.TimeStamp=2025.06.17-14.11.14
MeshFileUtils.LastCompileMethod=Unknown
HairStrandsDeformer.TimeStamp=2025.06.17-14.11.19
HairStrandsDeformer.LastCompileMethod=Unknown
HairStrandsRuntime.TimeStamp=2025.06.17-14.11.19
HairStrandsRuntime.LastCompileMethod=Unknown
HairStrandsEditor.TimeStamp=2025.06.17-14.11.19
HairStrandsEditor.LastCompileMethod=Unknown
HairCardGeneratorFramework.TimeStamp=2025.06.17-14.11.19
HairCardGeneratorFramework.LastCompileMethod=Unknown
HairStrandsDataflow.TimeStamp=2025.06.17-14.11.19
HairStrandsDataflow.LastCompileMethod=Unknown
HairStrandsSolver.TimeStamp=2025.06.17-14.11.19
HairStrandsSolver.LastCompileMethod=Unknown
GooglePAD.TimeStamp=2025.06.17-14.11.19
GooglePAD.LastCompileMethod=Unknown
InputDebugging.TimeStamp=2025.06.17-14.11.26
InputDebugging.LastCompileMethod=Unknown
InputDebuggingEditor.TimeStamp=2025.06.17-14.11.26
InputDebuggingEditor.LastCompileMethod=Unknown
InstancedActors.TimeStamp=2025.06.17-14.11.26
InstancedActors.LastCompileMethod=Unknown
InstancedActorsTestSuite.TimeStamp=2025.06.17-14.11.26
InstancedActorsTestSuite.LastCompileMethod=Unknown
InstancedActorsEditor.TimeStamp=2025.06.17-14.11.26
InstancedActorsEditor.LastCompileMethod=Unknown
MobilePatchingUtils.TimeStamp=2025.06.17-14.11.30
MobilePatchingUtils.LastCompileMethod=Unknown
MeshModelingTools.TimeStamp=2025.06.17-14.11.27
MeshModelingTools.LastCompileMethod=Unknown
MeshModelingToolsEditorOnly.TimeStamp=2025.06.17-14.11.27
MeshModelingToolsEditorOnly.LastCompileMethod=Unknown
ModelingComponents.TimeStamp=2025.06.17-14.11.27
ModelingComponents.LastCompileMethod=Unknown
GeometryFramework.TimeStamp=2025.06.17-14.06.56
GeometryFramework.LastCompileMethod=Unknown
ModelingComponentsEditorOnly.TimeStamp=2025.06.17-14.11.27
ModelingComponentsEditorOnly.LastCompileMethod=Unknown
ModelingOperators.TimeStamp=2025.06.17-14.11.27
ModelingOperators.LastCompileMethod=Unknown
ModelingOperatorsEditorOnly.TimeStamp=2025.06.17-14.11.27
ModelingOperatorsEditorOnly.LastCompileMethod=Unknown
SkeletalMeshModifiers.TimeStamp=2025.06.17-14.11.28
SkeletalMeshModifiers.LastCompileMethod=Unknown
MassCommon.TimeStamp=2025.06.17-14.11.26
MassCommon.LastCompileMethod=Unknown
MassActors.TimeStamp=2025.06.17-14.11.26
MassActors.LastCompileMethod=Unknown
MassEQS.TimeStamp=2025.06.17-14.11.26
MassEQS.LastCompileMethod=Unknown
MassSignals.TimeStamp=2025.06.17-14.11.26
MassSignals.LastCompileMethod=Unknown
MassSpawner.TimeStamp=2025.06.17-14.11.26
MassSpawner.LastCompileMethod=Unknown
MassSmartObjects.TimeStamp=2025.06.17-14.11.26
MassSmartObjects.LastCompileMethod=Unknown
MassSimulation.TimeStamp=2025.06.17-14.11.26
MassSimulation.LastCompileMethod=Unknown
MassLOD.TimeStamp=2025.06.17-14.11.26
MassLOD.LastCompileMethod=Unknown
MassMovement.TimeStamp=2025.06.17-14.11.26
MassMovement.LastCompileMethod=Unknown
MassReplication.TimeStamp=2025.06.17-14.11.26
MassReplication.LastCompileMethod=Unknown
MassRepresentation.TimeStamp=2025.06.17-14.11.26
MassRepresentation.LastCompileMethod=Unknown
MassGameplayDebug.TimeStamp=2025.06.17-14.11.26
MassGameplayDebug.LastCompileMethod=Unknown
MassGameplayEditor.TimeStamp=2025.06.17-14.11.26
MassGameplayEditor.LastCompileMethod=Unknown
MassGameplayExternalTraits.TimeStamp=2025.06.17-14.11.26
MassGameplayExternalTraits.LastCompileMethod=Unknown
MassGameplayTestSuite.TimeStamp=2025.06.17-14.11.26
MassGameplayTestSuite.LastCompileMethod=Unknown
ProceduralMeshComponentEditor.TimeStamp=2025.06.17-14.11.38
ProceduralMeshComponentEditor.LastCompileMethod=Unknown
PropertyBindingUtilsEditor.TimeStamp=2025.06.17-14.11.38
PropertyBindingUtilsEditor.LastCompileMethod=Unknown
EditorScriptableToolsFramework.TimeStamp=2025.06.17-14.11.40
EditorScriptableToolsFramework.LastCompileMethod=Unknown
SkeletalMerging.TimeStamp=2025.06.17-14.11.41
SkeletalMerging.LastCompileMethod=Unknown
SynthesisEditor.TimeStamp=2025.06.17-14.11.41
SynthesisEditor.LastCompileMethod=Unknown
StateTreeEditorModule.TimeStamp=2025.06.17-14.11.41
StateTreeEditorModule.LastCompileMethod=Unknown
ZoneGraphAnnotations.TimeStamp=2025.06.17-14.11.46
ZoneGraphAnnotations.LastCompileMethod=Unknown
WorldConditions.TimeStamp=2025.06.17-14.11.46
WorldConditions.LastCompileMethod=Unknown
WorldConditionsTestSuite.TimeStamp=2025.06.17-14.11.46
WorldConditionsTestSuite.LastCompileMethod=Unknown
Paper2DEditor.TimeStamp=2025.06.17-14.08.51
Paper2DEditor.LastCompileMethod=Unknown
PaperSpriteSheetImporter.TimeStamp=2025.06.17-14.08.51
PaperSpriteSheetImporter.LastCompileMethod=Unknown
PaperTiledImporter.TimeStamp=2025.06.17-14.08.51
PaperTiledImporter.LastCompileMethod=Unknown
ZoneGraph.TimeStamp=2025.06.17-14.11.46
ZoneGraph.LastCompileMethod=Unknown
ZoneGraphTestSuite.TimeStamp=2025.06.17-14.11.46
ZoneGraphTestSuite.LastCompileMethod=Unknown
ZoneGraphDebug.TimeStamp=2025.06.17-14.11.46
ZoneGraphDebug.LastCompileMethod=Unknown
OodleNetworkHandlerComponent.TimeStamp=2025.06.17-14.09.04
OodleNetworkHandlerComponent.LastCompileMethod=Unknown
GameplayCamerasUncookedOnly.TimeStamp=2025.06.17-14.09.00
GameplayCamerasUncookedOnly.LastCompileMethod=Unknown
AdvancedRenamer.TimeStamp=2025.06.17-14.09.18
AdvancedRenamer.LastCompileMethod=Unknown
AutomationUtils.TimeStamp=2025.06.17-14.09.23
AutomationUtils.LastCompileMethod=Unknown
AutomationUtilsEditor.TimeStamp=2025.06.17-14.09.23
AutomationUtilsEditor.LastCompileMethod=Unknown
BackChannel.TimeStamp=2025.06.17-14.09.23
BackChannel.LastCompileMethod=Unknown
FractureEditor.TimeStamp=2025.06.17-14.09.25
FractureEditor.LastCompileMethod=Unknown
ChaosNiagara.TimeStamp=2025.06.17-14.09.29
ChaosNiagara.LastCompileMethod=Unknown
ChaosUserDataPT.TimeStamp=2025.06.17-14.09.30
ChaosUserDataPT.LastCompileMethod=Unknown
ChaosSolverEditor.TimeStamp=2025.06.17-14.09.30
ChaosSolverEditor.LastCompileMethod=Unknown
DataflowAssetTools.TimeStamp=2025.06.17-14.09.31
DataflowAssetTools.LastCompileMethod=Unknown
DataflowEnginePlugin.TimeStamp=2025.06.17-14.09.31
DataflowEnginePlugin.LastCompileMethod=Unknown
DataflowEngine.TimeStamp=2025.06.17-14.06.54
DataflowEngine.LastCompileMethod=Unknown
DataflowSimulation.TimeStamp=2025.06.17-14.06.54
DataflowSimulation.LastCompileMethod=Unknown
DataflowNodes.TimeStamp=2025.06.17-14.09.31
DataflowNodes.LastCompileMethod=Unknown
TedsAlerts.TimeStamp=2025.06.17-14.09.32
TedsAlerts.LastCompileMethod=Unknown
TypedElementFramework.TimeStamp=2025.06.17-14.07.01
TypedElementFramework.LastCompileMethod=Unknown
TedsAssetData.TimeStamp=2025.06.17-14.09.32
TedsAssetData.LastCompileMethod=Unknown
TedsContentBrowser.TimeStamp=2025.06.17-14.09.32
TedsContentBrowser.LastCompileMethod=Unknown
TedsDebugger.TimeStamp=2025.06.17-14.09.32
TedsDebugger.LastCompileMethod=Unknown
TedsOutliner.TimeStamp=2025.06.17-14.09.32
TedsOutliner.LastCompileMethod=Unknown
TedsPropertyEditor.TimeStamp=2025.06.17-14.09.32
TedsPropertyEditor.LastCompileMethod=Unknown
TedsQueryStack.TimeStamp=2025.06.17-14.09.32
TedsQueryStack.LastCompileMethod=Unknown
TedsRevisionControl.TimeStamp=2025.06.17-14.09.32
TedsRevisionControl.LastCompileMethod=Unknown
TedsSettings.TimeStamp=2025.06.17-14.09.32
TedsSettings.LastCompileMethod=Unknown
TedsTableViewer.TimeStamp=2025.06.17-14.09.32
TedsTableViewer.LastCompileMethod=Unknown
TedsCore.TimeStamp=2025.06.17-14.09.32
TedsCore.LastCompileMethod=Unknown
MassEntityEditor.TimeStamp=2025.06.17-14.06.57
MassEntityEditor.LastCompileMethod=Unknown
MassEntityDebugger.TimeStamp=2025.06.17-14.06.57
MassEntityDebugger.LastCompileMethod=Unknown
TedsUI.TimeStamp=2025.06.17-14.09.32
TedsUI.LastCompileMethod=Unknown
TargetingSystem.TimeStamp=2025.06.17-14.09.32
TargetingSystem.LastCompileMethod=Unknown
GeometryCollectionEditor.TimeStamp=2025.06.17-14.09.32
GeometryCollectionEditor.LastCompileMethod=Unknown
GeometryCollectionTracks.TimeStamp=2025.06.17-14.09.32
GeometryCollectionTracks.LastCompileMethod=Unknown
GeometryCollectionSequencer.TimeStamp=2025.06.17-14.09.32
GeometryCollectionSequencer.LastCompileMethod=Unknown
GeometryCollectionEngine.TimeStamp=2025.06.17-14.06.56
GeometryCollectionEngine.LastCompileMethod=Unknown
GeometryCollectionNodes.TimeStamp=2025.06.17-14.09.32
GeometryCollectionNodes.LastCompileMethod=Unknown
GeometryCollectionDepNodes.TimeStamp=2025.06.17-14.09.32
GeometryCollectionDepNodes.LastCompileMethod=Unknown
GeometryDataflowNodes.TimeStamp=2025.06.17-14.09.32
GeometryDataflowNodes.LastCompileMethod=Unknown
GizmoSettings.TimeStamp=2025.06.17-14.09.33
GizmoSettings.LastCompileMethod=Unknown
GeometryFlowCore.TimeStamp=2025.06.17-14.09.32
GeometryFlowCore.LastCompileMethod=Unknown
GeometryFlowMeshProcessing.TimeStamp=2025.06.17-14.09.32
GeometryFlowMeshProcessing.LastCompileMethod=Unknown
GeometryFlowMeshProcessingEditor.TimeStamp=2025.06.17-14.09.32
GeometryFlowMeshProcessingEditor.LastCompileMethod=Unknown
ImageWidgets.TimeStamp=2025.06.17-14.09.34
ImageWidgets.LastCompileMethod=Unknown
Landmass.TimeStamp=2025.06.17-14.09.35
Landmass.LastCompileMethod=Unknown
LandmassEditor.TimeStamp=2025.06.17-14.09.35
LandmassEditor.LastCompileMethod=Unknown
LocalizableMessage.TimeStamp=2025.06.17-14.09.37
LocalizableMessage.LastCompileMethod=Unknown
LocalizableMessageBlueprint.TimeStamp=2025.06.17-14.09.37
LocalizableMessageBlueprint.LastCompileMethod=Unknown
MeshModelingToolsExp.TimeStamp=2025.06.17-14.09.37
MeshModelingToolsExp.LastCompileMethod=Unknown
MeshModelingToolsEditorOnlyExp.TimeStamp=2025.06.17-14.09.37
MeshModelingToolsEditorOnlyExp.LastCompileMethod=Unknown
GeometryProcessingAdapters.TimeStamp=2025.06.17-14.09.37
GeometryProcessingAdapters.LastCompileMethod=Unknown
ModelingEditorUI.TimeStamp=2025.06.17-14.09.37
ModelingEditorUI.LastCompileMethod=Unknown
ModelingUI.TimeStamp=2025.06.17-14.09.37
ModelingUI.LastCompileMethod=Unknown
StructUtils.TimeStamp=2025.06.17-14.09.55
StructUtils.LastCompileMethod=Unknown
StructUtilsEngine.TimeStamp=2025.06.17-14.09.55
StructUtilsEngine.LastCompileMethod=Unknown
ToolPresetAsset.TimeStamp=2025.06.17-14.09.57
ToolPresetAsset.LastCompileMethod=Unknown
ToolPresetEditor.TimeStamp=2025.06.17-14.09.57
ToolPresetEditor.LastCompileMethod=Unknown
AlembicImporter.TimeStamp=2025.06.17-14.10.19
AlembicImporter.LastCompileMethod=Unknown
AlembicLibrary.TimeStamp=2025.06.17-14.10.19
AlembicLibrary.LastCompileMethod=Unknown
NNEDenoiser.TimeStamp=2025.06.17-14.11.01
NNEDenoiser.LastCompileMethod=Unknown
PCGExternalDataInterop.TimeStamp=2025.06.17-14.11.10
PCGExternalDataInterop.LastCompileMethod=Unknown
PCGExternalDataInteropEditor.TimeStamp=2025.06.17-14.11.10
PCGExternalDataInteropEditor.LastCompileMethod=Unknown
MetaHumanSDKEditor.TimeStamp=2025.06.17-14.10.59
MetaHumanSDKEditor.LastCompileMethod=Unknown
MetaHumanSDKRuntime.TimeStamp=2025.06.17-14.10.59
MetaHumanSDKRuntime.LastCompileMethod=Unknown
CameraCalibrationCoreEditor.TimeStamp=2025.06.17-14.11.57
CameraCalibrationCoreEditor.LastCompileMethod=Unknown
TakeMovieScene.TimeStamp=2025.06.17-14.12.09
TakeMovieScene.LastCompileMethod=Unknown
TakeSequencer.TimeStamp=2025.06.17-14.12.09
TakeSequencer.LastCompileMethod=Unknown
ContentBrowserAssetDataSource.TimeStamp=2025.06.17-14.09.07
ContentBrowserAssetDataSource.LastCompileMethod=Unknown
CollectionManager.TimeStamp=2025.06.17-14.06.54
CollectionManager.LastCompileMethod=Unknown
ContentBrowserClassDataSource.TimeStamp=2025.06.17-14.09.07
ContentBrowserClassDataSource.LastCompileMethod=Unknown
ContentBrowserFileDataSource.TimeStamp=2025.06.17-14.09.07
ContentBrowserFileDataSource.LastCompileMethod=Unknown
PortableObjectFileDataSource.TimeStamp=2025.06.17-14.09.08
PortableObjectFileDataSource.LastCompileMethod=Unknown
XInputDevice.TimeStamp=2025.06.17-14.11.46
XInputDevice.LastCompileMethod=Unknown
BaseCharacterFXEditor.TimeStamp=2025.06.17-14.09.30
BaseCharacterFXEditor.LastCompileMethod=Unknown
LightMixer.TimeStamp=2025.06.17-14.09.09
LightMixer.LastCompileMethod=Unknown
ObjectMixerEditor.TimeStamp=2025.06.17-14.09.09
ObjectMixerEditor.LastCompileMethod=Unknown
Bridge.TimeStamp=2025.06.17-14.25.40
Bridge.LastCompileMethod=Unknown
MegascansPlugin.TimeStamp=2025.06.17-14.25.41
MegascansPlugin.LastCompileMethod=Unknown
CmdLinkServer.TimeStamp=2025.06.17-14.09.04
CmdLinkServer.LastCompileMethod=Unknown
Fab.TimeStamp=2025.06.17-14.25.36
Fab.LastCompileMethod=Unknown
AudioSynesthesiaEditor.TimeStamp=2025.06.17-14.11.11
AudioSynesthesiaEditor.LastCompileMethod=Unknown
DataflowEditor.TimeStamp=2025.06.17-14.09.31
DataflowEditor.LastCompileMethod=Unknown
LevelSequenceNavigatorBridge.TimeStamp=2025.06.17-14.09.35
LevelSequenceNavigatorBridge.LastCompileMethod=Unknown
TakesCore.TimeStamp=2025.06.17-14.12.09
TakesCore.LastCompileMethod=Unknown
TakeTrackRecorders.TimeStamp=2025.06.17-14.12.09
TakeTrackRecorders.LastCompileMethod=Unknown
TakeRecorderSources.TimeStamp=2025.06.17-14.12.09
TakeRecorderSources.LastCompileMethod=Unknown
CacheTrackRecorder.TimeStamp=2025.06.17-14.12.09
CacheTrackRecorder.LastCompileMethod=Unknown
TakeRecorderNamingTokens.TimeStamp=2025.06.17-14.12.09
TakeRecorderNamingTokens.LastCompileMethod=Unknown
ProfileVisualizer.TimeStamp=2025.06.17-14.06.59
ProfileVisualizer.LastCompileMethod=Unknown
ImageWriteQueue.TimeStamp=2025.06.17-14.06.56
ImageWriteQueue.LastCompileMethod=Unknown
TypedElementRuntime.TimeStamp=2025.06.17-14.07.01
TypedElementRuntime.LastCompileMethod=Unknown
LevelInstanceEditor.TimeStamp=2025.06.17-14.06.57
LevelInstanceEditor.LastCompileMethod=Unknown
ChaosVDRuntime.TimeStamp=2025.06.17-14.06.54
ChaosVDRuntime.LastCompileMethod=Unknown
AIModule.TimeStamp=2025.06.17-14.06.52
AIModule.LastCompileMethod=Unknown
NavigationSystem.TimeStamp=2025.06.17-14.06.58
NavigationSystem.LastCompileMethod=Unknown
AITestSuite.TimeStamp=2025.06.17-14.06.52
AITestSuite.LastCompileMethod=Unknown
MessagingRpc.TimeStamp=2025.06.17-14.06.57
MessagingRpc.LastCompileMethod=Unknown
PortalRpc.TimeStamp=2025.06.17-14.06.59
PortalRpc.LastCompileMethod=Unknown
PortalServices.TimeStamp=2025.06.17-14.06.59
PortalServices.LastCompileMethod=Unknown
AnalyticsET.TimeStamp=2025.06.17-14.06.52
AnalyticsET.LastCompileMethod=Unknown
LauncherPlatform.TimeStamp=2025.06.17-14.06.57
LauncherPlatform.LastCompileMethod=Unknown
AudioMixerXAudio2.TimeStamp=2025.06.17-14.06.54
AudioMixerXAudio2.LastCompileMethod=Unknown
AudioMixer.TimeStamp=2025.06.17-14.06.54
AudioMixer.LastCompileMethod=Unknown
StreamingPauseRendering.TimeStamp=2025.06.17-14.07.01
StreamingPauseRendering.LastCompileMethod=Unknown
MovieScene.TimeStamp=2025.06.17-14.06.57
MovieScene.LastCompileMethod=Unknown
MovieSceneTracks.TimeStamp=2025.06.17-14.06.58
MovieSceneTracks.LastCompileMethod=Unknown
CinematicCamera.TimeStamp=2025.06.17-14.06.54
CinematicCamera.LastCompileMethod=Unknown
SparseVolumeTexture.TimeStamp=2025.06.17-14.07.01
SparseVolumeTexture.LastCompileMethod=Unknown
Documentation.TimeStamp=2025.06.17-14.06.55
Documentation.LastCompileMethod=Unknown
OutputLog.TimeStamp=2025.06.17-14.06.59
OutputLog.LastCompileMethod=Unknown
SourceControlWindows.TimeStamp=2025.06.17-14.07.01
SourceControlWindows.LastCompileMethod=Unknown
SourceControlWindowExtender.TimeStamp=2025.06.17-14.07.01
SourceControlWindowExtender.LastCompileMethod=Unknown
UncontrolledChangelists.TimeStamp=2025.06.17-14.07.01
UncontrolledChangelists.LastCompileMethod=Unknown
ClassViewer.TimeStamp=2025.06.17-14.06.54
ClassViewer.LastCompileMethod=Unknown
StructViewer.TimeStamp=2025.06.17-14.07.01
StructViewer.LastCompileMethod=Unknown
GraphEditor.TimeStamp=2025.06.17-14.06.56
GraphEditor.LastCompileMethod=Unknown
Kismet.TimeStamp=2025.06.17-14.06.56
Kismet.LastCompileMethod=Unknown
KismetWidgets.TimeStamp=2025.06.17-14.06.56
KismetWidgets.LastCompileMethod=Unknown
Persona.TimeStamp=2025.06.17-14.06.59
Persona.LastCompileMethod=Unknown
PackagesDialog.TimeStamp=2025.06.17-14.06.59
PackagesDialog.LastCompileMethod=Unknown
DetailCustomizations.TimeStamp=2025.06.17-14.06.55
DetailCustomizations.LastCompileMethod=Unknown
ComponentVisualizers.TimeStamp=2025.06.17-14.06.54
ComponentVisualizers.LastCompileMethod=Unknown
Layers.TimeStamp=2025.06.17-14.06.57
Layers.LastCompileMethod=Unknown
AutomationWindow.TimeStamp=2025.06.17-14.06.54
AutomationWindow.LastCompileMethod=Unknown
AutomationController.TimeStamp=2025.06.17-14.06.54
AutomationController.LastCompileMethod=Unknown
DeviceManager.TimeStamp=2025.06.17-14.06.55
DeviceManager.LastCompileMethod=Unknown
ProfilerClient.TimeStamp=
ProfilerClient.LastCompileMethod=Unknown
SessionFrontend.TimeStamp=2025.06.17-14.07.00
SessionFrontend.LastCompileMethod=Unknown
LegacyProjectLauncher.TimeStamp=2025.06.17-14.06.57
LegacyProjectLauncher.LastCompileMethod=Unknown
SettingsEditor.TimeStamp=2025.06.17-14.07.00
SettingsEditor.LastCompileMethod=Unknown
EditorSettingsViewer.TimeStamp=2025.06.17-14.06.55
EditorSettingsViewer.LastCompileMethod=Unknown
InternationalizationSettings.TimeStamp=2025.06.17-14.06.56
InternationalizationSettings.LastCompileMethod=Unknown
ProjectSettingsViewer.TimeStamp=2025.06.17-14.06.59
ProjectSettingsViewer.LastCompileMethod=Unknown
ProjectTargetPlatformEditor.TimeStamp=2025.06.17-14.06.59
ProjectTargetPlatformEditor.LastCompileMethod=Unknown
Blutility.TimeStamp=2025.06.17-14.06.54
Blutility.LastCompileMethod=Unknown
XmlParser.TimeStamp=2025.06.17-14.07.02
XmlParser.LastCompileMethod=Unknown
UndoHistory.TimeStamp=2025.06.17-14.07.01
UndoHistory.LastCompileMethod=Unknown
DeviceProfileEditor.TimeStamp=2025.06.17-14.06.55
DeviceProfileEditor.LastCompileMethod=Unknown
HardwareTargeting.TimeStamp=2025.06.17-14.06.56
HardwareTargeting.LastCompileMethod=Unknown
LocalizationDashboard.TimeStamp=2025.06.17-14.06.57
LocalizationDashboard.LastCompileMethod=Unknown
LocalizationService.TimeStamp=2025.06.17-14.06.57
LocalizationService.LastCompileMethod=Unknown
MergeActors.TimeStamp=2025.06.17-14.06.57
MergeActors.LastCompileMethod=Unknown
InputBindingEditor.TimeStamp=2025.06.17-14.06.56
InputBindingEditor.LastCompileMethod=Unknown
EditorInteractiveToolsFramework.TimeStamp=2025.06.17-14.06.55
EditorInteractiveToolsFramework.LastCompileMethod=Unknown
InteractiveToolsFramework.TimeStamp=2025.06.17-14.06.56
InteractiveToolsFramework.LastCompileMethod=Unknown
EditorFramework.TimeStamp=2025.06.17-14.06.55
EditorFramework.LastCompileMethod=Unknown
EditorConfig.TimeStamp=2025.06.17-14.06.55
EditorConfig.LastCompileMethod=Unknown
DerivedDataEditor.TimeStamp=2025.06.17-14.06.54
DerivedDataEditor.LastCompileMethod=Unknown
ZenEditor.TimeStamp=2025.06.17-14.07.03
ZenEditor.LastCompileMethod=Unknown
CSVtoSVG.TimeStamp=2025.06.17-14.06.54
CSVtoSVG.LastCompileMethod=Unknown
VirtualizationEditor.TimeStamp=2025.06.17-14.07.02
VirtualizationEditor.LastCompileMethod=Unknown
AnimationSettings.TimeStamp=2025.06.17-14.06.52
AnimationSettings.LastCompileMethod=Unknown
GameplayDebuggerEditor.TimeStamp=2025.06.17-14.06.55
GameplayDebuggerEditor.LastCompileMethod=Unknown
RenderResourceViewer.TimeStamp=2025.06.17-14.06.59
RenderResourceViewer.LastCompileMethod=Unknown
UniversalObjectLocatorEditor.TimeStamp=2025.06.17-14.07.01
UniversalObjectLocatorEditor.LastCompileMethod=Unknown
StructUtilsEditor.TimeStamp=2025.06.17-14.07.01
StructUtilsEditor.LastCompileMethod=Unknown
StructUtilsTestSuite.TimeStamp=2025.06.17-14.07.01
StructUtilsTestSuite.LastCompileMethod=Unknown
SVGDistanceField.TimeStamp=2025.06.17-14.07.01
SVGDistanceField.LastCompileMethod=Unknown
DataHierarchyEditor.TimeStamp=2025.06.17-14.06.54
DataHierarchyEditor.LastCompileMethod=Unknown
AndroidRuntimeSettings.TimeStamp=2025.06.17-14.06.45
AndroidRuntimeSettings.LastCompileMethod=Unknown
IOSRuntimeSettings.TimeStamp=2025.06.17-14.06.48
IOSRuntimeSettings.LastCompileMethod=Unknown
MacPlatformEditor.TimeStamp=2025.06.17-14.06.57
MacPlatformEditor.LastCompileMethod=Unknown
WindowsPlatformEditor.TimeStamp=2025.06.17-14.07.02
WindowsPlatformEditor.LastCompileMethod=Unknown
AndroidPlatformEditor.TimeStamp=2025.06.17-14.06.45
AndroidPlatformEditor.LastCompileMethod=Unknown
AndroidDeviceDetection.TimeStamp=2025.06.17-14.06.45
AndroidDeviceDetection.LastCompileMethod=Unknown
PIEPreviewDeviceProfileSelector.TimeStamp=2025.06.17-14.06.59
PIEPreviewDeviceProfileSelector.LastCompileMethod=Unknown
IOSPlatformEditor.TimeStamp=2025.06.17-14.06.48
IOSPlatformEditor.LastCompileMethod=Unknown
LogVisualizer.TimeStamp=2025.06.17-14.06.57
LogVisualizer.LastCompileMethod=Unknown
WidgetRegistration.TimeStamp=2025.06.17-14.07.02
WidgetRegistration.LastCompileMethod=Unknown
ClothPainter.TimeStamp=2025.06.17-14.06.54
ClothPainter.LastCompileMethod=Unknown
ViewportInteraction.TimeStamp=2025.06.17-14.07.02
ViewportInteraction.LastCompileMethod=Unknown
EditorWidgets.TimeStamp=2025.06.17-14.06.55
EditorWidgets.LastCompileMethod=Unknown
ViewportSnapping.TimeStamp=2025.06.17-14.07.02
ViewportSnapping.LastCompileMethod=Unknown
PlacementMode.TimeStamp=2025.06.17-14.06.59
PlacementMode.LastCompileMethod=Unknown
MeshPaint.TimeStamp=2025.06.17-14.06.57
MeshPaint.LastCompileMethod=Unknown
SessionServices.TimeStamp=2025.06.17-14.07.00
SessionServices.LastCompileMethod=Unknown
TextureGraphEditor.TimeStamp=2025.06.17-14.11.47
TextureGraphEditor.LastCompileMethod=Unknown
AnimatorKitSettings.TimeStamp=2025.06.17-14.08.53
AnimatorKitSettings.LastCompileMethod=Unknown
ControlRigEditor.TimeStamp=2025.06.17-14.08.53
ControlRigEditor.LastCompileMethod=Unknown
OptimusEditor.TimeStamp=2025.06.17-14.08.56
OptimusEditor.LastCompileMethod=Unknown
RewindDebugger.TimeStamp=2025.06.17-14.08.56
RewindDebugger.LastCompileMethod=Unknown
RewindDebuggerVLog.TimeStamp=2025.06.17-14.08.57
RewindDebuggerVLog.LastCompileMethod=Unknown
IKRigEditor.TimeStamp=2025.06.17-14.08.57
IKRigEditor.LastCompileMethod=Unknown
GeometryMode.TimeStamp=2025.06.17-14.09.08
GeometryMode.LastCompileMethod=Unknown
BspMode.TimeStamp=2025.06.17-14.09.08
BspMode.LastCompileMethod=Unknown
TextureAlignMode.TimeStamp=2025.06.17-14.09.08
TextureAlignMode.LastCompileMethod=Unknown
EngineAssetDefinitions.TimeStamp=2025.06.17-14.09.08
EngineAssetDefinitions.LastCompileMethod=Unknown
AndroidMediaEditor.TimeStamp=2025.06.17-14.10.23
AndroidMediaEditor.LastCompileMethod=Unknown
AndroidMediaFactory.TimeStamp=2025.06.17-14.10.23
AndroidMediaFactory.LastCompileMethod=Unknown
AvfMediaEditor.TimeStamp=2025.06.17-14.10.23
AvfMediaEditor.LastCompileMethod=Unknown
AvfMediaFactory.TimeStamp=2025.06.17-14.10.23
AvfMediaFactory.LastCompileMethod=Unknown
ImgMediaEditor.TimeStamp=2025.06.17-14.10.25
ImgMediaEditor.LastCompileMethod=Unknown
ImgMediaFactory.TimeStamp=2025.06.17-14.10.25
ImgMediaFactory.LastCompileMethod=Unknown
OpenExrWrapper.TimeStamp=2025.06.17-14.10.25
OpenExrWrapper.LastCompileMethod=Unknown
MediaCompositingEditor.TimeStamp=2025.06.17-14.10.25
MediaCompositingEditor.LastCompileMethod=Unknown
SequenceRecorder.TimeStamp=2025.06.17-14.07.00
SequenceRecorder.LastCompileMethod=Unknown
MediaPlayerEditor.TimeStamp=2025.06.17-14.10.25
MediaPlayerEditor.LastCompileMethod=Unknown
WebMMedia.TimeStamp=2025.06.17-14.10.28
WebMMedia.LastCompileMethod=Unknown
WebMMediaEditor.TimeStamp=2025.06.17-14.10.28
WebMMediaEditor.LastCompileMethod=Unknown
WebMMediaFactory.TimeStamp=2025.06.17-14.10.28
WebMMediaFactory.LastCompileMethod=Unknown
WmfMediaEditor.TimeStamp=2025.06.17-14.10.28
WmfMediaEditor.LastCompileMethod=Unknown
WmfMediaFactory.TimeStamp=2025.06.17-14.10.28
WmfMediaFactory.LastCompileMethod=Unknown
ActorSequenceEditor.TimeStamp=2025.06.17-14.10.59
ActorSequenceEditor.LastCompileMethod=Unknown
LevelSequenceEditor.TimeStamp=2025.06.17-14.10.59
LevelSequenceEditor.LastCompileMethod=Unknown
TemplateSequenceEditor.TimeStamp=2025.06.17-14.10.59
TemplateSequenceEditor.LastCompileMethod=Unknown
AndroidFileServerEditor.TimeStamp=2025.06.17-14.11.11
AndroidFileServerEditor.LastCompileMethod=Unknown
AudioCaptureEditor.TimeStamp=2025.06.17-14.11.11
AudioCaptureEditor.LastCompileMethod=Unknown
GooglePADEditor.TimeStamp=2025.06.17-14.11.19
GooglePADEditor.LastCompileMethod=Unknown
MassMovementEditor.TimeStamp=2025.06.17-14.11.26
MassMovementEditor.LastCompileMethod=Unknown
ResonanceAudioEditor.TimeStamp=2025.06.17-14.11.38
ResonanceAudioEditor.LastCompileMethod=Unknown
RigVMEditor.TimeStamp=2025.06.17-14.11.40
RigVMEditor.LastCompileMethod=Unknown
SmartObjectsEditorModule.TimeStamp=2025.06.17-14.11.41
SmartObjectsEditorModule.LastCompileMethod=Unknown
WaveTableEditor.TimeStamp=2025.06.17-14.11.46
WaveTableEditor.LastCompileMethod=Unknown
WorldConditionsEditor.TimeStamp=2025.06.17-14.11.46
WorldConditionsEditor.LastCompileMethod=Unknown
SmartSnapping.TimeStamp=2025.06.17-14.08.51
SmartSnapping.LastCompileMethod=Unknown
ZoneGraphEditor.TimeStamp=2025.06.17-14.11.46
ZoneGraphEditor.LastCompileMethod=Unknown
CameraShakePreviewer.TimeStamp=2025.06.17-14.09.00
CameraShakePreviewer.LastCompileMethod=Unknown
GameplayCamerasEditor.TimeStamp=2025.06.17-14.09.00
GameplayCamerasEditor.LastCompileMethod=Unknown
CharacterAI.TimeStamp=2025.06.17-14.09.30
CharacterAI.LastCompileMethod=Unknown
FractureEngine.TimeStamp=2025.06.17-14.09.32
FractureEngine.LastCompileMethod=Unknown
PlanarCut.TimeStamp=2025.06.17-14.09.53
PlanarCut.LastCompileMethod=Unknown
ActorPickerMode.TimeStamp=2025.06.17-14.06.52
ActorPickerMode.LastCompileMethod=Unknown
SceneDepthPickerMode.TimeStamp=2025.06.17-14.06.59
SceneDepthPickerMode.LastCompileMethod=Unknown
LandscapeEditor.TimeStamp=2025.06.17-14.06.57
LandscapeEditor.LastCompileMethod=Unknown
FoliageEdit.TimeStamp=2025.06.17-14.06.55
FoliageEdit.LastCompileMethod=Unknown
VirtualTexturingEditor.TimeStamp=2025.06.17-14.07.02
VirtualTexturingEditor.LastCompileMethod=Unknown
AutomationWorker.TimeStamp=2025.06.17-14.06.54
AutomationWorker.LastCompileMethod=Unknown
SequenceRecorderSections.TimeStamp=2025.06.17-14.07.00
SequenceRecorderSections.LastCompileMethod=Unknown
StatsViewer.TimeStamp=2025.06.17-14.07.01
StatsViewer.LastCompileMethod=Unknown
DataLayerEditor.TimeStamp=2025.06.17-14.06.54
DataLayerEditor.LastCompileMethod=Unknown
AndroidDeviceProfileSelector.TimeStamp=2025.06.17-14.11.11
AndroidDeviceProfileSelector.LastCompileMethod=Unknown
GameProjectGeneration.TimeStamp=2025.06.17-14.06.56
GameProjectGeneration.LastCompileMethod=Unknown
UnsavedAssetsTracker.TimeStamp=2025.06.17-14.07.02
UnsavedAssetsTracker.LastCompileMethod=Unknown
StatusBar.TimeStamp=2025.06.17-14.07.01
StatusBar.LastCompileMethod=Unknown
SceneOutliner.TimeStamp=2025.06.17-14.06.59
SceneOutliner.LastCompileMethod=Unknown
AddContentDialog.TimeStamp=2025.06.17-14.06.52
AddContentDialog.LastCompileMethod=Unknown
WidgetCarousel.TimeStamp=2025.06.17-14.07.02
WidgetCarousel.LastCompileMethod=Unknown
SubobjectEditor.TimeStamp=2025.06.17-14.07.01
SubobjectEditor.LastCompileMethod=Unknown
HierarchicalLODOutliner.TimeStamp=2025.06.17-14.06.56
HierarchicalLODOutliner.LastCompileMethod=Unknown
HierarchicalLODUtilities.TimeStamp=2025.06.17-14.06.56
HierarchicalLODUtilities.LastCompileMethod=Unknown
Voice.TimeStamp=2025.06.17-14.07.02
Voice.LastCompileMethod=Unknown
MovieSceneCapture.TimeStamp=2025.06.17-14.06.57
MovieSceneCapture.LastCompileMethod=Unknown
SequencerWidgets.TimeStamp=2025.06.17-14.07.00
SequencerWidgets.LastCompileMethod=Unknown
ExternalImagePicker.TimeStamp=2025.06.17-14.06.55
ExternalImagePicker.LastCompileMethod=Unknown
LauncherServices.TimeStamp=2025.06.17-14.06.57
LauncherServices.LastCompileMethod=Unknown
SQLiteCore.TimeStamp=2025.06.17-14.11.13
SQLiteCore.LastCompileMethod=Unknown
DatabaseSupport.TimeStamp=2025.06.17-14.11.13
DatabaseSupport.LastCompileMethod=Unknown
SQLiteSupport.TimeStamp=2025.06.17-14.11.13
SQLiteSupport.LastCompileMethod=Unknown
LudusCore.TimeStamp=2025.06.11-21.50.18
LudusCore.LastCompileMethod=Unknown
LudusClient.TimeStamp=2025.06.11-21.50.44
LudusClient.LastCompileMethod=Unknown
LudusChatUI.TimeStamp=2025.06.11-21.50.54
LudusChatUI.LastCompileMethod=Unknown
LudusEditor.TimeStamp=2025.06.11-21.50.38
LudusEditor.LastCompileMethod=Unknown
LudusMarkdown.TimeStamp=2025.06.11-21.50.40
LudusMarkdown.LastCompileMethod=Unknown
LudusDatabase.TimeStamp=2025.06.11-21.50.18
LudusDatabase.LastCompileMethod=Unknown
LudusGraphManipulator.TimeStamp=2025.06.11-21.50.36
LudusGraphManipulator.LastCompileMethod=Unknown
DataTableEditor.TimeStamp=2025.06.17-14.06.54
DataTableEditor.LastCompileMethod=Unknown
HTTPServer.TimeStamp=2025.06.17-14.06.56
HTTPServer.LastCompileMethod=Unknown

[MessageLog]
LastLogListing=MapCheck

[AssetEditorSubsystem]
CleanShutdown=True
DebuggerAttached=False
RecentAssetEditors=Behavior Tree
RecentAssetEditors=WidgetBlueprintEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=MaterialInstanceEditor
RecentAssetEditors=MaterialInstanceEditor
RecentAssetEditors=MaterialInstanceEditor
RecentAssetEditors=MaterialInstanceEditor
RecentAssetEditors=MaterialInstanceEditor
RecentAssetEditors=DataTableEditor
RecentAssetEditors=UserDefinedStructureEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=GenericAssetEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=GenericAssetEditor
RecentAssetEditors=GenericAssetEditor
RecentAssetEditors=GenericAssetEditor
RecentAssetEditors=GenericAssetEditor
RecentAssetEditors=GenericAssetEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=GenericAssetEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=BlueprintEditor
RecentAssetEditors=BlueprintEditor

[ContentBrowser]
ContentBrowserTab4.SelectedPaths=/Game
ContentBrowserTab4.PluginFilters=
AssetPropertyPicker.ThumbnailSizeGrid=2
AssetPropertyPicker.ThumbnailSizeList=2
AssetPropertyPicker.ThumbnailSizeCustom=2
AssetPropertyPicker.ThumbnailSizeColumn=0
AssetPropertyPicker.CurrentViewType=0
AssetPropertyPicker.ZoomScale=0
AssetPropertyPicker.ListViewColumnsManuallyChangedOnce=False
AssetPropertyPicker.ColumnViewColumnsManuallyChangedOnce=False
AssetPropertyPicker.ListHiddenColumns=Class
ContentBrowserTab1.SourcesExpanded=True
ContentBrowserTab1.IsLocked=False
ContentBrowserTab1.FavoritesAreaExpanded=False
ContentBrowserTab1.FavoritesSearchAreaExpanded=False
ContentBrowserTab1.PathAreaExpanded=True
ContentBrowserTab1.PathSearchAreaExpanded=False
ContentBrowserTab1.VerticalSplitter.FixedSlotSize0=230
ContentBrowserTab1.VerticalSplitter.SlotSize1=1
ContentBrowserTab1.VerticalSplitter.SlotSize2=1
ContentBrowserTab1.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserTab1.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserTab1.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserTab1.SelectedPaths=
ContentBrowserTab1.PluginFilters=
ContentBrowserTab1.Favorites.SelectedPaths=
FavoritePaths=
ContentBrowserTab1.SelectedCollections=
ContentBrowserTab1.ExpandedCollections=
ContentBrowserTab1.CollectionAreaExpanded=False
ContentBrowserTab1.CollectionSearchAreaExpanded=False
ContentBrowserTab1.ThumbnailSizeGrid=2
ContentBrowserTab1.ThumbnailSizeList=2
ContentBrowserTab1.ThumbnailSizeCustom=2
ContentBrowserTab1.ThumbnailSizeColumn=0
ContentBrowserTab1.CurrentViewType=1
ContentBrowserTab1.ZoomScale=0
ContentBrowserTab1.ListViewColumnsManuallyChangedOnce=False
ContentBrowserTab1.ColumnViewColumnsManuallyChangedOnce=False
ContentBrowserTab2.SourcesExpanded=True
ContentBrowserTab2.IsLocked=False
ContentBrowserTab2.FavoritesAreaExpanded=False
ContentBrowserTab2.FavoritesSearchAreaExpanded=False
ContentBrowserTab2.PathAreaExpanded=True
ContentBrowserTab2.PathSearchAreaExpanded=False
ContentBrowserTab2.VerticalSplitter.FixedSlotSize0=230
ContentBrowserTab2.VerticalSplitter.SlotSize1=1
ContentBrowserTab2.VerticalSplitter.SlotSize2=1
ContentBrowserTab2.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserTab2.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserTab2.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserTab2.SelectedPaths=
ContentBrowserTab2.PluginFilters=
ContentBrowserTab2.Favorites.SelectedPaths=
ContentBrowserTab2.SelectedCollections=
ContentBrowserTab2.ExpandedCollections=
ContentBrowserTab2.CollectionAreaExpanded=False
ContentBrowserTab2.CollectionSearchAreaExpanded=False
ContentBrowserTab2.ThumbnailSizeGrid=2
ContentBrowserTab2.ThumbnailSizeList=2
ContentBrowserTab2.ThumbnailSizeCustom=2
ContentBrowserTab2.ThumbnailSizeColumn=0
ContentBrowserTab2.CurrentViewType=1
ContentBrowserTab2.ZoomScale=0
ContentBrowserTab2.ListViewColumnsManuallyChangedOnce=False
ContentBrowserTab2.ColumnViewColumnsManuallyChangedOnce=False
ContentBrowserTab4.SourcesExpanded=True
ContentBrowserTab4.IsLocked=False
ContentBrowserTab4.FavoritesAreaExpanded=False
ContentBrowserTab4.FavoritesSearchAreaExpanded=False
ContentBrowserTab4.PathAreaExpanded=True
ContentBrowserTab4.PathSearchAreaExpanded=False
ContentBrowserTab4.VerticalSplitter.FixedSlotSize0=230
ContentBrowserTab4.VerticalSplitter.SlotSize1=1
ContentBrowserTab4.VerticalSplitter.SlotSize2=1
ContentBrowserTab4.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserTab4.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserTab4.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserTab4.Favorites.SelectedPaths=
ContentBrowserTab4.SelectedCollections=
ContentBrowserTab4.ExpandedCollections=
ContentBrowserTab4.CollectionAreaExpanded=False
ContentBrowserTab4.CollectionSearchAreaExpanded=False
ContentBrowserTab4.ThumbnailSizeGrid=2
ContentBrowserTab4.ThumbnailSizeList=2
ContentBrowserTab4.ThumbnailSizeCustom=2
ContentBrowserTab4.ThumbnailSizeColumn=0
ContentBrowserTab4.CurrentViewType=1
ContentBrowserTab4.ZoomScale=0
ContentBrowserTab4.ListViewColumnsManuallyChangedOnce=False
ContentBrowserTab4.ColumnViewColumnsManuallyChangedOnce=False
ContentBrowserTab4.JumpMRU=/All/Game
ContentBrowserTab4.JumpMRU=/All/Game/UnitBPs
ContentBrowserTab4.JumpMRU=/All/Game/GameInit
ContentBrowserTab4.JumpMRU=/All/Game/GameInit/EnhancedInput
ContentBrowserTab4.JumpMRU=/All/Game/GridBasedBuilder/Demo
ContentBrowserTab4.JumpMRU=/All/Game/GridBasedBuilder
ContentBrowserTab4.JumpMRU=/All/Game/GridBasedBuilder/Blueprints
ContentBrowserTab4.JumpMRU=/All/Game/Models
ContentBrowserTab4.JumpMRU=/All/Game/GridBasedBuilder/Data
ContentBrowserTab4.JumpMRU=/All
ContentBrowserTab4.JumpMRU=/All/Game/Models/Test
ContentBrowserTab4.JumpMRU=/All/Classes_Game
ContentBrowserTab4.JumpMRU=/All/Classes_Game/ArmorWars
ContentBrowserTab4.JumpMRU=/All/Classes_Game/ArmorWars/Public
ContentBrowserTab3.SourcesExpanded=True
ContentBrowserTab3.IsLocked=False
ContentBrowserTab3.FavoritesAreaExpanded=False
ContentBrowserTab3.FavoritesSearchAreaExpanded=False
ContentBrowserTab3.PathAreaExpanded=True
ContentBrowserTab3.PathSearchAreaExpanded=False
ContentBrowserTab3.VerticalSplitter.FixedSlotSize0=230
ContentBrowserTab3.VerticalSplitter.SlotSize1=1
ContentBrowserTab3.VerticalSplitter.SlotSize2=1
ContentBrowserTab3.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserTab3.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserTab3.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserTab3.SelectedPaths=/Game/GameInit
ContentBrowserTab3.PluginFilters=
ContentBrowserTab3.Favorites.SelectedPaths=
ContentBrowserTab3.SelectedCollections=
ContentBrowserTab3.ExpandedCollections=
ContentBrowserTab3.CollectionAreaExpanded=False
ContentBrowserTab3.CollectionSearchAreaExpanded=False
ContentBrowserTab3.ThumbnailSizeGrid=2
ContentBrowserTab3.ThumbnailSizeList=2
ContentBrowserTab3.ThumbnailSizeCustom=2
ContentBrowserTab3.ThumbnailSizeColumn=0
ContentBrowserTab3.CurrentViewType=1
ContentBrowserTab3.ZoomScale=0
ContentBrowserTab3.ListViewColumnsManuallyChangedOnce=False
ContentBrowserTab3.ColumnViewColumnsManuallyChangedOnce=False
SequenceBrowser.ThumbnailSizeGrid=3
SequenceBrowser.ThumbnailSizeList=3
SequenceBrowser.ThumbnailSizeCustom=3
SequenceBrowser.ThumbnailSizeColumn=0
SequenceBrowser.CurrentViewType=2
SequenceBrowser.ZoomScale=0
SequenceBrowser.HiddenColumns=Source Frame Rate
SequenceBrowser.HiddenColumns=Number of Frames
SequenceBrowser.HiddenColumns=Number of Keys
SequenceBrowser.HiddenColumns=AnimSyncMarkerList
SequenceBrowser.HiddenColumns=Compressed Size (KB)
SequenceBrowser.HiddenColumns=Target Frame Rate
SequenceBrowser.HiddenColumns=ImportFileFramerate
SequenceBrowser.HiddenColumns=ImportResampleFramerate
SequenceBrowser.HiddenColumns=AdditiveAnimType
SequenceBrowser.HiddenColumns=RetargetSource
SequenceBrowser.HiddenColumns=RetargetSourceAsset
SequenceBrowser.HiddenColumns=Interpolation
SequenceBrowser.HiddenColumns=bEnableRootMotion
SequenceBrowser.HiddenColumns=bUseNormalizedRootMotionScale
SequenceBrowser.HiddenColumns=PlatformTargetFrameRate
SequenceBrowser.HiddenColumns=NumberOfSampledKeys
SequenceBrowser.HiddenColumns=SequenceLength
SequenceBrowser.HiddenColumns=Skeleton
SequenceBrowser.HiddenColumns=ParentAsset
SequenceBrowser.HiddenColumns=PreviewSkeletalMesh
SequenceBrowser.HiddenColumns=HasParentAsset
SequenceBrowser.HiddenColumns=AnimNotifyList
SequenceBrowser.HiddenColumns=CurveNameList
SequenceBrowser.HiddenColumns=Class
SequenceBrowser.ListViewColumnsManuallyChangedOnce=False
SequenceBrowser.ColumnViewColumnsManuallyChangedOnce=False
SequenceBrowser.ListHiddenColumns=Class
ContentBrowserTab3.JumpMRU=/All/Game/GameInit
ContentBrowserTab3.JumpMRU=/All/Game
ContentBrowserTab3.JumpMRU=/All/Game/UnitBPs
ContentBrowserTab3.JumpMRU=/All/Game/Models
ContentBrowserTab3.JumpMRU=/All/Game/GameInit/EnhancedInput
ContentBrowserTab3.JumpMRU=/All/Game/GridBasedBuilder/Blueprints/DemoBuildings
ContentBrowserTab3.JumpMRU=/All/Game/GridBasedBuilder/Blueprints
ContentBrowserTab3.JumpMRU=/All/Game/GridBasedBuilder
ContentBrowserTab3.JumpMRU=/All/Game/GridBasedBuilder/Demo
AssetDialog.ThumbnailSizeGrid=3
AssetDialog.ThumbnailSizeList=3
AssetDialog.ThumbnailSizeCustom=3
AssetDialog.ThumbnailSizeColumn=0
AssetDialog.CurrentViewType=1
AssetDialog.ZoomScale=0
AssetDialog.ListViewColumnsManuallyChangedOnce=False
AssetDialog.ColumnViewColumnsManuallyChangedOnce=False
AssetDialog.ListHiddenColumns=Class
ContentBrowserDrawer.SourcesExpanded=True
ContentBrowserDrawer.IsLocked=False
ContentBrowserDrawer.FavoritesAreaExpanded=False
ContentBrowserDrawer.FavoritesSearchAreaExpanded=False
ContentBrowserDrawer.PathAreaExpanded=True
ContentBrowserDrawer.PathSearchAreaExpanded=False
ContentBrowserDrawer.VerticalSplitter.FixedSlotSize0=230
ContentBrowserDrawer.VerticalSplitter.SlotSize1=1
ContentBrowserDrawer.VerticalSplitter.SlotSize2=0.75
ContentBrowserDrawer.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserDrawer.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserDrawer.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserDrawer.SelectedPaths=
ContentBrowserDrawer.PluginFilters=
ContentBrowserDrawer.Favorites.SelectedPaths=
ContentBrowserDrawer.SelectedCollections=
ContentBrowserDrawer.ExpandedCollections=
ContentBrowserDrawer.CollectionAreaExpanded=False
ContentBrowserDrawer.CollectionSearchAreaExpanded=False
ContentBrowserDrawer.ThumbnailSizeGrid=2
ContentBrowserDrawer.ThumbnailSizeList=2
ContentBrowserDrawer.ThumbnailSizeCustom=2
ContentBrowserDrawer.ThumbnailSizeColumn=0
ContentBrowserDrawer.CurrentViewType=1
ContentBrowserDrawer.ZoomScale=0
ContentBrowserDrawer.ListViewColumnsManuallyChangedOnce=False
ContentBrowserDrawer.ColumnViewColumnsManuallyChangedOnce=False

[AssetEditorSubsystemRecents]
MRUItem0=/Game/GameInit/TestBB
MRUItem1=/Game/GameInit/ArmorWarsHUDWidget
MRUItem2=/Game/UnitBPs/BP_MediumTank
MRUItem3=/Game/Models/AltColor3
MRUItem4=/Game/Models/AltColor2
MRUItem5=/Game/Models/AltColor1
MRUItem6=/Game/Models/ColorStripes
MRUItem7=/Game/Models/TankBase
MRUItem8=/Game/DT_TeamColor
MRUItem9=/Game/TeamColors
MRUItem10=/Game/UnitBPs/MedTankAIcontroller
MRUItem11=/Game/GameInit/BP_RTSPawn
MRUItem12=/Game/GameInit/EnhancedInput/ArmorWarsIMC
MRUItem13=/Game/GameInit/ArmorWarsPlayerController
MRUItem14=/Game/GameInit/EnhancedInput/CameraRotate
MRUItem15=/Game/GameInit/EnhancedInput/CameraMoveRight
MRUItem16=/Game/GameInit/EnhancedInput/CameraMoveForward
MRUItem17=/Game/GameInit/EnhancedInput/PrimaryAction
MRUItem18=/Game/GameInit/EnhancedInput/CameraZoom
MRUItem19=/Game/GameInit/ArmorWarsGameMode
MRUItem20=/Game/GameInit/ArmorWarsGameState
MRUItem21=/Game/GameInit/EnhancedInput/SecondaryAction
MRUItem22=/Game/GameInit/ArmorWarsGameInstance
MRUItem23=/Engine/EditorBlueprintResources/StandardMacros
MRUItem24=/Game/GridBasedBuilder/Demo/BP_Demo_Camera
MRUItem25=/Game/GameInit/ArmorWarsHud
MRUItem26=/Game/GridBasedBuilder/Demo/PC_Demo_PlayerCntroller
MRUItem27=/Game/GridBasedBuilder/Demo/GM_Demo_GameMode
MRUItem28=/Game/UnitBPs/MedTankProjectile

[DetailCategories]
ArmorWarsGameInstance_C.Debug=True
EdGraph.Graph=True
EdGraph.Inputs=True
EdGraph.Outputs=True
K2Node_FunctionEntry.GraphNodeDetail=True
K2Node_FunctionEntry.Graph=True
K2Node_FunctionEntry.Inputs=True
K2Node_FunctionEntry.Outputs=True
Blueprint.ClassOptions=True
Blueprint.BlueprintOptions=True
Blueprint.Imports=True
Blueprint.Interfaces=True
ArmorWarsGameMode_C.Game Settings=True
ArmorWarsGameMode_C.Tick=True
ArmorWarsGameMode_C.Debug=True
ArmorWarsGameMode_C.Classes=True
ArmorWarsGameMode_C.GameMode=True
ArmorWarsGameMode_C.Game=True
ArmorWarsGameMode_C.Physics=True
ArmorWarsGameMode_C.Events=True
ArmorWarsPlayerController_C.Tick=True
ArmorWarsPlayerController_C.Components=True
ArmorWarsPlayerController_C.Camera=True
ArmorWarsPlayerController_C.Debug=True
ArmorWarsPlayerController_C.PlayerController=True
ArmorWarsPlayerController_C.Cheat Manager=True
ArmorWarsPlayerController_C.MouseInterface=True
ArmorWarsPlayerController_C.Game=True
ArmorWarsPlayerController_C.Input=True
ArmorWarsPlayerController_C.WorldPartition=True
ArmorWarsPlayerController_C.Replication=True
ArmorWarsPlayerController_C.Actor=True
ArmorWarsPlayerController_C.HLOD=True
ArmorWarsPlayerController_C.Controller=True
ArmorWarsPlayerController_C.Physics=True
ArmorWarsPlayerController_C.Events=True
RTSLandUnit_C.TransformCommon=True
RTSLandUnit_C.StaticMesh=True
RTSLandUnit_C.Tick=True
RTSLandUnit_C.Terrain=True
RTSLandUnit_C.Land Unit=True
RTSLandUnit_C.ComponentTick=True
RTSLandUnit_C.Physics=True
RTSLandUnit_C.Collision=True
RTSLandUnit_C.Lighting=True
RTSLandUnit_C.Mesh Painting=True
RTSLandUnit_C.Rendering=True
RTSLandUnit_C.HLOD=True
RTSLandUnit_C.VirtualTexture=True
RTSLandUnit_C.Tags=True
RTSLandUnit_C.Replication=True
RTSLandUnit_C.ComponentReplication=True
RTSLandUnit_C.Cooking=True
RTSLandUnit_C.Navigation=True
RTSLandUnit_C.AssetUserData=True
RTSLandUnit_C.Variable=True
RTSLandUnit_C.FloatingPawnMovement=True
RTSLandUnit_C.Velocity=True
RTSLandUnit_C.PlanarMovement=True
RTSLandUnit_C.MovementComponent=True
RTSLandUnit_C.Activation=True
RTSLandUnit_C.NavMovement=True
RTSLandUnit_C.Armor=True
RTSLandUnit_C.AI Behavior=True
RTSLandUnit_C.Special Abilities=True
RTSLandUnit_C.Combat=True
RTSLandUnit_C.Tactical AI=True
RTSLandUnit_C.Weapon Controller=True
RTSLandUnit_C.RTS=True
RTSLandUnit_C.Actor=True
RTSLandUnit_C.Input=True
RTSLandUnit_C.Events=True
RTSArmorComponent.Variable=True
RTSArmorComponent.Sockets=True
RTSArmorComponent.Armor=True
RTSArmorComponent.Tags=True
RTSArmorComponent.ComponentReplication=True
RTSArmorComponent.ComponentTick=True
RTSArmorComponent.Activation=True
RTSArmorComponent.Cooking=True
RTSArmorComponent.Events=True
RTSTacticalAIComponent.Variable=True
RTSTacticalAIComponent.Sockets=True
RTSTacticalAIComponent.Tactical AI=True
RTSTacticalAIComponent.Tags=True
RTSTacticalAIComponent.ComponentReplication=True
RTSTacticalAIComponent.ComponentTick=True
RTSTacticalAIComponent.Activation=True
RTSTacticalAIComponent.Cooking=True
RTSTacticalAIComponent.Events=True
RTSWeaponController.Variable=True
RTSWeaponController.Sockets=True
RTSWeaponController.Weapon Controller=True
RTSWeaponController.Tags=True
RTSWeaponController.ComponentReplication=True
RTSWeaponController.ComponentTick=True
RTSWeaponController.Activation=True
RTSWeaponController.Cooking=True
RTSWeaponController.Events=True
InterchangeGenericAssetsPipeline.Common=True
InterchangeGenericAssetsPipeline.Common Meshes=True
InterchangeGenericAssetsPipeline.Common Skeletal Meshes and Animations=True
InterchangeGenericAssetsPipeline.Static Meshes=True
InterchangeGenericAssetsPipeline.Skeletal Meshes=True
InterchangeGenericAssetsPipeline.Animations=True
InterchangeGenericAssetsPipeline.Materials=True
InterchangeGenericAssetsPipeline.Textures=True
InterchangeGenericAssetsPipeline.Extra Information=True
StaticMesh.StaticMeshMaterials=True
StaticMesh.NaniteSettings=True
StaticMesh.LODCustomMode=True
StaticMesh.LOD0=True
StaticMesh.LodSettings=True
StaticMesh.StaticMesh=True
StaticMesh.Collision=True
StaticMesh.ImportSettings=True
StaticMesh.RayTracing=True
StaticMesh.Navigation=True
AssetViewerSettings.Settings=True
StaticMeshSocket.StaticMeshSocket=True
StaticMeshComponent.Variable=True
StaticMeshComponent.TransformCommon=True
StaticMeshComponent.Sockets=True
StaticMeshComponent.StaticMesh=True
StaticMeshComponent.ComponentTick=True
StaticMeshComponent.Physics=True
StaticMeshComponent.Mesh Painting=True
StaticMeshComponent.Collision=True
StaticMeshComponent.HLOD=True
StaticMeshComponent.Lighting=True
StaticMeshComponent.Rendering=True
StaticMeshComponent.VirtualTexture=True
StaticMeshComponent.Tags=True
StaticMeshComponent.ComponentReplication=True
StaticMeshComponent.Cooking=True
StaticMeshComponent.Navigation=True
StaticMeshComponent.Events=True
RTSWeaponComponent.Variable=True
RTSWeaponComponent.Sockets=True
RTSWeaponComponent.Weapon=True
RTSWeaponComponent.Ballistics=True
RTSWeaponComponent.Targeting=True
RTSWeaponComponent.Turret=True
RTSWeaponComponent.ComponentTick=True
RTSWeaponComponent.Multi-Barrel=True
RTSWeaponComponent.Tags=True
RTSWeaponComponent.ComponentReplication=True
RTSWeaponComponent.Activation=True
RTSWeaponComponent.Cooking=True
RTSWeaponComponent.Events=True
TankTurret_C.Weapon=True
TankTurret_C.ComponentTick=True
TankTurret_C.Ballistics=True
TankTurret_C.Targeting=True
TankTurret_C.Turret=True
TankTurret_C.Multi-Barrel=True
TankTurret_C.Tags=True
TankTurret_C.ComponentReplication=True
TankTurret_C.Activation=True
TankTurret_C.Cooking=True
TankTurret_C.Variable=True
TankTurret_C.Sockets=True
TankTurret_C.Events=True
PropertyWrapper.Variable=True
PropertyWrapper.DefaultValueCategory=True
PropertyWrapper.Events=True
MaterialEditorInstanceConstant.ParameterGroups=True
MaterialEditorInstanceConstant.General=True
MaterialEditorInstanceConstant.Previewing=True
SkeletalMesh.Material Slots=True
SkeletalMesh.LODCustomMode=True
SkeletalMesh.LOD0=True
SkeletalMesh.LODSettings=True
SkeletalMesh.Clothing=True
SkeletalMesh.Animation=True
SkeletalMesh.SkeletalMesh=True
SkeletalMesh.Mesh=True
SkeletalMesh.SkinWeights=True
SkeletalMesh.ImportSettings=True
SkeletalMesh.AnimationRig=True
SkeletalMesh.Lighting=True
SkeletalMesh.Deformer=True
SkeletalMesh.Rendering=True
SkeletalMesh.Physics=True
SkeletalMesh.RayTracing=True
SkeletalMesh.Sampling=True
BoneProxy.Transforms=True
BoneProxy.Bone=True
SkeletalMeshSocket.Socket Parameters=True
SkeletonEditingTool.Action=True
MeshTopologySelectionMechanicProperties.SelectionActions=True
MeshTopologySelectionMechanicProperties.SelectionFilter=True
MeshTopologySelectionMechanicProperties.AdditionalSelectionOptions=True
MirroringProperties.Mirror=True
OrientingProperties.Orient=True
SkeletonEditingProperties.Details=True
SkeletonEditingProperties.Viewport Axis Settings=True
PersonaPreviewSceneDescription.Animation=True
PersonaPreviewSceneDescription.Mesh=True
PersonaPreviewSceneDescription.Physics=True
PersonaPreviewSceneDescription.Additional Meshes=True
BP_MediumTank_C.TransformCommon=True
BP_MediumTank_C.StaticMesh=False
BP_MediumTank_C.Tick=True
BP_MediumTank_C.Terrain=False
BP_MediumTank_C.Land Unit=True
BP_MediumTank_C.ComponentTick=False
BP_MediumTank_C.Physics=False
BP_MediumTank_C.Collision=True
BP_MediumTank_C.Lighting=False
BP_MediumTank_C.Mesh Painting=False
BP_MediumTank_C.Rendering=False
BP_MediumTank_C.HLOD=False
BP_MediumTank_C.VirtualTexture=False
BP_MediumTank_C.Tags=False
BP_MediumTank_C.Replication=True
BP_MediumTank_C.ComponentReplication=False
BP_MediumTank_C.Cooking=False
BP_MediumTank_C.Navigation=True
BP_MediumTank_C.AssetUserData=True
BP_MediumTank_C.Variable=False
BP_MediumTank_C.FloatingPawnMovement=True
BP_MediumTank_C.Velocity=True
BP_MediumTank_C.PlanarMovement=True
BP_MediumTank_C.MovementComponent=True
BP_MediumTank_C.Activation=False
BP_MediumTank_C.NavMovement=True
BP_MediumTank_C.Armor=False
BP_MediumTank_C.AI Behavior=True
BP_MediumTank_C.Special Abilities=True
BP_MediumTank_C.Combat=True
BP_MediumTank_C.Tactical AI=True
BP_MediumTank_C.Weapon Controller=True
BP_MediumTank_C.RTS=True
BP_MediumTank_C.Actor=True
BP_MediumTank_C.Input=True
BP_MediumTank_C.Events=True
BPC_MedTankTurret_C.TransformCommon=True
BPC_MedTankTurret_C.StaticMesh=True
BPC_MedTankTurret_C.ComponentTick=True
BPC_MedTankTurret_C.Weapon=True
BPC_MedTankTurret_C.Ballistics=True
BPC_MedTankTurret_C.Targeting=True
BPC_MedTankTurret_C.Physics=True
BPC_MedTankTurret_C.Turret=True
BPC_MedTankTurret_C.Collision=True
BPC_MedTankTurret_C.Multi-Barrel=True
BPC_MedTankTurret_C.Lighting=True
BPC_MedTankTurret_C.Barrel Sockets=True
BPC_MedTankTurret_C.Rotation=True
BPC_MedTankTurret_C.Rendering=True
BPC_MedTankTurret_C.Mesh Painting=True
BPC_MedTankTurret_C.HLOD=True
BPC_MedTankTurret_C.VirtualTexture=True
BPC_MedTankTurret_C.Tags=True
BPC_MedTankTurret_C.ComponentReplication=True
BPC_MedTankTurret_C.Cooking=True
BPC_MedTankTurret_C.Navigation=True
BPC_MedTankTurret_C.Variable=True
BPC_MedTankTurret_C.Materials=True
BPC_MedTankTurret_C.Sockets=True
BPC_MedTankTurret_C.Events=True
SceneComponent.Variable=True
SceneComponent.TransformCommon=True
SceneComponent.Sockets=True
SceneComponent.Rendering=True
SceneComponent.ComponentTick=True
SceneComponent.Tags=True
SceneComponent.ComponentReplication=True
SceneComponent.Activation=True
SceneComponent.Cooking=True
SceneComponent.Events=True
StaticMeshComponent.Materials=True
MedTankProjectile_C.TransformCommon=True
MedTankProjectile_C.StaticMesh=True
MedTankProjectile_C.Shape=True
MedTankProjectile_C.Tick=True
MedTankProjectile_C.HLOD=True
MedTankProjectile_C.ComponentTick=True
MedTankProjectile_C.Mobile=True
MedTankProjectile_C.Physics=True
MedTankProjectile_C.RayTracing=True
MedTankProjectile_C.Collision=True
MedTankProjectile_C.Lighting=True
MedTankProjectile_C.Tags=True
MedTankProjectile_C.Rendering=True
MedTankProjectile_C.Replication=True
MedTankProjectile_C.ComponentReplication=True
MedTankProjectile_C.Cooking=True
MedTankProjectile_C.Navigation=True
MedTankProjectile_C.AssetUserData=True
MedTankProjectile_C.Variable=True
MedTankProjectile_C.Mesh Painting=True
MedTankProjectile_C.VirtualTexture=True
MedTankProjectile_C.Projectile=True
MedTankProjectile_C.ProjectileBounces=True
MedTankProjectile_C.ProjectileSimulation=True
MedTankProjectile_C.Homing=True
MedTankProjectile_C.ProjectileInterpolation=True
MedTankProjectile_C.Velocity=True
MedTankProjectile_C.PlanarMovement=True
MedTankProjectile_C.MovementComponent=True
MedTankProjectile_C.Activation=True
MedTankProjectile_C.Ballistics=True
MedTankProjectile_C.Combat=True
MedTankProjectile_C.Guidance=True
MedTankProjectile_C.Actor=True
MedTankProjectile_C.Input=True
MedTankProjectile_C.Events=True
SphereComponent.Variable=True
SphereComponent.TransformCommon=True
SphereComponent.Sockets=True
SphereComponent.Shape=True
SphereComponent.HLOD=True
SphereComponent.ComponentTick=True
SphereComponent.Rendering=True
SphereComponent.Physics=True
SphereComponent.Collision=True
SphereComponent.Tags=True
SphereComponent.ComponentReplication=True
SphereComponent.Cooking=True
SphereComponent.Navigation=True
SphereComponent.Events=True
MedTankProjectile_C.Materials=True
BP_Demo_Camera_C.Default=True
BP_Demo_Camera_C.Tick=True
BP_Demo_Camera_C.Replication=True
BP_Demo_Camera_C.Rendering=True
BP_Demo_Camera_C.Actor=True
BP_Demo_Camera_C.HLOD=True
BP_Demo_Camera_C.Collision=True
BP_Demo_Camera_C.Input=True
BP_Demo_Camera_C.Physics=True
BP_Demo_Camera_C.Events=True
SpringArmComponent.Variable=True
SpringArmComponent.TransformCommon=True
SpringArmComponent.Sockets=True
SpringArmComponent.Camera=True
SpringArmComponent.CameraCollision=True
SpringArmComponent.CameraSettings=True
SpringArmComponent.ComponentTick=True
SpringArmComponent.Lag=True
SpringArmComponent.Rendering=True
SpringArmComponent.Tags=True
SpringArmComponent.ComponentReplication=True
SpringArmComponent.Activation=True
SpringArmComponent.Cooking=True
SpringArmComponent.Events=True
CameraComponent.Variable=True
CameraComponent.TransformCommon=True
CameraComponent.Sockets=True
CameraComponent.CameraSettings=True
CameraComponent.CameraOptions=True
CameraComponent.Camera=True
CameraComponent.ComponentTick=True
CameraComponent.PostProcess=True
CameraComponent.Tags=True
CameraComponent.ComponentReplication=True
CameraComponent.Activation=True
CameraComponent.Cooking=True
CameraComponent.Events=True
GM_Demo_GameMode_C.Tick=True
GM_Demo_GameMode_C.Classes=True
GM_Demo_GameMode_C.GameMode=True
GM_Demo_GameMode_C.Game=True
GM_Demo_GameMode_C.Physics=True
GM_Demo_GameMode_C.Events=True
PC_Demo_PlayerCntroller_C.Tick=True
PC_Demo_PlayerCntroller_C.Camera=True
PC_Demo_PlayerCntroller_C.PlayerController=True
PC_Demo_PlayerCntroller_C.Cheat Manager=True
PC_Demo_PlayerCntroller_C.MouseInterface=True
PC_Demo_PlayerCntroller_C.Game=True
PC_Demo_PlayerCntroller_C.Input=True
PC_Demo_PlayerCntroller_C.WorldPartition=True
PC_Demo_PlayerCntroller_C.Replication=True
PC_Demo_PlayerCntroller_C.Actor=True
PC_Demo_PlayerCntroller_C.HLOD=True
PC_Demo_PlayerCntroller_C.Controller=True
PC_Demo_PlayerCntroller_C.Physics=True
PC_Demo_PlayerCntroller_C.Events=True
K2Node_VariableGet.Variable=True
K2Node_VariableGet.DefaultValueCategory=True
K2Node_VariableGet.Events=True
ArmorWarsPlayerController_C.Default=True
K2Node_InputAxisKeyEvent.Input=True
AnimationAuthoringSettings.Interaction=True
AnimatorKitSettings.Animation Settings=True
EditorStyleSettings.Theme=True
EditorStyleSettings.UserInterface=True
EditorStyleSettings.Accessibility=True
EditorStyleSettings.Graphs=True
EditorStyleSettings.Text=True
AudioEditorSettings.AudioOutputDevice=True
AudioEditorSettings.NonGameWorld=True
AudioEditorSettings.AssetMenu=True
BlueprintEditorSettings.Workflow=True
BlueprintEditorSettings.VisualStyle=True
BlueprintEditorSettings.Compiler=True
BlueprintEditorSettings.DeveloperTools=True
BlueprintEditorSettings.FindInBlueprints=True
BlueprintEditorSettings.Play=True
CollectionSettings.Collections=True
EnhancedInputEditorSettings.Logging=True
EnhancedInputEditorSettings.Editor=True
EnhancedInputEditorSettings.Blueprints=True
EditorExperimentalSettings.Performance=True
EditorExperimentalSettings.HDR=True
EditorExperimentalSettings.Foliage=True
EditorExperimentalSettings.Tools=True
EditorExperimentalSettings.UserInterface=True
EditorExperimentalSettings.Blueprints=True
EditorExperimentalSettings.Cooking=True
EditorExperimentalSettings.PIE=True
EditorExperimentalSettings.LightingBuilds=True
EditorExperimentalSettings.Core=True
EditorExperimentalSettings.Materials=True
EditorExperimentalSettings.Content Browser=True
EditorExperimentalSettings.WorldPartition=True
EditorExperimentalSettings.LevelInstance=True
GameplayAbilitiesEditorDeveloperSettings.Debug=True
EditorSettings.DerivedDataCache=True
EditorSettings.Derived Data Cache Notifications=True
EditorSettings.Derived Data Cache S3=True
EditorSettings.Horde=True
InterchangeEditorSettings.Show Dialog=True
InterchangeEditorSettings.Group Used=True
EditorKeyboardShortcutSettings.ActorBrowsingModeCommands=True
EditorKeyboardShortcutSettings.AdvancedPreviewScene=True
EditorKeyboardShortcutSettings.AdvancedRenamer=True
EditorKeyboardShortcutSettings.AnimGraph=True
EditorKeyboardShortcutSettings.AnimSequenceCurveEditor=True
EditorKeyboardShortcutSettings.AnimViewportLODCmd=True
EditorKeyboardShortcutSettings.AnimViewportMenu=True
EditorKeyboardShortcutSettings.AnimViewportPlayback=True
EditorKeyboardShortcutSettings.AnimViewportShowCmd=True
EditorKeyboardShortcutSettings.AssetEditor=True
EditorKeyboardShortcutSettings.AssetManagerEditorCommands=True
EditorKeyboardShortcutSettings.BindWidget=True
EditorKeyboardShortcutSettings.BlueprintDebugger=True
EditorKeyboardShortcutSettings.BlueprintEditor=True
EditorKeyboardShortcutSettings.BlueprintEditorSpawnNodes=True
EditorKeyboardShortcutSettings.CameraAssetEditor=True
EditorKeyboardShortcutSettings.CameraRigAssetEditor=True
EditorKeyboardShortcutSettings.CameraRigTransitionEditor=True
EditorKeyboardShortcutSettings.CameraShakeAssetEditor=True
EditorKeyboardShortcutSettings.CameraShakePreviewer=True
EditorKeyboardShortcutSettings.CameraVariableCollectionEditor=True
EditorKeyboardShortcutSettings.ChaosCacheEditor=True
EditorKeyboardShortcutSettings.ChaosVDEditor=True
EditorKeyboardShortcutSettings.ClothPainterTools=True
EditorKeyboardShortcutSettings.ClothPainter=True
EditorKeyboardShortcutSettings.TakeRecorderSources=True
EditorKeyboardShortcutSettings.GenericCommands=True
EditorKeyboardShortcutSettings.EditorViewport=True
EditorKeyboardShortcutSettings.ContentBrowser=True
EditorKeyboardShortcutSettings.GenericCurveEditor=True
EditorKeyboardShortcutSettings.ToolbarPromotedCurveEditorFilters=True
EditorKeyboardShortcutSettings.CurveEditorTools=True
EditorKeyboardShortcutSettings.CurveViewer=True
EditorKeyboardShortcutSettings.DataHierarchyEditorCommands=True
EditorKeyboardShortcutSettings.DataflowEditor=True
EditorKeyboardShortcutSettings.DataflowEditorSkinWeightPaintToolContext=True
EditorKeyboardShortcutSettings.DataflowEditorWeightMapPaintToolContext=True
EditorKeyboardShortcutSettings.OptimusEditor=True
EditorKeyboardShortcutSettings.OptimusEditorGraph=True
EditorKeyboardShortcutSettings.OptimusShaderTextEditorDocumentTextBox=True
EditorKeyboardShortcutSettings.DerivedDataSettings=True
EditorKeyboardShortcutSettings.ColorGrading=True
EditorKeyboardShortcutSettings.TabCommands=True
EditorKeyboardShortcutSettings.BuilderCommandCreationManager=True
EditorKeyboardShortcutSettings.RigVMExecutionStack=True
EditorKeyboardShortcutSettings.RigVMEditorGraphExplorer=True
EditorKeyboardShortcutSettings.OptimusEditorGraphExplorer=True
EditorKeyboardShortcutSettings.FoliageEditMode=True
EditorKeyboardShortcutSettings.FractureEditor=True
EditorKeyboardShortcutSettings.FullBlueprintEditor=True
EditorKeyboardShortcutSettings.GameplayCameras_Debugger=True
EditorKeyboardShortcutSettings.GeometryCollectionSelection=True
EditorKeyboardShortcutSettings.GPUSkinCacheVisualizationMenu=True
EditorKeyboardShortcutSettings.GraphEditor=True
EditorKeyboardShortcutSettings.GroomVisualizationMenu=True
EditorKeyboardShortcutSettings.GroomEditorCommands=True
EditorKeyboardShortcutSettings.IKRetarget=True
EditorKeyboardShortcutSettings.IKRig=True
EditorKeyboardShortcutSettings.IKRigSkeleton=True
EditorKeyboardShortcutSettings.InsightsCommands=True
EditorKeyboardShortcutSettings.LoadingProfilerCommands=True
EditorKeyboardShortcutSettings.MemoryProfilerCommands=True
EditorKeyboardShortcutSettings.NetworkingProfilerCommands=True
EditorKeyboardShortcutSettings.TimingProfilerCommands=True
EditorKeyboardShortcutSettings.InsightsStatusBarWidgetCommands=True
EditorKeyboardShortcutSettings.LandscapeEditor=True
EditorKeyboardShortcutSettings.LayersView=True
EditorKeyboardShortcutSettings.LevelEditor=True
EditorKeyboardShortcutSettings.LevelEditorModes=True
EditorKeyboardShortcutSettings.LevelInstanceEditorMode=True
EditorKeyboardShortcutSettings.LevelSequenceEditor=True
EditorKeyboardShortcutSettings.LevelViewport=True
EditorKeyboardShortcutSettings.LightActor=True
EditorKeyboardShortcutSettings.LumenVisualizationMenu=True
EditorKeyboardShortcutSettings.MainFrame=True
EditorKeyboardShortcutSettings.MassDebugger=True
EditorKeyboardShortcutSettings.MaterialEditor=True
EditorKeyboardShortcutSettings.MediaPlateEditor=True
EditorKeyboardShortcutSettings.MediaPlayerEditor=True
EditorKeyboardShortcutSettings.MeshPainter=True
EditorKeyboardShortcutSettings.MeshPaint=True
EditorKeyboardShortcutSettings.MeshPaintingTools=True
EditorKeyboardShortcutSettings.ModelingModeCommands=True
EditorKeyboardShortcutSettings.ModelingToolsManagerCommands=True
EditorKeyboardShortcutSettings.ModelingToolsMeshAttributePaintTool=True
EditorKeyboardShortcutSettings.ModelingToolsCubeGridTool=True
EditorKeyboardShortcutSettings.ModelingToolsDrawPolygonTool=True
EditorKeyboardShortcutSettings.ModelingToolsDrawAndRevolveTool=True
EditorKeyboardShortcutSettings.ModelingToolsEditMeshMaterials=True
EditorKeyboardShortcutSettings.ModelingToolsEditMeshPolygonsTool=True
EditorKeyboardShortcutSettings.ModelingToolsMeshGroupPaintTool=True
EditorKeyboardShortcutSettings.ModelingToolsMeshPlaneCutTool=True
EditorKeyboardShortcutSettings.ModelingToolsMeshSelectionTool=True
EditorKeyboardShortcutSettings.ModelingToolsSculptTool=True
EditorKeyboardShortcutSettings.ModelingToolsEditMode=True
EditorKeyboardShortcutSettings.ModelingToolsTransformTool=True
EditorKeyboardShortcutSettings.ModelingToolsMeshVertexPaintTool=True
EditorKeyboardShortcutSettings.ModelingToolsVertexSculptTool=True
EditorKeyboardShortcutSettings.ControlRigModularRigModel=True
EditorKeyboardShortcutSettings.MyBlueprint=True
EditorKeyboardShortcutSettings.NaniteVisualizationMenu=True
EditorKeyboardShortcutSettings.NiagaraEditor=True
EditorKeyboardShortcutSettings.ObjectTreeGraphEditor=True
EditorKeyboardShortcutSettings.PCGEditor=True
EditorKeyboardShortcutSettings.PCGEditorSpawnNodes=True
EditorKeyboardShortcutSettings.PCGShaderTextEditorDocumentTextBox=True
EditorKeyboardShortcutSettings.PersonaCommon=True
EditorKeyboardShortcutSettings.PlayWorld=True
EditorKeyboardShortcutSettings.RayTracingDebugVisualizationMenu=True
EditorKeyboardShortcutSettings.VisualizationMenu=True
EditorKeyboardShortcutSettings.SourceControl=True
EditorKeyboardShortcutSettings.RewindDebugger=True
EditorKeyboardShortcutSettings.ControlRigEditMode=True
EditorKeyboardShortcutSettings.ControlRigBlueprint=True
EditorKeyboardShortcutSettings.ControlRigHierarchy=True
EditorKeyboardShortcutSettings.RigVMBlueprint=True
EditorKeyboardShortcutSettings.SampleToolsEditorMode=True
EditorKeyboardShortcutSettings.ScriptableToolsEditorModeToolCommands=True
EditorKeyboardShortcutSettings.SCSEditorViewport=True
EditorKeyboardShortcutSettings.SequenceNavigator=True
EditorKeyboardShortcutSettings.SequenceRecorder.Common=True
EditorKeyboardShortcutSettings.Sequencer=True
EditorKeyboardShortcutSettings.ShowFlagsMenu=True
EditorKeyboardShortcutSettings.SkeletalMeshEditor=True
EditorKeyboardShortcutSettings.SkeletalMeshModelingTools=True
EditorKeyboardShortcutSettings.SkeletalMeshModelingToolsSkeletonEditing=True
EditorKeyboardShortcutSettings.SkeletonEditor=True
EditorKeyboardShortcutSettings.SkeletonTree=True
EditorKeyboardShortcutSettings.SplineComponentVisualizer=True
EditorKeyboardShortcutSettings.StandardToolCommands=True
EditorKeyboardShortcutSettings.StateTreeEditor.Debugger=True
EditorKeyboardShortcutSettings.StateTreeEditor=True
EditorKeyboardShortcutSettings.MeshModelingTools=True
EditorKeyboardShortcutSettings.StaticMeshViewportLODCmd=True
EditorKeyboardShortcutSettings.StaticMeshEditor=True
EditorKeyboardShortcutSettings.SystemWideCommands=True
EditorKeyboardShortcutSettings.TakeRecorder=True
EditorKeyboardShortcutSettings.TG_Editor=True
EditorKeyboardShortcutSettings.TextureGraphExporter=True
EditorKeyboardShortcutSettings.TextureGraphInsightTab=True
EditorKeyboardShortcutSettings.TweeningUtils=True
EditorKeyboardShortcutSettings.WidgetDesigner=True
EditorKeyboardShortcutSettings.UVEditor=True
EditorKeyboardShortcutSettings.UVBrushSelect=True
EditorKeyboardShortcutSettings.FVariantManagerEditorCommands=True
EditorKeyboardShortcutSettings.EditorViewportClient=True
EditorKeyboardShortcutSettings.VirtualShadowMapVisualizationMenu=True
EditorKeyboardShortcutSettings.VirtualTextureVisualizationMenu=True
EditorKeyboardShortcutSettings.VisualLogger=True
EditorKeyboardShortcutSettings.WidgetPreviewEditor=True
EditorKeyboardShortcutSettings.WorldBookmark=True
EditorKeyboardShortcutSettings.ZenSettings=True
EditorKeyboardShortcutSettings.ZoneGraphEditorCommands=True
EditorKeyboardShortcutSettings.ZoneShapeComponentVisualizer=True
LiveCodingSettings.General=True
LiveCodingSettings.Modules=True
EditorLoadingSavingSettings.Startup=True
EditorLoadingSavingSettings.AutoReimport=True
EditorLoadingSavingSettings.Blueprints=True
EditorLoadingSavingSettings.AutoSave=True
EditorLoadingSavingSettings.SourceControl=True
EditorPerProjectUserSettings.DeveloperTools=True
EditorPerProjectUserSettings.AI=True
EditorPerProjectUserSettings.SimplygonSwarm=True
EditorPerProjectUserSettings.HotReload=True
EditorPerProjectUserSettings.Import=True
EditorPerProjectUserSettings.Export=True
EditorPerProjectUserSettings.Behavior=True
EditorPerProjectUserSettings.UnrealAutomationTool=True
OutputLogSettings.Output Log=True
EditorPerformanceSettings.EditorPerformanceTool=True
EditorPerformanceSettings.EditorPerformance=True
EditorPerformanceSettings.ViewportResolution=True
InternationalizationSettingsModel.Internationalization=True
InternationalizationSettingsModel.Time=True
SourceCodeAccessSettings.Accessor=True
StateTreeEditorUserSettings.State View=True
TextureEncodingUserSettings.EncodeSpeeds=True
TextureImportUserSettings.ImportSettings=True
TransformGizmoEditorSettings.Transform Gizmo=True
TransformGizmoEditorSettings.Experimental=True
VRModeSettings.General=True
VRModeSettings.Cinematics=True
VRModeSettings.World Movement=True
VRModeSettings.UI Customization=True
VRModeSettings.Motion Controllers=True
WorldBookmarkEditorPerProjectUserSettings.Default Level Bookmark=True
WorldBookmarkEditorPerProjectUserSettings.Home Bookmark=True
WorldPartitionEditorSettings.MapConversion=True
WorldPartitionEditorSettings.Foliage=True
WorldPartitionEditorSettings.MiniMap=True
WorldPartitionEditorSettings.WorldPartition=True
WorldPartitionEditorSettings.HLOD=True
WorldPartitionEditorPerProjectUserSettings.Default=True
WorldPartitionEditorPerProjectUserSettings.Data Layer=True
LevelEditorMiscSettings.Editing=True
LevelEditorMiscSettings.Sound=True
LevelEditorMiscSettings.Levels=True
LevelEditorMiscSettings.Screenshots=True
LevelEditorPlaySettings.PlayInEditor=True
LevelEditorPlaySettings.GameViewportSettings=True
LevelEditorPlaySettings.PlayInNewWindow=True
LevelEditorPlaySettings.PlayInStandaloneGame=True
LevelEditorPlaySettings.Multiplayer Options=True
LevelEditorPlaySettings.PlayOnDevice=True
OnlinePIESettings.Logins=True
LevelEditorViewportSettings.Controls=True
LevelEditorViewportSettings.LookAndFeel=True
LevelEditorViewportSettings.GridSnapping=True
LevelEditorViewportSettings.Preview=True
LevelEditorViewportSettings.Behavior=True
AnimGraphSettings.Workflow=True
AnimationBlueprintEditorSettings.Debugging=True
AnimationBlueprintEditorSettings.Graphs=True
PersonaOptions.Preview Scene=True
PersonaOptions.Assets=True
PersonaOptions.Viewport=True
PersonaOptions.Audio=True
PersonaOptions.Composites and Montages=True
PersonaOptions.Skeleton Tree=True
PersonaOptions.Mesh=True
PersonaOptions.Asset Browser=True
PersonaOptions.Timeline=True
ContentBrowserSettings.ContentBrowser=True
ContentBrowserSettings.Collections=True
ControlRigEditorSettings.Interaction=True
ControlRigEditorSettings.Compilation=True
ControlRigEditorSettings.NodeGraph=True
ControlRigEditorSettings.Viewport=True
ControlRigEditorSettings.Hierarchy=True
ControlRigEditorSettings.Outliner=True
ControlRigEditorSettings.Workflow=True
CurveEditorSettings.Curve Editor=True
SequencerSettings.Keyframing=True
SequencerSettings.General=True
SequencerSettings.Timeline=True
SequencerSettings.Snapping=True
SequencerSettings.CurveEditor=True
SequencerSettings.Playback=True
SequencerSettings.Filtering=True
FlipbookEditorSettings.Background=True
GraphEditorSettings.GeneralStyle=True
GraphEditorSettings.Splines=True
GraphEditorSettings.PinColors=True
GraphEditorSettings.NodeTitleColors=True
GraphEditorSettings.Tracing=True
GraphEditorSettings.ContextMenu=True
GraphEditorSettings.CommentNodes=True
LevelInstanceEditorPerProjectUserSettings.Create=True
LevelInstanceEditorPerProjectUserSettings.Pivot=True
LevelInstanceEditorPerProjectUserSettings.Selection=True
LevelInstanceEditorPerProjectUserSettings.Break=True
MaterialEditorSettings.Editor Defaults=True
MaterialEditorSettings.Context Menu=True
MaterialEditorSettings.Offline Shader Compilers=True
MaterialEditorSettings.User Interface Domain=True
MeshPaintSettings.Visualization=True
MetasoundEditorSettings.AssetMenu=True
MetasoundEditorSettings.Audition (Experimental)=True
MetasoundEditorSettings.General=True
MetasoundEditorSettings.PinColors=True
MetasoundEditorSettings.NodeTitleColors=True
MetasoundEditorSettings.Spectrogram=True
MetasoundEditorSettings.SpectrumAnalyzer=True
MetasoundEditorSettings.GraphAnimation=True
MetasoundEditorSettings.Widget Styling (Experimental)=True
PCGEditorSettings.Node=True
PCGEditorSettings.Wire=True
PCGEditorSettings.Workflow=True
PCGEditorSettings.Editor Performance=True
PCGEditorSettings.Content Browser=True
PCGEditorSettings.Graph UI=True
RigVMEditorSettings.Interaction=True
RigVMEditorSettings.Workflow=True
SkeletalMeshEditorSettings.AnimationPreview=True
SpriteEditorSettings.Background=True
TakeRecorderUserSettings.User Settings=True
TileMapEditorSettings.Background=True
TileMapEditorSettings.Grid=True
TileSetEditorSettings.Background=True
TileSetEditorSettings.Tile Editor=True
TileSetEditorSettings.Tile Sheet Conditioning=True
WidgetDesignerSettings.GridSnapping=True
WidgetDesignerSettings.Dragging=True
WidgetDesignerSettings.Visuals=True
WidgetDesignerSettings.Interaction=True
CrashReportsPrivacySettings.Options=True
AnalyticsPrivacySettings.Options=True
BlueprintHeaderViewSettings.Settings=True
CameraCalibrationEditorSettings.Settings=True
DataflowEditorOptions.UI=True
FractureModeCustomizationSettings.Fracture Mode=True
GameplayCamerasEditorSettings.NodeTitleColors=True
GameplayCamerasEditorSettings.Editor Preview=True
LightMixerEditorSettings.Light Mixer=True
ModelingToolsModeCustomizationSettings.Modeling Mode=True
ModelingComponentsEditorSettings.Modeling Tools=True
ObjectMixerEditorSettings.Object Mixer=True
PythonScriptPluginUserSettings.Python=True
RewindDebuggerSettings.Camera=True
RewindDebuggerSettings.Other=True
RewindDebuggerSettings.Filters=True
RewindDebuggerVLogSettings.VisualLogger=True
StateTreeEditorSettings.Compiler=True
StateTreeEditorSettings.Debugger=True
StateTreeEditorSettings.Experimental=True
VisualStudioSourceCodeAccessSettings.Visual Studio Source Code=True
NavigationToolSettings.Editor UX=True
NavigationToolSettings.Filtering=True
AutomationTestSettings.Loading=True
AutomationTestSettings.Automation=True
AutomationTestSettings.Open Asset Tests=True
AutomationTestSettings.PIE Test Maps=True
AutomationTestSettings.Play all project Maps In PIE=True
AutomationTestSettings.MiscAutomationSetups=True
AutomationTestSettings.ExternalTools=True
AutomationTestSettings.Screenshots=True
CrashReporterSettings.CrashReporter=True
DataValidationSettings.Data Validation=True
GameplayDebuggerUserSettings.GameplayDebugger=True
GameplayTagsDeveloperSettings.GameplayTags=True
EditorDataStorageSettings.MassSettings=True
LogVisualizerSettings.VisualLogger=True
GeneralProjectSettings.About=True
GeneralProjectSettings.Publisher=True
GeneralProjectSettings.Legal=True
GeneralProjectSettings.Displayed=True
GeneralProjectSettings.Settings=True
CryptoKeysSettings.Encryption=True
CryptoKeysSettings.Signing=True
GameplayEffectCreationMenu.Gameplay Effect=True
GameplayTagsSettings.GameplayTags=True
GameplayTagsSettings.Advanced Gameplay Tags=True
GameplayTagsSettings.Advanced Replication=True
GameMapsSettings.DefaultModes=True
GameMapsSettings.DefaultMaps=True
GameMapsSettings.LocalMultiplayer=True
GameMapsSettings.GameInstance=True
MoviePlayerSettings.Movies=True
ProjectPackagingSettings.CustomBuilds=True
ProjectPackagingSettings.Packaging=True
ProjectPackagingSettings.Project=True
ProjectPackagingSettings.Prerequisites=True
HardwareTargetingSettings.Target Hardware=True
HardwareTargetingSettings.Pending Changes=True
AssetManagerSettings.Asset Manager=True
AssetManagerSettings.Redirects=True
AssetReferencingPolicySettings.Engine Plugins=True
AssetReferencingPolicySettings.Project Plugins=True
AssetReferencingPolicySettings.Project Content=True
AssetReferencingPolicySettings.Settings=True
AssetToolsSettings.Advanced Copy=True
DataRegistrySettings.Data Registry=True
GameFeaturesSubsystemSettings.DefaultClasses=True
GameFeaturesSubsystemSettings.GameFeatures=True
GameplayAbilitiesDeveloperSettings.Gameplay=True
GameplayAbilitiesDeveloperSettings.Attribute=True
GameplayAbilitiesDeveloperSettings.GameplayCue=True
GameplayAbilitiesDeveloperSettings.GameplayEffects=True
GameplayAbilitiesDeveloperSettings.Advanced=True
SlateRHIRendererSettings.PostProcessing=True
ZenStreamingSettings.ZenStreaming=True
AISystem.AISystem=True
AISystem.Movement=True
AISystem.EQS=True
AISystem.Blackboard=True
AISystem.Behavior Tree=True
AISystem.PerceptionSystem=True
AnimationSettings.Compression=True
AnimationSettings.Performance=True
AnimationSettings.AnimationAttributes=True
AnimationSettings.Mirroring=True
AnimationSettings.AnimationData=True
AnimationModifierSettings.Modifiers=True
AudioSettings.Dialogue=True
AudioSettings.Audio=True
AudioSettings.Mix=True
AudioSettings.Occlusion=True
AudioSettings.Quality=True
AudioSettings.Debug=True
ChaosSolverSettings.GameInstance=True
CineCameraSettings.Lens=True
CineCameraSettings.Filmback=True
CineCameraSettings.Crop=True
CollisionProfile.Object Channels=True
CollisionProfile.Trace Channels=True
ConsoleSettings.General=True
ConsoleSettings.AutoComplete=True
ConsoleSettings.Colors=True
ControlRigSettings.Shapes=True
ControlRigSettings.ModularRigging=True
CookerSettings.Cooker=True
CookerSettings.Textures=True
CookerSettings.Editor=True
CQTestSettings.Test Settings=True
CrowdManager.Config=True
DataDrivenConsoleVariableSettings.DataDrivenCVar=True
DebugCameraControllerSettings.General=True
OptimusSettings.DeformerGraph=True
EnhancedInputDeveloperSettings.Enhanced Input=True
InputModifierSmoothDelta.Settings=True
InputModifierDeadZone.Settings=True
InputModifierResponseCurveExponential.Settings=True
InputModifierFOVScaling.Settings=True
EnhancedInputDeveloperSettings.Modifier Default Values=True
InputTriggerDown.Trigger Settings=True
InputTriggerPressed.Trigger Settings=True
InputTriggerReleased.Trigger Settings=True
InputTriggerHold.Trigger Settings=True
InputTriggerHoldAndRelease.Trigger Settings=True
InputTriggerTap.Trigger Settings=True
InputTriggerRepeatedTap.Trigger Settings=True
InputTriggerPulse.Trigger Settings=True
EnhancedInputDeveloperSettings.Trigger Default Values=True
EnhancedInputEditorProjectSettings.Default=True
MegascansMaterialParentSettings.Parent Materials=True
InterchangeFbxSettings.FBX=True
InterchangeFbxSettings.FBX=True
InterchangeFbxSettings.FBX=True
GameplayDebuggerConfig.Input=True
GameplayDebuggerConfig.Display=True
GameplayDebuggerConfig.AddOns=True
GarbageCollectionSettings.General=True
GarbageCollectionSettings.Optimization=True
GarbageCollectionSettings.Debug=True
Engine.Fonts=True
Engine.DefaultClasses=True
Engine.DefaultMaterials=True
Engine.Settings=True
Engine.Subtitles=True
Engine.Blueprints=True
Engine.Anim Blueprints=True
Engine.Framerate=True
Engine.Timecode=True
Engine.Screenshots=True
GLTFPipelineSettings.PredefinedglTFMaterialLibrary=True
HierarchicalLODSettings.HLODSystem=True
InputSettings.Bindings=True
InputSettings.Platforms=True
InputSettings.ViewportProperties=True
InputSettings.Input=True
InputSettings.Mobile=True
InputSettings.Virtual Keyboard (Mobile)=True
InputSettings.DefaultClasses=True
InputSettings.Console=True
InstancedActorsProjectSettings.Grid=True
InstancedActorsProjectSettings.ActorClassSettings=True
InterchangeProjectSettings.ImportContent=True
InterchangeProjectSettings.ImportIntoLevel=True
InterchangeProjectSettings.EditorInterface=True
InterchangeProjectSettings.Generic=True
InterchangeProjectSettings.Editor Generic Pipeline Class=True
InterchangeProjectSettings.Converters=True
InterchangeProjectSettings.Groups=True
LandscapeSettings.Edit Layers=True
LandscapeSettings.Layers=True
LandscapeSettings.Configuration=True
LandscapeSettings.Materials=True
LandscapeSettings.Target Layers=True
LandscapeSettings.HLOD=True
LandscapeSettings.Spline=True
LevelSequenceProjectSettings.Timeline=True
MassSettings.Mass=True
MaterialXPipelineSettings.MaterialXPredefined . Surface Shaders=True
MaterialXPipelineSettings.MaterialXPredefined . BSDF=True
MaterialXPipelineSettings.MaterialXPredefined . EDF=True
MaterialXPipelineSettings.MaterialXPredefined . VDF=True
MeshBudgetProjectSettings.StaticMesh=True
MeshDrawCommandStatsSettings.Engine=True
RecastNavMesh.HLOD=True
RecastNavMesh.Display=True
RecastNavMesh.Generation=True
RecastNavMesh.Query=True
RecastNavMesh.Runtime=True
RecastNavMesh.Tick=True
RecastNavMesh.Collision=True
RecastNavMesh.Physics=True
RecastNavMesh.Networking=True
NavigationSystemV1.Navigation=True
NavigationSystemV1.NavigationSystem=True
NavigationSystemV1.Navigation Enforcing=True
NavigationSystemV1.Agents=True
NetworkSettings.libcurl=True
NetworkSettings.World=True
PhysicsSettings.Replication=True
PhysicsSettings.Simulation=True
PhysicsSettings.Optimization=True
PhysicsSettings.Framerate=True
PhysicsSettings.Broadphase=True
PhysicsSettings.ChaosPhysics=True
PhysicsSettings.Constants=True
PhysicsSettings.Physical Surface=True
RendererSettings.Mobile=True
RendererSettings.Materials=True
RendererSettings.Culling=True
RendererSettings.Textures=True
RendererSettings.VirtualTextures=True
RendererSettings.MeshPaintVirtualTextures=True
RendererSettings.Runtime Virtual Textures=True
RendererSettings.WorkingColorSpace=True
RendererSettings.GlobalIllumination=True
RendererSettings.Reflections=True
RendererSettings.Lumen=True
RendererSettings.DirectLighting=True
RendererSettings.HardwareRayTracing=True
RendererSettings.SoftwareRayTracing=True
RendererSettings.Nanite=True
RendererSettings.MiscLighting=True
RendererSettings.ForwardRenderer=True
RendererSettings.Translucency=True
RendererSettings.VR=True
RendererSettings.Postprocessing=True
RendererSettings.DefaultSettings=True
RendererSettings.DefaultScreenPercentage=True
RendererSettings.Optimizations=True
RendererSettings.LightFunctionAtlas=True
RendererSettings.Debugging=True
RendererSettings.Mesh Streaming=True
RendererSettings.Heterogeneous Volumes=True
RendererSettings.Editor=True
RendererSettings.ShaderPermutationReduction=True
RendererSettings.Substrate=True
RendererSettings.HairStrands=True
RendererSettings.MobileShaderPermutationReduction=True
RendererSettings.Skinning=True
RendererSettings.PostProcessCalibrationMaterials=True
RendererOverrideSettings.ShaderPermutationReduction=True
SlateSettings.ConstraintCanvas=True
SmartObjectSettings.SmartObject=True
StateTreeSettings.StateTree=True
StreamingSettings.PackageStreaming=True
StreamingSettings.LevelStreaming=True
StreamingSettings.General=True
StreamingSettings.Deprecated Settings=True
TextureEncodingProjectSettings.EncodeSettings=True
TextureEncodingProjectSettings.EncodeSpeedSettings=True
TextureEncodingProjectSettings.EncodeSpeeds=True
UserInterfaceSettings.Focus=True
UserInterfaceSettings.Hardware Cursors=True
UserInterfaceSettings.Software Cursors=True
UserInterfaceSettings.DPI Scaling=True
UserInterfaceSettings.Widgets=True
UserInterfaceSettings.UMG Fonts=True
VirtualTexturePoolConfig.PoolConfig=True
WorldPartitionSettings.WorldPartition=True
ZoneGraphSettings.ZoneGraph=True
LevelEditor2DSettings.General=True
LevelEditor2DSettings.LayerSnapping=True
EditorProjectAppearanceSettings.Units=True
EditorProjectAppearanceSettings.ReferenceViewer=True
EditorProjectAssetSettings.Redirectors=True
EditorProjectAssetSettings.Internationalization=True
BlueprintEditorProjectSettings.Blueprints=True
BlueprintEditorProjectSettings.Actors=True
BlueprintEditorProjectSettings.Experimental=True
BlueprintEditorProjectSettings.Play=True
ClassViewerProjectSettings.ClassVisibilityManagement=True
ContentBrowserCollectionProjectSettings.Collections=True
DDCProjectSettings.Warnings=True
EditorUtilityWidgetProjectSettings.Designer=True
EditorUtilityWidgetProjectSettings.Compiler=True
EditorUtilityWidgetProjectSettings.Class Filtering=True
EditorUtilityWidgetProjectSettings.Class Settings=True
GameFeaturesEditorSettings.Plugins=True
ProxyLODMeshSimplificationSettings.General=True
LevelEditorProjectSettings.Editing=True
LevelInstanceEditorSettings.World Partition=True
MovieSceneToolsProjectSettings.Timeline=True
MovieSceneToolsProjectSettings.Shots=True
MovieSceneToolsProjectSettings.TrackSettings=True
MeshSimplificationSettings.General=True
PaperImporterSettings.NewAssetSettings=True
PaperImporterSettings.ImportSettings=True
PaperImporterSettings.MaterialSettings=True
PCGEditorProjectSettings.Builder=True
PCGEditorProjectSettings.Workflow=True
EditorPerformanceProjectSettings.ViewportResolution=True
RenderResourceViewerSettings.Treemap=True
SourceControlPreferences.SourceControl=True
SourceControlPreferences.Internationalization=True
RigVMProjectSettings.Variants=True
SkeletalMeshSimplificationSettings.General=True
PlasticSourceControlProjectSettings.Unity Version Control=True
StructViewerProjectSettings.StructVisibilityManagement=True
TextureImportSettings.VirtualTextures=True
TextureImportSettings.ImportSettings=True
UMGEditorProjectSettings.Compiler=True
UMGEditorProjectSettings.Class Filtering=True
UMGEditorProjectSettings.Designer=True
UMGEditorProjectSettings.Class Settings=True
WorldBookmarkEditorSettings.Bookmark Categories=True
AndroidRuntimeSettings.APK Packaging=True
AndroidRuntimeSettings.App Bundles=True
AndroidRuntimeSettings.Build=True
AndroidRuntimeSettings.Advanced APK Packaging=True
AndroidRuntimeSettings.DistributionSigning=True
AndroidRuntimeSettings.GooglePlayServices=True
AndroidRuntimeSettings.Icons=True
AndroidRuntimeSettings.LaunchImages=True
AndroidRuntimeSettings.Input=True
AndroidRuntimeSettings.GraphicsDebugger=True
AndroidRuntimeSettings.Audio=True
AndroidRuntimeSettings.MultiTextureFormats=True
AndroidRuntimeSettings.TextureFormatPriorities=True
AndroidRuntimeSettings.Misc=True
ShaderPlatformQualitySettings.Forward Rendering Overrides=True
AndroidSDKSettings.SDKConfig=True
IOSRuntimeSettings.Mobile Provision=True
IOSRuntimeSettings.BundleInformation=True
IOSRuntimeSettings.PowerUsage=True
IOSRuntimeSettings.Orientation=True
IOSRuntimeSettings.FileSystem=True
IOSRuntimeSettings.Input=True
IOSRuntimeSettings.Rendering=True
IOSRuntimeSettings.Build=True
IOSRuntimeSettings.Online=True
IOSRuntimeSettings.RequiredIOSIcons=True
IOSRuntimeSettings.OptionalIOSIcons=True
IOSRuntimeSettings.RequiredTVOSAssets=True
IOSRuntimeSettings.OptionalTVOSAssets=True
IOSRuntimeSettings.LaunchScreen=True
IOSRuntimeSettings.Remote Build=True
IOSRuntimeSettings.Audio=True
LinuxTargetSettings.Targeted RHIs=True
LinuxTargetSettings.Splash=True
LinuxTargetSettings.Icon=True
LinuxTargetSettings.Audio=True
LinuxTargetSettings.Renderer=True
MacTargetSettings.Targeted RHIs=True
MacTargetSettings.Rendering=True
MacTargetSettings.Packaging=True
MacTargetSettings.Splash=True
MacTargetSettings.Icon=True
MacTargetSettings.Audio=True
WindowsTargetSettings.D3D12 Targeted Shader Formats=True
WindowsTargetSettings.D3D11 Targeted Shader Formats=True
WindowsTargetSettings.Vulkan Targeted Shader Formats=True
WindowsTargetSettings.Targeted RHIs=True
WindowsTargetSettings.Renderer=True
WindowsTargetSettings.Splash=True
WindowsTargetSettings.Toolchain=True
WindowsTargetSettings.Icon=True
WindowsTargetSettings.Audio=True
XcodeProjectSettings.Xcode=True
XcodeProjectSettings.Plist Files=True
XcodeProjectSettings.Entitlements=True
XcodeProjectSettings.Code Signing=True
XcodeProjectSettings.Privacy Manifests=True
AndroidFileServerRuntimeSettings.Packaging=True
AndroidFileServerRuntimeSettings.Deployment=True
AndroidFileServerRuntimeSettings.Connection=True
AvfMediaSettings.Debug=True
CameraCalibrationSettings.Settings=True
CameraCalibrationSettings.Overlays=True
CompositeCorePluginSettings.CompositeCore=True
DataflowSettings.NodeColors=True
DataflowSettings.PinSettings=True
DataflowSettings.TransformLevelColors=True
FractureModeSettings.Fracture Mode=True
GameplayCamerasSettings.General=True
GameplayCamerasSettings.IK Aiming=True
GeometryCacheStreamerSettings.Geometry Cache Streamer=True
GooglePADRuntimeSettings.Packaging=True
GroomPluginSettings.GroomCache=True
ImgMediaSettings.General=True
ImgMediaSettings.Caching=True
ImgMediaSettings.EXR=True
ImgMediaSettings.Proxies=True
ToolPresetProjectSettings.Interactive Tool Presets=True
LevelSequenceEditorSettings.Tracks=True
LevelSequenceEditorSettings.Playback=True
MetaHumanSDKSettings.MetaHuman Import Paths=True
MetaHumanSDKSettings.MetaHuman Packaging Paths=True
MetaSoundSettings.AutoUpdate=True
MetaSoundSettings.Registration=True
MetaSoundSettings.Pages (Experimental)=True
MetaSoundSettings.Quality=True
ModelingToolsEditorModeSettings.Modeling Mode=True
ModelingComponentsSettings.Modeling Tools=True
CustomizableObjectEditorSettings.Compilation=True
CustomizableObjectEditorSettings.AutomaticCompilation=True
CustomizableObjectEditorSettings.DerivedDataCache=True
MutableValidationSettings.Validation=True
NiagaraSettings.Niagara=True
NiagaraSettings.Viewport=True
NiagaraSettings.SimulationCaching=True
NiagaraSettings.Scalability=True
NiagaraSettings.Renderer=True
NiagaraSettings.LightRenderer=True
NiagaraSettings.SkeletalMeshDI=True
NiagaraSettings.StaticMeshDI=True
NiagaraSettings.AsyncGpuTraceDI=True
NiagaraSettings.SimCache=True
NiagaraEditorSettings.Niagara=True
NiagaraEditorSettings.Niagara Renderer=True
NiagaraEditorSettings.Niagara Graph=True
NiagaraEditorSettings.SimulationOptions=True
NiagaraEditorSettings.Niagara Colors=True
NNEDenoiserSettings.NNE Denoiser=True
NNERuntimeORTSettings.ONNX Runtime=True
PaperRuntimeSettings.Experimental=True
PaperRuntimeSettings.Settings=True
PCGEngineSettings.Workflow=True
PCGEngineSettings.Tracking=True
PythonScriptPluginSettings.Python=True
PythonScriptPluginSettings.PythonPipInstall=True
PythonScriptPluginSettings.PythonRemoteExecution=True
RenderDocPluginSettings.Frame Capture Settings=True
RenderDocPluginSettings.Advanced Settings=True
ResonanceAudioSettings.Reverb=True
ResonanceAudioSettings.General=True
ScriptableToolsModeCustomizationSettings.Scriptable Tools Mode=True
TakeRecorderProjectSettings.Take Recorder=True
TakeRecorderProjectSettings.Movie Scene Take Settings=True
TakeRecorderProjectSettings.Microphone Audio Recorder=True
TakeRecorderProjectSettings.Audio Input Device=True
TakeRecorderProjectSettings.Animation Recorder=True
TakeRecorderProjectSettings.World Recorder=True
TcpMessagingSettings.Transport=True
UdpMessagingSettings.Availability=True
UdpMessagingSettings.Transport=True
UdpMessagingSettings.Tunnel=True
WmfMediaSettings.Media=True
WmfMediaSettings.Debug=True
K2Node_CustomEvent.GraphNodeDetail=True
K2Node_CustomEvent.Graph=True
K2Node_CustomEvent.Inputs=True
K2Node_CallFunction.Graph=True
ArmorWarsHud_C.Selection=True
ArmorWarsHud_C.Tick=True
ArmorWarsHud_C.Display=True
ArmorWarsHud_C.UI=True
ArmorWarsHud_C.Debug=True
ArmorWarsHud_C.HUD=True
ArmorWarsHud_C.HLOD=True
ArmorWarsHud_C.Collision=True
ArmorWarsHud_C.Physics=True
ArmorWarsHud_C.Events=True
K2Node_InputKey.Input=True
K2Node_InputKey.Modifier=True
K2Node_VariableSet.Variable=True
K2Node_VariableSet.DefaultValueCategory=True
K2Node_VariableSet.Events=True
InputMappingContext.Mappings=True
InputMappingContext.Input Modes=True
InputMappingContext.Registration=True
InputMappingContext.Description=True
InputAction.Description=True
InputAction.Action=True
InputAction.Input Consumption=True
InputAction.User Settings=True
ArmorWarsHUDWidget_C.Appearance=True
ArmorWarsHUDWidget_C.Input=True
ArmorWarsHUDWidget_C.Accessibility=True
ArmorWarsHUDWidget_C.Interaction=True
ArmorWarsHUDWidget_C.Performance=True
ArmorWarsHUDWidget_C.Behavior=True
ArmorWarsHUDWidget_C.Render Transform=True
ArmorWarsHUDWidget_C.Rendering=True
ArmorWarsHUDWidget_C.Navigation=True
ArmorWarsHUDWidget_C.Designer=True
ArmorWarsHUDWidget_C.Localization=True
ArmorWarsHUDWidget_C.Events=True
ArmorWarsHud_C.Networking=True
ArmorWarsHud_C.TransformCommon=True
BP_MediumTank_C.Networking=True
BP_MediumTank_C.Materials=False
RTSUnitAIComponent.Variable=True
RTSUnitAIComponent.Sockets=True
RTSUnitAIComponent.AI Behavior=True
RTSUnitAIComponent.Special Abilities=True
RTSUnitAIComponent.Combat=True
RTSUnitAIComponent.ComponentTick=True
RTSUnitAIComponent.Tags=True
RTSUnitAIComponent.ComponentReplication=True
RTSUnitAIComponent.Activation=True
RTSUnitAIComponent.Cooking=True
RTSUnitAIComponent.Events=True
RTSGameState.Debug=True
RTSGameState.Replication=True
RTSGameState.Physics=True
RTSGameState.Networking=True
RTSGameState.Actor=True
ArmorWarsGameState_C.Tick=True
ArmorWarsGameState_C.Debug=True
ArmorWarsGameState_C.Replication=False
ArmorWarsGameState_C.Actor=True
ArmorWarsGameState_C.GameState=True
ArmorWarsGameState_C.Physics=True
ArmorWarsGameState_C.Events=True
BP_Demo_Camera_C.TransformCommon=True
BP_Demo_Camera_C.Networking=True
ArmorWarsPlayerController_C.TransformCommon=True
ArmorWarsPlayerController_C.Networking=True
LandscapeStreamingProxy.TransformCommon=True
LandscapeStreamingProxy.Information=True
LandscapeStreamingProxy.VirtualTexture=True
LandscapeStreamingProxy.Rendering=True
LandscapeStreamingProxy.Nanite=True
LandscapeStreamingProxy.LandscapeProxy=True
LandscapeStreamingProxy.Landscape=True
LandscapeStreamingProxy.LOD=True
LandscapeStreamingProxy.LOD Distribution=True
LandscapeStreamingProxy.Lighting=True
LandscapeStreamingProxy.Lightmass=True
LandscapeStreamingProxy.Collision=True
LandscapeStreamingProxy.Navigation=True
LandscapeStreamingProxy.HLOD=True
LandscapeStreamingProxy.Target Layers=True
LandscapeStreamingProxy.Replication=True
LandscapeStreamingProxy.Networking=True
LandscapeStreamingProxy.Input=True
LandscapeStreamingProxy.Actor=True
StaticMeshActor.TransformCommon=True
StaticMeshActor.StaticMesh=True
StaticMeshActor.Materials=True
StaticMeshActor.Physics=True
StaticMeshActor.Collision=True
StaticMeshActor.Lighting=True
StaticMeshActor.Mesh Painting=True
StaticMeshActor.Rendering=True
StaticMeshActor.HLOD=True
StaticMeshActor.VirtualTexture=True
StaticMeshActor.Tags=True
StaticMeshActor.Cooking=True
StaticMeshActor.Navigation=True
StaticMeshActor.Replication=True
StaticMeshActor.Networking=True
StaticMeshActor.Actor=True
Blueprint.Thumbnail=True
EdGraphNode_Comment.GraphNodeDetail=True
EdGraphNode_Comment.Comment=True
ArmorWarsCameraPawn_C.TransformCommon=True
ArmorWarsCameraPawn_C.StaticMesh=True
ArmorWarsCameraPawn_C.CameraSettings=True
ArmorWarsCameraPawn_C.CameraOptions=True
ArmorWarsCameraPawn_C.Tick=True
ArmorWarsCameraPawn_C.ComponentTick=True
ArmorWarsCameraPawn_C.LOD=True
ArmorWarsCameraPawn_C.Tags=True
ArmorWarsCameraPawn_C.Physics=True
ArmorWarsCameraPawn_C.Replication=True
ArmorWarsCameraPawn_C.Collision=True
ArmorWarsCameraPawn_C.Lighting=True
ArmorWarsCameraPawn_C.ComponentReplication=True
ArmorWarsCameraPawn_C.Activation=True
ArmorWarsCameraPawn_C.Cooking=True
ArmorWarsCameraPawn_C.Rendering=True
ArmorWarsCameraPawn_C.AssetUserData=True
ArmorWarsCameraPawn_C.Variable=True
ArmorWarsCameraPawn_C.Navigation=True
ArmorWarsCameraPawn_C.Camera=True
ArmorWarsCameraPawn_C.CameraCollision=True
ArmorWarsCameraPawn_C.Lag=True
ArmorWarsCameraPawn_C.PostProcess=True
ArmorWarsCameraPawn_C.Mesh Painting=True
ArmorWarsCameraPawn_C.HLOD=True
ArmorWarsCameraPawn_C.VirtualTexture=True
ArmorWarsCameraPawn_C.Camera Settings=True
ArmorWarsCameraPawn_C.Debug=True
ArmorWarsCameraPawn_C.Pawn=True
ArmorWarsCameraPawn_C.Actor=True
ArmorWarsCameraPawn_C.Input=True
ArmorWarsCameraPawn_C.Events=True
ArmorWarsPlayerController_C.Selection=True
ArmorWarsPlayerController_C.Enhanced Input=True
Landscape.TransformCommon=True
Landscape.Information=True
Landscape.VirtualTexture=True
Landscape.Rendering=True
Landscape.Landscape=True
Landscape.Nanite=True
Landscape.WorldPartition=True
Landscape.LOD=True
Landscape.LOD Distribution=True
Landscape.Lighting=True
Landscape.Lightmass=True
Landscape.Collision=True
Landscape.Navigation=True
Landscape.HLOD=True
Landscape.Target Layers=True
Landscape.Replication=True
Landscape.Networking=True
Landscape.Input=True
Landscape.Actor=True
BP_RTSPawn_C.TransformCommon=True
BP_RTSPawn_C.StaticMesh=True
BP_RTSPawn_C.CameraSettings=True
BP_RTSPawn_C.CameraOptions=True
BP_RTSPawn_C.Physics=True
BP_RTSPawn_C.Collision=True
BP_RTSPawn_C.LOD=True
BP_RTSPawn_C.Lighting=True
BP_RTSPawn_C.Tags=True
BP_RTSPawn_C.Activation=True
BP_RTSPawn_C.Cooking=True
BP_RTSPawn_C.Rendering=True
BP_RTSPawn_C.AssetUserData=True
BP_RTSPawn_C.Navigation=True
BP_RTSPawn_C.Camera=True
BP_RTSPawn_C.CameraCollision=True
BP_RTSPawn_C.Lag=True
BP_RTSPawn_C.PostProcess=True
BP_RTSPawn_C.Mesh Painting=True
BP_RTSPawn_C.HLOD=True
BP_RTSPawn_C.VirtualTexture=True
BP_RTSPawn_C.Camera Settings=True
BP_RTSPawn_C.Debug=True
BP_RTSPawn_C.Pawn=True
BP_RTSPawn_C.Replication=True
BP_RTSPawn_C.Networking=True
BP_RTSPawn_C.Input=True
BP_RTSPawn_C.Actor=True
BP_RTSPawn_C.Tick=True
BP_RTSPawn_C.ComponentTick=True
BP_RTSPawn_C.ComponentReplication=True
BP_RTSPawn_C.Variable=True
BP_RTSPawn_C.Events=True
DecalComponent.Variable=True
DecalComponent.TransformCommon=True
DecalComponent.Sockets=True
DecalComponent.Decal=True
DecalComponent.Rendering=True
DecalComponent.ComponentTick=True
DecalComponent.Tags=True
DecalComponent.ComponentReplication=True
DecalComponent.Cooking=True
DecalComponent.Events=True
ArrowComponent.Variable=True
ArrowComponent.TransformCommon=True
ArrowComponent.Sockets=True
ArrowComponent.ArrowComponent=True
ArrowComponent.HLOD=True
ArrowComponent.Rendering=True
ArrowComponent.ComponentTick=True
ArrowComponent.Tags=True
ArrowComponent.ComponentReplication=True
ArrowComponent.Cooking=True
ArrowComponent.Navigation=True
ArrowComponent.Events=True
MedTankAIcontroller_C.Tick=True
MedTankAIcontroller_C.AI Settings=True
MedTankAIcontroller_C.Debug=True
MedTankAIcontroller_C.ComponentTick=True
MedTankAIcontroller_C.AI=True
MedTankAIcontroller_C.Tags=True
MedTankAIcontroller_C.Replication=True
MedTankAIcontroller_C.ComponentReplication=True
MedTankAIcontroller_C.Activation=True
MedTankAIcontroller_C.Cooking=True
MedTankAIcontroller_C.Variable=True
MedTankAIcontroller_C.Actor=True
MedTankAIcontroller_C.HLOD=True
MedTankAIcontroller_C.Controller=True
MedTankAIcontroller_C.Input=True
MedTankAIcontroller_C.Physics=True
MedTankAIcontroller_C.Events=True
PathFollowingComponent.Variable=True
PathFollowingComponent.Sockets=True
PathFollowingComponent.Tags=True
PathFollowingComponent.ComponentReplication=True
PathFollowingComponent.ComponentTick=True
PathFollowingComponent.Activation=True
PathFollowingComponent.Cooking=True
PathFollowingComponent.Events=True
UserDefinedStruct.Structure=True
TeamColors.None=True
BP_MediumTank_C.Land Movement=True
BP_MediumTank_C.AI Movement=True
BP_MediumTank_C.Debug=True
BP_MediumTank_C.Pawn=True
BP_MediumTank_C.Camera=True
RTSInterfaceComponent.Variable=True
RTSInterfaceComponent.Sockets=True
RTSInterfaceComponent.Debug=True
RTSInterfaceComponent.Tags=True
RTSInterfaceComponent.ComponentReplication=True
RTSInterfaceComponent.ComponentTick=True
RTSInterfaceComponent.Activation=True
RTSInterfaceComponent.Cooking=True
RTSInterfaceComponent.Events=True
RTSInterfaceComponent.Replication=False
BP_MediumTank_C.RayTracing=True
BP_MediumTank_C.Mobile=True
BP_MediumTank_C.Shape=True
CapsuleComponent.Variable=True
CapsuleComponent.TransformCommon=True
CapsuleComponent.Sockets=True
CapsuleComponent.Shape=True
CapsuleComponent.HLOD=True
CapsuleComponent.ComponentTick=True
CapsuleComponent.Rendering=True
CapsuleComponent.Physics=True
CapsuleComponent.Collision=True
CapsuleComponent.Tags=True
CapsuleComponent.ComponentReplication=True
CapsuleComponent.Cooking=True
CapsuleComponent.Navigation=True
CapsuleComponent.Events=True
RTSLandMovementComponent.NavMovement=True
RTSLandMovementComponent.Cooking=True
RTSLandMovementComponent.Activation=True
RTSLandMovementComponent.ComponentReplication=True
RTSLandMovementComponent.Tags=True
RTSLandMovementComponent.MovementComponent=True
RTSLandMovementComponent.PlanarMovement=True
RTSLandMovementComponent.Velocity=True
RTSLandMovementComponent.FloatingPawnMovement=True
RTSLandMovementComponent.ComponentTick=True
RTSLandMovementComponent.Debug=True
RTSLandMovementComponent.AI Movement=True
RTSLandMovementComponent.Land Movement=True
RTSLandMovementComponent.Sockets=True
RTSLandMovementComponent.Variable=True
RTSLandMovementComponent.Events=True
WB_BuildingsSelector_C.Layout=True
WB_BuildingsSelector_C.Default=True
WB_BuildingsSelector_C.Appearance=True
WB_BuildingsSelector_C.Accessibility=True
WB_BuildingsSelector_C.Input=True
WB_BuildingsSelector_C.Interaction=True
WB_BuildingsSelector_C.Performance=True
WB_BuildingsSelector_C.Behavior=True
WB_BuildingsSelector_C.Render Transform=True
WB_BuildingsSelector_C.Rendering=True
WB_BuildingsSelector_C.Navigation=True
WB_BuildingsSelector_C.Localization=True
WB_BuildingsSelector_C.Events=True
CustomizableObject.CustomizableObject=True
CustomizableObject.States=True
CustomizableObject.Versioning=True
CustomizableObject.CompileOptions=True
CustomizableInstancePrivate.NoCategory=True
CustomizableInstancePrivate.Animation=True
CustomizableObjectInstance.ParametersVisibility=True
CustomizableObjectInstance.Instance Parameters=True
CustomizableObjectInstance.TextureParameter=True
CustomizableObjectNodeObjectChild.CustomizableObject=True
CustomizableObjectNodeObjectChild.UI=True
CustomizableObjectNodeObjectChild.RealTimeMorphTargets=True
CustomizableObjectNodeObject.CustomizableObject=False
CustomizableObjectNodeObject.AttachedToExternalObject=True
CustomizableObjectNodeObject.UI=True
CustomizableObjectNodeObject.RealTimeMorphTargets=True
BlackboardData.Parent=True
BlackboardKeyType_Object.Blackboard=True
BlackboardData.Key=True
BehaviorTreeGraphNode_Root.AI=True
BTComposite_Sequence.Composite=True
BTComposite_Sequence.Description=True

[Interchange_StackName__Assets__PipelinePathHash_3978065961]
PipelineDisplayName=Default Assets Pipeline
ReimportStrategy=ApplyNoProperties
bUseSourceNameForAsset=True
bSceneNameSubFolder=False
bAssetTypeSubFolders=False
AssetName=
ImportOffsetTranslation=(X=0.000000,Y=0.000000,Z=0.000000)
ImportOffsetRotation=(Pitch=0.000000,Yaw=0.000000,Roll=0.000000)
ImportOffsetUniformScale=0.100000

[Interchange_StackName__Assets__PipelinePathHash_3978065961__SubPipelineClassName__InterchangeGenericCommonMeshesProperties]
ForceAllMeshAsType=IFMT_StaticMesh
bAutoDetectMeshType=True
bImportLods=True
bBakeMeshes=True
bBakePivotMeshes=False
bKeepSectionsSeparate=False
VertexColorImportOption=IVCIO_Replace
VertexOverrideColor=(B=0,G=0,R=0,A=0)
bImportSockets=True
bRecomputeNormals=False
bRecomputeTangents=False
bUseMikkTSpace=True
bComputeWeightedNormals=True
bUseHighPrecisionTangentBasis=False
bUseFullPrecisionUVs=False
bUseBackwardsCompatibleF16TruncUVs=False
bRemoveDegenerates=False

[Interchange_StackName__Assets__PipelinePathHash_3978065961__SubPipelineClassName__InterchangeGenericCommonSkeletalMeshesAndAnimationsProperties]
bImportOnlyAnimations=False
Skeleton=None
bImportMeshesInBoneHierarchy=True
bUseT0AsRefPose=False
bAddCurveMetadataToSkeleton=True
bConvertStaticsWithMorphTargetsToSkeletals=False

[Interchange_StackName__Assets__PipelinePathHash_3978065961__SubPipelineClassName__InterchangeGenericMeshPipeline]
bImportStaticMeshes=True
bCombineStaticMeshes=False
LodGroup=None
bAutoComputeLODScreenSizes=True
bCollision=True
bImportCollisionAccordingToMeshName=True
bOneConvexHullPerUCX=True
Collision=Convex18DOP
bForceCollisionPrimitiveGeneration=False
bBuildNanite=True
bBuildReversedIndexBuffer=False
bGenerateLightmapUVs=False
bGenerateDistanceFieldAsIfTwoSided=False
bSupportFaceRemap=False
MinLightmapResolution=64
SrcLightmapIndex=0
DstLightmapIndex=1
BuildScale3D=(X=1.000000,Y=1.000000,Z=1.000000)
DistanceFieldResolutionScale=1.000000
DistanceFieldReplacementMesh=None
MaxLumenMeshCards=12
bImportSkeletalMeshes=True
SkeletalMeshImportContentType=All
bImportMorphTargets=True
bMergeMorphTargetsWithSameName=True
bImportVertexAttributes=False
bUpdateSkeletonReferencePose=False
bCreatePhysicsAsset=True
PhysicsAsset=None
bUseHighPrecisionSkinWeights=False
ThresholdPosition=0.000020
ThresholdTangentNormal=0.000020
ThresholdUV=0.000977
MorphThresholdPosition=0.015000
BoneInfluenceLimit=0
bImportGeometryCaches=False
bFlattenTracks=True
CompressedPositionPrecision=0.010000
CompressedTextureCoordinatesNumberOfBits=10
bOverrideTimeRange=False
FrameStart=0
FrameEnd=1
MotionVectors=NoMotionVectors
bApplyConstantTopologyOptimizations=False
bStoreImportedVertexNumbers=False
bOptimizeIndexBuffers=False

[Interchange_StackName__Assets__PipelinePathHash_3978065961__SubPipelineClassName__InterchangeGenericAnimationPipeline]
bImportAnimations=True
bImportBoneTracks=True
AnimationRange=Timeline
FrameImportRange=(Min=0,Max=0)
bUse30HzToBakeBoneAnimation=False
CustomBoneAnimationSampleRate=0
bSnapToClosestFrameBoundary=False
bImportCustomAttribute=True
bSetMaterialDriveParameterOnCustomAttribute=False
MaterialCurveSuffixes=_mat
bRemoveCurveRedundantKeys=True
bDoNotImportCurveWithZero=True
bDeleteExistingNonCurveCustomAttributes=False
bDeleteExistingCustomAttributeCurves=False
bDeleteExistingMorphTargetCurves=False

[Interchange_StackName__Assets__PipelinePathHash_3978065961__SubPipelineClassName__InterchangeGenericMaterialPipeline]
PipelineDisplayName=
bImportMaterials=True
SearchLocation=AllAssets
AssetName=
MaterialImport=ImportAsMaterialInstances
bIdentifyDuplicateMaterials=True
bCreateMaterialInstanceForParent=False
ParentMaterial=None
bOverrideDisplacement=False
OverrideDisplacementCenter=0.500000

[Interchange_StackName__Assets__PipelinePathHash_3978065961__SubPipelineClassName__InterchangeGenericTexturePipeline]
PipelineDisplayName=
bImportTextures=True
AssetName=
bDetectNormalMapTexture=True
bFlipNormalMapGreenChannel=False
bImportUDIMs=True
FileExtensionsToImportAsLongLatCubemap=("hdr")
bPreferCompressedSourceData=False
bAllowNonPowerOfTwo=True

[Interchange_StackName__Assets__PipelinePathHash_3978065961__SubPipelineClassName__InterchangeSparseVolumeTexturePipeline]
PipelineDisplayName=
bImportSparseVolumeTextures=True
bImportAnimatedSparseVolumeTextures=True
AssetName=

[InterchangeSelectPipeline]
Assets_LastSelectedPipeline=InterchangeGenericAssetsPipeline

[InterchangeImportDialogOptions]
ImportContentDialogSizeX=1000
ImportContentDialogSizeY=650

[DetailCategoriesAdvanced]
StaticMeshSocket.StaticMeshSocket=True
Blueprint.ClassOptions=True
Engine.DefaultClasses=True
GameMapsSettings.DefaultModes=True
GameMapsSettings.DefaultMaps=True
InputAction.Action=False
ArmorWarsHud_C.Tick=True
BP_MediumTank_C.Actor=True
BP_MediumTank_C.AssetUserData=False
BP_MediumTank_C.Navigation=False
RTSGameState.Actor=False
ArmorWarsPlayerController_C.Tick=True
CapsuleComponent.Shape=True

[DetailPropertyExpansion]
TankTurret_C="\"Object.Weapon.MuzzleOffset\" "
RTSWeaponComponent="\"Object.Weapon.MuzzleOffset\" \"Object.Collision.BodyInstance\" "
ActorComponent=
Object=
PreviewMeshCollection="\"Object.SkeletalMeshes\" "
DataAsset="\"Object.Blackboard\" "
BPC_MedTankTurret_C="\"Object.Weapon.MuzzleOffset\" \"Object.Collision.BodyInstance\" "
StaticMeshComponent=
MeshComponent=
PrimitiveComponent=
SceneComponent=
MedTankProjectile_C="\"Object.Components.CollisionComponent.Object.Collision.BodyInstance\" \"Object.Components.MeshComponent.Object.Collision.BodyInstance\" "
RTSProjectile="\"Object.Components.CollisionComponent.Object.Collision.BodyInstance\" \"Object.Components.MeshComponent.Object.Collision.BodyInstance\" "
SphereComponent=
ShapeComponent="\"Object.Collision.BodyInstance\" "
ArmorWarsPlayerController_C=
RTSPlayerController=
PlayerController=
Controller=
EditorLoadingSavingSettings="\"Object.AutoReimport\" \"Object.AutoSave\" \"Object.Blueprints\" "
CrashReportsPrivacySettings="\"Object.Options.bSendUnattendedBugReports\" "
AnalyticsPrivacySettings="\"Object.Options.bSendUsageData\" "
MassSettings="\"Object.Mass\" "
DeveloperSettings=
SmartObjectSettings="\"Object.SmartObject\" "
PC_Demo_PlayerCntroller_C="\"Object.MouseInterface.ClickEventKeys\" "
InputMappingContext="\"Object.Mappings.Mappings.Mappings[0]\" \"Object.Mappings.Mappings.Mappings[0].Triggers\" \"Object.Mappings.Mappings.Mappings[0].Triggers.Triggers[0]\" \"Object.Mappings.Mappings.Mappings[0].Triggers.Triggers[1]\" \"Object.Mappings.Mappings.Mappings[0].Triggers.Triggers[2]\" \"Object.Mappings.Mappings.Mappings[0].Modifiers\" \"Object.Mappings.Mappings.Mappings[1]\" \"Object.Mappings.Mappings.Mappings[1].Triggers\" \"Object.Mappings.Mappings.Mappings[1].Triggers.Triggers[0]\" \"Object.Mappings.Mappings.Mappings[1].Triggers.Triggers[1]\" \"Object.Mappings.Mappings.Mappings[2]\" \"Object.Mappings.Mappings.Mappings[2].Triggers\" \"Object.Mappings.Mappings.Mappings[2].Triggers.Triggers[0]\" \"Object.Mappings.Mappings.Mappings[2].Modifiers\" \"Object.Mappings.Mappings.Mappings[3]\" \"Object.Mappings.Mappings.Mappings[3].Triggers\" \"Object.Mappings.Mappings.Mappings[3].Triggers.Triggers[0]\" \"Object.Mappings.Mappings.Mappings[3].Modifiers\" \"Object.Mappings.Mappings.Mappings[4]\" \"Object.Mappings.Mappings.Mappings[4].Triggers\" \"Object.Mappings.Mappings.Mappings[4].Triggers.Triggers[0]\" \"Object.Mappings.Mappings.Mappings[5]\" \"Object.Mappings.Mappings.Mappings[5].Triggers\" \"Object.Mappings.Mappings.Mappings[5].Triggers.Triggers[0]\" \"Object.Mappings.Mappings.Mappings[5].Modifiers\" \"Object.Mappings.Mappings.Mappings[6]\" \"Object.Mappings.Mappings.Mappings[6].Triggers\" \"Object.Mappings.Mappings.Mappings[7]\" \"Object.Mappings.Mappings.Mappings[7].Triggers\" \"Object.Mappings.Mappings.Mappings[7].Triggers.Triggers[0]\" \"Object.Mappings.Mappings.Mappings[7].Modifiers\" \"Object.Mappings.Mappings.Mappings[8]\" \"Object.Mappings.Mappings.Mappings[8].Triggers\" \"Object.Mappings.Mappings.Mappings[8].Triggers.Triggers[0]\" \"Object.Mappings.Mappings.Mappings[8].Modifiers\" "
InputAction=
BP_MediumTank_C="\"Object.Terrain.TerrainMovementData\" \"Object.Terrain.TerrainMovementData.TerrainMovementData[0]\" \"Object.Components.LandMovementComponent.Object.NavMovement.NavMovementProperties\" \"Object.Components.LandMovementComponent.Object.NavMovement.NavAgentProps\" \"Object.Components.MeshComponent.Object.Collision.BodyInstance\" \"Object.Components.CollisionComponent.Object.Collision.BodyInstance\" \"Object.Components.ArmorComponent.Object.Armor.ArmorFacings\" \"Object.Components.ArmorComponent.Object.Armor.ArmorFacings.ArmorFacings[0]\" \"Object.Components.AIComponent.Object.AI Behavior.BehaviorSettings\" \"Object.Components.TacticalAIComponent.Object.Tactical AI.TacticalSettings\" \"Object.RTS.RTS|Team\" \"Object.RTS.RTS|Tech\" \"Object.RTS.RTS|Unit\" \"Object.RTS.RTS|Movement\" \"Object.RTS.RTS|Vision\" "
RTSLandUnit="\"Object.Terrain.TerrainMovementData\" \"Object.Terrain.TerrainMovementData.TerrainMovementData[0]\" \"Object.Components.LandMovementComponent.Object.NavMovement.NavMovementProperties\" \"Object.Components.LandMovementComponent.Object.NavMovement.NavAgentProps\" \"Object.Components.MeshComponent.Object.Collision.BodyInstance\" \"Object.Components.CollisionComponent.Object.Collision.BodyInstance\" \"Object.Components.ArmorComponent.Object.Armor.ArmorFacings\" \"Object.Components.ArmorComponent.Object.Armor.ArmorFacings.ArmorFacings[0]\" \"Object.Components.AIComponent.Object.AI Behavior.BehaviorSettings\" \"Object.Components.TacticalAIComponent.Object.Tactical AI.TacticalSettings\" \"Object.RTS.RTS|Team\" \"Object.RTS.RTS|Tech\" \"Object.RTS.RTS|Unit\" \"Object.RTS.RTS|Movement\" \"Object.RTS.RTS|Vision\" "
RTSUnit="\"Object.Terrain.TerrainMovementData\" \"Object.Terrain.TerrainMovementData.TerrainMovementData[0]\" \"Object.Components.LandMovementComponent.Object.NavMovement.NavMovementProperties\" \"Object.Components.LandMovementComponent.Object.NavMovement.NavAgentProps\" \"Object.Components.MeshComponent.Object.Collision.BodyInstance\" \"Object.Components.CollisionComponent.Object.Collision.BodyInstance\" \"Object.Components.ArmorComponent.Object.Armor.ArmorFacings\" \"Object.Components.ArmorComponent.Object.Armor.ArmorFacings.ArmorFacings[0]\" \"Object.Components.AIComponent.Object.AI Behavior.BehaviorSettings\" \"Object.Components.TacticalAIComponent.Object.Tactical AI.TacticalSettings\" \"Object.RTS.RTS|Team\" \"Object.RTS.RTS|Tech\" \"Object.RTS.RTS|Unit\" \"Object.RTS.RTS|Movement\" \"Object.RTS.RTS|Vision\" "
RTSBaseActor="\"Object.Terrain.TerrainMovementData\" \"Object.Terrain.TerrainMovementData.TerrainMovementData[0]\" \"Object.Components.MeshComponent.Object.Collision.BodyInstance\" \"Object.Components.ArmorComponent.Object.Armor.ArmorFacings\" \"Object.Components.ArmorComponent.Object.Armor.ArmorFacings.ArmorFacings[0]\" \"Object.Components.TacticalAIComponent.Object.Tactical AI.TacticalSettings\" \"Object.RTS.RTS|Unit\" \"Object.RTS.RTS|Movement\" \"Object.RTS.RTS|Vision\" \"Object.RTS.RTS|Type\" \"Object.RTS.RTS|Tags\" \"Object.RTS.RTS|Team\" \"Object.RTS.RTS|Tech\" \"Object.RTS.RTS|Info\" \"Object.RTS.RTS|Stats\" \"Object.RTS.RTS|Selection\" "
EnhancedInputDeveloperSettings="\"Object.Enhanced Input.DefaultMappingContexts\" \"Object.Enhanced Input.DefaultMappingContexts.DefaultMappingContexts[0]\" "
DeveloperSettingsBackedByCVars=
ArmorWarsCameraPawn_C="\"Object.Components.MeshComponent.Object.Collision.BodyInstance\" "
RTSPawn="\"Object.Components.MeshComponent.Object.Collision.BodyInstance\" "
Pawn="\"Object.Terrain.TerrainMovementData\" \"Object.Terrain.TerrainMovementData.TerrainMovementData[0]\" \"Object.Components.LandMovementComponent.Object.NavMovement.NavMovementProperties\" \"Object.Components.LandMovementComponent.Object.NavMovement.NavAgentProps\" \"Object.Components.MeshComponent.Object.Collision.BodyInstance\" \"Object.Components.CollisionComponent.Object.Collision.BodyInstance\" \"Object.Components.ArmorComponent.Object.Armor.ArmorFacings\" \"Object.Components.ArmorComponent.Object.Armor.ArmorFacings.ArmorFacings[0]\" \"Object.Components.AIComponent.Object.AI Behavior.BehaviorSettings\" \"Object.Components.TacticalAIComponent.Object.Tactical AI.TacticalSettings\" \"Object.RTS.RTS|Team\" \"Object.RTS.RTS|Tech\" \"Object.RTS.RTS|Unit\" \"Object.RTS.RTS|Movement\" \"Object.RTS.RTS|Vision\" "
BP_RTSPawn_C="\"Object.Components.MeshComponent.Object.Collision.BodyInstance\" "
BlueprintEditorSettings="\"Object.Workflow.TypePromotionPinDenyList\" "
DecalComponent="\"Object.Decal.DecalSize\" "
NavModifierVolume="\"Object.BrushBuilder.BrushBuilder.Object.BrushSettings\" "
Volume="\"Object.BrushBuilder.BrushBuilder.Object.BrushSettings\" "
Brush="\"Object.BrushBuilder.BrushBuilder.Object.BrushSettings\" "
NavMeshBoundsVolume="\"Object.BrushBuilder.BrushBuilder.Object.BrushSettings\" "
RTSUnitAIComponent="\"Object.AI Behavior.BehaviorSettings\" "
MaterialEditorInstanceConstant=
MaterialEditorParameters=
CapsuleComponent="\"Object.Collision.BodyInstance\" "
RTSTacticalAIComponent="\"Object.Tactical AI.TacticalSettings\" "
BlackboardData="\"Object.Blackboard\" "

[PlacementMode]
RecentlyPlaced=/Game/UnitBPs/BP_MediumTank.BP_MediumTank;
RecentlyPlaced=/Script/NavigationSystem.NavMeshBoundsVolume;/Engine/Transient.ActorFactoryBoxVolume_0
RecentlyPlaced=/Script/NavigationSystem.NavModifierVolume;/Engine/Transient.ActorFactoryBoxVolume_1
RecentlyPlaced=/Game/GameInit/BP_RTSPawn.BP_RTSPawn;
RecentlyPlaced=/Game/RTSLandUnit.RTSLandUnit;

[AssetEditorToolkitTabLocation]
/Game/GameInit/ArmorWarsGameInstance.ArmorWarsGameInstance=1
/Game/GameInit/ArmorWarsGameMode.ArmorWarsGameMode=1
/Game/GameInit/ArmorWarsPlayerController.ArmorWarsPlayerController=1
/Game/RTSLandUnit.RTSLandUnit=1
/Game/TankBase1.TankBase1=1
/Game/TankTurret.TankTurret=1
/Game/Models/SM_MediumTank.SM_MediumTank=1
/Game/Models/RTSTank3_Skeleton.RTSTank3_Skeleton=1
/Game/Models/AltColor1.AltColor1=1
/Game/Models/RTSTank3.RTSTank3=1
/Game/Models/Test/Cannonbase.Cannonbase=1
/Game/Models/Test/TankBase.TankBase=1
/Game/Models/Test/Turret.Turret=1
/Game/UnitBPs/BP_MediumTank.BP_MediumTank=1
/Game/UnitBPs/BPC_MedTankTurret.BPC_MedTankTurret=1
/Game/UnitBPs/MedTankProjectile.MedTankProjectile=1
/Game/GridBasedBuilder/Demo/BP_Demo_Camera.BP_Demo_Camera=1
/Game/GridBasedBuilder/Demo/GM_Demo_GameMode.GM_Demo_GameMode=1
/Game/GridBasedBuilder/Demo/PC_Demo_PlayerCntroller.PC_Demo_PlayerCntroller=1
/Game/GameInit/ArmorWarsHud.ArmorWarsHud=1
/Game/GameInit/EnhancedInput/ArmorWarsIMC.ArmorWarsIMC=1
/Game/GameInit/EnhancedInput/MouseClick.MouseClick=1
/Game/GameInit/ArmorWarsHUDWidget.ArmorWarsHUDWidget=1
/Game/GameInit/ArmorWarsGameState.ArmorWarsGameState=1
/Game/GameInit/EnhancedInput/SecondaryAction.SecondaryAction=1
/Game/GameInit/EnhancedInput/CameraMoveForward.CameraMoveForward=1
/Game/GameInit/EnhancedInput/CameraMoveRight.CameraMoveRight=1
/Game/GameInit/EnhancedInput/CameraZoom.CameraZoom=1
/Engine/EditorBlueprintResources/StandardMacros.StandardMacros=1
/Game/GameInit/ArmorWarsCameraPawn.ArmorWarsCameraPawn=1
/Game/GameInit/EnhancedInput/CameraRotate.CameraRotate=1
/Game/GameInit/EnhancedInput/PrimaryAction.PrimaryAction=1
/Game/GameInit/BP_RTSPawn.BP_RTSPawn=1
/Game/UnitBPs/MedTankAIcontroller.MedTankAIcontroller=1
/Game/TeamColors.TeamColors=1
/Game/DT_TeamColor.DT_TeamColor=1
/Game/Models/TankBase.TankBase=1
/Game/Models/ColorStripes.ColorStripes=1
/Game/Models/AltColor2.AltColor2=1
/Game/Models/AltColor3.AltColor3=1
/Game/GameInit/TestBB.TestBB=1

[/Script/BlueprintGraph.BlueprintEditorSettings]
bDrawMidpointArrowsInBlueprints=False
bShowGraphInstructionText=True
bHideUnrelatedNodes=False
bShowShortTooltips=True
bShowFunctionParameterIcon=True
bShowFunctionLocalVariableIcon=True
bEnableInputTriggerSupportWarnings=False
bSplitContextTargetSettings=True
bExposeAllMemberComponentFunctions=True
bShowContextualFavorites=False
bExposeDeprecatedFunctions=False
bCompactCallOnMemberNodes=False
bFlattenFavoritesMenus=True
bAutoCastObjectConnections=False
bShowViewportOnSimulate=False
bSpawnDefaultBlueprintNodes=True
bHideConstructionScriptComponentsInDetailsView=True
bHostFindInBlueprintsInGlobalTab=True
bNavigateToNativeFunctionsFromCallNodes=True
bDoubleClickNavigatesToParent=True
bEnableTypePromotion=True
bShowPanelContextMenuForIncompatibleConnections=True
TypePromotionPinDenyList=text
TypePromotionPinDenyList=string
BreakpointReloadMethod=RestoreAll
bEnablePinValueInspectionTooltips=True
bEnableNamespaceEditorFeatures=True
bEnableContextMenuTimeSlicing=True
ContextMenuTimeSlicingThresholdMs=50
bIncludeActionsForSelectedAssetsInContextMenu=False
bLimitAssetActionBindingToSingleSelectionOnly=False
bLoadSelectedAssetsForContextMenuActionBinding=True
bDoNotMarkAllInstancesDirtyOnDefaultValueChange=True
bFavorPureCastNodes=False
SaveOnCompile=SoC_Never
bJumpToNodeErrors=False
bAllowExplicitImpureNodeDisabling=False
bShowActionMenuItemSignatures=False
bBlueprintNodeUniqueNames=False
NodeTemplateCacheCapMB=20.000000
AllowIndexAllBlueprints=LoadOnly
bShowInheritedVariables=False
bAlwaysShowInterfacesInOverrides=True
bShowParentClassInOverrides=True
bShowEmptySections=True
bShowAccessSpecifier=False
Bookmarks=()
PerBlueprintSettings=()
bIncludeCommentNodesInBookmarksTab=True
bShowBookmarksForCurrentDocumentOnlyInTab=False
GraphEditorQuickJumps=()

[RootWindow]
ScreenPosition=X=939.000 Y=288.000
WindowSize=X=1280.000 Y=720.000
InitiallyMaximized=True

[SlateAdditionalLayoutConfig]
Viewport 2.LayoutType=FourPanes2x2
FourPanes2x2.Viewport 2.Percentages0=X=0.500 Y=0.500
FourPanes2x2.Viewport 2.Percentages1=X=0.500 Y=0.500
FourPanes2x2.Viewport 2.Percentages2=X=0.500 Y=0.500
FourPanes2x2.Viewport 2.Percentages3=X=0.500 Y=0.500
FourPanes2x2.Viewport 2.Viewport0.TypeWithinLayout=Default
FourPanes2x2.Viewport 2.Viewport1.TypeWithinLayout=Default
FourPanes2x2.Viewport 2.Viewport2.TypeWithinLayout=Default
FourPanes2x2.Viewport 2.Viewport3.TypeWithinLayout=Default
FourPanes2x2.Viewport 2.bIsMaximized=True
FourPanes2x2.Viewport 2.MaximizedViewport=FourPanes2x2.Viewport 2.Viewport1
Viewport 1.LayoutType=FourPanes2x2
FourPanes2x2.Viewport 1.Percentages0=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages1=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages2=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages3=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Viewport0.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport1.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport2.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport3.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.bIsMaximized=True
FourPanes2x2.Viewport 1.MaximizedViewport=FourPanes2x2.Viewport 1.Viewport1

[Directories2]
UNR=../../../../ArmorWars/Content/GridBasedBuilder/Demo/Levels
BRUSH=../../../../ArmorWars/Content/
FBX=../../../../ArmorWars/Content/
FBXAnim=../../../../ArmorWars/Content/
GenericImport=../../../../ArmorWars/Content/
GenericExport=../../../../ArmorWars/Content/
GenericOpen=../../../../ArmorWars/Content/
GenericSave=../../../../ArmorWars/Content/
MeshImportExport=../../../../ArmorWars/Content/
WorldRoot=../../../../ArmorWars/Content/
Level=../../../../ArmorWars/Content/GridBasedBuilder/Demo/Levels
Project=F:/UE_5.6/

[/Script/RewindDebuggerVLog.RewindDebuggerVLogSettings]
DisplayVerbosity=4
DisplayCategories=()

[/Script/RewindDebugger.RewindDebuggerSettings]
CameraMode=Replay
bShouldAutoEject=False
bShouldAutoRecordOnPIE=False
PlaybackRate=1.000000
bShowEmptyObjectTracks=False
DebugTargetActor=

[Python]
LastDirectory=
RecentsFiles=F:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py

[SkeletalMeshModelingTools]
EditingModeActive=True

[/Script/PinnedCommandList.PinnedCommandListSettings]
Contexts=(Name="SkeletonEditor",Commands=)
Contexts=(Name="PersonaViewport",Commands=)
Contexts=(Name="SkeletalMeshEditor",Commands=)

[MaterialInstanceEditor]
bDrawGrid=False
PrimType=1

[/Script/UnrealEd.MaterialStatsOptions]
bPlatformUsed[0]=0
bPlatformUsed[1]=0
bPlatformUsed[2]=0
bPlatformUsed[3]=0
bPlatformUsed[4]=0
bPlatformUsed[5]=0
bPlatformUsed[6]=0
bPlatformUsed[7]=0
bPlatformUsed[8]=0
bPlatformUsed[9]=0
bPlatformUsed[10]=0
bPlatformUsed[11]=0
bPlatformUsed[12]=0
bPlatformUsed[13]=0
bPlatformUsed[14]=1
bPlatformUsed[15]=0
bPlatformUsed[16]=0
bPlatformUsed[17]=0
bPlatformUsed[18]=0
bPlatformUsed[19]=0
bPlatformUsed[20]=0
bPlatformUsed[21]=0
bPlatformUsed[22]=0
bPlatformUsed[23]=0
bPlatformUsed[24]=0
bPlatformUsed[25]=0
bPlatformUsed[26]=0
bPlatformUsed[27]=0
bPlatformUsed[28]=0
bPlatformUsed[29]=0
bPlatformUsed[30]=0
bPlatformUsed[31]=0
bPlatformUsed[32]=0
bPlatformUsed[33]=0
bPlatformUsed[34]=0
bPlatformUsed[35]=0
bPlatformUsed[36]=0
bPlatformUsed[37]=0
bPlatformUsed[38]=0
bPlatformUsed[39]=0
bPlatformUsed[40]=0
bPlatformUsed[41]=0
bPlatformUsed[42]=0
bPlatformUsed[43]=0
bPlatformUsed[44]=0
bPlatformUsed[45]=0
bPlatformUsed[46]=0
bPlatformUsed[47]=0
bPlatformUsed[48]=0
bPlatformUsed[49]=1
bPlatformUsed[50]=0
bPlatformUsed[51]=0
bPlatformUsed[52]=0
bPlatformUsed[53]=0
bPlatformUsed[54]=0
bPlatformUsed[55]=0
bPlatformUsed[56]=0
bPlatformUsed[57]=0
bPlatformUsed[58]=0
bPlatformUsed[59]=0
bPlatformUsed[60]=0
bPlatformUsed[61]=0
bPlatformUsed[62]=0
bPlatformUsed[63]=0
bPlatformUsed[64]=0
bPlatformUsed[65]=0
bPlatformUsed[66]=0
bPlatformUsed[67]=0
bPlatformUsed[68]=0
bPlatformUsed[69]=0
bPlatformUsed[70]=0
bPlatformUsed[71]=0
bPlatformUsed[72]=0
bPlatformUsed[73]=0
bPlatformUsed[74]=0
bPlatformUsed[75]=0
bPlatformUsed[76]=0
bPlatformUsed[77]=0
bPlatformUsed[78]=0
bPlatformUsed[79]=0
bPlatformUsed[80]=0
bPlatformUsed[81]=0
bPlatformUsed[82]=0
bPlatformUsed[83]=0
bPlatformUsed[84]=0
bPlatformUsed[85]=0
bPlatformUsed[86]=0
bPlatformUsed[87]=0
bPlatformUsed[88]=0
bPlatformUsed[89]=0
bPlatformUsed[90]=0
bPlatformUsed[91]=0
bPlatformUsed[92]=0
bPlatformUsed[93]=0
bPlatformUsed[94]=0
bPlatformUsed[95]=0
bPlatformUsed[96]=0
bPlatformUsed[97]=0
bPlatformUsed[98]=0
bPlatformUsed[99]=0
bPlatformUsed[100]=0
bPlatformUsed[101]=0
bPlatformUsed[102]=0
bPlatformUsed[103]=0
bPlatformUsed[104]=0
bPlatformUsed[105]=0
bPlatformUsed[106]=0
bPlatformUsed[107]=0
bPlatformUsed[108]=0
bPlatformUsed[109]=0
bPlatformUsed[110]=0
bPlatformUsed[111]=0
bPlatformUsed[112]=0
bPlatformUsed[113]=0
bPlatformUsed[114]=0
bPlatformUsed[115]=0
bPlatformUsed[116]=0
bPlatformUsed[117]=0
bPlatformUsed[118]=0
bPlatformUsed[119]=0
bPlatformUsed[120]=0
bPlatformUsed[121]=0
bPlatformUsed[122]=0
bPlatformUsed[123]=0
bPlatformUsed[124]=0
bPlatformUsed[125]=0
bPlatformUsed[126]=0
bPlatformUsed[127]=0
bPlatformUsed[128]=0
bPlatformUsed[129]=0
bPlatformUsed[130]=0
bPlatformUsed[131]=0
bPlatformUsed[132]=0
bPlatformUsed[133]=0
bPlatformUsed[134]=0
bPlatformUsed[135]=0
bPlatformUsed[136]=0
bPlatformUsed[137]=0
bPlatformUsed[138]=0
bPlatformUsed[139]=0
bPlatformUsed[140]=0
bPlatformUsed[141]=0
bPlatformUsed[142]=0
bPlatformUsed[143]=0
bPlatformUsed[144]=0
bPlatformUsed[145]=0
bPlatformUsed[146]=0
bPlatformUsed[147]=0
bPlatformUsed[148]=0
bPlatformUsed[149]=0
bPlatformUsed[150]=0
bPlatformUsed[151]=0
bPlatformUsed[152]=0
bPlatformUsed[153]=0
bPlatformUsed[154]=0
bMaterialQualityUsed[0]=0
bMaterialQualityUsed[1]=1
bMaterialQualityUsed[2]=0
bMaterialQualityUsed[3]=0
MaterialStatsDerivedMIOption=Ignore

[DetailMultiObjectNodeExpansion]
AnimationAuthoringSettings=True
EditorStyleSettings=True
GeneralProjectSettings=True
AssetManagerSettings=True
Engine=True
GameMapsSettings=True
EnhancedInputDeveloperSettings=True
BlueprintEditorSettings=True
LiveCodingSettings=True
AnimatorKitSettings=False
AudioEditorSettings=True
CollectionSettings=True
EnhancedInputEditorSettings=True
EditorExperimentalSettings=True
GameplayAbilitiesEditorDeveloperSettings=True
EditorSettings=True
InterchangeEditorSettings=True
EditorKeyboardShortcutSettings=True
EditorLoadingSavingSettings=True
EditorPerProjectUserSettings=True
OutputLogSettings=True
EditorPerformanceSettings=True
InternationalizationSettingsModel=True
SourceCodeAccessSettings=True
StateTreeEditorUserSettings=True
TextureEncodingUserSettings=True
TextureImportUserSettings=True
TransformGizmoEditorSettings=True
VRModeSettings=True
WorldBookmarkEditorPerProjectUserSettings=True
WorldPartitionEditorSettings=True
WorldPartitionEditorPerProjectUserSettings=True
LevelEditorMiscSettings=True
LevelEditorPlaySettings=True
OnlinePIESettings=True
LevelEditorViewportSettings=True
AnimGraphSettings=True
AnimationBlueprintEditorSettings=True
PersonaOptions=True
ContentBrowserSettings=True
ControlRigEditorSettings=True
CurveEditorSettings=True
SequencerSettings=True
FlipbookEditorSettings=True
GraphEditorSettings=True
LevelInstanceEditorPerProjectUserSettings=True
MaterialEditorSettings=True
MeshPaintSettings=True
MetasoundEditorSettings=True
PCGEditorSettings=True
RigVMEditorSettings=True
SkeletalMeshEditorSettings=True
SpriteEditorSettings=True
TakeRecorderUserSettings=True
TileMapEditorSettings=True
TileSetEditorSettings=True
WidgetDesignerSettings=True
CrashReportsPrivacySettings=True
AnalyticsPrivacySettings=True
BlueprintHeaderViewSettings=True
CameraCalibrationEditorSettings=True
DataflowEditorOptions=True
FractureModeCustomizationSettings=True
GameplayCamerasEditorSettings=True
LightMixerEditorSettings=True
ModelingToolsModeCustomizationSettings=True
ModelingComponentsEditorSettings=True
ObjectMixerEditorSettings=True
PythonScriptPluginUserSettings=True
RewindDebuggerSettings=True
RewindDebuggerVLogSettings=True
StateTreeEditorSettings=True
VisualStudioSourceCodeAccessSettings=True
NavigationToolSettings=True
AutomationTestSettings=True
CrashReporterSettings=True
DataValidationSettings=True
GameplayDebuggerUserSettings=True
GameplayTagsDeveloperSettings=True
EditorDataStorageSettings=True
LogVisualizerSettings=True

[UMGEditor.Designer]
bCommonResolutionSelected=False
PreviewWidth=1280
PreviewHeight=720
PreviewAspectRatio=16:9
bIsInPortraitMode=False
ProfileName=
ScaleFactor=1
bCanPreviewSwapAspectRatio=False

[UMGSequencerSettings SequencerSettings]
AutoChangeMode=None
AllowEditsMode=AllEdits
KeyGroupMode=KeyChanged
KeyInterpolation=Auto
bAutoSetTrackDefaults=False
SpawnPosition=SSP_Origin
bCreateSpawnableCameras=True
bShowRangeSlider=False
bIsSnapEnabled=True
bSnapKeyTimesToElements=False
bSnapSectionTimesToElements=False
bSnapKeysAndSectionsToPlayRange=False
bSnapPlayTimeToKeys=False
bSnapPlayTimeToSections=False
bSnapPlayTimeToMarkers=False
bSnapPlayTimeToPressedKey=True
bSnapPlayTimeToDraggedKey=True
bSnapCurveValueToInterval=False
bForceWholeFrames=True
bShowSelectedNodesOnly=False
bRewindOnRecord=False
bLeftMouseDragDoesMarquee=False
ZoomPosition=SZP_CurrentTime
bAutoScrollEnabled=False
bLinkCurveEditorTimeRange=False
bLinkFiltersWithCurveEditor=False
bSynchronizeCurveEditorSelection=True
bIsolateCurveEditorToSelection=True
bCurveEditorVisible=False
CurveEditorZoomScaling=(MouseWheelZoomMultiplier=1.000000,HorizontalZoomScale=(EditorCurveData=(Keys=((Value=1.000000),(Time=1.000000,Value=1.000000),(Time=100.000000,Value=6.000000)),DefaultValue=1.000000,PreInfinityExtrap=RCCE_Constant,PostInfinityExtrap=RCCE_Linear),ExternalCurve=None),VerticalZoomScale=(EditorCurveData=(Keys=((Value=1.000000),(Time=1.000000,Value=1.000000),(Time=100.000000,Value=6.000000)),DefaultValue=1.000000,PreInfinityExtrap=RCCE_Constant,PostInfinityExtrap=RCCE_Linear),ExternalCurve=None),bLimitHorizontalZoomOut=False,MaxHorizontalZoomOut=900.000000,bLimitVerticalZoomOut=False,MaxVerticalZoomOut=10000.000000)
LoopMode=SLM_NoLoop
bResetPlayheadWhenNavigating=False
bKeepCursorInPlayRangeWhileScrubbing=False
bKeepPlayRangeInSectionBounds=True
ZeroPadFrames=0
JumpFrameIncrement=(Value=5)
TimeWarpDisplay=Both
bShowLayerBars=True
bShowKeyBars=True
bInfiniteKeyAreas=False
bShowChannelColors=False
bShowInfoButton=True
bShowTickLines=True
bShowSequencerToolbar=True
bShowMarkedFrames=True
bShowScalingAnchors=True
KeyAreaCurveExtents=
KeyAreaHeightWithCurves=15.000000
ReduceKeysTolerance=0.000100
bDeleteKeysWhenTrimming=True
bDisableSectionsAfterBaking=True
MarkedFrameColor=(R=0.000000,G=1.000000,B=1.000000,A=0.400000)
SectionColorTints=(B=142,G=102,R=88,A=255)
SectionColorTints=(B=132,G=137,R=99,A=255)
SectionColorTints=(B=92,G=127,R=110,A=255)
SectionColorTints=(B=102,G=142,R=151,A=255)
SectionColorTints=(B=101,G=119,R=147,A=255)
SectionColorTints=(B=108,G=95,R=139,A=255)
SectionColorTints=(B=121,G=74,R=109,A=255)
bCleanPlaybackMode=True
bActivateRealtimeViewports=True
bEvaluateSubSequencesInIsolation=False
bRerunConstructionScripts=True
bShowDebugVisualization=False
bVisualizePreAndPostRoll=True
bCompileDirectorOnEvaluate=False
TrajectoryPathCap=250
FrameNumberDisplayFormat=Seconds
MovieRendererName=
bAutoExpandNodesOnSelection=True
bRestoreOriginalViewportOnCameraCutUnlock=True
TreeViewWidth=0.300000
ViewDensity=Relaxed
AssetBrowserWidth=500.000000
AssetBrowserHeight=300.000000
ColumnVisibilitySettings=(ColumnName="Indicator",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Pin",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Lock",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Deactivate",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Mute",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Solo",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Label",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Edit",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Add",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Nav",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="KeyFrame",bIsVisible=False)
ColumnVisibilitySettings=(ColumnName="ColorPicker",bIsVisible=True)
SidebarState=(("UMGSequencerSettings", (DrawerStates=((DrawerId="SelectionDetails")))))
TrackFilterBars=(("UMGSequencerSettings", ()))
bIncludePinnedInFilter=False
bAutoExpandNodesOnFilterPass=False
bUseFilterSubmenusForCategories=False
bFilterBarVisible=False
LastFilterBarLayout=Horizontal
LastFilterBarSizeCoefficient=0.000000
ThumbnailCaptureSettings=(CaptureFrameLocationRule=CurrentFrame)
bNavigationToolVisible=False

[ColorThemes]
Theme0=New Theme

[RecentColors]
Color0=(R=115.906624,G=1.000000,B=0.802083,A=1.000000)
Color1=(R=1.278403,G=1.000000,B=0.973958,A=1.000000)
Color2=(R=229.699356,G=1.000000,B=0.447917,A=1.000000)
Color3=(R=121.774323,G=0.996046,B=0.756522,A=1.000000)
Color4=(R=35.221588,G=1.000000,B=0.828125,A=1.000000)
Color5=(R=2.253613,G=1.000000,B=0.800000,A=1.000000)
Color6=(R=231.523071,G=1.000000,B=0.432292,A=1.000000)
Color7=(R=123.934624,G=0.876853,B=1.000000,A=1.000000)

[/Script/Engine.WorldPartitionEditorPerProjectUserSettings]
bHideEditorDataLayers=False
bHideRuntimeDataLayers=False
bHideDataLayerActors=True
bHideUnloadedActors=False
bShowOnlySelectedActors=False
bHighlightSelectedDataLayers=True
bHideLevelInstanceContent=True
bDisableLoadingOfLastLoadedRegions=False
bBugItGoLoadRegion=False
bShowCellCoords=False
MinimapUnloadedOpacity=0.660000
PerWorldEditorSettings=(("/Temp/Untitled_1.Untitled_1", (LoadedEditorRegions=((Min=(X=-102480.867188,Y=-102536.195312,Z=-2097152.000000),Max=(X=102491.187500,Y=102276.851562,Z=2097152.000000),IsValid=True)))))


// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSReturnFireSystem.h"

#ifdef ARMORWARS_RTSReturnFireSystem_generated_h
#error "RTSReturnFireSystem.generated.h already included, missing '#pragma once' in RTSReturnFireSystem.h"
#endif
#define ARMORWARS_RTSReturnFireSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class ARTSBaseActor;
class ARTSUnit;
class URTSWeaponController;

// ********** Begin ScriptStruct FRTSReturnFireConfig **********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_37_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSReturnFireConfig;
// ********** End ScriptStruct FRTSReturnFireConfig ************************************************

// ********** Begin ScriptStruct FRTSReturnFireState ***********************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_113_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSReturnFireState_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSReturnFireState;
// ********** End ScriptStruct FRTSReturnFireState *************************************************

// ********** Begin Delegate FOnReturnFireStarted **************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_278_DELEGATE \
static void FOnReturnFireStarted_DelegateWrapper(const FMulticastScriptDelegate& OnReturnFireStarted, ARTSBaseActor* Target);


// ********** End Delegate FOnReturnFireStarted ****************************************************

// ********** Begin Delegate FOnReturnFireStopped **************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_279_DELEGATE \
static void FOnReturnFireStopped_DelegateWrapper(const FMulticastScriptDelegate& OnReturnFireStopped);


// ********** End Delegate FOnReturnFireStopped ****************************************************

// ********** Begin Delegate FOnTargetChanged ******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_280_DELEGATE \
static void FOnTargetChanged_DelegateWrapper(const FMulticastScriptDelegate& OnTargetChanged, ARTSBaseActor* OldTarget, ARTSBaseActor* NewTarget);


// ********** End Delegate FOnTargetChanged ********************************************************

// ********** Begin Delegate FOnAttackerRegistered *************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_281_DELEGATE \
static void FOnAttackerRegistered_DelegateWrapper(const FMulticastScriptDelegate& OnAttackerRegistered, ARTSBaseActor* Attacker);


// ********** End Delegate FOnAttackerRegistered ***************************************************

// ********** Begin Class URTSReturnFireComponent **************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_164_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetEffectiveReturnFireRange); \
	DECLARE_FUNCTION(execGetWeaponController); \
	DECLARE_FUNCTION(execGetOwnerUnit); \
	DECLARE_FUNCTION(execHandleMovementWhileFiring); \
	DECLARE_FUNCTION(execShouldStopToFire); \
	DECLARE_FUNCTION(execGetMovementSpeedMultiplier); \
	DECLARE_FUNCTION(execShouldReturnFire); \
	DECLARE_FUNCTION(execCanReturnFire); \
	DECLARE_FUNCTION(execGetCurrentTarget); \
	DECLARE_FUNCTION(execIsReturningFire); \
	DECLARE_FUNCTION(execGetMostRecentAttacker); \
	DECLARE_FUNCTION(execGetRecentAttackers); \
	DECLARE_FUNCTION(execCleanupOldAttackers); \
	DECLARE_FUNCTION(execRegisterAttacker); \
	DECLARE_FUNCTION(execSetReturnFireTarget); \
	DECLARE_FUNCTION(execShouldSwitchTarget); \
	DECLARE_FUNCTION(execSelectReturnFireTarget); \
	DECLARE_FUNCTION(execProcessReturnFire); \
	DECLARE_FUNCTION(execStopReturnFire); \
	DECLARE_FUNCTION(execStartReturnFire);


ARMORWARS_API UClass* Z_Construct_UClass_URTSReturnFireComponent_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_164_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSReturnFireComponent(); \
	friend struct Z_Construct_UClass_URTSReturnFireComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSReturnFireComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSReturnFireComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSReturnFireComponent_NoRegister) \
	DECLARE_SERIALIZER(URTSReturnFireComponent)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_164_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSReturnFireComponent(URTSReturnFireComponent&&) = delete; \
	URTSReturnFireComponent(const URTSReturnFireComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSReturnFireComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSReturnFireComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSReturnFireComponent) \
	NO_API virtual ~URTSReturnFireComponent();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_161_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_164_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_164_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_164_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h_164_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSReturnFireComponent;

// ********** End Class URTSReturnFireComponent ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h

// ********** Begin Enum ERTSReturnFireBehavior ****************************************************
#define FOREACH_ENUM_ERTSRETURNFIREBEHAVIOR(op) \
	op(ERTSReturnFireBehavior::None) \
	op(ERTSReturnFireBehavior::Opportunistic) \
	op(ERTSReturnFireBehavior::Aggressive) \
	op(ERTSReturnFireBehavior::Defensive) \
	op(ERTSReturnFireBehavior::AlwaysReturn) 

enum class ERTSReturnFireBehavior : uint8;
template<> struct TIsUEnumClass<ERTSReturnFireBehavior> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSReturnFireBehavior>();
// ********** End Enum ERTSReturnFireBehavior ******************************************************

// ********** Begin Enum ERTSReturnFirePriority ****************************************************
#define FOREACH_ENUM_ERTSRETURNFIREPRIORITY(op) \
	op(ERTSReturnFirePriority::NearestEnemy) \
	op(ERTSReturnFirePriority::AttackingEnemy) \
	op(ERTSReturnFirePriority::WeakestEnemy) \
	op(ERTSReturnFirePriority::StrongestThreat) \
	op(ERTSReturnFirePriority::LastAttacker) 

enum class ERTSReturnFirePriority : uint8;
template<> struct TIsUEnumClass<ERTSReturnFirePriority> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSReturnFirePriority>();
// ********** End Enum ERTSReturnFirePriority ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

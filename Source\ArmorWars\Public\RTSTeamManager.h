#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "RTSBaseActor.h"
#include "RTSTeamManager.generated.h"

// Struct for team information
USTRUCT(BlueprintType)
struct ARMORWARS_API FRTSTeamInfo
{
    GENERATED_BODY()

    // Team ID
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    int32 TeamID = 0;

    // Team name
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    FText TeamName;

    // Whether this team is controlled by AI
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    bool bIsAIControlled = false;

    // Whether this team is active
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    bool bIsActive = true;

    FRTSTeamInfo()
    {
        TeamID = 0;
        TeamName = FText::FromString(TEXT("Team"));
        bIsAIControlled = false;
        bIsActive = true;
    }

    FRTSTeamInfo(int32 InTeamID, const FText& InTeamName)
    {
        TeamID = InTeamID;
        TeamName = InTeamName;
        bIsAIControlled = false;
        bIsActive = true;
    }
};

/**
 * Simple team manager subsystem for basic team functionality
 * All teams are enemies except for the same team ID
 */
UCLASS(BlueprintType)
class ARMORWARS_API URTSTeamManager : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    URTSTeamManager();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

protected:
    // Team data storage
    UPROPERTY(BlueprintReadOnly, Category = "Teams")
    TMap<int32, FRTSTeamInfo> Teams;

public:
    // Team management functions
    UFUNCTION(BlueprintCallable, Category = "RTS|Teams")
    bool CreateTeam(int32 TeamID, const FText& TeamName, bool bIsAI = false);

    UFUNCTION(BlueprintCallable, Category = "RTS|Teams")
    bool RemoveTeam(int32 TeamID);

    UFUNCTION(BlueprintCallable, Category = "RTS|Teams")
    bool DoesTeamExist(int32 TeamID) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    FRTSTeamInfo GetTeamInfo(int32 TeamID) const;

    UFUNCTION(BlueprintCallable, Category = "RTS|Teams")
    bool SetTeamInfo(int32 TeamID, const FRTSTeamInfo& NewTeamInfo);

    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    TArray<int32> GetAllTeamIDs() const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    TArray<FRTSTeamInfo> GetAllTeams() const;

    // Simple team relationship functions (all different teams are enemies)
    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    bool AreTeamsAllied(int32 TeamA, int32 TeamB) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    bool AreTeamsEnemies(int32 TeamA, int32 TeamB) const;

    // Actor utility functions
    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    bool AreActorsAllied(const ARTSBaseActor* ActorA, const ARTSBaseActor* ActorB) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    bool AreActorsEnemies(const ARTSBaseActor* ActorA, const ARTSBaseActor* ActorB) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    TArray<ARTSBaseActor*> GetAllActorsOnTeam(int32 TeamID) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    TArray<ARTSBaseActor*> GetAllEnemyActors(int32 TeamID) const;

    // Enhanced AI support functions
    UFUNCTION(BlueprintPure, Category = "RTS|Teams|AI")
    TArray<ARTSBaseActor*> FindEnemiesInRange(int32 TeamID, const FVector& Location, float Range) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams|AI")
    ARTSBaseActor* FindNearestEnemy(int32 TeamID, const FVector& Location, float MaxRange = 0.0f) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams|AI")
    ARTSBaseActor* FindHighestPriorityTarget(int32 TeamID, const FVector& Location, float MaxRange = 0.0f) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams|AI")
    bool IsValidTarget(const ARTSBaseActor* Attacker, const ARTSBaseActor* Target) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams|AI")
    float CalculateTargetPriority(const ARTSBaseActor* Attacker, const ARTSBaseActor* Target) const;

    // Team state functions
    UFUNCTION(BlueprintCallable, Category = "RTS|Teams")
    void SetTeamActive(int32 TeamID, bool bActive);

    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    bool IsTeamActive(int32 TeamID) const;

    UFUNCTION(BlueprintPure, Category = "RTS|Teams")
    int32 GetActiveTeamCount() const;

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "RTS|Teams")
    void InitializeDefaultTeams();

    UFUNCTION(BlueprintCallable, Category = "RTS|Teams")
    void ClearAllTeams();

public:
    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTeamCreated, int32, TeamID, const FRTSTeamInfo&, TeamInfo);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTeamRemoved, int32, TeamID);

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnTeamCreated OnTeamCreated;

    UPROPERTY(BlueprintAssignable, Category = "RTS|Events")
    FOnTeamRemoved OnTeamRemoved;
};

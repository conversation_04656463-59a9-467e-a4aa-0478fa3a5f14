// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSBehaviorNodes.h"

#ifdef ARMORWARS_RTSBehaviorNodes_generated_h
#error "RTSBehaviorNodes.generated.h already included, missing '#pragma once' in RTSBehaviorNodes.h"
#endif
#define ARMORWARS_RTSBehaviorNodes_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class URTSMoveToLocationTask ***************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSMoveToLocationTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_19_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSMoveToLocationTask(); \
	friend struct Z_Construct_UClass_URTSMoveToLocationTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSMoveToLocationTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSMoveToLocationTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSMoveToLocationTask_NoRegister) \
	DECLARE_SERIALIZER(URTSMoveToLocationTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_19_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSMoveToLocationTask(URTSMoveToLocationTask&&) = delete; \
	URTSMoveToLocationTask(const URTSMoveToLocationTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSMoveToLocationTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSMoveToLocationTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSMoveToLocationTask) \
	NO_API virtual ~URTSMoveToLocationTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_16_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_19_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_19_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_19_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSMoveToLocationTask;

// ********** End Class URTSMoveToLocationTask *****************************************************

// ********** Begin Class URTSAttackTargetTask *****************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSAttackTargetTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_42_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSAttackTargetTask(); \
	friend struct Z_Construct_UClass_URTSAttackTargetTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSAttackTargetTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSAttackTargetTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSAttackTargetTask_NoRegister) \
	DECLARE_SERIALIZER(URTSAttackTargetTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_42_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSAttackTargetTask(URTSAttackTargetTask&&) = delete; \
	URTSAttackTargetTask(const URTSAttackTargetTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSAttackTargetTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSAttackTargetTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSAttackTargetTask) \
	NO_API virtual ~URTSAttackTargetTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_39_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_42_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_42_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_42_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSAttackTargetTask;

// ********** End Class URTSAttackTargetTask *******************************************************

// ********** Begin Class URTSPatrolTask ***********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSPatrolTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_65_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSPatrolTask(); \
	friend struct Z_Construct_UClass_URTSPatrolTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSPatrolTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSPatrolTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSPatrolTask_NoRegister) \
	DECLARE_SERIALIZER(URTSPatrolTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_65_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSPatrolTask(URTSPatrolTask&&) = delete; \
	URTSPatrolTask(const URTSPatrolTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSPatrolTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSPatrolTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSPatrolTask) \
	NO_API virtual ~URTSPatrolTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_62_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_65_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_65_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_65_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSPatrolTask;

// ********** End Class URTSPatrolTask *************************************************************

// ********** Begin Class URTSFindEnemyTask ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSFindEnemyTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_94_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSFindEnemyTask(); \
	friend struct Z_Construct_UClass_URTSFindEnemyTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSFindEnemyTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSFindEnemyTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSFindEnemyTask_NoRegister) \
	DECLARE_SERIALIZER(URTSFindEnemyTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_94_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSFindEnemyTask(URTSFindEnemyTask&&) = delete; \
	URTSFindEnemyTask(const URTSFindEnemyTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSFindEnemyTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSFindEnemyTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSFindEnemyTask) \
	NO_API virtual ~URTSFindEnemyTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_91_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_94_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_94_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_94_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSFindEnemyTask;

// ********** End Class URTSFindEnemyTask **********************************************************

// ********** Begin Class URTSHealthLowCondition ***************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSHealthLowCondition_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_114_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSHealthLowCondition(); \
	friend struct Z_Construct_UClass_URTSHealthLowCondition_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSHealthLowCondition_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSHealthLowCondition, URTSConditionNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSHealthLowCondition_NoRegister) \
	DECLARE_SERIALIZER(URTSHealthLowCondition)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_114_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSHealthLowCondition(URTSHealthLowCondition&&) = delete; \
	URTSHealthLowCondition(const URTSHealthLowCondition&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSHealthLowCondition); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSHealthLowCondition); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSHealthLowCondition) \
	NO_API virtual ~URTSHealthLowCondition();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_111_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_114_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_114_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_114_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSHealthLowCondition;

// ********** End Class URTSHealthLowCondition *****************************************************

// ********** Begin Class URTSEnemyNearbyCondition *************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnemyNearbyCondition_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_131_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSEnemyNearbyCondition(); \
	friend struct Z_Construct_UClass_URTSEnemyNearbyCondition_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSEnemyNearbyCondition_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSEnemyNearbyCondition, URTSConditionNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSEnemyNearbyCondition_NoRegister) \
	DECLARE_SERIALIZER(URTSEnemyNearbyCondition)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_131_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSEnemyNearbyCondition(URTSEnemyNearbyCondition&&) = delete; \
	URTSEnemyNearbyCondition(const URTSEnemyNearbyCondition&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSEnemyNearbyCondition); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSEnemyNearbyCondition); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSEnemyNearbyCondition) \
	NO_API virtual ~URTSEnemyNearbyCondition();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_128_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_131_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_131_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_131_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSEnemyNearbyCondition;

// ********** End Class URTSEnemyNearbyCondition ***************************************************

// ********** Begin Class URTSHasValidTargetCondition **********************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSHasValidTargetCondition_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_148_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSHasValidTargetCondition(); \
	friend struct Z_Construct_UClass_URTSHasValidTargetCondition_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSHasValidTargetCondition_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSHasValidTargetCondition, URTSConditionNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSHasValidTargetCondition_NoRegister) \
	DECLARE_SERIALIZER(URTSHasValidTargetCondition)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_148_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSHasValidTargetCondition(URTSHasValidTargetCondition&&) = delete; \
	URTSHasValidTargetCondition(const URTSHasValidTargetCondition&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSHasValidTargetCondition); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSHasValidTargetCondition); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSHasValidTargetCondition) \
	NO_API virtual ~URTSHasValidTargetCondition();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_145_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_148_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_148_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_148_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSHasValidTargetCondition;

// ********** End Class URTSHasValidTargetCondition ************************************************

// ********** Begin Class URTSInverterDecorator ****************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSInverterDecorator_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_165_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSInverterDecorator(); \
	friend struct Z_Construct_UClass_URTSInverterDecorator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSInverterDecorator_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSInverterDecorator, URTSDecoratorNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSInverterDecorator_NoRegister) \
	DECLARE_SERIALIZER(URTSInverterDecorator)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_165_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSInverterDecorator(URTSInverterDecorator&&) = delete; \
	URTSInverterDecorator(const URTSInverterDecorator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSInverterDecorator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSInverterDecorator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSInverterDecorator) \
	NO_API virtual ~URTSInverterDecorator();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_162_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_165_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_165_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_165_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSInverterDecorator;

// ********** End Class URTSInverterDecorator ******************************************************

// ********** Begin Class URTSRepeaterDecorator ****************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSRepeaterDecorator_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_179_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSRepeaterDecorator(); \
	friend struct Z_Construct_UClass_URTSRepeaterDecorator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSRepeaterDecorator_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSRepeaterDecorator, URTSDecoratorNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSRepeaterDecorator_NoRegister) \
	DECLARE_SERIALIZER(URTSRepeaterDecorator)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_179_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSRepeaterDecorator(URTSRepeaterDecorator&&) = delete; \
	URTSRepeaterDecorator(const URTSRepeaterDecorator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSRepeaterDecorator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSRepeaterDecorator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSRepeaterDecorator) \
	NO_API virtual ~URTSRepeaterDecorator();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_176_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_179_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_179_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_179_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSRepeaterDecorator;

// ********** End Class URTSRepeaterDecorator ******************************************************

// ********** Begin Class URTSCooldownDecorator ****************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSCooldownDecorator_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_200_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSCooldownDecorator(); \
	friend struct Z_Construct_UClass_URTSCooldownDecorator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSCooldownDecorator_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSCooldownDecorator, URTSDecoratorNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSCooldownDecorator_NoRegister) \
	DECLARE_SERIALIZER(URTSCooldownDecorator)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_200_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSCooldownDecorator(URTSCooldownDecorator&&) = delete; \
	URTSCooldownDecorator(const URTSCooldownDecorator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSCooldownDecorator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSCooldownDecorator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSCooldownDecorator) \
	NO_API virtual ~URTSCooldownDecorator();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_197_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_200_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_200_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_200_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSCooldownDecorator;

// ********** End Class URTSCooldownDecorator ******************************************************

// ********** Begin Class URTSExecuteCommandTaskNode ***********************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSExecuteCommandTaskNode_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_223_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSExecuteCommandTaskNode(); \
	friend struct Z_Construct_UClass_URTSExecuteCommandTaskNode_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSExecuteCommandTaskNode_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSExecuteCommandTaskNode, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSExecuteCommandTaskNode_NoRegister) \
	DECLARE_SERIALIZER(URTSExecuteCommandTaskNode)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_223_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSExecuteCommandTaskNode(URTSExecuteCommandTaskNode&&) = delete; \
	URTSExecuteCommandTaskNode(const URTSExecuteCommandTaskNode&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSExecuteCommandTaskNode); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSExecuteCommandTaskNode); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSExecuteCommandTaskNode) \
	NO_API virtual ~URTSExecuteCommandTaskNode();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_220_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_223_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_223_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_223_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSExecuteCommandTaskNode;

// ********** End Class URTSExecuteCommandTaskNode *************************************************

// ********** Begin Class URTSEnhancedMoveTask *****************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnhancedMoveTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_251_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSEnhancedMoveTask(); \
	friend struct Z_Construct_UClass_URTSEnhancedMoveTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSEnhancedMoveTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSEnhancedMoveTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSEnhancedMoveTask_NoRegister) \
	DECLARE_SERIALIZER(URTSEnhancedMoveTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_251_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSEnhancedMoveTask(URTSEnhancedMoveTask&&) = delete; \
	URTSEnhancedMoveTask(const URTSEnhancedMoveTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSEnhancedMoveTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSEnhancedMoveTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSEnhancedMoveTask) \
	NO_API virtual ~URTSEnhancedMoveTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_248_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_251_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_251_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_251_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSEnhancedMoveTask;

// ********** End Class URTSEnhancedMoveTask *******************************************************

// ********** Begin Class URTSEnhancedCombatTask ***************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnhancedCombatTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_289_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSEnhancedCombatTask(); \
	friend struct Z_Construct_UClass_URTSEnhancedCombatTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSEnhancedCombatTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSEnhancedCombatTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSEnhancedCombatTask_NoRegister) \
	DECLARE_SERIALIZER(URTSEnhancedCombatTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_289_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSEnhancedCombatTask(URTSEnhancedCombatTask&&) = delete; \
	URTSEnhancedCombatTask(const URTSEnhancedCombatTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSEnhancedCombatTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSEnhancedCombatTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSEnhancedCombatTask) \
	NO_API virtual ~URTSEnhancedCombatTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_286_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_289_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_289_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_289_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSEnhancedCombatTask;

// ********** End Class URTSEnhancedCombatTask *****************************************************

// ********** Begin Class URTSFollowFormationTask **************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSFollowFormationTask_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_329_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSFollowFormationTask(); \
	friend struct Z_Construct_UClass_URTSFollowFormationTask_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSFollowFormationTask_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSFollowFormationTask, URTSTaskNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSFollowFormationTask_NoRegister) \
	DECLARE_SERIALIZER(URTSFollowFormationTask)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_329_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSFollowFormationTask(URTSFollowFormationTask&&) = delete; \
	URTSFollowFormationTask(const URTSFollowFormationTask&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSFollowFormationTask); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSFollowFormationTask); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSFollowFormationTask) \
	NO_API virtual ~URTSFollowFormationTask();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_326_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_329_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_329_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_329_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSFollowFormationTask;

// ********** End Class URTSFollowFormationTask ****************************************************

// ********** Begin Class URTSHasCommandsCondition *************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSHasCommandsCondition_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_362_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSHasCommandsCondition(); \
	friend struct Z_Construct_UClass_URTSHasCommandsCondition_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSHasCommandsCondition_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSHasCommandsCondition, URTSConditionNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSHasCommandsCondition_NoRegister) \
	DECLARE_SERIALIZER(URTSHasCommandsCondition)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_362_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSHasCommandsCondition(URTSHasCommandsCondition&&) = delete; \
	URTSHasCommandsCondition(const URTSHasCommandsCondition&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSHasCommandsCondition); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSHasCommandsCondition); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSHasCommandsCondition) \
	NO_API virtual ~URTSHasCommandsCondition();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_359_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_362_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_362_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_362_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSHasCommandsCondition;

// ********** End Class URTSHasCommandsCondition ***************************************************

// ********** Begin Class URTSEnemiesInRangeCondition **********************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSEnemiesInRangeCondition_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_385_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSEnemiesInRangeCondition(); \
	friend struct Z_Construct_UClass_URTSEnemiesInRangeCondition_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSEnemiesInRangeCondition_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSEnemiesInRangeCondition, URTSConditionNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSEnemiesInRangeCondition_NoRegister) \
	DECLARE_SERIALIZER(URTSEnemiesInRangeCondition)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_385_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSEnemiesInRangeCondition(URTSEnemiesInRangeCondition&&) = delete; \
	URTSEnemiesInRangeCondition(const URTSEnemiesInRangeCondition&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSEnemiesInRangeCondition); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSEnemiesInRangeCondition); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSEnemiesInRangeCondition) \
	NO_API virtual ~URTSEnemiesInRangeCondition();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_382_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_385_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_385_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_385_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSEnemiesInRangeCondition;

// ********** End Class URTSEnemiesInRangeCondition ************************************************

// ********** Begin Class URTSInFormationCondition *************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSInFormationCondition_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_417_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSInFormationCondition(); \
	friend struct Z_Construct_UClass_URTSInFormationCondition_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSInFormationCondition_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSInFormationCondition, URTSConditionNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSInFormationCondition_NoRegister) \
	DECLARE_SERIALIZER(URTSInFormationCondition)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_417_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSInFormationCondition(URTSInFormationCondition&&) = delete; \
	URTSInFormationCondition(const URTSInFormationCondition&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSInFormationCondition); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSInFormationCondition); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSInFormationCondition) \
	NO_API virtual ~URTSInFormationCondition();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_414_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_417_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_417_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_417_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSInFormationCondition;

// ********** End Class URTSInFormationCondition ***************************************************

// ********** Begin Class URTSCommandInterruptDecorator ********************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandInterruptDecorator_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_443_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSCommandInterruptDecorator(); \
	friend struct Z_Construct_UClass_URTSCommandInterruptDecorator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSCommandInterruptDecorator_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSCommandInterruptDecorator, URTSDecoratorNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSCommandInterruptDecorator_NoRegister) \
	DECLARE_SERIALIZER(URTSCommandInterruptDecorator)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_443_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSCommandInterruptDecorator(URTSCommandInterruptDecorator&&) = delete; \
	URTSCommandInterruptDecorator(const URTSCommandInterruptDecorator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSCommandInterruptDecorator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSCommandInterruptDecorator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSCommandInterruptDecorator) \
	NO_API virtual ~URTSCommandInterruptDecorator();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_440_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_443_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_443_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_443_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSCommandInterruptDecorator;

// ********** End Class URTSCommandInterruptDecorator **********************************************

// ********** Begin Class URTSStatBasedDecorator ***************************************************
ARMORWARS_API UClass* Z_Construct_UClass_URTSStatBasedDecorator_NoRegister();

#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_469_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURTSStatBasedDecorator(); \
	friend struct Z_Construct_UClass_URTSStatBasedDecorator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend ARMORWARS_API UClass* Z_Construct_UClass_URTSStatBasedDecorator_NoRegister(); \
public: \
	DECLARE_CLASS2(URTSStatBasedDecorator, URTSDecoratorNode, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/ArmorWars"), Z_Construct_UClass_URTSStatBasedDecorator_NoRegister) \
	DECLARE_SERIALIZER(URTSStatBasedDecorator)


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_469_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URTSStatBasedDecorator(URTSStatBasedDecorator&&) = delete; \
	URTSStatBasedDecorator(const URTSStatBasedDecorator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URTSStatBasedDecorator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URTSStatBasedDecorator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URTSStatBasedDecorator) \
	NO_API virtual ~URTSStatBasedDecorator();


#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_466_PROLOG
#define FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_469_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_469_INCLASS_NO_PURE_DECLS \
	FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h_469_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URTSStatBasedDecorator;

// ********** End Class URTSStatBasedDecorator *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSBehaviorNodes_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

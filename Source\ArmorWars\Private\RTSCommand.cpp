#include "RTSCommand.h"
#include "RTSCommandComponent.h"
#include "RTSBaseActor.h"
#include "RTSUnit.h"
#include "RTSAIController.h"
#include "Engine/World.h"
#include "Engine/Engine.h"

// FRTSCommandQueue Implementation

void FRTSCommandQueue::AddCommand(const FRTSCommand& Command, bool bClearQueue)
{
    if (bClearQueue)
    {
        ClearQueue();
    }
    
    // Check if we should queue or replace
    if (Command.bQueueCommand && Commands.Num() < MaxQueueSize)
    {
        Commands.Add(Command);
    }
    else
    {
        // Replace current command or add as first if queue is empty
        if (Commands.Num() > 0)
        {
            Commands[0] = Command;
            CurrentCommandIndex = 0;
        }
        else
        {
            Commands.Add(Command);
            CurrentCommandIndex = 0;
        }
    }
    
    // If this is the first command, set it as current
    if (CurrentCommandIndex == -1 && Commands.Num() > 0)
    {
        CurrentCommandIndex = 0;
    }
}

void FRTSCommandQueue::InsertCommand(const FRTSCommand& Command, int32 Index)
{
    if (Index < 0)
    {
        Index = 0;
    }
    
    if (Index >= Commands.Num())
    {
        Commands.Add(Command);
    }
    else
    {
        Commands.Insert(Command, Index);
        
        // Adjust current command index if necessary
        if (CurrentCommandIndex >= Index)
        {
            CurrentCommandIndex++;
        }
    }
    
    // If this is the first command, set it as current
    if (CurrentCommandIndex == -1 && Commands.Num() > 0)
    {
        CurrentCommandIndex = 0;
    }
}

bool FRTSCommandQueue::RemoveCommand(int32 Index)
{
    if (Index < 0 || Index >= Commands.Num())
    {
        return false;
    }
    
    Commands.RemoveAt(Index);
    
    // Adjust current command index
    if (CurrentCommandIndex == Index)
    {
        // Current command was removed, don't change index (next command moves into this slot)
        if (CurrentCommandIndex >= Commands.Num())
        {
            CurrentCommandIndex = Commands.Num() - 1;
        }
    }
    else if (CurrentCommandIndex > Index)
    {
        CurrentCommandIndex--;
    }
    
    // If no commands left, reset index
    if (Commands.Num() == 0)
    {
        CurrentCommandIndex = -1;
    }
    
    return true;
}

void FRTSCommandQueue::ClearQueue()
{
    Commands.Empty();
    CurrentCommandIndex = -1;
}

FRTSCommand* FRTSCommandQueue::GetCurrentCommand()
{
    if (CurrentCommandIndex >= 0 && CurrentCommandIndex < Commands.Num())
    {
        return &Commands[CurrentCommandIndex];
    }
    return nullptr;
}

const FRTSCommand* FRTSCommandQueue::GetCurrentCommand() const
{
    if (CurrentCommandIndex >= 0 && CurrentCommandIndex < Commands.Num())
    {
        return &Commands[CurrentCommandIndex];
    }
    return nullptr;
}

FRTSCommand* FRTSCommandQueue::GetNextCommand()
{
    int32 NextIndex = CurrentCommandIndex + 1;
    if (NextIndex >= 0 && NextIndex < Commands.Num())
    {
        return &Commands[NextIndex];
    }
    return nullptr;
}

bool FRTSCommandQueue::AdvanceToNextCommand()
{
    if (CurrentCommandIndex + 1 < Commands.Num())
    {
        CurrentCommandIndex++;
        return true;
    }
    else
    {
        // No more commands, clear the queue
        ClearQueue();
        return false;
    }
}

bool FRTSCommandQueue::HasCommands() const
{
    return Commands.Num() > 0 && CurrentCommandIndex >= 0;
}

int32 FRTSCommandQueue::GetCommandCount() const
{
    return Commands.Num();
}

void FRTSCommandQueue::SetCurrentCommandStatus(ERTSCommandStatus NewStatus)
{
    if (FRTSCommand* CurrentCommand = GetCurrentCommand())
    {
        CurrentCommand->Status = NewStatus;
    }
}

void FRTSCommandQueue::CompleteCurrentCommand()
{
    SetCurrentCommandStatus(ERTSCommandStatus::Completed);
    AdvanceToNextCommand();
}

void FRTSCommandQueue::FailCurrentCommand()
{
    SetCurrentCommandStatus(ERTSCommandStatus::Failed);
    AdvanceToNextCommand();
}

void FRTSCommandQueue::CancelCurrentCommand()
{
    SetCurrentCommandStatus(ERTSCommandStatus::Cancelled);
    AdvanceToNextCommand();
}

// URTSCommandComponent Implementation

URTSCommandComponent::URTSCommandComponent()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // Update 10 times per second by default

    bEnableDebugLogging = false;
    CommandUpdateInterval = 0.1f;
    bAutoExecuteCommands = true;
    MinimumInterruptPriority = ERTSCommandPriority::High;

    bIsExecutingCommand = false;
    LastCommandUpdateTime = 0.0f;
    CurrentFormationOffset = FVector::ZeroVector;
    CurrentFormationIndex = -1;
}

void URTSCommandComponent::BeginPlay()
{
    Super::BeginPlay();

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCommandComponent: BeginPlay for %s"),
            GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
    }
}

void URTSCommandComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (bAutoExecuteCommands)
    {
        UpdateCommandExecution(DeltaTime);
    }
}

bool URTSCommandComponent::IssueCommand(const FRTSCommand& Command, bool bClearQueue)
{
    if (!CanExecuteCommand(Command))
    {
        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSCommandComponent: Cannot execute command of type %s"),
                *UEnum::GetValueAsString(Command.CommandType));
        }
        return false;
    }

    // Check if we can interrupt current command
    bool bInterrupted = false;
    if (CommandQueue.HasCommands() && !bClearQueue)
    {
        if (CanInterruptCurrentCommand(Command.Priority))
        {
            bInterrupted = true;
            if (bEnableDebugLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("RTSCommandComponent: Interrupting current command with higher priority command"));
            }
        }
        else if (Command.Priority < ERTSCommandPriority::High)
        {
            // Lower priority command cannot interrupt, reject it
            if (bEnableDebugLogging)
            {
                UE_LOG(LogTemp, Warning, TEXT("RTSCommandComponent: Command rejected - insufficient priority to interrupt"));
            }
            return false;
        }
    }

    // Create a copy of the command with timestamp
    FRTSCommand NewCommand = Command;
    NewCommand.CommandTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Add command to queue
    CommandQueue.AddCommand(NewCommand, bClearQueue);

    // Broadcast events
    BroadcastCommandReceived(NewCommand, bInterrupted);
    OnCommandQueueChanged.Broadcast(CommandQueue.GetCommandCount());

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCommandComponent: Command issued - Type: %s, Priority: %s, Queue Size: %d"),
            *UEnum::GetValueAsString(Command.CommandType),
            *UEnum::GetValueAsString(Command.Priority),
            CommandQueue.GetCommandCount());
    }

    return true;
}

bool URTSCommandComponent::IssueMoveCommand(const FVector& TargetLocation, ERTSCommandPriority Priority, bool bClearQueue)
{
    FRTSCommand Command;
    Command.CommandType = ERTSCommandType::Move;
    Command.Priority = Priority;
    Command.TargetLocation = TargetLocation;
    Command.bInterruptible = true;

    return IssueCommand(Command, bClearQueue);
}

bool URTSCommandComponent::IssueAttackCommand(ARTSBaseActor* Target, ERTSCommandPriority Priority, bool bClearQueue)
{
    if (!Target)
    {
        return false;
    }

    FRTSCommand Command;
    Command.CommandType = ERTSCommandType::AttackTarget;
    Command.Priority = Priority;
    Command.TargetActor = Target;
    Command.TargetLocation = Target->GetActorLocation();
    Command.bInterruptible = true;

    return IssueCommand(Command, bClearQueue);
}

bool URTSCommandComponent::IssueAttackMoveCommand(const FVector& TargetLocation, ERTSCommandPriority Priority, bool bClearQueue)
{
    FRTSCommand Command;
    Command.CommandType = ERTSCommandType::AttackMove;
    Command.Priority = Priority;
    Command.TargetLocation = TargetLocation;
    Command.bInterruptible = true;

    return IssueCommand(Command, bClearQueue);
}

bool URTSCommandComponent::IssueStopCommand(ERTSCommandPriority Priority)
{
    FRTSCommand Command;
    Command.CommandType = ERTSCommandType::Stop;
    Command.Priority = Priority;
    Command.bInterruptible = false;

    return IssueCommand(Command, true); // Stop always clears queue
}

bool URTSCommandComponent::IssueHoldCommand(ERTSCommandPriority Priority)
{
    FRTSCommand Command;
    Command.CommandType = ERTSCommandType::Hold;
    Command.Priority = Priority;
    Command.bInterruptible = true;

    return IssueCommand(Command, true); // Hold clears queue but allows interruption
}

bool URTSCommandComponent::IssuePatrolCommand(const TArray<FVector>& PatrolPoints, ERTSCommandPriority Priority, bool bClearQueue)
{
    if (PatrolPoints.Num() < 2)
    {
        return false;
    }

    FRTSCommand Command;
    Command.CommandType = ERTSCommandType::Patrol;
    Command.Priority = Priority;
    Command.WaypointLocations = PatrolPoints;
    Command.TargetLocation = PatrolPoints[0];
    Command.bInterruptible = true;

    return IssueCommand(Command, bClearQueue);
}

bool URTSCommandComponent::IssueFollowCommand(ARTSBaseActor* Target, ERTSCommandPriority Priority, bool bClearQueue)
{
    if (!Target)
    {
        return false;
    }

    FRTSCommand Command;
    Command.CommandType = ERTSCommandType::Follow;
    Command.Priority = Priority;
    Command.TargetActor = Target;
    Command.TargetLocation = Target->GetActorLocation();
    Command.bInterruptible = true;

    return IssueCommand(Command, bClearQueue);
}

bool URTSCommandComponent::IssueFormationCommand(ERTSFormationCommandType FormationType, const FVector& FormationCenter, const FVector& FormationOffset, ARTSUnit* FormationLeader)
{
    FRTSCommand Command;
    Command.CommandType = ERTSCommandType::Formation;
    Command.Priority = ERTSCommandPriority::High; // Formation commands are high priority
    Command.FormationType = FormationType;
    Command.TargetLocation = FormationCenter;
    Command.FormationOffset = FormationOffset;
    Command.FormationLeader = FormationLeader;
    Command.bInterruptible = true;

    return IssueCommand(Command, false); // Don't clear queue, formation is additive
}

void URTSCommandComponent::ClearCommandQueue()
{
    CommandQueue.ClearQueue();
    bIsExecutingCommand = false;
    OnCommandQueueChanged.Broadcast(0);

    if (bEnableDebugLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("RTSCommandComponent: Command queue cleared"));
    }
}

bool URTSCommandComponent::CancelCurrentCommand()
{
    if (CommandQueue.HasCommands())
    {
        FRTSCommand* CurrentCommand = CommandQueue.GetCurrentCommand();
        if (CurrentCommand)
        {
            BroadcastCommandCompleted(*CurrentCommand, ERTSCommandStatus::Cancelled);
        }

        CommandQueue.CancelCurrentCommand();
        bIsExecutingCommand = false;
        OnCommandQueueChanged.Broadcast(CommandQueue.GetCommandCount());

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSCommandComponent: Current command cancelled"));
        }

        return true;
    }

    return false;
}

bool URTSCommandComponent::RemoveCommand(int32 CommandIndex)
{
    if (CommandQueue.RemoveCommand(CommandIndex))
    {
        OnCommandQueueChanged.Broadcast(CommandQueue.GetCommandCount());

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSCommandComponent: Command at index %d removed"), CommandIndex);
        }

        return true;
    }

    return false;
}

FRTSCommand URTSCommandComponent::GetCurrentCommand() const
{
    const FRTSCommand* CurrentCommand = CommandQueue.GetCurrentCommand();
    return CurrentCommand ? *CurrentCommand : FRTSCommand();
}

bool URTSCommandComponent::HasCommands() const
{
    return CommandQueue.HasCommands();
}

int32 URTSCommandComponent::GetCommandQueueSize() const
{
    return CommandQueue.GetCommandCount();
}

TArray<FRTSCommand> URTSCommandComponent::GetAllCommands() const
{
    return CommandQueue.Commands;
}

void URTSCommandComponent::ExecuteCurrentCommand()
{
    FRTSCommand* CurrentCommand = CommandQueue.GetCurrentCommand();
    if (!CurrentCommand)
    {
        return;
    }

    if (CurrentCommand->Status == ERTSCommandStatus::Pending)
    {
        if (StartCommandExecution(*CurrentCommand))
        {
            CurrentCommand->Status = ERTSCommandStatus::Executing;
            CurrentCommand->ExecutionStartTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
            bIsExecutingCommand = true;

            if (bEnableDebugLogging)
            {
                UE_LOG(LogTemp, Log, TEXT("RTSCommandComponent: Started executing command: %s"),
                    *UEnum::GetValueAsString(CurrentCommand->CommandType));
            }
        }
        else
        {
            FailCurrentCommand();
        }
    }
}

void URTSCommandComponent::CompleteCurrentCommand()
{
    FRTSCommand* CurrentCommand = CommandQueue.GetCurrentCommand();
    if (CurrentCommand)
    {
        BroadcastCommandCompleted(*CurrentCommand, ERTSCommandStatus::Completed);
        CommandQueue.CompleteCurrentCommand();
        bIsExecutingCommand = false;
        OnCommandQueueChanged.Broadcast(CommandQueue.GetCommandCount());

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("RTSCommandComponent: Command completed: %s"),
                *UEnum::GetValueAsString(CurrentCommand->CommandType));
        }
    }
}

void URTSCommandComponent::FailCurrentCommand()
{
    FRTSCommand* CurrentCommand = CommandQueue.GetCurrentCommand();
    if (CurrentCommand)
    {
        BroadcastCommandCompleted(*CurrentCommand, ERTSCommandStatus::Failed);
        CommandQueue.FailCurrentCommand();
        bIsExecutingCommand = false;
        OnCommandQueueChanged.Broadcast(CommandQueue.GetCommandCount());

        if (bEnableDebugLogging)
        {
            UE_LOG(LogTemp, Warning, TEXT("RTSCommandComponent: Command failed: %s"),
                *UEnum::GetValueAsString(CurrentCommand->CommandType));
        }
    }
}

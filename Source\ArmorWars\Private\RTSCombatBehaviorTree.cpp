#include "RTSCombatBehaviorTree.h"
#include "RTSBehaviorTreeComponent.h"
#include "RTSUnit.h"
#include "RTSCommandComponent.h"
#include "RTSTeamManager.h"
#include "RTSFormationManager.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"

// URTSCombatBehaviorTreeFactory Implementation

URTSCombatBehaviorTreeFactory::URTSCombatBehaviorTreeFactory()
{
}

URTSBehaviorNode* URTSCombatBehaviorTreeFactory::CreateBasicCombatTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Basic Combat Root");
    
    // Target acquisition and engagement
    URTSCompositeNode* CombatSequence = NewObject<URTSCompositeNode>();
    CombatSequence->CompositeType = ERTSCompositeType::Sequence;
    CombatSequence->NodeName = TEXT("Combat Sequence");
    
    // Find and select target
    URTSAdvancedTargetSelectionTask* TargetSelection = NewObject<URTSAdvancedTargetSelectionTask>();
    CombatSequence->AddChildNode(TargetSelection);
    
    // Engage target
    URTSEnhancedCombatTask* CombatTask = NewObject<URTSEnhancedCombatTask>();
    CombatTask->bAutoSelectTargets = false; // Use selected target
    CombatTask->TargetActorKey = TEXT("SelectedTarget");
    CombatSequence->AddChildNode(CombatTask);
    
    RootSelector->AddChildNode(CombatSequence);
    
    return RootSelector;
}

URTSBehaviorNode* URTSCombatBehaviorTreeFactory::CreateTacticalCombatTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Tactical Combat Root");
    
    // Tactical advantage check
    URTSCompositeNode* AdvantageSequence = NewObject<URTSCompositeNode>();
    AdvantageSequence->CompositeType = ERTSCompositeType::Sequence;
    AdvantageSequence->NodeName = TEXT("Tactical Advantage");
    
    URTSTacticalAdvantageCondition* AdvantageCheck = NewObject<URTSTacticalAdvantageCondition>();
    AdvantageSequence->AddChildNode(AdvantageCheck);
    
    // Aggressive engagement when advantage
    AdvantageSequence->AddChildNode(CreateEngagementSequence());
    RootSelector->AddChildNode(AdvantageSequence);
    
    // Defensive positioning when disadvantaged
    URTSCompositeNode* DefensiveSequence = NewObject<URTSCompositeNode>();
    DefensiveSequence->CompositeType = ERTSCompositeType::Sequence;
    DefensiveSequence->NodeName = TEXT("Defensive Positioning");
    
    URTSTacticalPositioningTask* Positioning = NewObject<URTSTacticalPositioningTask>();
    Positioning->bSeekCover = true;
    DefensiveSequence->AddChildNode(Positioning);
    
    DefensiveSequence->AddChildNode(CreateReturnFireSequence());
    RootSelector->AddChildNode(DefensiveSequence);
    
    return RootSelector;
}

URTSBehaviorNode* URTSCombatBehaviorTreeFactory::CreateSupportCombatTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Support Combat Root");
    
    // Coordinated attack sequence
    URTSCompositeNode* CoordinatedSequence = NewObject<URTSCompositeNode>();
    CoordinatedSequence->CompositeType = ERTSCompositeType::Sequence;
    CoordinatedSequence->NodeName = TEXT("Coordinated Attack");
    
    URTSAdvancedTargetSelectionTask* TargetSelection = NewObject<URTSAdvancedTargetSelectionTask>();
    CoordinatedSequence->AddChildNode(TargetSelection);
    
    URTSCoordinatedAttackTask* CoordinatedAttack = NewObject<URTSCoordinatedAttackTask>();
    CoordinatedSequence->AddChildNode(CoordinatedAttack);
    
    RootSelector->AddChildNode(CoordinatedSequence);
    
    // Fallback to return fire
    RootSelector->AddChildNode(CreateReturnFireSequence());
    
    return RootSelector;
}

URTSBehaviorNode* URTSCombatBehaviorTreeFactory::CreateAggressiveCombatTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Aggressive Combat Root");
    
    // Always engage when enemies in range
    URTSCompositeNode* AggressiveSequence = NewObject<URTSCompositeNode>();
    AggressiveSequence->CompositeType = ERTSCompositeType::Sequence;
    AggressiveSequence->NodeName = TEXT("Aggressive Engagement");
    
    URTSEnemiesInRangeCondition* EnemiesInRange = NewObject<URTSEnemiesInRangeCondition>();
    EnemiesInRange->bUseWeaponRange = true;
    AggressiveSequence->AddChildNode(EnemiesInRange);
    
    URTSAdvancedTargetSelectionTask* TargetSelection = NewObject<URTSAdvancedTargetSelectionTask>();
    TargetSelection->ThreatPriorityWeight = 1.0f; // Less threat consideration
    TargetSelection->DistancePriorityWeight = 3.0f; // Prefer closer targets
    AggressiveSequence->AddChildNode(TargetSelection);
    
    URTSEnhancedCombatTask* CombatTask = NewObject<URTSEnhancedCombatTask>();
    CombatTask->bPursueTargets = true;
    CombatTask->TargetActorKey = TEXT("SelectedTarget");
    AggressiveSequence->AddChildNode(CombatTask);
    
    RootSelector->AddChildNode(AggressiveSequence);
    
    return RootSelector;
}

URTSBehaviorNode* URTSCombatBehaviorTreeFactory::CreateDefensiveCombatTree()
{
    // Create root selector
    URTSCompositeNode* RootSelector = NewObject<URTSCompositeNode>();
    RootSelector->CompositeType = ERTSCompositeType::Selector;
    RootSelector->NodeName = TEXT("Defensive Combat Root");
    
    // Return fire when under attack
    URTSCompositeNode* DefenseSequence = NewObject<URTSCompositeNode>();
    DefenseSequence->CompositeType = ERTSCompositeType::Sequence;
    DefenseSequence->NodeName = TEXT("Defensive Response");
    
    URTSUnderAttackCondition* UnderAttack = NewObject<URTSUnderAttackCondition>();
    DefenseSequence->AddChildNode(UnderAttack);
    
    URTSReturnFireTask* ReturnFire = NewObject<URTSReturnFireTask>();
    ReturnFire->bPrioritizeAttackers = true;
    DefenseSequence->AddChildNode(ReturnFire);
    
    RootSelector->AddChildNode(DefenseSequence);
    
    // Tactical positioning
    URTSTacticalPositioningTask* Positioning = NewObject<URTSTacticalPositioningTask>();
    Positioning->bSeekCover = true;
    Positioning->bMaintainFormationPosition = true;
    RootSelector->AddChildNode(Positioning);
    
    return RootSelector;
}

URTSCompositeNode* URTSCombatBehaviorTreeFactory::CreateTargetAcquisitionSequence()
{
    URTSCompositeNode* TargetSequence = NewObject<URTSCompositeNode>();
    TargetSequence->CompositeType = ERTSCompositeType::Sequence;
    TargetSequence->NodeName = TEXT("Target Acquisition");
    
    URTSEnemiesInRangeCondition* EnemiesInRange = NewObject<URTSEnemiesInRangeCondition>();
    TargetSequence->AddChildNode(EnemiesInRange);
    
    URTSAdvancedTargetSelectionTask* TargetSelection = NewObject<URTSAdvancedTargetSelectionTask>();
    TargetSequence->AddChildNode(TargetSelection);
    
    return TargetSequence;
}

URTSCompositeNode* URTSCombatBehaviorTreeFactory::CreateEngagementSequence()
{
    URTSCompositeNode* EngagementSequence = NewObject<URTSCompositeNode>();
    EngagementSequence->CompositeType = ERTSCompositeType::Sequence;
    EngagementSequence->NodeName = TEXT("Engagement");
    
    EngagementSequence->AddChildNode(CreateTargetAcquisitionSequence());
    
    URTSEnhancedCombatTask* CombatTask = NewObject<URTSEnhancedCombatTask>();
    CombatTask->TargetActorKey = TEXT("SelectedTarget");
    EngagementSequence->AddChildNode(CombatTask);
    
    return EngagementSequence;
}

URTSCompositeNode* URTSCombatBehaviorTreeFactory::CreateTacticalPositioningSequence()
{
    URTSCompositeNode* PositioningSequence = NewObject<URTSCompositeNode>();
    PositioningSequence->CompositeType = ERTSCompositeType::Sequence;
    PositioningSequence->NodeName = TEXT("Tactical Positioning");
    
    URTSAdvancedTargetSelectionTask* TargetSelection = NewObject<URTSAdvancedTargetSelectionTask>();
    PositioningSequence->AddChildNode(TargetSelection);
    
    URTSTacticalPositioningTask* Positioning = NewObject<URTSTacticalPositioningTask>();
    PositioningSequence->AddChildNode(Positioning);
    
    return PositioningSequence;
}

URTSCompositeNode* URTSCombatBehaviorTreeFactory::CreateReturnFireSequence()
{
    URTSCompositeNode* ReturnFireSequence = NewObject<URTSCompositeNode>();
    ReturnFireSequence->CompositeType = ERTSCompositeType::Sequence;
    ReturnFireSequence->NodeName = TEXT("Return Fire");
    
    URTSUnderAttackCondition* UnderAttack = NewObject<URTSUnderAttackCondition>();
    ReturnFireSequence->AddChildNode(UnderAttack);
    
    URTSReturnFireTask* ReturnFire = NewObject<URTSReturnFireTask>();
    ReturnFireSequence->AddChildNode(ReturnFire);
    
    return ReturnFireSequence;
}

URTSCompositeNode* URTSCombatBehaviorTreeFactory::CreateRetreatSequence()
{
    URTSCompositeNode* RetreatSequence = NewObject<URTSCompositeNode>();
    RetreatSequence->CompositeType = ERTSCompositeType::Sequence;
    RetreatSequence->NodeName = TEXT("Retreat");
    
    URTSHealthCondition* LowHealth = NewObject<URTSHealthCondition>();
    LowHealth->HealthThreshold = 0.3f; // 30% health
    LowHealth->bCheckLowHealth = true;
    RetreatSequence->AddChildNode(LowHealth);
    
    // Move away from enemies
    URTSEnhancedMoveTask* RetreatMove = NewObject<URTSEnhancedMoveTask>();
    RetreatMove->TargetLocationKey = TEXT("RetreatLocation");
    RetreatMove->bReturnFireWhileMoving = true;
    RetreatSequence->AddChildNode(RetreatMove);
    
    return RetreatSequence;
}

// URTSAdvancedTargetSelectionTask Implementation

URTSAdvancedTargetSelectionTask::URTSAdvancedTargetSelectionTask()
{
    NodeType = ERTSBehaviorNodeType::Task;
    NodeName = TEXT("Advanced Target Selection");
    NodeDescription = TEXT("Selects optimal targets based on priority system");
    MaxTargetRange = 1000.0f;
    bUseUnitWeaponRange = true;
    HealthPriorityWeight = 2.0f;
    DistancePriorityWeight = 1.0f;
    ThreatPriorityWeight = 3.0f;
    SelectedTargetKey = TEXT("SelectedTarget");
    TargetSwitchCooldown = 3.0f;
    LastTargetSwitchTime = 0.0f;
}

void URTSAdvancedTargetSelectionTask::InitializeNode(URTSBehaviorTreeComponent* BehaviorTreeComponent)
{
    Super::InitializeNode(BehaviorTreeComponent);
    LastTargetSwitchTime = 0.0f;
    CurrentTarget = nullptr;
}

ERTSBehaviorNodeStatus URTSAdvancedTargetSelectionTask::ExecuteNode(URTSBehaviorTreeComponent* BehaviorTreeComponent, float DeltaTime)
{
    if (!BehaviorTreeComponent)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    ARTSUnit* Unit = Cast<ARTSUnit>(GetControlledPawn(BehaviorTreeComponent));
    if (!Unit)
    {
        return ERTSBehaviorNodeStatus::Failure;
    }

    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Check if we should switch targets
    bool bShouldSelectNewTarget = false;
    if (!CurrentTarget.IsValid() || !CurrentTarget->IsAlive())
    {
        bShouldSelectNewTarget = true;
    }
    else if (CurrentTime - LastTargetSwitchTime > TargetSwitchCooldown)
    {
        bShouldSelectNewTarget = true;
    }

    if (bShouldSelectNewTarget)
    {
        UWorld* World = Unit->GetWorld();
        if (!World)
        {
            return ERTSBehaviorNodeStatus::Failure;
        }

        URTSTeamManager* TeamManager = World->GetSubsystem<URTSTeamManager>();
        if (!TeamManager)
        {
            return ERTSBehaviorNodeStatus::Failure;
        }

        // Determine range to use
        float RangeToUse = bUseUnitWeaponRange ? Unit->GetMaxAttackRange() : MaxTargetRange;
        if (RangeToUse <= 0.0f)
        {
            RangeToUse = MaxTargetRange;
        }

        // Find all enemies in range
        TArray<ARTSBaseActor*> EnemiesInRange = TeamManager->FindEnemiesInRange(Unit->TeamID, Unit->GetActorLocation(), RangeToUse);

        if (EnemiesInRange.Num() == 0)
        {
            return ERTSBehaviorNodeStatus::Failure;
        }

        // Find best target based on priority
        ARTSBaseActor* BestTarget = nullptr;
        float BestPriority = -1.0f;

        for (ARTSBaseActor* Enemy : EnemiesInRange)
        {
            if (IsValidTarget(Unit, Enemy))
            {
                float Priority = CalculateTargetPriority(Unit, Enemy);
                if (Priority > BestPriority)
                {
                    BestPriority = Priority;
                    BestTarget = Enemy;
                }
            }
        }

        if (BestTarget)
        {
            CurrentTarget = BestTarget;
            BehaviorTreeComponent->SetBlackboardObject(SelectedTargetKey, BestTarget);
            LastTargetSwitchTime = CurrentTime;
            return ERTSBehaviorNodeStatus::Success;
        }
    }
    else if (CurrentTarget.IsValid())
    {
        // Keep current target
        BehaviorTreeComponent->SetBlackboardObject(SelectedTargetKey, CurrentTarget.Get());
        return ERTSBehaviorNodeStatus::Success;
    }

    return ERTSBehaviorNodeStatus::Failure;
}

float URTSAdvancedTargetSelectionTask::CalculateTargetPriority(ARTSUnit* Unit, ARTSBaseActor* Target)
{
    if (!Unit || !Target)
    {
        return 0.0f;
    }

    float Priority = 1.0f;

    // Health priority (lower health = higher priority)
    if (Target->GetMaxHealth() > 0.0f)
    {
        float HealthRatio = Target->GetCurrentHealth() / Target->GetMaxHealth();
        Priority += (1.0f - HealthRatio) * HealthPriorityWeight;
    }

    // Distance priority (closer = higher priority)
    float Distance = FVector::Dist(Unit->GetActorLocation(), Target->GetActorLocation());
    float MaxRange = bUseUnitWeaponRange ? Unit->GetMaxAttackRange() : MaxTargetRange;
    if (MaxRange > 0.0f)
    {
        float DistanceRatio = 1.0f - (Distance / MaxRange);
        Priority += DistanceRatio * DistancePriorityWeight;
    }

    // Threat priority
    float ThreatLevel = CalculateThreatLevel(Unit, Target);
    Priority += ThreatLevel * ThreatPriorityWeight;

    return Priority;
}

float URTSAdvancedTargetSelectionTask::CalculateThreatLevel(ARTSUnit* Unit, ARTSBaseActor* Target)
{
    if (!Unit || !Target)
    {
        return 0.0f;
    }

    float ThreatLevel = 1.0f;

    // Check if target can attack us
    if (ARTSUnit* TargetUnit = Cast<ARTSUnit>(Target))
    {
        if (TargetUnit->HasWeapons())
        {
            float TargetRange = TargetUnit->GetMaxAttackRange();
            float Distance = FVector::Dist(Unit->GetActorLocation(), Target->GetActorLocation());

            if (Distance <= TargetRange)
            {
                ThreatLevel += 2.0f; // High threat if target can attack us
            }

            // Consider target's damage potential
            ThreatLevel += TargetRange / 1000.0f; // Normalize range
        }
    }

    return ThreatLevel;
}

bool URTSAdvancedTargetSelectionTask::IsValidTarget(ARTSUnit* Unit, ARTSBaseActor* Target)
{
    if (!Unit || !Target)
    {
        return false;
    }

    if (!Target->IsAlive())
    {
        return false;
    }

    // Check if it's an enemy
    UWorld* World = Unit->GetWorld();
    if (World)
    {
        if (URTSTeamManager* TeamManager = World->GetSubsystem<URTSTeamManager>())
        {
            return TeamManager->AreTeamsEnemies(Unit->TeamID, Target->TeamID);
        }
    }

    return false;
}

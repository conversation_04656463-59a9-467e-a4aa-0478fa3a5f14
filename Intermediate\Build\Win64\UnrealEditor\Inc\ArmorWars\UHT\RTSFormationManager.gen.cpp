// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSFormationManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSFormationManager() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFormationManager();
ARMORWARS_API UClass* Z_Construct_UClass_URTSFormationManager_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSCommandPriority();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSFormationType();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSFormationGroup();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FRTSFormationGroup ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSFormationGroup;
class UScriptStruct* FRTSFormationGroup::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSFormationGroup.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSFormationGroup.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSFormationGroup, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSFormationGroup"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSFormationGroup.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSFormationGroup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation data for a group of units\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation data for a group of units" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroupID_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unique ID for this formation group\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unique ID for this formation group" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Leader_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation leader (elected automatically)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation leader (elected automatically)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Units_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// All units in the formation\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "All units in the formation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationType_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation type\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationCenter_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation center (leader's target location)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation center (leader's target location)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationDirection_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation direction (forward vector)\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation direction (forward vector)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnitSpacing_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unit spacing\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unit spacing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether the formation is active\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether the formation is active" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation creation time\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation creation time" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_Leader;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Units_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Units;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FormationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FormationType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FormationCenter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FormationDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnitSpacing;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSFormationGroup>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSFormationGroup, GroupID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroupID_MetaData), NewProp_GroupID_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_Leader = { "Leader", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSFormationGroup, Leader), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Leader_MetaData), NewProp_Leader_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_Units_Inner = { "Units", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_Units = { "Units", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSFormationGroup, Units), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Units_MetaData), NewProp_Units_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_FormationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_FormationType = { "FormationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSFormationGroup, FormationType), Z_Construct_UEnum_ArmorWars_ERTSFormationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationType_MetaData), NewProp_FormationType_MetaData) }; // 1472012862
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_FormationCenter = { "FormationCenter", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSFormationGroup, FormationCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationCenter_MetaData), NewProp_FormationCenter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_FormationDirection = { "FormationDirection", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSFormationGroup, FormationDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationDirection_MetaData), NewProp_FormationDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_UnitSpacing = { "UnitSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSFormationGroup, UnitSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnitSpacing_MetaData), NewProp_UnitSpacing_MetaData) };
void Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FRTSFormationGroup*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSFormationGroup), &Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSFormationGroup, CreationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_Leader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_Units_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_Units,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_FormationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_FormationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_FormationCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_FormationDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_UnitSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewProp_CreationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSFormationGroup",
	Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::PropPointers),
	sizeof(FRTSFormationGroup),
	alignof(FRTSFormationGroup),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSFormationGroup()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSFormationGroup.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSFormationGroup.InnerSingleton, Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSFormationGroup.InnerSingleton;
}
// ********** End ScriptStruct FRTSFormationGroup **************************************************

// ********** Begin Delegate FOnFormationCreated ***************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnFormationCreated_Parms
	{
		int32 GroupID;
		FRTSFormationGroup FormationGroup;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate for formation events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate for formation events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationGroup_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FormationGroup;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnFormationCreated_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::NewProp_FormationGroup = { "FormationGroup", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnFormationCreated_Parms, FormationGroup), Z_Construct_UScriptStruct_FRTSFormationGroup, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationGroup_MetaData), NewProp_FormationGroup_MetaData) }; // 3472923679
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::NewProp_FormationGroup,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnFormationCreated__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::_Script_ArmorWars_eventOnFormationCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::_Script_ArmorWars_eventOnFormationCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnFormationCreated_DelegateWrapper(const FMulticastScriptDelegate& OnFormationCreated, int32 GroupID, FRTSFormationGroup const& FormationGroup)
{
	struct _Script_ArmorWars_eventOnFormationCreated_Parms
	{
		int32 GroupID;
		FRTSFormationGroup FormationGroup;
	};
	_Script_ArmorWars_eventOnFormationCreated_Parms Parms;
	Parms.GroupID=GroupID;
	Parms.FormationGroup=FormationGroup;
	OnFormationCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFormationCreated *****************************************************

// ********** Begin Delegate FOnFormationDisbanded *************************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnFormationDisbanded_Parms
	{
		int32 GroupID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnFormationDisbanded_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::NewProp_GroupID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnFormationDisbanded__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::_Script_ArmorWars_eventOnFormationDisbanded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::_Script_ArmorWars_eventOnFormationDisbanded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnFormationDisbanded_DelegateWrapper(const FMulticastScriptDelegate& OnFormationDisbanded, int32 GroupID)
{
	struct _Script_ArmorWars_eventOnFormationDisbanded_Parms
	{
		int32 GroupID;
	};
	_Script_ArmorWars_eventOnFormationDisbanded_Parms Parms;
	Parms.GroupID=GroupID;
	OnFormationDisbanded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFormationDisbanded ***************************************************

// ********** Begin Delegate FOnFormationLeaderChanged *********************************************
struct Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics
{
	struct _Script_ArmorWars_eventOnFormationLeaderChanged_Parms
	{
		int32 GroupID;
		ARTSUnit* NewLeader;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewLeader;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnFormationLeaderChanged_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::NewProp_NewLeader = { "NewLeader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_ArmorWars_eventOnFormationLeaderChanged_Parms, NewLeader), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::NewProp_NewLeader,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_ArmorWars, nullptr, "OnFormationLeaderChanged__DelegateSignature", Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnFormationLeaderChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::_Script_ArmorWars_eventOnFormationLeaderChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnFormationLeaderChanged_DelegateWrapper(const FMulticastScriptDelegate& OnFormationLeaderChanged, int32 GroupID, ARTSUnit* NewLeader)
{
	struct _Script_ArmorWars_eventOnFormationLeaderChanged_Parms
	{
		int32 GroupID;
		ARTSUnit* NewLeader;
	};
	_Script_ArmorWars_eventOnFormationLeaderChanged_Parms Parms;
	Parms.GroupID=GroupID;
	Parms.NewLeader=NewLeader;
	OnFormationLeaderChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFormationLeaderChanged ***********************************************

// ********** Begin Class URTSFormationManager Function AddUnitToFormation *************************
struct Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics
{
	struct RTSFormationManager_eventAddUnitToFormation_Parms
	{
		int32 GroupID;
		ARTSUnit* Unit;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventAddUnitToFormation_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventAddUnitToFormation_Parms, Unit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSFormationManager_eventAddUnitToFormation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSFormationManager_eventAddUnitToFormation_Parms), &Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "AddUnitToFormation", Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::RTSFormationManager_eventAddUnitToFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::RTSFormationManager_eventAddUnitToFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execAddUnitToFormation)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_GET_OBJECT(ARTSUnit,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddUnitToFormation(Z_Param_GroupID,Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function AddUnitToFormation ***************************

// ********** Begin Class URTSFormationManager Function CalculateFormationPositions ****************
struct Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics
{
	struct RTSFormationManager_eventCalculateFormationPositions_Parms
	{
		int32 GroupID;
		FVector FormationCenter;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation position calculation\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation position calculation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationCenter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FormationCenter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventCalculateFormationPositions_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::NewProp_FormationCenter = { "FormationCenter", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventCalculateFormationPositions_Parms, FormationCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationCenter_MetaData), NewProp_FormationCenter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventCalculateFormationPositions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::NewProp_FormationCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "CalculateFormationPositions", Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::RTSFormationManager_eventCalculateFormationPositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::RTSFormationManager_eventCalculateFormationPositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execCalculateFormationPositions)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_FormationCenter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->CalculateFormationPositions(Z_Param_GroupID,Z_Param_Out_FormationCenter);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function CalculateFormationPositions ******************

// ********** Begin Class URTSFormationManager Function CalculateUnitFormationOffset ***************
struct Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics
{
	struct RTSFormationManager_eventCalculateUnitFormationOffset_Parms
	{
		int32 GroupID;
		ARTSUnit* Unit;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventCalculateUnitFormationOffset_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventCalculateUnitFormationOffset_Parms, Unit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventCalculateUnitFormationOffset_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "CalculateUnitFormationOffset", Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::RTSFormationManager_eventCalculateUnitFormationOffset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::RTSFormationManager_eventCalculateUnitFormationOffset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execCalculateUnitFormationOffset)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_GET_OBJECT(ARTSUnit,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateUnitFormationOffset(Z_Param_GroupID,Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function CalculateUnitFormationOffset *****************

// ********** Begin Class URTSFormationManager Function ChangeFormationLeader **********************
struct Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics
{
	struct RTSFormationManager_eventChangeFormationLeader_Parms
	{
		int32 GroupID;
		ARTSUnit* NewLeader;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewLeader;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventChangeFormationLeader_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::NewProp_NewLeader = { "NewLeader", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventChangeFormationLeader_Parms, NewLeader), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSFormationManager_eventChangeFormationLeader_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSFormationManager_eventChangeFormationLeader_Parms), &Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::NewProp_NewLeader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "ChangeFormationLeader", Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::RTSFormationManager_eventChangeFormationLeader_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::RTSFormationManager_eventChangeFormationLeader_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execChangeFormationLeader)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_GET_OBJECT(ARTSUnit,Z_Param_NewLeader);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ChangeFormationLeader(Z_Param_GroupID,Z_Param_NewLeader);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function ChangeFormationLeader ************************

// ********** Begin Class URTSFormationManager Function CleanupInvalidFormations *******************
struct Z_Construct_UFunction_URTSFormationManager_CleanupInvalidFormations_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_CleanupInvalidFormations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "CleanupInvalidFormations", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_CleanupInvalidFormations_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_CleanupInvalidFormations_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSFormationManager_CleanupInvalidFormations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_CleanupInvalidFormations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execCleanupInvalidFormations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupInvalidFormations();
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function CleanupInvalidFormations *********************

// ********** Begin Class URTSFormationManager Function CreateFormation ****************************
struct Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics
{
	struct RTSFormationManager_eventCreateFormation_Parms
	{
		TArray<ARTSUnit*> Units;
		ERTSFormationType FormationType;
		float UnitSpacing;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation creation and management\n" },
#endif
		{ "CPP_Default_FormationType", "Line" },
		{ "CPP_Default_UnitSpacing", "200.000000" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation creation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Units_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Units_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Units;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FormationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FormationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnitSpacing;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_Units_Inner = { "Units", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_Units = { "Units", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventCreateFormation_Parms, Units), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Units_MetaData), NewProp_Units_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_FormationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_FormationType = { "FormationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventCreateFormation_Parms, FormationType), Z_Construct_UEnum_ArmorWars_ERTSFormationType, METADATA_PARAMS(0, nullptr) }; // 1472012862
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_UnitSpacing = { "UnitSpacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventCreateFormation_Parms, UnitSpacing), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventCreateFormation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_Units_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_Units,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_FormationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_FormationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_UnitSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "CreateFormation", Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::RTSFormationManager_eventCreateFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::RTSFormationManager_eventCreateFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_CreateFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_CreateFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execCreateFormation)
{
	P_GET_TARRAY_REF(ARTSUnit*,Z_Param_Out_Units);
	P_GET_ENUM(ERTSFormationType,Z_Param_FormationType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_UnitSpacing);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->CreateFormation(Z_Param_Out_Units,ERTSFormationType(Z_Param_FormationType),Z_Param_UnitSpacing);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function CreateFormation ******************************

// ********** Begin Class URTSFormationManager Function DisbandFormation ***************************
struct Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics
{
	struct RTSFormationManager_eventDisbandFormation_Parms
	{
		int32 GroupID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventDisbandFormation_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSFormationManager_eventDisbandFormation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSFormationManager_eventDisbandFormation_Parms), &Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "DisbandFormation", Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::RTSFormationManager_eventDisbandFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::RTSFormationManager_eventDisbandFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_DisbandFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_DisbandFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execDisbandFormation)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DisbandFormation(Z_Param_GroupID);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function DisbandFormation *****************************

// ********** Begin Class URTSFormationManager Function DoesFormationExist *************************
struct Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics
{
	struct RTSFormationManager_eventDoesFormationExist_Parms
	{
		int32 GroupID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation queries\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation queries" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventDoesFormationExist_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSFormationManager_eventDoesFormationExist_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSFormationManager_eventDoesFormationExist_Parms), &Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "DoesFormationExist", Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::RTSFormationManager_eventDoesFormationExist_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::RTSFormationManager_eventDoesFormationExist_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_DoesFormationExist()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_DoesFormationExist_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execDoesFormationExist)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesFormationExist(Z_Param_GroupID);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function DoesFormationExist ***************************

// ********** Begin Class URTSFormationManager Function ElectSquadLeader ***************************
struct Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics
{
	struct RTSFormationManager_eventElectSquadLeader_Parms
	{
		TArray<ARTSUnit*> Units;
		ARTSUnit* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Squad leader election\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Squad leader election" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Units_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Units_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Units;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::NewProp_Units_Inner = { "Units", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::NewProp_Units = { "Units", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventElectSquadLeader_Parms, Units), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Units_MetaData), NewProp_Units_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventElectSquadLeader_Parms, ReturnValue), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::NewProp_Units_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::NewProp_Units,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "ElectSquadLeader", Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::RTSFormationManager_eventElectSquadLeader_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::RTSFormationManager_eventElectSquadLeader_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execElectSquadLeader)
{
	P_GET_TARRAY_REF(ARTSUnit*,Z_Param_Out_Units);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSUnit**)Z_Param__Result=P_THIS->ElectSquadLeader(Z_Param_Out_Units);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function ElectSquadLeader *****************************

// ********** Begin Class URTSFormationManager Function GetAllFormationIDs *************************
struct Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics
{
	struct RTSFormationManager_eventGetAllFormationIDs_Parms
	{
		TArray<int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventGetAllFormationIDs_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "GetAllFormationIDs", Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::RTSFormationManager_eventGetAllFormationIDs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::RTSFormationManager_eventGetAllFormationIDs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execGetAllFormationIDs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<int32>*)Z_Param__Result=P_THIS->GetAllFormationIDs();
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function GetAllFormationIDs ***************************

// ********** Begin Class URTSFormationManager Function GetFormationForUnit ************************
struct Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics
{
	struct RTSFormationManager_eventGetFormationForUnit_Parms
	{
		ARTSUnit* Unit;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventGetFormationForUnit_Parms, Unit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventGetFormationForUnit_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "GetFormationForUnit", Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::RTSFormationManager_eventGetFormationForUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::RTSFormationManager_eventGetFormationForUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execGetFormationForUnit)
{
	P_GET_OBJECT(ARTSUnit,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetFormationForUnit(Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function GetFormationForUnit **************************

// ********** Begin Class URTSFormationManager Function GetFormationGroup **************************
struct Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics
{
	struct RTSFormationManager_eventGetFormationGroup_Parms
	{
		int32 GroupID;
		FRTSFormationGroup ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventGetFormationGroup_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventGetFormationGroup_Parms, ReturnValue), Z_Construct_UScriptStruct_FRTSFormationGroup, METADATA_PARAMS(0, nullptr) }; // 3472923679
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "GetFormationGroup", Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::RTSFormationManager_eventGetFormationGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::RTSFormationManager_eventGetFormationGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_GetFormationGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_GetFormationGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execGetFormationGroup)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FRTSFormationGroup*)Z_Param__Result=P_THIS->GetFormationGroup(Z_Param_GroupID);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function GetFormationGroup ****************************

// ********** Begin Class URTSFormationManager Function GetFormationLeader *************************
struct Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics
{
	struct RTSFormationManager_eventGetFormationLeader_Parms
	{
		int32 GroupID;
		ARTSUnit* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventGetFormationLeader_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventGetFormationLeader_Parms, ReturnValue), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "GetFormationLeader", Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::RTSFormationManager_eventGetFormationLeader_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::RTSFormationManager_eventGetFormationLeader_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_GetFormationLeader()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_GetFormationLeader_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execGetFormationLeader)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSUnit**)Z_Param__Result=P_THIS->GetFormationLeader(Z_Param_GroupID);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function GetFormationLeader ***************************

// ********** Begin Class URTSFormationManager Function GetFormationUnits **************************
struct Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics
{
	struct RTSFormationManager_eventGetFormationUnits_Parms
	{
		int32 GroupID;
		TArray<ARTSUnit*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventGetFormationUnits_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventGetFormationUnits_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "GetFormationUnits", Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::RTSFormationManager_eventGetFormationUnits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::RTSFormationManager_eventGetFormationUnits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_GetFormationUnits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_GetFormationUnits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execGetFormationUnits)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSUnit*>*)Z_Param__Result=P_THIS->GetFormationUnits(Z_Param_GroupID);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function GetFormationUnits ****************************

// ********** Begin Class URTSFormationManager Function MoveFormation ******************************
struct Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics
{
	struct RTSFormationManager_eventMoveFormation_Parms
	{
		int32 GroupID;
		FVector TargetLocation;
		ERTSCommandPriority Priority;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation movement\n" },
#endif
		{ "CPP_Default_Priority", "Normal" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation movement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventMoveFormation_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventMoveFormation_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventMoveFormation_Parms, Priority), Z_Construct_UEnum_ArmorWars_ERTSCommandPriority, METADATA_PARAMS(0, nullptr) }; // 136588220
void Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSFormationManager_eventMoveFormation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSFormationManager_eventMoveFormation_Parms), &Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "MoveFormation", Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::RTSFormationManager_eventMoveFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::RTSFormationManager_eventMoveFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_MoveFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_MoveFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execMoveFormation)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TargetLocation);
	P_GET_ENUM(ERTSCommandPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MoveFormation(Z_Param_GroupID,Z_Param_Out_TargetLocation,ERTSCommandPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function MoveFormation ********************************

// ********** Begin Class URTSFormationManager Function RemoveUnitFromFormation ********************
struct Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics
{
	struct RTSFormationManager_eventRemoveUnitFromFormation_Parms
	{
		int32 GroupID;
		ARTSUnit* Unit;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Unit;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventRemoveUnitFromFormation_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::NewProp_Unit = { "Unit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventRemoveUnitFromFormation_Parms, Unit), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSFormationManager_eventRemoveUnitFromFormation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSFormationManager_eventRemoveUnitFromFormation_Parms), &Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::NewProp_Unit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "RemoveUnitFromFormation", Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::RTSFormationManager_eventRemoveUnitFromFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::RTSFormationManager_eventRemoveUnitFromFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execRemoveUnitFromFormation)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_GET_OBJECT(ARTSUnit,Z_Param_Unit);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveUnitFromFormation(Z_Param_GroupID,Z_Param_Unit);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function RemoveUnitFromFormation **********************

// ********** Begin Class URTSFormationManager Function SetFormationSpacing ************************
struct Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics
{
	struct RTSFormationManager_eventSetFormationSpacing_Parms
	{
		int32 GroupID;
		float NewSpacing;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewSpacing;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventSetFormationSpacing_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::NewProp_NewSpacing = { "NewSpacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventSetFormationSpacing_Parms, NewSpacing), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSFormationManager_eventSetFormationSpacing_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSFormationManager_eventSetFormationSpacing_Parms), &Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::NewProp_NewSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "SetFormationSpacing", Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::RTSFormationManager_eventSetFormationSpacing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::RTSFormationManager_eventSetFormationSpacing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execSetFormationSpacing)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewSpacing);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetFormationSpacing(Z_Param_GroupID,Z_Param_NewSpacing);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function SetFormationSpacing **************************

// ********** Begin Class URTSFormationManager Function SetFormationType ***************************
struct Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics
{
	struct RTSFormationManager_eventSetFormationType_Parms
	{
		int32 GroupID;
		ERTSFormationType NewFormationType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewFormationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewFormationType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventSetFormationType_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::NewProp_NewFormationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::NewProp_NewFormationType = { "NewFormationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventSetFormationType_Parms, NewFormationType), Z_Construct_UEnum_ArmorWars_ERTSFormationType, METADATA_PARAMS(0, nullptr) }; // 1472012862
void Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSFormationManager_eventSetFormationType_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSFormationManager_eventSetFormationType_Parms), &Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::NewProp_GroupID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::NewProp_NewFormationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::NewProp_NewFormationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "SetFormationType", Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::RTSFormationManager_eventSetFormationType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::RTSFormationManager_eventSetFormationType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_SetFormationType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_SetFormationType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execSetFormationType)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_GET_ENUM(ERTSFormationType,Z_Param_NewFormationType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetFormationType(Z_Param_GroupID,ERTSFormationType(Z_Param_NewFormationType));
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function SetFormationType *****************************

// ********** Begin Class URTSFormationManager Function UpdateAllFormations ************************
struct Z_Construct_UFunction_URTSFormationManager_UpdateAllFormations_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation maintenance\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation maintenance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_UpdateAllFormations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "UpdateAllFormations", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_UpdateAllFormations_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_UpdateAllFormations_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSFormationManager_UpdateAllFormations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_UpdateAllFormations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execUpdateAllFormations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAllFormations();
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function UpdateAllFormations **************************

// ********** Begin Class URTSFormationManager Function UpdateFormation ****************************
struct Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics
{
	struct RTSFormationManager_eventUpdateFormation_Parms
	{
		int32 GroupID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GroupID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::NewProp_GroupID = { "GroupID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSFormationManager_eventUpdateFormation_Parms, GroupID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::NewProp_GroupID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSFormationManager, nullptr, "UpdateFormation", Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::RTSFormationManager_eventUpdateFormation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::RTSFormationManager_eventUpdateFormation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSFormationManager_UpdateFormation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSFormationManager_UpdateFormation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSFormationManager::execUpdateFormation)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_GroupID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFormation(Z_Param_GroupID);
	P_NATIVE_END;
}
// ********** End Class URTSFormationManager Function UpdateFormation ******************************

// ********** Begin Class URTSFormationManager *****************************************************
void URTSFormationManager::StaticRegisterNativesURTSFormationManager()
{
	UClass* Class = URTSFormationManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddUnitToFormation", &URTSFormationManager::execAddUnitToFormation },
		{ "CalculateFormationPositions", &URTSFormationManager::execCalculateFormationPositions },
		{ "CalculateUnitFormationOffset", &URTSFormationManager::execCalculateUnitFormationOffset },
		{ "ChangeFormationLeader", &URTSFormationManager::execChangeFormationLeader },
		{ "CleanupInvalidFormations", &URTSFormationManager::execCleanupInvalidFormations },
		{ "CreateFormation", &URTSFormationManager::execCreateFormation },
		{ "DisbandFormation", &URTSFormationManager::execDisbandFormation },
		{ "DoesFormationExist", &URTSFormationManager::execDoesFormationExist },
		{ "ElectSquadLeader", &URTSFormationManager::execElectSquadLeader },
		{ "GetAllFormationIDs", &URTSFormationManager::execGetAllFormationIDs },
		{ "GetFormationForUnit", &URTSFormationManager::execGetFormationForUnit },
		{ "GetFormationGroup", &URTSFormationManager::execGetFormationGroup },
		{ "GetFormationLeader", &URTSFormationManager::execGetFormationLeader },
		{ "GetFormationUnits", &URTSFormationManager::execGetFormationUnits },
		{ "MoveFormation", &URTSFormationManager::execMoveFormation },
		{ "RemoveUnitFromFormation", &URTSFormationManager::execRemoveUnitFromFormation },
		{ "SetFormationSpacing", &URTSFormationManager::execSetFormationSpacing },
		{ "SetFormationType", &URTSFormationManager::execSetFormationType },
		{ "UpdateAllFormations", &URTSFormationManager::execUpdateAllFormations },
		{ "UpdateFormation", &URTSFormationManager::execUpdateFormation },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSFormationManager;
UClass* URTSFormationManager::GetPrivateStaticClass()
{
	using TClass = URTSFormationManager;
	if (!Z_Registration_Info_UClass_URTSFormationManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSFormationManager"),
			Z_Registration_Info_UClass_URTSFormationManager.InnerSingleton,
			StaticRegisterNativesURTSFormationManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSFormationManager.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSFormationManager_NoRegister()
{
	return URTSFormationManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSFormationManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Formation Manager subsystem for managing unit formations\n * Handles squad leader election, formation coordination, and collision avoidance\n */" },
#endif
		{ "IncludePath", "RTSFormationManager.h" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation Manager subsystem for managing unit formations\nHandles squad leader election, formation coordination, and collision avoidance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationGroups_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Formation groups storage\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Formation groups storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationUpdateInterval_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Update interval for formation management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update interval for formation management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug logging\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug logging" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFormationCreated_MetaData[] = {
		{ "Category", "RTS|Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFormationDisbanded_MetaData[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFormationLeaderChanged_MetaData[] = {
		{ "Category", "RTS|Formation" },
		{ "ModuleRelativePath", "Public/RTSFormationManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FormationGroups_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FormationGroups_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_FormationGroups;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FormationUpdateInterval;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFormationCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFormationDisbanded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFormationLeaderChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSFormationManager_AddUnitToFormation, "AddUnitToFormation" }, // 279675134
		{ &Z_Construct_UFunction_URTSFormationManager_CalculateFormationPositions, "CalculateFormationPositions" }, // 3162530465
		{ &Z_Construct_UFunction_URTSFormationManager_CalculateUnitFormationOffset, "CalculateUnitFormationOffset" }, // 2349068991
		{ &Z_Construct_UFunction_URTSFormationManager_ChangeFormationLeader, "ChangeFormationLeader" }, // 443959063
		{ &Z_Construct_UFunction_URTSFormationManager_CleanupInvalidFormations, "CleanupInvalidFormations" }, // 267434227
		{ &Z_Construct_UFunction_URTSFormationManager_CreateFormation, "CreateFormation" }, // 2369294947
		{ &Z_Construct_UFunction_URTSFormationManager_DisbandFormation, "DisbandFormation" }, // 3787223751
		{ &Z_Construct_UFunction_URTSFormationManager_DoesFormationExist, "DoesFormationExist" }, // 1574996283
		{ &Z_Construct_UFunction_URTSFormationManager_ElectSquadLeader, "ElectSquadLeader" }, // 887648116
		{ &Z_Construct_UFunction_URTSFormationManager_GetAllFormationIDs, "GetAllFormationIDs" }, // 2059055105
		{ &Z_Construct_UFunction_URTSFormationManager_GetFormationForUnit, "GetFormationForUnit" }, // 918024550
		{ &Z_Construct_UFunction_URTSFormationManager_GetFormationGroup, "GetFormationGroup" }, // 2832119022
		{ &Z_Construct_UFunction_URTSFormationManager_GetFormationLeader, "GetFormationLeader" }, // 2527215593
		{ &Z_Construct_UFunction_URTSFormationManager_GetFormationUnits, "GetFormationUnits" }, // 1732474778
		{ &Z_Construct_UFunction_URTSFormationManager_MoveFormation, "MoveFormation" }, // 1355734946
		{ &Z_Construct_UFunction_URTSFormationManager_RemoveUnitFromFormation, "RemoveUnitFromFormation" }, // 2997595226
		{ &Z_Construct_UFunction_URTSFormationManager_SetFormationSpacing, "SetFormationSpacing" }, // 3091876135
		{ &Z_Construct_UFunction_URTSFormationManager_SetFormationType, "SetFormationType" }, // 869624028
		{ &Z_Construct_UFunction_URTSFormationManager_UpdateAllFormations, "UpdateAllFormations" }, // 3475584083
		{ &Z_Construct_UFunction_URTSFormationManager_UpdateFormation, "UpdateFormation" }, // 803290478
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSFormationManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSFormationManager_Statics::NewProp_FormationGroups_ValueProp = { "FormationGroups", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FRTSFormationGroup, METADATA_PARAMS(0, nullptr) }; // 3472923679
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URTSFormationManager_Statics::NewProp_FormationGroups_Key_KeyProp = { "FormationGroups_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URTSFormationManager_Statics::NewProp_FormationGroups = { "FormationGroups", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFormationManager, FormationGroups), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationGroups_MetaData), NewProp_FormationGroups_MetaData) }; // 3472923679
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URTSFormationManager_Statics::NewProp_FormationUpdateInterval = { "FormationUpdateInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFormationManager, FormationUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationUpdateInterval_MetaData), NewProp_FormationUpdateInterval_MetaData) };
void Z_Construct_UClass_URTSFormationManager_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((URTSFormationManager*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSFormationManager_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSFormationManager), &Z_Construct_UClass_URTSFormationManager_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSFormationManager_Statics::NewProp_OnFormationCreated = { "OnFormationCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFormationManager, OnFormationCreated), Z_Construct_UDelegateFunction_ArmorWars_OnFormationCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFormationCreated_MetaData), NewProp_OnFormationCreated_MetaData) }; // 3987551185
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSFormationManager_Statics::NewProp_OnFormationDisbanded = { "OnFormationDisbanded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFormationManager, OnFormationDisbanded), Z_Construct_UDelegateFunction_ArmorWars_OnFormationDisbanded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFormationDisbanded_MetaData), NewProp_OnFormationDisbanded_MetaData) }; // 591515758
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSFormationManager_Statics::NewProp_OnFormationLeaderChanged = { "OnFormationLeaderChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSFormationManager, OnFormationLeaderChanged), Z_Construct_UDelegateFunction_ArmorWars_OnFormationLeaderChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFormationLeaderChanged_MetaData), NewProp_OnFormationLeaderChanged_MetaData) }; // 3722751008
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSFormationManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationManager_Statics::NewProp_FormationGroups_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationManager_Statics::NewProp_FormationGroups_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationManager_Statics::NewProp_FormationGroups,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationManager_Statics::NewProp_FormationUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationManager_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationManager_Statics::NewProp_OnFormationCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationManager_Statics::NewProp_OnFormationDisbanded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSFormationManager_Statics::NewProp_OnFormationLeaderChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFormationManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSFormationManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFormationManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSFormationManager_Statics::ClassParams = {
	&URTSFormationManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSFormationManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSFormationManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSFormationManager_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSFormationManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSFormationManager()
{
	if (!Z_Registration_Info_UClass_URTSFormationManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSFormationManager.OuterSingleton, Z_Construct_UClass_URTSFormationManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSFormationManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSFormationManager);
URTSFormationManager::~URTSFormationManager() {}
// ********** End Class URTSFormationManager *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h__Script_ArmorWars_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRTSFormationGroup::StaticStruct, Z_Construct_UScriptStruct_FRTSFormationGroup_Statics::NewStructOps, TEXT("RTSFormationGroup"), &Z_Registration_Info_UScriptStruct_FRTSFormationGroup, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSFormationGroup), 3472923679U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSFormationManager, URTSFormationManager::StaticClass, TEXT("URTSFormationManager"), &Z_Registration_Info_UClass_URTSFormationManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSFormationManager), 1939086874U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h__Script_ArmorWars_2040583982(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h__Script_ArmorWars_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h__Script_ArmorWars_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSFormationManager_h__Script_ArmorWars_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

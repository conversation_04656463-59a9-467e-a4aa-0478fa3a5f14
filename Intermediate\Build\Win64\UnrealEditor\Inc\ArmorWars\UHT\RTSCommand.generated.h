// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RTSCommand.h"

#ifdef ARMORWARS_RTSCommand_generated_h
#error "RTSCommand.generated.h already included, missing '#pragma once' in RTSCommand.h"
#endif
#define ARMORWARS_RTSCommand_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FRTSCommand *******************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommand_h_72_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSCommand_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSCommand;
// ********** End ScriptStruct FRTSCommand *********************************************************

// ********** Begin ScriptStruct FRTSCommandQueue **************************************************
#define FID_ArmorWars_Source_ArmorWars_Public_RTSCommand_h_220_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRTSCommandQueue_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRTSCommandQueue;
// ********** End ScriptStruct FRTSCommandQueue ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_ArmorWars_Source_ArmorWars_Public_RTSCommand_h

// ********** Begin Enum ERTSCommandType ***********************************************************
#define FOREACH_ENUM_ERTSCOMMANDTYPE(op) \
	op(ERTSCommandType::None) \
	op(ERTSCommandType::Move) \
	op(ERTSCommandType::AttackMove) \
	op(ERTSCommandType::AttackTarget) \
	op(ERTSCommandType::Stop) \
	op(ERTSCommandType::Hold) \
	op(ERTSCommandType::Patrol) \
	op(ERTSCommandType::Follow) \
	op(ERTSCommandType::Guard) \
	op(ERTSCommandType::Formation) \
	op(ERTSCommandType::Retreat) \
	op(ERTSCommandType::SpecialAbility) 

enum class ERTSCommandType : uint8;
template<> struct TIsUEnumClass<ERTSCommandType> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSCommandType>();
// ********** End Enum ERTSCommandType *************************************************************

// ********** Begin Enum ERTSCommandPriority *******************************************************
#define FOREACH_ENUM_ERTSCOMMANDPRIORITY(op) \
	op(ERTSCommandPriority::Low) \
	op(ERTSCommandPriority::Normal) \
	op(ERTSCommandPriority::High) \
	op(ERTSCommandPriority::Critical) \
	op(ERTSCommandPriority::Override) 

enum class ERTSCommandPriority : uint8;
template<> struct TIsUEnumClass<ERTSCommandPriority> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSCommandPriority>();
// ********** End Enum ERTSCommandPriority *********************************************************

// ********** Begin Enum ERTSCommandStatus *********************************************************
#define FOREACH_ENUM_ERTSCOMMANDSTATUS(op) \
	op(ERTSCommandStatus::Pending) \
	op(ERTSCommandStatus::Executing) \
	op(ERTSCommandStatus::Completed) \
	op(ERTSCommandStatus::Failed) \
	op(ERTSCommandStatus::Cancelled) \
	op(ERTSCommandStatus::Interrupted) 

enum class ERTSCommandStatus : uint8;
template<> struct TIsUEnumClass<ERTSCommandStatus> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSCommandStatus>();
// ********** End Enum ERTSCommandStatus ***********************************************************

// ********** Begin Enum ERTSFormationCommandType **************************************************
#define FOREACH_ENUM_ERTSFORMATIONCOMMANDTYPE(op) \
	op(ERTSFormationCommandType::Line) \
	op(ERTSFormationCommandType::Wedge) \
	op(ERTSFormationCommandType::Circle) \
	op(ERTSFormationCommandType::Column) \
	op(ERTSFormationCommandType::Square) \
	op(ERTSFormationCommandType::Scattered) \
	op(ERTSFormationCommandType::Custom) 

enum class ERTSFormationCommandType : uint8;
template<> struct TIsUEnumClass<ERTSFormationCommandType> { enum { Value = true }; };
template<> ARMORWARS_API UEnum* StaticEnum<ERTSFormationCommandType>();
// ********** End Enum ERTSFormationCommandType ****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

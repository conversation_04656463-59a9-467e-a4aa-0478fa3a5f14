#pragma once

#include "CoreMinimal.h"
#include "RTSArmorTypes.generated.h"

/**
 * Enum for different armor types used in penetration calculations
 * Each type has different effectiveness against various damage types
 */
UENUM(BlueprintType)
enum class ERTSArmorType : uint8
{
    /** Light armor - minimal protection, high mobility */
    Light       UMETA(DisplayName = "Light"),

    /** Medium armor - balanced protection and mobility */
    Medium      UMETA(DisplayName = "Medium"),

    /** Heavy armor - maximum protection, reduced mobility */
    Heavy       UMETA(DisplayName = "Heavy"),

    /** Reactive armor - explosive reactive armor, effective against shaped charges */
    Reactive    UMETA(DisplayName = "Reactive"),

    /** Composite armor - layered armor with multiple materials */
    Composite   UMETA(DisplayName = "Composite")
};

/**
 * Enum for armor facing directions used in directional armor calculations
 * Different facings can have different armor thickness and types
 */
UENUM(BlueprintType)
enum class ERTSArmorFacing : uint8
{
    /** Front facing - typically the strongest armor */
    Front       UMETA(DisplayName = "Front"),

    /** Side facing - moderate armor protection */
    Side        UMETA(DisplayName = "Side"),

    /** Rear facing - typically the weakest armor */
    Rear        UMETA(DisplayName = "Rear"),

    /** Top facing - protection against air attacks */
    Top         UMETA(DisplayName = "Top"),

    /** Bottom facing - protection against mines and ground attacks */
    Bottom      UMETA(DisplayName = "Bottom")
};



#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "RTSWeaponComponent.h"
#include "RTSBaseActor.h"
#include "RTSWeaponController.generated.h"

/**
 * Weapon controller component that manages multiple weapon components on an RTS unit
 * Handles weapon coordination, targeting, and firing control
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class ARMORWARS_API URTSWeaponController : public UActorComponent
{
    GENERATED_BODY()

public:
    URTSWeaponController();

protected:
    virtual void BeginPlay() override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

public:
    // Weapon management
    UFUNCTION(BlueprintCallable, Category = "Weapon Controller")
    void AddWeapon(URTSWeaponComponent* WeaponComponent);

    UFUNCTION(BlueprintCallable, Category = "Weapon Controller")
    void RemoveWeapon(URTSWeaponComponent* WeaponComponent);

    UFUNCTION(BlueprintCallable, Category = "Weapon Controller")
    void ClearAllWeapons();

    UFUNCTION(BlueprintPure, Category = "Weapon Controller")
    TArray<URTSWeaponComponent*> GetAllWeapons() const { return WeaponComponents; }

    UFUNCTION(BlueprintPure, Category = "Weapon Controller")
    int32 GetWeaponCount() const { return WeaponComponents.Num(); }

    // Targeting functions
    UFUNCTION(BlueprintCallable, Category = "Weapon Controller|Targeting")
    void SetTarget(ARTSBaseActor* Target);

    UFUNCTION(BlueprintCallable, Category = "Weapon Controller|Targeting")
    void ClearTarget();

    UFUNCTION(BlueprintPure, Category = "Weapon Controller|Targeting")
    ARTSBaseActor* GetCurrentTarget() const { return CurrentTarget.Get(); }

    UFUNCTION(BlueprintPure, Category = "Weapon Controller|Targeting")
    bool HasValidTarget() const;

    UFUNCTION(BlueprintCallable, Category = "Weapon Controller|Targeting")
    ARTSBaseActor* FindBestTarget(float SearchRange = 0.0f) const;

    // Firing functions
    UFUNCTION(BlueprintCallable, Category = "Weapon Controller|Firing")
    void StartFiring();

    UFUNCTION(BlueprintCallable, Category = "Weapon Controller|Firing")
    void StopFiring();

    UFUNCTION(BlueprintCallable, Category = "Weapon Controller|Firing")
    bool FireAtTarget(ARTSBaseActor* Target);

    UFUNCTION(BlueprintCallable, Category = "Weapon Controller|Firing")
    bool FireAtLocation(const FVector& TargetLocation);

    UFUNCTION(BlueprintPure, Category = "Weapon Controller|Firing")
    bool CanFire() const;

    UFUNCTION(BlueprintPure, Category = "Weapon Controller|Firing")
    bool IsFiring() const { return bIsFiring; }

    // Weapon selection and coordination
    UFUNCTION(BlueprintCallable, Category = "Weapon Controller|Selection")
    void SetActiveWeaponGroup(int32 GroupIndex);

    UFUNCTION(BlueprintCallable, Category = "Weapon Controller|Selection")
    void SetAllWeaponsActive(bool bActive);

    UFUNCTION(BlueprintPure, Category = "Weapon Controller|Selection")
    TArray<URTSWeaponComponent*> GetActiveWeapons() const;

    // Combat statistics
    UFUNCTION(BlueprintPure, Category = "Weapon Controller|Stats")
    bool HasWeapons() const;

    UFUNCTION(BlueprintPure, Category = "Weapon Controller|Stats")
    float GetMaxRange() const;

    UFUNCTION(BlueprintPure, Category = "Weapon Controller|Stats")
    float GetTotalDamagePerSecond() const;

    UFUNCTION(BlueprintPure, Category = "Weapon Controller|Stats")
    bool CanTargetDomain(ERTSUnitDomain Domain) const;

    UFUNCTION(BlueprintCallable, Category = "Weapon Controller")
    virtual void UpdateTargeting(float DeltaTime);

protected:
    // Weapon components managed by this controller
    UPROPERTY(BlueprintReadOnly, Category = "Weapon Controller")
    TArray<URTSWeaponComponent*> WeaponComponents;

    // Current target
    UPROPERTY(BlueprintReadOnly, Category = "Weapon Controller")
    TWeakObjectPtr<ARTSBaseActor> CurrentTarget;

    // Whether the controller is currently firing
    UPROPERTY(BlueprintReadOnly, Category = "Weapon Controller")
    bool bIsFiring = false;

    // Active weapon group (-1 means all weapons)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Controller")
    int32 ActiveWeaponGroup = -1;

    // Whether to automatically acquire targets
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Controller")
    bool bAutoTarget = true;

    // Auto-target search range (0 = use max weapon range)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Controller")
    float AutoTargetRange = 0.0f;


protected:
    // Internal update functions
    virtual void UpdateFiring(float DeltaTime);
    virtual void UpdateWeaponCoordination(float DeltaTime);

    // Internal targeting logic
    virtual void AutoAcquireTarget();
    virtual bool IsValidTarget(const ARTSBaseActor* Target) const;
    virtual float CalculateTargetPriority(const ARTSBaseActor* Target) const;

    // Weapon coordination
    virtual void CoordinateWeaponFiring();
    virtual TArray<URTSWeaponComponent*> SelectWeaponsForTarget(const ARTSBaseActor* Target) const;

    // Timing variables
    float LastTargetSearchTime = 0.0f;
    float TargetSearchInterval = 0.5f; // Search for targets every 0.5 seconds

private:
    // Internal event handler
    UFUNCTION()
    void OnWeaponFiredInternal(URTSWeaponComponent* Weapon, const FVector& MuzzleLocation, const FVector& TargetLocation);

protected:
    // Blueprint events
    UFUNCTION(BlueprintImplementableEvent, Category = "Weapon Controller")
    void OnTargetAcquired(ARTSBaseActor* Target);

    UFUNCTION(BlueprintImplementableEvent, Category = "Weapon Controller")
    void OnTargetLost(ARTSBaseActor* Target);

    UFUNCTION(BlueprintImplementableEvent, Category = "Weapon Controller")
    void OnFiringStarted();

    UFUNCTION(BlueprintImplementableEvent, Category = "Weapon Controller")
    void OnFiringStopped();

public:
    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTargetChanged, URTSWeaponController*, Controller, ARTSBaseActor*, NewTarget);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnFiringStateChanged, URTSWeaponController*, Controller, bool, bFiring);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnWeaponFired, URTSWeaponController*, Controller, URTSWeaponComponent*, Weapon, ARTSBaseActor*, Target);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTargetChanged OnTargetChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnFiringStateChanged OnFiringStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnWeaponFired OnWeaponFired;
};

// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RTSReturnFireSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRTSReturnFireSystem() {}

// ********** Begin Cross Module References ********************************************************
ARMORWARS_API UClass* Z_Construct_UClass_ARTSBaseActor_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_ARTSUnit_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSReturnFireComponent();
ARMORWARS_API UClass* Z_Construct_UClass_URTSReturnFireComponent_NoRegister();
ARMORWARS_API UClass* Z_Construct_UClass_URTSWeaponController_NoRegister();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior();
ARMORWARS_API UEnum* Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature();
ARMORWARS_API UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSReturnFireConfig();
ARMORWARS_API UScriptStruct* Z_Construct_UScriptStruct_FRTSReturnFireState();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
UPackage* Z_Construct_UPackage__Script_ArmorWars();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERTSReturnFireBehavior ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSReturnFireBehavior;
static UEnum* ERTSReturnFireBehavior_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSReturnFireBehavior.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSReturnFireBehavior.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSReturnFireBehavior"));
	}
	return Z_Registration_Info_UEnum_ERTSReturnFireBehavior.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSReturnFireBehavior>()
{
	return ERTSReturnFireBehavior_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "ERTSReturnFireBehavior::Aggressive" },
		{ "AlwaysReturn.DisplayName", "Always Return Fire" },
		{ "AlwaysReturn.Name", "ERTSReturnFireBehavior::AlwaysReturn" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Return fire behavior types\n" },
#endif
		{ "Defensive.DisplayName", "Defensive Only" },
		{ "Defensive.Name", "ERTSReturnFireBehavior::Defensive" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
		{ "None.DisplayName", "No Return Fire" },
		{ "None.Name", "ERTSReturnFireBehavior::None" },
		{ "Opportunistic.DisplayName", "Opportunistic" },
		{ "Opportunistic.Name", "ERTSReturnFireBehavior::Opportunistic" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Return fire behavior types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSReturnFireBehavior::None", (int64)ERTSReturnFireBehavior::None },
		{ "ERTSReturnFireBehavior::Opportunistic", (int64)ERTSReturnFireBehavior::Opportunistic },
		{ "ERTSReturnFireBehavior::Aggressive", (int64)ERTSReturnFireBehavior::Aggressive },
		{ "ERTSReturnFireBehavior::Defensive", (int64)ERTSReturnFireBehavior::Defensive },
		{ "ERTSReturnFireBehavior::AlwaysReturn", (int64)ERTSReturnFireBehavior::AlwaysReturn },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSReturnFireBehavior",
	"ERTSReturnFireBehavior",
	Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior()
{
	if (!Z_Registration_Info_UEnum_ERTSReturnFireBehavior.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSReturnFireBehavior.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSReturnFireBehavior.InnerSingleton;
}
// ********** End Enum ERTSReturnFireBehavior ******************************************************

// ********** Begin Enum ERTSReturnFirePriority ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERTSReturnFirePriority;
static UEnum* ERTSReturnFirePriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERTSReturnFirePriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERTSReturnFirePriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("ERTSReturnFirePriority"));
	}
	return Z_Registration_Info_UEnum_ERTSReturnFirePriority.OuterSingleton;
}
template<> ARMORWARS_API UEnum* StaticEnum<ERTSReturnFirePriority>()
{
	return ERTSReturnFirePriority_StaticEnum();
}
struct Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AttackingEnemy.DisplayName", "Attacking Enemy" },
		{ "AttackingEnemy.Name", "ERTSReturnFirePriority::AttackingEnemy" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Return fire target priority\n" },
#endif
		{ "LastAttacker.DisplayName", "Last Attacker" },
		{ "LastAttacker.Name", "ERTSReturnFirePriority::LastAttacker" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
		{ "NearestEnemy.DisplayName", "Nearest Enemy" },
		{ "NearestEnemy.Name", "ERTSReturnFirePriority::NearestEnemy" },
		{ "StrongestThreat.DisplayName", "Strongest Threat" },
		{ "StrongestThreat.Name", "ERTSReturnFirePriority::StrongestThreat" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Return fire target priority" },
#endif
		{ "WeakestEnemy.DisplayName", "Weakest Enemy" },
		{ "WeakestEnemy.Name", "ERTSReturnFirePriority::WeakestEnemy" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERTSReturnFirePriority::NearestEnemy", (int64)ERTSReturnFirePriority::NearestEnemy },
		{ "ERTSReturnFirePriority::AttackingEnemy", (int64)ERTSReturnFirePriority::AttackingEnemy },
		{ "ERTSReturnFirePriority::WeakestEnemy", (int64)ERTSReturnFirePriority::WeakestEnemy },
		{ "ERTSReturnFirePriority::StrongestThreat", (int64)ERTSReturnFirePriority::StrongestThreat },
		{ "ERTSReturnFirePriority::LastAttacker", (int64)ERTSReturnFirePriority::LastAttacker },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	"ERTSReturnFirePriority",
	"ERTSReturnFirePriority",
	Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority()
{
	if (!Z_Registration_Info_UEnum_ERTSReturnFirePriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERTSReturnFirePriority.InnerSingleton, Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERTSReturnFirePriority.InnerSingleton;
}
// ********** End Enum ERTSReturnFirePriority ******************************************************

// ********** Begin ScriptStruct FRTSReturnFireConfig **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSReturnFireConfig;
class UScriptStruct* FRTSReturnFireConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSReturnFireConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSReturnFireConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSReturnFireConfig, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSReturnFireConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSReturnFireConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Return fire configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Return fire configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Behavior_MetaData[] = {
		{ "Category", "Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Behavior settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPriority_MetaData[] = {
		{ "Category", "Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxReturnFireRange_MetaData[] = {
		{ "Category", "Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Range and timing\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Range and timing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWeaponRange_MetaData[] = {
		{ "Category", "Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnFireDuration_MetaData[] = {
		{ "Category", "Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnFireCooldown_MetaData[] = {
		{ "Category", "Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowReturnFireWhileMoving_MetaData[] = {
		{ "Category", "Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement interaction\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement interaction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSlowDownWhileFiring_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedMultiplier_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStopToFireAccurately_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccuracyPenaltyWhileMoving_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPrioritizeAttackers_MetaData[] = {
		{ "Category", "Target Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target selection\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target selection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackerMemoryDuration_MetaData[] = {
		{ "Category", "Target Selection" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSwitchTargetsOpportunistically_MetaData[] = {
		{ "Category", "Target Selection" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetSwitchCooldown_MetaData[] = {
		{ "Category", "Target Selection" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Behavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Behavior;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetPriority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxReturnFireRange;
	static void NewProp_bUseWeaponRange_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWeaponRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnFireDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnFireCooldown;
	static void NewProp_bAllowReturnFireWhileMoving_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowReturnFireWhileMoving;
	static void NewProp_bSlowDownWhileFiring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSlowDownWhileFiring;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedMultiplier;
	static void NewProp_bStopToFireAccurately_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStopToFireAccurately;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccuracyPenaltyWhileMoving;
	static void NewProp_bPrioritizeAttackers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPrioritizeAttackers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackerMemoryDuration;
	static void NewProp_bSwitchTargetsOpportunistically_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSwitchTargetsOpportunistically;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetSwitchCooldown;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSReturnFireConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_Behavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_Behavior = { "Behavior", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireConfig, Behavior), Z_Construct_UEnum_ArmorWars_ERTSReturnFireBehavior, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Behavior_MetaData), NewProp_Behavior_MetaData) }; // 2735397557
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_TargetPriority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_TargetPriority = { "TargetPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireConfig, TargetPriority), Z_Construct_UEnum_ArmorWars_ERTSReturnFirePriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPriority_MetaData), NewProp_TargetPriority_MetaData) }; // 1857039825
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_MaxReturnFireRange = { "MaxReturnFireRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireConfig, MaxReturnFireRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxReturnFireRange_MetaData), NewProp_MaxReturnFireRange_MetaData) };
void Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bUseWeaponRange_SetBit(void* Obj)
{
	((FRTSReturnFireConfig*)Obj)->bUseWeaponRange = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bUseWeaponRange = { "bUseWeaponRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSReturnFireConfig), &Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bUseWeaponRange_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWeaponRange_MetaData), NewProp_bUseWeaponRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_ReturnFireDuration = { "ReturnFireDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireConfig, ReturnFireDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnFireDuration_MetaData), NewProp_ReturnFireDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_ReturnFireCooldown = { "ReturnFireCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireConfig, ReturnFireCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnFireCooldown_MetaData), NewProp_ReturnFireCooldown_MetaData) };
void Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bAllowReturnFireWhileMoving_SetBit(void* Obj)
{
	((FRTSReturnFireConfig*)Obj)->bAllowReturnFireWhileMoving = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bAllowReturnFireWhileMoving = { "bAllowReturnFireWhileMoving", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSReturnFireConfig), &Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bAllowReturnFireWhileMoving_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowReturnFireWhileMoving_MetaData), NewProp_bAllowReturnFireWhileMoving_MetaData) };
void Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bSlowDownWhileFiring_SetBit(void* Obj)
{
	((FRTSReturnFireConfig*)Obj)->bSlowDownWhileFiring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bSlowDownWhileFiring = { "bSlowDownWhileFiring", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSReturnFireConfig), &Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bSlowDownWhileFiring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSlowDownWhileFiring_MetaData), NewProp_bSlowDownWhileFiring_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_MovementSpeedMultiplier = { "MovementSpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireConfig, MovementSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedMultiplier_MetaData), NewProp_MovementSpeedMultiplier_MetaData) };
void Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bStopToFireAccurately_SetBit(void* Obj)
{
	((FRTSReturnFireConfig*)Obj)->bStopToFireAccurately = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bStopToFireAccurately = { "bStopToFireAccurately", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSReturnFireConfig), &Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bStopToFireAccurately_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStopToFireAccurately_MetaData), NewProp_bStopToFireAccurately_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_AccuracyPenaltyWhileMoving = { "AccuracyPenaltyWhileMoving", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireConfig, AccuracyPenaltyWhileMoving), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccuracyPenaltyWhileMoving_MetaData), NewProp_AccuracyPenaltyWhileMoving_MetaData) };
void Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bPrioritizeAttackers_SetBit(void* Obj)
{
	((FRTSReturnFireConfig*)Obj)->bPrioritizeAttackers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bPrioritizeAttackers = { "bPrioritizeAttackers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSReturnFireConfig), &Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bPrioritizeAttackers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPrioritizeAttackers_MetaData), NewProp_bPrioritizeAttackers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_AttackerMemoryDuration = { "AttackerMemoryDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireConfig, AttackerMemoryDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackerMemoryDuration_MetaData), NewProp_AttackerMemoryDuration_MetaData) };
void Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bSwitchTargetsOpportunistically_SetBit(void* Obj)
{
	((FRTSReturnFireConfig*)Obj)->bSwitchTargetsOpportunistically = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bSwitchTargetsOpportunistically = { "bSwitchTargetsOpportunistically", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSReturnFireConfig), &Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bSwitchTargetsOpportunistically_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSwitchTargetsOpportunistically_MetaData), NewProp_bSwitchTargetsOpportunistically_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_TargetSwitchCooldown = { "TargetSwitchCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireConfig, TargetSwitchCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetSwitchCooldown_MetaData), NewProp_TargetSwitchCooldown_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_Behavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_Behavior,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_TargetPriority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_TargetPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_MaxReturnFireRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bUseWeaponRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_ReturnFireDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_ReturnFireCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bAllowReturnFireWhileMoving,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bSlowDownWhileFiring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_MovementSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bStopToFireAccurately,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_AccuracyPenaltyWhileMoving,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bPrioritizeAttackers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_AttackerMemoryDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_bSwitchTargetsOpportunistically,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewProp_TargetSwitchCooldown,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSReturnFireConfig",
	Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::PropPointers),
	sizeof(FRTSReturnFireConfig),
	alignof(FRTSReturnFireConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSReturnFireConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSReturnFireConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSReturnFireConfig.InnerSingleton, Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSReturnFireConfig.InnerSingleton;
}
// ********** End ScriptStruct FRTSReturnFireConfig ************************************************

// ********** Begin ScriptStruct FRTSReturnFireState ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRTSReturnFireState;
class UScriptStruct* FRTSReturnFireState::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSReturnFireState.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRTSReturnFireState.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRTSReturnFireState, (UObject*)Z_Construct_UPackage__Script_ArmorWars(), TEXT("RTSReturnFireState"));
	}
	return Z_Registration_Info_UScriptStruct_FRTSReturnFireState.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRTSReturnFireState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Return fire state data\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Return fire state data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsReturningFire_MetaData[] = {
		{ "Category", "Return Fire State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current state\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTarget_MetaData[] = {
		{ "Category", "Return Fire State" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnFireStartTime_MetaData[] = {
		{ "Category", "Return Fire State" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastReturnFireTime_MetaData[] = {
		{ "Category", "Return Fire State" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastTargetSwitchTime_MetaData[] = {
		{ "Category", "Return Fire State" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecentAttackers_MetaData[] = {
		{ "Category", "Return Fire State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attacker tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attacker tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackerTimestamps_MetaData[] = {
		{ "Category", "Return Fire State" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bWasMovingWhenFiring_MetaData[] = {
		{ "Category", "Return Fire State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement state\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalMoveTarget_MetaData[] = {
		{ "Category", "Return Fire State" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsReturningFire_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsReturningFire;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_CurrentTarget;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnFireStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastReturnFireTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastTargetSwitchTime;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_RecentAttackers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RecentAttackers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackerTimestamps_ValueProp;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AttackerTimestamps_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AttackerTimestamps;
	static void NewProp_bWasMovingWhenFiring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWasMovingWhenFiring;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OriginalMoveTarget;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRTSReturnFireState>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_bIsReturningFire_SetBit(void* Obj)
{
	((FRTSReturnFireState*)Obj)->bIsReturningFire = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_bIsReturningFire = { "bIsReturningFire", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSReturnFireState), &Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_bIsReturningFire_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsReturningFire_MetaData), NewProp_bIsReturningFire_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_CurrentTarget = { "CurrentTarget", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireState, CurrentTarget), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTarget_MetaData), NewProp_CurrentTarget_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_ReturnFireStartTime = { "ReturnFireStartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireState, ReturnFireStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnFireStartTime_MetaData), NewProp_ReturnFireStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_LastReturnFireTime = { "LastReturnFireTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireState, LastReturnFireTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastReturnFireTime_MetaData), NewProp_LastReturnFireTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_LastTargetSwitchTime = { "LastTargetSwitchTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireState, LastTargetSwitchTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastTargetSwitchTime_MetaData), NewProp_LastTargetSwitchTime_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_RecentAttackers_Inner = { "RecentAttackers", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_RecentAttackers = { "RecentAttackers", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireState, RecentAttackers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecentAttackers_MetaData), NewProp_RecentAttackers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_AttackerTimestamps_ValueProp = { "AttackerTimestamps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_AttackerTimestamps_Key_KeyProp = { "AttackerTimestamps_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_AttackerTimestamps = { "AttackerTimestamps", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireState, AttackerTimestamps), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackerTimestamps_MetaData), NewProp_AttackerTimestamps_MetaData) };
void Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_bWasMovingWhenFiring_SetBit(void* Obj)
{
	((FRTSReturnFireState*)Obj)->bWasMovingWhenFiring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_bWasMovingWhenFiring = { "bWasMovingWhenFiring", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRTSReturnFireState), &Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_bWasMovingWhenFiring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bWasMovingWhenFiring_MetaData), NewProp_bWasMovingWhenFiring_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_OriginalMoveTarget = { "OriginalMoveTarget", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRTSReturnFireState, OriginalMoveTarget), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalMoveTarget_MetaData), NewProp_OriginalMoveTarget_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_bIsReturningFire,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_CurrentTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_ReturnFireStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_LastReturnFireTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_LastTargetSwitchTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_RecentAttackers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_RecentAttackers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_AttackerTimestamps_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_AttackerTimestamps_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_AttackerTimestamps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_bWasMovingWhenFiring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewProp_OriginalMoveTarget,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
	nullptr,
	&NewStructOps,
	"RTSReturnFireState",
	Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::PropPointers),
	sizeof(FRTSReturnFireState),
	alignof(FRTSReturnFireState),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRTSReturnFireState()
{
	if (!Z_Registration_Info_UScriptStruct_FRTSReturnFireState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRTSReturnFireState.InnerSingleton, Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRTSReturnFireState.InnerSingleton;
}
// ********** End ScriptStruct FRTSReturnFireState *************************************************

// ********** Begin Delegate FOnReturnFireStarted **************************************************
struct Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics
{
	struct RTSReturnFireComponent_eventOnReturnFireStarted_Parms
	{
		ARTSBaseActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventOnReturnFireStarted_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "OnReturnFireStarted__DelegateSignature", Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::RTSReturnFireComponent_eventOnReturnFireStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::RTSReturnFireComponent_eventOnReturnFireStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSReturnFireComponent::FOnReturnFireStarted_DelegateWrapper(const FMulticastScriptDelegate& OnReturnFireStarted, ARTSBaseActor* Target)
{
	struct RTSReturnFireComponent_eventOnReturnFireStarted_Parms
	{
		ARTSBaseActor* Target;
	};
	RTSReturnFireComponent_eventOnReturnFireStarted_Parms Parms;
	Parms.Target=Target;
	OnReturnFireStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnReturnFireStarted ****************************************************

// ********** Begin Delegate FOnReturnFireStopped **************************************************
struct Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "OnReturnFireStopped__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSReturnFireComponent::FOnReturnFireStopped_DelegateWrapper(const FMulticastScriptDelegate& OnReturnFireStopped)
{
	OnReturnFireStopped.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnReturnFireStopped ****************************************************

// ********** Begin Delegate FOnTargetChanged ******************************************************
struct Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics
{
	struct RTSReturnFireComponent_eventOnTargetChanged_Parms
	{
		ARTSBaseActor* OldTarget;
		ARTSBaseActor* NewTarget;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OldTarget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewTarget;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::NewProp_OldTarget = { "OldTarget", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventOnTargetChanged_Parms, OldTarget), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::NewProp_NewTarget = { "NewTarget", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventOnTargetChanged_Parms, NewTarget), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::NewProp_OldTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::NewProp_NewTarget,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "OnTargetChanged__DelegateSignature", Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::RTSReturnFireComponent_eventOnTargetChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::RTSReturnFireComponent_eventOnTargetChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSReturnFireComponent::FOnTargetChanged_DelegateWrapper(const FMulticastScriptDelegate& OnTargetChanged, ARTSBaseActor* OldTarget, ARTSBaseActor* NewTarget)
{
	struct RTSReturnFireComponent_eventOnTargetChanged_Parms
	{
		ARTSBaseActor* OldTarget;
		ARTSBaseActor* NewTarget;
	};
	RTSReturnFireComponent_eventOnTargetChanged_Parms Parms;
	Parms.OldTarget=OldTarget;
	Parms.NewTarget=NewTarget;
	OnTargetChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTargetChanged ********************************************************

// ********** Begin Delegate FOnAttackerRegistered *************************************************
struct Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics
{
	struct RTSReturnFireComponent_eventOnAttackerRegistered_Parms
	{
		ARTSBaseActor* Attacker;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Attacker;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::NewProp_Attacker = { "Attacker", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventOnAttackerRegistered_Parms, Attacker), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::NewProp_Attacker,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "OnAttackerRegistered__DelegateSignature", Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::RTSReturnFireComponent_eventOnAttackerRegistered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::RTSReturnFireComponent_eventOnAttackerRegistered_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void URTSReturnFireComponent::FOnAttackerRegistered_DelegateWrapper(const FMulticastScriptDelegate& OnAttackerRegistered, ARTSBaseActor* Attacker)
{
	struct RTSReturnFireComponent_eventOnAttackerRegistered_Parms
	{
		ARTSBaseActor* Attacker;
	};
	RTSReturnFireComponent_eventOnAttackerRegistered_Parms Parms;
	Parms.Attacker=Attacker;
	OnAttackerRegistered.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAttackerRegistered ***************************************************

// ********** Begin Class URTSReturnFireComponent Function CanReturnFire ***************************
struct Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics
{
	struct RTSReturnFireComponent_eventCanReturnFire_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSReturnFireComponent_eventCanReturnFire_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSReturnFireComponent_eventCanReturnFire_Parms), &Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "CanReturnFire", Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::RTSReturnFireComponent_eventCanReturnFire_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::RTSReturnFireComponent_eventCanReturnFire_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execCanReturnFire)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanReturnFire();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function CanReturnFire *****************************

// ********** Begin Class URTSReturnFireComponent Function CleanupOldAttackers *********************
struct Z_Construct_UFunction_URTSReturnFireComponent_CleanupOldAttackers_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_CleanupOldAttackers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "CleanupOldAttackers", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_CleanupOldAttackers_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_CleanupOldAttackers_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_CleanupOldAttackers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_CleanupOldAttackers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execCleanupOldAttackers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupOldAttackers();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function CleanupOldAttackers ***********************

// ********** Begin Class URTSReturnFireComponent Function GetCurrentTarget ************************
struct Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics
{
	struct RTSReturnFireComponent_eventGetCurrentTarget_Parms
	{
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventGetCurrentTarget_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "GetCurrentTarget", Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::RTSReturnFireComponent_eventGetCurrentTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::RTSReturnFireComponent_eventGetCurrentTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execGetCurrentTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->GetCurrentTarget();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function GetCurrentTarget **************************

// ********** Begin Class URTSReturnFireComponent Function GetEffectiveReturnFireRange *************
struct Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics
{
	struct RTSReturnFireComponent_eventGetEffectiveReturnFireRange_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventGetEffectiveReturnFireRange_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "GetEffectiveReturnFireRange", Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::RTSReturnFireComponent_eventGetEffectiveReturnFireRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::RTSReturnFireComponent_eventGetEffectiveReturnFireRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execGetEffectiveReturnFireRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetEffectiveReturnFireRange();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function GetEffectiveReturnFireRange ***************

// ********** Begin Class URTSReturnFireComponent Function GetMostRecentAttacker *******************
struct Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics
{
	struct RTSReturnFireComponent_eventGetMostRecentAttacker_Parms
	{
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventGetMostRecentAttacker_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "GetMostRecentAttacker", Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::RTSReturnFireComponent_eventGetMostRecentAttacker_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::RTSReturnFireComponent_eventGetMostRecentAttacker_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execGetMostRecentAttacker)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->GetMostRecentAttacker();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function GetMostRecentAttacker *********************

// ********** Begin Class URTSReturnFireComponent Function GetMovementSpeedMultiplier **************
struct Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics
{
	struct RTSReturnFireComponent_eventGetMovementSpeedMultiplier_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement integration\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement integration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventGetMovementSpeedMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "GetMovementSpeedMultiplier", Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::RTSReturnFireComponent_eventGetMovementSpeedMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::RTSReturnFireComponent_eventGetMovementSpeedMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execGetMovementSpeedMultiplier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMovementSpeedMultiplier();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function GetMovementSpeedMultiplier ****************

// ********** Begin Class URTSReturnFireComponent Function GetOwnerUnit ****************************
struct Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics
{
	struct RTSReturnFireComponent_eventGetOwnerUnit_Parms
	{
		ARTSUnit* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventGetOwnerUnit_Parms, ReturnValue), Z_Construct_UClass_ARTSUnit_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "GetOwnerUnit", Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::RTSReturnFireComponent_eventGetOwnerUnit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::RTSReturnFireComponent_eventGetOwnerUnit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execGetOwnerUnit)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSUnit**)Z_Param__Result=P_THIS->GetOwnerUnit();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function GetOwnerUnit ******************************

// ********** Begin Class URTSReturnFireComponent Function GetRecentAttackers **********************
struct Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics
{
	struct RTSReturnFireComponent_eventGetRecentAttackers_Parms
	{
		TArray<ARTSBaseActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventGetRecentAttackers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "GetRecentAttackers", Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::RTSReturnFireComponent_eventGetRecentAttackers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::RTSReturnFireComponent_eventGetRecentAttackers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execGetRecentAttackers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ARTSBaseActor*>*)Z_Param__Result=P_THIS->GetRecentAttackers();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function GetRecentAttackers ************************

// ********** Begin Class URTSReturnFireComponent Function GetWeaponController *********************
struct Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics
{
	struct RTSReturnFireComponent_eventGetWeaponController_Parms
	{
		URTSWeaponController* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventGetWeaponController_Parms, ReturnValue), Z_Construct_UClass_URTSWeaponController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "GetWeaponController", Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::RTSReturnFireComponent_eventGetWeaponController_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::RTSReturnFireComponent_eventGetWeaponController_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execGetWeaponController)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(URTSWeaponController**)Z_Param__Result=P_THIS->GetWeaponController();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function GetWeaponController ***********************

// ********** Begin Class URTSReturnFireComponent Function HandleMovementWhileFiring ***************
struct Z_Construct_UFunction_URTSReturnFireComponent_HandleMovementWhileFiring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_HandleMovementWhileFiring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "HandleMovementWhileFiring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_HandleMovementWhileFiring_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_HandleMovementWhileFiring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_HandleMovementWhileFiring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_HandleMovementWhileFiring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execHandleMovementWhileFiring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HandleMovementWhileFiring();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function HandleMovementWhileFiring *****************

// ********** Begin Class URTSReturnFireComponent Function IsReturningFire *************************
struct Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics
{
	struct RTSReturnFireComponent_eventIsReturningFire_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// State queries\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "State queries" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSReturnFireComponent_eventIsReturningFire_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSReturnFireComponent_eventIsReturningFire_Parms), &Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "IsReturningFire", Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::RTSReturnFireComponent_eventIsReturningFire_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::RTSReturnFireComponent_eventIsReturningFire_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execIsReturningFire)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsReturningFire();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function IsReturningFire ***************************

// ********** Begin Class URTSReturnFireComponent Function ProcessReturnFire ***********************
struct Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics
{
	struct RTSReturnFireComponent_eventProcessReturnFire_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventProcessReturnFire_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "ProcessReturnFire", Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::RTSReturnFireComponent_eventProcessReturnFire_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::RTSReturnFireComponent_eventProcessReturnFire_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execProcessReturnFire)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ProcessReturnFire(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function ProcessReturnFire *************************

// ********** Begin Class URTSReturnFireComponent Function RegisterAttacker ************************
struct Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics
{
	struct RTSReturnFireComponent_eventRegisterAttacker_Parms
	{
		ARTSBaseActor* Attacker;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attacker tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attacker tracking" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Attacker;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::NewProp_Attacker = { "Attacker", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventRegisterAttacker_Parms, Attacker), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::NewProp_Attacker,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "RegisterAttacker", Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::RTSReturnFireComponent_eventRegisterAttacker_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::RTSReturnFireComponent_eventRegisterAttacker_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execRegisterAttacker)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Attacker);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterAttacker(Z_Param_Attacker);
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function RegisterAttacker **************************

// ********** Begin Class URTSReturnFireComponent Function SelectReturnFireTarget ******************
struct Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics
{
	struct RTSReturnFireComponent_eventSelectReturnFireTarget_Parms
	{
		ARTSBaseActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Target management\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventSelectReturnFireTarget_Parms, ReturnValue), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "SelectReturnFireTarget", Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::RTSReturnFireComponent_eventSelectReturnFireTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::RTSReturnFireComponent_eventSelectReturnFireTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execSelectReturnFireTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ARTSBaseActor**)Z_Param__Result=P_THIS->SelectReturnFireTarget();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function SelectReturnFireTarget ********************

// ********** Begin Class URTSReturnFireComponent Function SetReturnFireTarget *********************
struct Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics
{
	struct RTSReturnFireComponent_eventSetReturnFireTarget_Parms
	{
		ARTSBaseActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventSetReturnFireTarget_Parms, Target), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "SetReturnFireTarget", Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::RTSReturnFireComponent_eventSetReturnFireTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::RTSReturnFireComponent_eventSetReturnFireTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execSetReturnFireTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetReturnFireTarget(Z_Param_Target);
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function SetReturnFireTarget ***********************

// ********** Begin Class URTSReturnFireComponent Function ShouldReturnFire ************************
struct Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics
{
	struct RTSReturnFireComponent_eventShouldReturnFire_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSReturnFireComponent_eventShouldReturnFire_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSReturnFireComponent_eventShouldReturnFire_Parms), &Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "ShouldReturnFire", Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::RTSReturnFireComponent_eventShouldReturnFire_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::RTSReturnFireComponent_eventShouldReturnFire_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execShouldReturnFire)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldReturnFire();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function ShouldReturnFire **************************

// ********** Begin Class URTSReturnFireComponent Function ShouldStopToFire ************************
struct Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics
{
	struct RTSReturnFireComponent_eventShouldStopToFire_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSReturnFireComponent_eventShouldStopToFire_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSReturnFireComponent_eventShouldStopToFire_Parms), &Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "ShouldStopToFire", Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::RTSReturnFireComponent_eventShouldStopToFire_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::RTSReturnFireComponent_eventShouldStopToFire_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execShouldStopToFire)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldStopToFire();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function ShouldStopToFire **************************

// ********** Begin Class URTSReturnFireComponent Function ShouldSwitchTarget **********************
struct Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics
{
	struct RTSReturnFireComponent_eventShouldSwitchTarget_Parms
	{
		ARTSBaseActor* NewTarget;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewTarget;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::NewProp_NewTarget = { "NewTarget", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventShouldSwitchTarget_Parms, NewTarget), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RTSReturnFireComponent_eventShouldSwitchTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RTSReturnFireComponent_eventShouldSwitchTarget_Parms), &Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::NewProp_NewTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "ShouldSwitchTarget", Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::RTSReturnFireComponent_eventShouldSwitchTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::RTSReturnFireComponent_eventShouldSwitchTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execShouldSwitchTarget)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_NewTarget);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldSwitchTarget(Z_Param_NewTarget);
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function ShouldSwitchTarget ************************

// ********** Begin Class URTSReturnFireComponent Function StartReturnFire *************************
struct Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics
{
	struct RTSReturnFireComponent_eventStartReturnFire_Parms
	{
		ARTSBaseActor* Attacker;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main return fire functions\n" },
#endif
		{ "CPP_Default_Attacker", "None" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main return fire functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Attacker;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::NewProp_Attacker = { "Attacker", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RTSReturnFireComponent_eventStartReturnFire_Parms, Attacker), Z_Construct_UClass_ARTSBaseActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::NewProp_Attacker,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "StartReturnFire", Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::PropPointers), sizeof(Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::RTSReturnFireComponent_eventStartReturnFire_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::RTSReturnFireComponent_eventStartReturnFire_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execStartReturnFire)
{
	P_GET_OBJECT(ARTSBaseActor,Z_Param_Attacker);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartReturnFire(Z_Param_Attacker);
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function StartReturnFire ***************************

// ********** Begin Class URTSReturnFireComponent Function StopReturnFire **************************
struct Z_Construct_UFunction_URTSReturnFireComponent_StopReturnFire_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URTSReturnFireComponent_StopReturnFire_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URTSReturnFireComponent, nullptr, "StopReturnFire", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URTSReturnFireComponent_StopReturnFire_Statics::Function_MetaDataParams), Z_Construct_UFunction_URTSReturnFireComponent_StopReturnFire_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_URTSReturnFireComponent_StopReturnFire()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URTSReturnFireComponent_StopReturnFire_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URTSReturnFireComponent::execStopReturnFire)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopReturnFire();
	P_NATIVE_END;
}
// ********** End Class URTSReturnFireComponent Function StopReturnFire ****************************

// ********** Begin Class URTSReturnFireComponent **************************************************
void URTSReturnFireComponent::StaticRegisterNativesURTSReturnFireComponent()
{
	UClass* Class = URTSReturnFireComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanReturnFire", &URTSReturnFireComponent::execCanReturnFire },
		{ "CleanupOldAttackers", &URTSReturnFireComponent::execCleanupOldAttackers },
		{ "GetCurrentTarget", &URTSReturnFireComponent::execGetCurrentTarget },
		{ "GetEffectiveReturnFireRange", &URTSReturnFireComponent::execGetEffectiveReturnFireRange },
		{ "GetMostRecentAttacker", &URTSReturnFireComponent::execGetMostRecentAttacker },
		{ "GetMovementSpeedMultiplier", &URTSReturnFireComponent::execGetMovementSpeedMultiplier },
		{ "GetOwnerUnit", &URTSReturnFireComponent::execGetOwnerUnit },
		{ "GetRecentAttackers", &URTSReturnFireComponent::execGetRecentAttackers },
		{ "GetWeaponController", &URTSReturnFireComponent::execGetWeaponController },
		{ "HandleMovementWhileFiring", &URTSReturnFireComponent::execHandleMovementWhileFiring },
		{ "IsReturningFire", &URTSReturnFireComponent::execIsReturningFire },
		{ "ProcessReturnFire", &URTSReturnFireComponent::execProcessReturnFire },
		{ "RegisterAttacker", &URTSReturnFireComponent::execRegisterAttacker },
		{ "SelectReturnFireTarget", &URTSReturnFireComponent::execSelectReturnFireTarget },
		{ "SetReturnFireTarget", &URTSReturnFireComponent::execSetReturnFireTarget },
		{ "ShouldReturnFire", &URTSReturnFireComponent::execShouldReturnFire },
		{ "ShouldStopToFire", &URTSReturnFireComponent::execShouldStopToFire },
		{ "ShouldSwitchTarget", &URTSReturnFireComponent::execShouldSwitchTarget },
		{ "StartReturnFire", &URTSReturnFireComponent::execStartReturnFire },
		{ "StopReturnFire", &URTSReturnFireComponent::execStopReturnFire },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URTSReturnFireComponent;
UClass* URTSReturnFireComponent::GetPrivateStaticClass()
{
	using TClass = URTSReturnFireComponent;
	if (!Z_Registration_Info_UClass_URTSReturnFireComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RTSReturnFireComponent"),
			Z_Registration_Info_UClass_URTSReturnFireComponent.InnerSingleton,
			StaticRegisterNativesURTSReturnFireComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URTSReturnFireComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_URTSReturnFireComponent_NoRegister()
{
	return URTSReturnFireComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URTSReturnFireComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Component that handles return fire while moving behavior\n * Allows units to engage enemies while maintaining movement commands\n */" },
#endif
		{ "IncludePath", "RTSReturnFireSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Component that handles return fire while moving behavior\nAllows units to engage enemies while maintaining movement commands" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "Category", "Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Return Fire" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current state\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableReturnFire_MetaData[] = {
		{ "Category", "Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Component settings\n" },
#endif
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Component settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnReturnFireStarted_MetaData[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnReturnFireStopped_MetaData[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTargetChanged_MetaData[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAttackerRegistered_MetaData[] = {
		{ "Category", "RTS Return Fire" },
		{ "ModuleRelativePath", "Public/RTSReturnFireSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FStructPropertyParams NewProp_State;
	static void NewProp_bEnableReturnFire_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableReturnFire;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnReturnFireStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnReturnFireStopped;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTargetChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAttackerRegistered;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URTSReturnFireComponent_CanReturnFire, "CanReturnFire" }, // 2861382966
		{ &Z_Construct_UFunction_URTSReturnFireComponent_CleanupOldAttackers, "CleanupOldAttackers" }, // 2002586488
		{ &Z_Construct_UFunction_URTSReturnFireComponent_GetCurrentTarget, "GetCurrentTarget" }, // 4246366683
		{ &Z_Construct_UFunction_URTSReturnFireComponent_GetEffectiveReturnFireRange, "GetEffectiveReturnFireRange" }, // 1028465626
		{ &Z_Construct_UFunction_URTSReturnFireComponent_GetMostRecentAttacker, "GetMostRecentAttacker" }, // 1520278105
		{ &Z_Construct_UFunction_URTSReturnFireComponent_GetMovementSpeedMultiplier, "GetMovementSpeedMultiplier" }, // 1468742044
		{ &Z_Construct_UFunction_URTSReturnFireComponent_GetOwnerUnit, "GetOwnerUnit" }, // 2479578044
		{ &Z_Construct_UFunction_URTSReturnFireComponent_GetRecentAttackers, "GetRecentAttackers" }, // 1502120212
		{ &Z_Construct_UFunction_URTSReturnFireComponent_GetWeaponController, "GetWeaponController" }, // 1839180752
		{ &Z_Construct_UFunction_URTSReturnFireComponent_HandleMovementWhileFiring, "HandleMovementWhileFiring" }, // 3951073472
		{ &Z_Construct_UFunction_URTSReturnFireComponent_IsReturningFire, "IsReturningFire" }, // 1043071352
		{ &Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature, "OnAttackerRegistered__DelegateSignature" }, // 1796494472
		{ &Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature, "OnReturnFireStarted__DelegateSignature" }, // 2828600492
		{ &Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature, "OnReturnFireStopped__DelegateSignature" }, // 95643671
		{ &Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature, "OnTargetChanged__DelegateSignature" }, // 631425351
		{ &Z_Construct_UFunction_URTSReturnFireComponent_ProcessReturnFire, "ProcessReturnFire" }, // 1436057227
		{ &Z_Construct_UFunction_URTSReturnFireComponent_RegisterAttacker, "RegisterAttacker" }, // 710542282
		{ &Z_Construct_UFunction_URTSReturnFireComponent_SelectReturnFireTarget, "SelectReturnFireTarget" }, // 3908399940
		{ &Z_Construct_UFunction_URTSReturnFireComponent_SetReturnFireTarget, "SetReturnFireTarget" }, // 1948626898
		{ &Z_Construct_UFunction_URTSReturnFireComponent_ShouldReturnFire, "ShouldReturnFire" }, // 1133199353
		{ &Z_Construct_UFunction_URTSReturnFireComponent_ShouldStopToFire, "ShouldStopToFire" }, // 3490269847
		{ &Z_Construct_UFunction_URTSReturnFireComponent_ShouldSwitchTarget, "ShouldSwitchTarget" }, // 3508173322
		{ &Z_Construct_UFunction_URTSReturnFireComponent_StartReturnFire, "StartReturnFire" }, // 3787990526
		{ &Z_Construct_UFunction_URTSReturnFireComponent_StopReturnFire, "StopReturnFire" }, // 2404675935
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URTSReturnFireComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSReturnFireComponent, Config), Z_Construct_UScriptStruct_FRTSReturnFireConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 2083769389
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSReturnFireComponent, State), Z_Construct_UScriptStruct_FRTSReturnFireState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 1897398699
void Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_bEnableReturnFire_SetBit(void* Obj)
{
	((URTSReturnFireComponent*)Obj)->bEnableReturnFire = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_bEnableReturnFire = { "bEnableReturnFire", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSReturnFireComponent), &Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_bEnableReturnFire_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableReturnFire_MetaData), NewProp_bEnableReturnFire_MetaData) };
void Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((URTSReturnFireComponent*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URTSReturnFireComponent), &Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_OnReturnFireStarted = { "OnReturnFireStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSReturnFireComponent, OnReturnFireStarted), Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnReturnFireStarted_MetaData), NewProp_OnReturnFireStarted_MetaData) }; // 2828600492
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_OnReturnFireStopped = { "OnReturnFireStopped", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSReturnFireComponent, OnReturnFireStopped), Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnReturnFireStopped__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnReturnFireStopped_MetaData), NewProp_OnReturnFireStopped_MetaData) }; // 95643671
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_OnTargetChanged = { "OnTargetChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSReturnFireComponent, OnTargetChanged), Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnTargetChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTargetChanged_MetaData), NewProp_OnTargetChanged_MetaData) }; // 631425351
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_OnAttackerRegistered = { "OnAttackerRegistered", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URTSReturnFireComponent, OnAttackerRegistered), Z_Construct_UDelegateFunction_URTSReturnFireComponent_OnAttackerRegistered__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAttackerRegistered_MetaData), NewProp_OnAttackerRegistered_MetaData) }; // 1796494472
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URTSReturnFireComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_bEnableReturnFire,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_OnReturnFireStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_OnReturnFireStopped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_OnTargetChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URTSReturnFireComponent_Statics::NewProp_OnAttackerRegistered,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSReturnFireComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URTSReturnFireComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_ArmorWars,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URTSReturnFireComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URTSReturnFireComponent_Statics::ClassParams = {
	&URTSReturnFireComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URTSReturnFireComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URTSReturnFireComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URTSReturnFireComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_URTSReturnFireComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URTSReturnFireComponent()
{
	if (!Z_Registration_Info_UClass_URTSReturnFireComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URTSReturnFireComponent.OuterSingleton, Z_Construct_UClass_URTSReturnFireComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URTSReturnFireComponent.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URTSReturnFireComponent);
URTSReturnFireComponent::~URTSReturnFireComponent() {}
// ********** End Class URTSReturnFireComponent ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h__Script_ArmorWars_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERTSReturnFireBehavior_StaticEnum, TEXT("ERTSReturnFireBehavior"), &Z_Registration_Info_UEnum_ERTSReturnFireBehavior, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2735397557U) },
		{ ERTSReturnFirePriority_StaticEnum, TEXT("ERTSReturnFirePriority"), &Z_Registration_Info_UEnum_ERTSReturnFirePriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1857039825U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRTSReturnFireConfig::StaticStruct, Z_Construct_UScriptStruct_FRTSReturnFireConfig_Statics::NewStructOps, TEXT("RTSReturnFireConfig"), &Z_Registration_Info_UScriptStruct_FRTSReturnFireConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSReturnFireConfig), 2083769389U) },
		{ FRTSReturnFireState::StaticStruct, Z_Construct_UScriptStruct_FRTSReturnFireState_Statics::NewStructOps, TEXT("RTSReturnFireState"), &Z_Registration_Info_UScriptStruct_FRTSReturnFireState, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRTSReturnFireState), 1897398699U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URTSReturnFireComponent, URTSReturnFireComponent::StaticClass, TEXT("URTSReturnFireComponent"), &Z_Registration_Info_UClass_URTSReturnFireComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URTSReturnFireComponent), 1263213514U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h__Script_ArmorWars_1031473378(TEXT("/Script/ArmorWars"),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h__Script_ArmorWars_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h__Script_ArmorWars_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h__Script_ArmorWars_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h__Script_ArmorWars_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h__Script_ArmorWars_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_ArmorWars_Source_ArmorWars_Public_RTSReturnFireSystem_h__Script_ArmorWars_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
